import { useState, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import toast from "react-hot-toast";

type TrainingStatus = "PENDING" | "RUNNING" | "COMPLETE" | "ERROR";

export interface TrainingEvent {
  timestamp: string;
  data: string;
}

export interface TrainingPlanDetail {
  sessionType: string;
  duration: string;
  intensity: string;
  details: string;
  macronutrients: {
    protein: string;
    carbohydrates: string;
    fat: string;
  };
  caloricIntake?: string;
}

interface PersonalInfo {
  numWeeks: number;
  personalGoals: string;
  athleteBackground: string;
  strengthsWeaknesses: string;
  athleteLifestyle: string;
  trainingResponse: string;
  targetAreas: string;
  trainingIntensity: string;
}

interface Message {
  id: string;
  text: string;
  type: "question" | "answer" | "event";
}

export interface TrainingPlan {
  [key: string]: TrainingPlanDetail[];
}

export const useCrewJob = (initialJobId: string) => {
  const [running, setRunning] = useState<boolean>(false);
  const [trainingDetails, setTrainingDetails] = useState<TrainingPlan[]>([]);
  const [currentJobId, setCurrentJobId] = useState<string>(initialJobId);
  const [eventLog, setEventLog] = useState<TrainingEvent[]>([]);
  const [completedWeeks, setCompletedWeeks] = useState<number>(0);
  const [pendingQuestionQueue, setPendingQuestionQueue] = useState<string[]>(
    [],
  );
  const [taskResults, setTaskResults] = useState<string[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<string | null>(null);
  const [answer, setAnswer] = useState<string>("");
  const [messageList, setMessageList] = useState<Message[]>([]);
  const addedQuestions = useRef<Set<string>>(new Set());
  const ws = useRef<WebSocket | null>(null);

  const MAX_RETRIES = 5;
  const INITIAL_RETRY_DELAY = 1000; // 1 second

  const addEvent = (event: TrainingEvent) => {
    setEventLog((prev) => [...prev, event]);
    if (event?.data?.includes("goal_options")) {
      setPendingQuestionQueue((prev) => [...prev, event.data]);
    }
  };

  const fetchJobStatus = useCallback(
    async (retries = 0) => {
      try {
        if (!currentJobId || !running) return;

        const response = await axios.get(
          `http://localhost:3002/api/training/${currentJobId}`,
        );
        const { status, result, events, pendingQuestion, task_results } =
          response.data;

        if (pendingQuestion && !addedQuestions.current.has(pendingQuestion)) {
          setPendingQuestionQueue((prev) => [...prev, pendingQuestion]);
          addedQuestions.current.add(pendingQuestion);
        }

        const newEvents = events.filter(
          (newEvent: any) =>
            !eventLog.some(
              (existingEvent) => existingEvent.timestamp === newEvent.timestamp,
            ),
        );

        setEventLog((prev) => [...prev, ...newEvents]);
        setTaskResults(task_results); // Update task results

        if (status === "COMPLETE" || status === "ERROR") {
          setRunning(false);
          toast.success(`Training ${status.toLowerCase()}.`);
        }
      } catch (error) {
        if (retries < MAX_RETRIES) {
          const retryDelay = INITIAL_RETRY_DELAY * Math.pow(2, retries); // Exponential backoff
          setTimeout(() => fetchJobStatus(retries + 1), retryDelay);
        } else {
          setRunning(false);
          toast.error("Failed to get training status.");
          console.error("Fetch Error:", error);
        }
      }
    },
    [currentJobId, running, eventLog],
  );

  useEffect(() => {
    const intervalId = setInterval(() => fetchJobStatus(), 10000); // Increase interval to 10 seconds
    return () => clearInterval(intervalId);
  }, [fetchJobStatus]);

  useEffect(() => {
    const updateTrainingDetails = async () => {
      try {
        if (currentJobId) {
          const response = await axios.get(
            `http://localhost:3002/api/training/${currentJobId}`,
          );
          console.log("Training details response:", response.data.result);

          // Append new training details to the existing array
          setTrainingDetails((prev) => [...prev, response.data.result]);
        }
      } catch (error) {
        console.error("Fetch Error:", error);
      }
    };

    updateTrainingDetails();
  }, [eventLog, currentJobId]);

  useEffect(() => {
    if (pendingQuestionQueue.length > 0 && !currentQuestion) {
      setCurrentQuestion(pendingQuestionQueue[0]);
      setPendingQuestionQueue((prev) => prev.slice(1));
    }
  }, [pendingQuestionQueue, currentQuestion]);

  useEffect(() => {
    if (!currentJobId) return;

    if (ws.current) {
      ws.current.close();
    }

    ws.current = new WebSocket("ws://localhost:8000/ws/" + currentJobId);

    ws.current.onopen = () => {
      sendMessage({ type: "start", jobId: currentJobId });
    };

    ws.current.onmessage = (event) => {
      const message = JSON.parse(event.data);
      console.log("WebSocket message received:", message); // Added logging here
      addEvent({
        timestamp: new Date().toISOString(),
        data: message.message,
      });
      console.log("Updating message list with:", {
        id: new Date().toISOString(),
        text: message.message,
        type: message.type === "initial_question" ? "question" : "event",
      }); // Log the message being added
      setMessageList((prev) => [
        ...prev,
        {
          id: new Date().toISOString(),
          text: message.message,
          type: message.type === "initial_question" ? "question" : "event",
        },
      ]);
    };

    ws.current.onerror = (error) => {
      toast.error("WebSocket error occurred. Check console for details.");
      console.error("WebSocket error: ", error);
    };

    ws.current.onclose = () => {
      toast.error("WebSocket connection closed unexpectedly.");
      console.error("WebSocket connection closed.");
    };

    return () => {
      if (ws.current) {
        ws.current.close();
      }
    };
  }, [currentJobId]);

  const sendMessage = (message: any) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
    } else {
      const interval = setInterval(() => {
        if (ws.current && ws.current.readyState === WebSocket.OPEN) {
          ws.current.send(JSON.stringify(message));
          clearInterval(interval);
        }
      }, 100);
    }
  };

  const startTraining = async (jobId: string) => {
    if (running) return; // Prevent starting multiple jobs
    setRunning(true);

    try {
      const response = await axios.post("http://localhost:3002/api/training", {
        job_id: jobId,
      });
      toast.success("Training started");

      // Delay or wait for job initialization before fetching status
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Delay for 1 second

      fetchJobStatus(); // Start polling for job status

      if (ws.current) {
        ws.current.close();
      }

      ws.current = new WebSocket("ws://localhost:8000/ws/" + jobId); // Use the correct job ID

      ws.current.onopen = () => {
        sendMessage({ type: "start", jobId: jobId });
      };

      ws.current.onmessage = (event) => {
        const message = JSON.parse(event.data);
        console.log("WebSocket message:", message);
        addEvent({
          timestamp: new Date().toISOString(),
          data: message.message,
        });
        setMessageList((prev) => [
          ...prev,
          {
            id: new Date().toISOString(),
            text: message.message,
            type: message.type === "initial_question" ? "question" : "event",
          },
        ]);
      };

      ws.current.onerror = (error) => {
        toast.error("WebSocket error occurred. Check console for details.");
        console.error("WebSocket error: ", error);
      };

      ws.current.onclose = () => {
        toast.error("WebSocket connection closed unexpectedly.");
        console.error("WebSocket connection closed.");
      };
    } catch (error) {
      setRunning(false);
      toast.error("Failed to start training");
      console.error(
        "Failed to start training for job ID:",
        currentJobId,
        error,
      );
    }
  };

  const submitAnswer = (answer: string) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify({ type: "answer", message: answer }));
      toast.success("Answer submitted");
      setCurrentQuestion(null); // Clear the current question after submission
      setAnswer(""); // Clear the input field after submission
    } else {
      toast.error("WebSocket is not connected.");
    }
  };

  return {
    running,
    trainingDetails,
    startTraining,
    eventLog,
    currentJobId,
    addEvent,
    currentQuestion,
    answer,
    setAnswer,
    submitAnswer,
    setCurrentQuestion,
    pendingQuestionQueue,
    setPendingQuestionQueue,
    taskResults,
    setJobId: setCurrentJobId,
    messageList: messageList.filter((message) => message.type !== "event"), // Filter out events
    setMessageList,
  };
};
