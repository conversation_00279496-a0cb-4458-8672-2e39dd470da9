// hooks/useCustomRouter.ts
import { useRouter as useNextRouter } from "next/navigation";
import { useEffect, useState } from "react";

export const useCustomRouter = () => {
  const [isReady, setIsReady] = useState(false);
  const router = useNextRouter();

  useEffect(() => {
    setIsReady(true);
  }, []);

  return {
    ...router,
    isReady,
    push: (url: string) => {
      if (isReady) {
        router.push(url);
      }
    },
  };
};
