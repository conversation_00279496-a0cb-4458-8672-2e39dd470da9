import React from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

// Updated component prop type to accept string OR object/array
export function AzureMapsOutput({
  content,
}: {
  content: string | any[] | Record<string, any>;
}) {
  let data: any = null;
  let pois: any[] = [];
  let parseError = false;

  // Check if content is already an object/array, otherwise try parsing
  if (typeof content === "string") {
    try {
      data = JSON.parse(content);
    } catch (err) {
      console.error(
        "AzureMapsOutput: Error parsing input content string:",
        err,
        "Input string:",
        content,
      );
      parseError = true;
    }
  } else if (typeof content === "object" && content !== null) {
    // Content is already an object/array, use it directly
    data = content;
  } else {
    // Invalid content type
    console.error(
      "AzureMapsOutput: Received invalid content type:",
      typeof content,
    );
    parseError = true;
  }

  // If parsing failed or data is null/not an object, show error
  if (parseError || typeof data !== "object" || data === null) {
    return <div>Error processing Azure Maps data.</div>;
  }

  // Now, try to extract the POI array from the processed data (which is an object or array)
  try {
    // Check 1: Is the data directly the array?
    if (Array.isArray(data)) {
      pois = data;
    }
    // Check 2: Does it have a 'content' property that is the array?
    else if ((data as any).content && Array.isArray((data as any).content)) {
      pois = (data as any).content;
    }
    // Check 3: Is it nested inside kwargs.content (like a ToolMessage structure)?
    else if (
      (data as any).kwargs &&
      (data as any).kwargs.content &&
      Array.isArray((data as any).kwargs.content)
    ) {
      console.log("AzureMapsOutput: Found POIs nested in kwargs.content");
      pois = (data as any).kwargs.content;
    }
    // Add other potential nested structures if needed
    else {
      // Default: No usable POI array found in the object structure
      console.warn(
        "AzureMapsOutput: Could not find POI array in expected locations within data structure:",
        data,
      );
      pois = [];
    }

    if (pois.length === 0) {
      // Check if data itself might be a single POI object (not an array)
      // This is less likely for Azure Maps nearby, but good fallback
      if (!Array.isArray(data) && data.id && data.position) {
        console.log(
          "AzureMapsOutput: Data appears to be a single POI object. Rendering it.",
        );
        pois = [data]; // Wrap the single object in an array
      } else {
        console.log(
          "AzureMapsOutput: No POIs found after checking structures:",
          data,
        );
        return <div>No points of interest found.</div>;
      }
    }

    // Render the POIs
    return (
      <div className="space-y-2">
        {pois.map((poi: any, index: number) => (
          <div
            key={poi.id || index}
            className="rounded border bg-white p-2 text-xs"
          >
            <div className="font-semibold">
              {poi.poi?.name ?? "Unknown POI"}
            </div>
            {poi.address?.freeformAddress && (
              <div className="text-gray-600">{poi.address.freeformAddress}</div>
            )}
            {poi.dist !== undefined && (
              <div className="text-gray-500">
                Distance: {poi.dist.toFixed(0)} m
              </div> // Format distance
            )}
          </div>
        ))}
      </div>
    );
  } catch (renderErr) {
    // Catch errors specifically during the POI extraction/rendering phase
    console.error(
      "AzureMapsOutput: Error rendering POIs from processed data:",
      renderErr,
      "Processed data:",
      data,
    );
    return <div>Error rendering Azure Maps points of interest.</div>;
  }
}
