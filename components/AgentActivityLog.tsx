"use client";

import { AgentActivityType } from "@/app/api/agent-tools/utils/agent-logger";
import React from "react";
// Define the log structure as it comes from the server
interface AgentActivityLog {
  type: AgentActivityType;
  timestamp: string;
  agentName?: string;
  message: string;
  details?: Record<string, any>;
}

interface AgentActivityLogProps {
  log: AgentActivityLog;
}

// Component to display a single agent activity log entry
export function AgentActivityLogEntry({ log }: AgentActivityLogProps) {
  // Format the timestamp to show just the time portion
  const formattedTime = new Date(log.timestamp).toLocaleTimeString();

  // Determine icon and color based on log type
  let icon = "🤖";
  let bgColor = "bg-gray-100";
  let textColor = "text-gray-800";
  let borderColor = "border-gray-300";

  switch (log.type) {
    case AgentActivityType.AGENT_ACTIVE:
      icon = "🟢";
      bgColor = "bg-green-50";
      textColor = "text-green-800";
      borderColor = "border-green-200";
      break;
    case AgentActivityType.SPECIALIZED_AGENT_CALL:
      icon = "📞";
      bgColor = "bg-blue-50";
      textColor = "text-blue-800";
      borderColor = "border-blue-200";
      break;
    case AgentActivityType.USER_DATA_FETCH:
      icon = "📊";
      bgColor = "bg-purple-50";
      textColor = "text-purple-800";
      borderColor = "border-purple-200";
      break;
    case AgentActivityType.TOOL_CALL:
      icon = "🔧";
      bgColor = "bg-yellow-50";
      textColor = "text-yellow-800";
      borderColor = "border-yellow-200";
      break;
    case AgentActivityType.STREAM_START:
      icon = "📡";
      bgColor = "bg-indigo-50";
      textColor = "text-indigo-800";
      borderColor = "border-indigo-200";
      break;
    case AgentActivityType.COLLECTION_AGENT_CALL:
      icon = "📚";
      bgColor = "bg-teal-50";
      textColor = "text-teal-800";
      borderColor = "border-teal-200";
      break;
  }

  return (
    <div
      className={`my-1 rounded-md p-2 text-xs ${bgColor} ${textColor} border ${borderColor} overflow-hidden`}
    >
      <div className="flex items-start">
        <span className="mr-2 text-lg">{icon}</span>
        <div className="flex-1">
          <div className="flex justify-between">
            <span className="font-semibold">
              {log.agentName ? `${log.agentName} | ` : ""}
              {log.type.replace(/_/g, " ")}
            </span>
            <span className="text-xs opacity-75">{formattedTime}</span>
          </div>
          <p className="mt-1">{log.message}</p>

          {/* Show details if available */}
          {log.details && Object.keys(log.details).length > 0 && (
            <div className="border-gray-300 ml-2 mt-1 border-l-2 pl-2">
              {Object.entries(log.details).map(([key, value]) => (
                <div key={key} className="text-xs">
                  <span className="font-medium">{key}: </span>
                  <span>
                    {typeof value === "object" ? JSON.stringify(value) : value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Component to display multiple log entries
interface AgentActivityLogListProps {
  logs: AgentActivityLog[];
}

export function AgentActivityLogList({ logs }: AgentActivityLogListProps) {
  if (!logs || logs.length === 0) {
    return null;
  }

  return (
    <div className="my-2 w-full space-y-1">
      {logs.map((log, index) => (
        <AgentActivityLogEntry key={`${log.timestamp}-${index}`} log={log} />
      ))}
    </div>
  );
}
