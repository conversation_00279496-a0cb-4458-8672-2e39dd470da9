import React from "react";
import Cycling<PERSON>ogo from "@/public/images/coaches/Cycling.png";
import StrengthLogo from "@/public/images/coaches/Strength.png";
import RunningLogo from "@/public/images/coaches/Running.png";
import NutritionLogo from "@/public/images/coaches/Nutrition.png";
import RecoveryLogo from "@/public/images/coaches/Recovery.png";
import Image from "next/image";

const roleMapping = {
  "Cycling Coach": { name: "Cycling Coach", logo: CyclingLogo },
  "Strength Coach": { name: "Strength Coach", logo: StrengthLogo },
  "Running Coach": { name: "Running Coach", logo: Running<PERSON>ogo },
  "Nutrition Coach": { name: "Nutrition Coach", logo: NutritionLogo },
  "Recovery Coach": { name: "Recovery Coach", logo: Recovery<PERSON>ogo },
};

const CoachesList: React.FC<{
  handleCoachClick: (coachName: string) => void;
}> = ({ handleCoachClick }) => {
  return (
    <div className="flex flex-col px-4">
      {Object.values(roleMapping).map((role) => (
        <div
          key={role.name}
          className="mb-1 flex cursor-pointer items-center rounded-md p-2 hover:bg-[#e9e9e9]  dark:hover:bg-boxdark-2 "
          onClick={() => handleCoachClick(role.name)} // Add onClick handler
        >
          <Image src={role.logo} alt={role.name} className="mr-2 h-6 w-6" />
          <span className="text-sm font-medium dark:text-white">
            {role.name}
          </span>
        </div>
      ))}
    </div>
  );
};

export default CoachesList;
