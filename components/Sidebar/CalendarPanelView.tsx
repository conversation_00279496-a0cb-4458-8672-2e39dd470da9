import { cn } from "@/app/lib/utlis";
import { FC, useEffect, useRef, useState } from "react";
import { ScrollArea } from "../ui/scroll-area";
import { Button } from "../ui/button";
import {
  ArrowLeft,
  ChevronDown,
  ChevronUp,
  LucideCalendarRange,
  Maximize2,
  Minimize2,
  X,
} from "lucide-react";
import Calendar from "../Calender";
import { useAppSelector } from "@/store/jobHook";
import { RootState } from "@/store/store";
import { setIsOpen, toggleCalendar } from "@/store/slices/calendarSlice";
import { useDispatch } from "react-redux";
import {
  setIsDualView,
  setIsSidebarFullExpanded,
  setRightCalendarPanel,
} from "@/store/slices/jobSlice";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

const CalendarPanelView: FC = () => {
  const dispatch = useDispatch();
  const pathname = usePathname();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const artifacts = [
    {
      title: "Calendar",
      icon: <LucideCalendarRange className="h-4.5 w-4.5" />,
      updatedText: "Added yesterday",
    },
  ];

  const [isArtifactsExpanded, setIsArtifactsExpanded] =
    useState<boolean>(false);
  const [isArtifactFullyExpanded, setIsArtifactFullyExpanded] =
    useState<boolean>(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

  const { rightCalendarPanel, isCoachesExpanded, isDualView } = useAppSelector(
    (state: RootState) => state.job,
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isArtifactsExpanded && isArtifactFullyExpanded) {
      dispatch(setIsSidebarFullExpanded(true));
    } else {
      dispatch(setIsSidebarFullExpanded(false));
    }
  }, [isArtifactsExpanded, isArtifactFullyExpanded]);

  useEffect(() => {
    if (isDualView) {
      setIsArtifactsExpanded(true);
      setIsArtifactFullyExpanded(true);
    }
  }, [isDualView]);

  const toggleExpand = () => {
    if (isArtifactsExpanded) setIsArtifactsExpanded(false);
    if (isArtifactFullyExpanded) setIsArtifactFullyExpanded(false);
  };

  return (
    // <motion.div
    //     className="flex items-center justify-center"
    //     initial={{ opacity: 0, x: 100 }}
    //     animate={{ opacity: 1, x: 0 }}
    //     exit={{ opacity: 0, x: 100 }}
    //     transition={{ duration: 0.5 }}
    // >
    // </motion.div>
    <div
      className={cn(
        "flex flex-col border-l border-[#bdbdbd] transition-all duration-300 ease-in-out",
        isArtifactsExpanded
          ? isArtifactFullyExpanded
            ? "w-[calc(50%-30px)]"
            : "w-[400px]"
          : "w-[400px]",
        isCoachesExpanded ? "w-0 overflow-hidden opacity-0" : "",
      )}
    >
      {rightCalendarPanel && (
        <div className="flex h-14 items-center justify-between border-b border-[#bdbdbd] px-4">
          <div className="flex w-full flex-row justify-between">
            <div className="flex items-center gap-2">
              {rightCalendarPanel && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-2"
                  onClick={() => {
                    dispatch(setRightCalendarPanel(false));
                    setIsArtifactsExpanded(false);
                    setIsArtifactFullyExpanded(false);
                  }}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              <div className="relative" ref={dropdownRef}>
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-2 flex w-[100px]"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                  <span className="font-semibold">Calendar</span>
                  {isDropdownOpen ? (
                    <ChevronUp
                      className={`h-4 w-4 transition-transform duration-200`}
                    />
                  ) : (
                    <ChevronDown
                      className={`h-4 w-4 transition-transform duration-200`}
                    />
                  )}
                </Button>

                {isDropdownOpen && (
                  <div
                    className="scrollbar-hide absolute left-0 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5"
                    style={{
                      zIndex: 1000,
                      top: "100%",
                      maxHeight: "200px",
                      overflowY: "auto",
                    }}
                  >
                    <div
                      className="py-1"
                      role="menu"
                      aria-orientation="vertical"
                      aria-labelledby="options-menu"
                    >
                      {artifacts.map((item, idx) => (
                        <div
                          key={idx}
                          className="flex items-center justify-between hover:bg-[#eeeeee]"
                        >
                          <button
                            className="text-gray-700 hover:bg-gray-700 hover:text-gray-900 block flex w-full items-center justify-between px-4 py-2 text-left text-sm"
                            role="menuitem"
                            onClick={() => {
                              if (item.title === "Calendar") {
                                dispatch(setRightCalendarPanel(true));
                                dispatch(setIsOpen(true));
                              }
                              setIsDropdownOpen(!isDropdownOpen);
                            }}
                          >
                            {item.title}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex">
              <div className="flex items-center gap-2">
                {isArtifactsExpanded && isArtifactFullyExpanded ? (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="mr-2"
                    onClick={toggleExpand}
                  >
                    <Minimize2
                      className={`h-4 w-4 transition-transform duration-200`}
                    />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="mr-2"
                    onClick={() => {
                      setIsArtifactsExpanded(true);
                      setIsArtifactFullyExpanded(true);
                    }}
                  >
                    <Maximize2
                      className={`h-4 w-4 transition-transform duration-200`}
                    />
                  </Button>
                )}
              </div>
              {rightCalendarPanel && (
                <button
                  className="text-slate-500 hover:text-slate-800"
                  onClick={() => {
                    dispatch(setRightCalendarPanel(false));
                    setIsArtifactsExpanded(false);
                    dispatch(setIsDualView(false));
                  }}
                >
                  <X size={20} />
                </button>
              )}
            </div>
          </div>
        </div>
      )}
      <ScrollArea className="flex-1">
        <div className="h-full">
          <Calendar onAnimationComplete={() => {}} />
        </div>
      </ScrollArea>
    </div>
  );
};

export default CalendarPanelView;
