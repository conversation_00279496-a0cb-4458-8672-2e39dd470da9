import React, { useState } from "react";

interface ContextData {
  userProfile: string;
  trainingHistory: string;
  planContext: string;
  domainKnowledge: string;
  sessionConsiderations: string;
  retrievalSources: string[];
}

interface SessionGenerationExplainerProps {
  contextData?: ContextData;
  domain: string;
}

/**
 * A component that explains the context and knowledge used to generate a workout session
 */
export const SessionGenerationExplainer: React.FC<
  SessionGenerationExplainerProps
> = ({ contextData, domain }) => {
  const [expanded, setExpanded] = useState(false);

  if (!contextData) {
    return null;
  }

  // Domain icon mapping
  const domainIcons = {
    strength_training: "💪",
    cardio: "🏃‍♂️",
    recovery: "🧘‍♀️",
    nutrition: "🥗",
    planning: "📋",
    knowledge: "📚",
    general: "⚡",
  };

  const domainIcon = domainIcons[domain as keyof typeof domainIcons] || "🏋️‍♀️";

  return (
    <div className="border-gray-200 mb-8 mt-6 overflow-hidden rounded-lg border">
      <button
        onClick={() => setExpanded(!expanded)}
        className="bg-gray-50 text-gray-800 hover:bg-gray-100 flex w-full items-center justify-between px-4 py-3 font-medium transition-colors"
      >
        <div className="flex items-center gap-2">
          <span className="text-xl">{domainIcon}</span>
          <span>Workout Generation Context</span>
        </div>
        <span>{expanded ? "▲" : "▼"}</span>
      </button>

      {expanded && (
        <div className="space-y-4 bg-white p-4 text-sm">
          {/* User Profile Section */}
          <section>
            <h3 className="text-gray-900 mb-2 font-medium">Your Profile</h3>
            <div className="bg-gray-50 whitespace-pre-wrap rounded p-3">
              {contextData.userProfile}
            </div>
          </section>

          {/* Training History Section */}
          <section>
            <h3 className="text-gray-900 mb-2 font-medium">
              Training History Analysis
            </h3>
            <div className="bg-gray-50 whitespace-pre-wrap rounded p-3">
              {contextData.trainingHistory}
            </div>
          </section>

          {/* Plan Context Section */}
          <section>
            <h3 className="text-gray-900 mb-2 font-medium">
              Training Plan Context
            </h3>
            <div className="bg-gray-50 whitespace-pre-wrap rounded p-3">
              {contextData.planContext}
            </div>
          </section>

          {/* Domain Knowledge Section */}
          <section>
            <h3 className="text-gray-900 mb-2 font-medium">
              {domainIcon} {domain.replace("_", " ")} Knowledge Applied
            </h3>
            <div className="bg-gray-50 whitespace-pre-wrap rounded p-3">
              {contextData.domainKnowledge}
            </div>
          </section>

          {/* Session Considerations Section */}
          <section>
            <h3 className="text-gray-900 mb-2 font-medium">
              Specific Considerations
            </h3>
            <div className="bg-gray-50 whitespace-pre-wrap rounded p-3">
              {contextData.sessionConsiderations}
            </div>
          </section>

          {/* Knowledge Sources Section */}
          <section>
            <h3 className="text-gray-900 mb-2 font-medium">
              Knowledge Sources
            </h3>
            <ul className="list-disc space-y-1 pl-5">
              {contextData.retrievalSources.map((source, index) => (
                <li key={index} className="text-gray-700">
                  {source}
                </li>
              ))}
            </ul>
          </section>

          <p className="text-gray-500 mt-2 text-xs italic">
            This workout was generated by analyzing your profile, training
            history, and applying domain-specific knowledge.
          </p>
        </div>
      )}
    </div>
  );
};

export default SessionGenerationExplainer;
