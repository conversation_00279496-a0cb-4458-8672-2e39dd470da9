import { useState, useEffect, useRef } from "react";
import {
  Stepper,
  StepperItem,
  StepperIndicator,
  StepperSeparator,
  StepperTrigger,
} from "@/components/ui/stepper";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppDispatch } from "@/store/jobHook";
import { PlanDetails } from "@/types/plan";
import {
  SummaryItem,
  UserGoals,
  SidebarStateData,
} from "@/app/api/onboarding/state";
import { OnboardingPlanCalendarView } from "@/components/Onboarding/OnboardingPlanCalendarView";
import React from "react";
import { File, Calendar, Clock, Target, Zap, CheckCircle } from "lucide-react";

// --- Local interfaces that are not conflicting ---

interface SportSuggestion {
  label: string;
  value: string;
}

interface OnboardingSidebarProps {
  sidebarData?: SidebarStateData | null;
  selectedSport?: string | null;
}

// Document metadata interface
interface UploadedDocument {
  filename: string;
  file_type: string;
  upload_date: string;
  source: string;
}

// Document List Component
const DocumentList: React.FC<{
  documents: UploadedDocument[];
  keyInsights?: Record<string, string>;
}> = ({ documents, keyInsights = {} }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedDocs, setExpandedDocs] = useState<Record<string, boolean>>({});
  const [showInsights, setShowInsights] = useState<Record<string, boolean>>({});
  const [showMoreInsights, setShowMoreInsights] = useState<
    Record<string, boolean>
  >({});

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case "pdf":
        return (
          <svg
            className="h-4 w-4 text-red-500"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
        );
      case "csv":
      case "spreadsheet":
        return (
          <svg
            className="h-4 w-4 text-green-500"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <path d="M9,13V15H7V13H9M13,13V15H11V13H13M17,13V15H15V13H17Z" />
          </svg>
        );
      case "fitness_data":
        return (
          <svg
            className="h-4 w-4 text-purple-500"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M20.57,14.86L22,13.43L20.57,12L17,15.57L8.43,7L12,3.43L10.57,2L9.14,3.43L7.71,2L5.57,4.14L4.14,2.71L2.71,4.14L4.14,5.57L2,7.71L3.43,9.14L4.86,7.71L13.43,16.29L9.86,19.86L11.29,21.29L12.71,19.86L14.14,21.29L16.29,19.14L17.71,20.57L19.14,19.14L17.71,17.71L19.86,15.57L18.43,14.14L20.57,14.86Z" />
          </svg>
        );
      case "image":
        return (
          <svg
            className="h-4 w-4 text-blue-500"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" />
          </svg>
        );
      case "document":
        return (
          <svg
            className="h-4 w-4 text-blue-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
        );
      case "text":
        return (
          <svg
            className="text-gray-500 h-4 w-4"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
        );
      default:
        return (
          <svg
            className="text-gray-400 h-4 w-4"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
        );
    }
  };

  const formatDate = (dateString: string) => {
    try {
      if (dateString === "Unknown Date") return "Unknown Date";
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch {
      return "Unknown Date";
    }
  };

  const getFileTypeLabel = (fileType: string) => {
    const labels: Record<string, string> = {
      pdf: "PDF",
      csv: "CSV",
      spreadsheet: "Spreadsheet",
      fitness_data: "Fitness Data",
      image: "Image",
      document: "Document",
      text: "Text",
      unknown: "File",
    };
    return labels[fileType] || "File";
  };

  // Helper function to parse insights from text
  const parseInsights = (insightText: string) => {
    // Split by common separators and filter out empty entries
    const insights = insightText
      .split(/[;|\n]/)
      .map((item) => item.trim())
      .filter((item) => item.length > 0 && item.length < 100); // Filter out very long items
    return insights;
  };

  // Get insights related to a specific document
  const getDocumentInsights = (filename: string) => {
    const insights: string[] = [];

    // Look for insights that might be related to this document
    Object.entries(keyInsights).forEach(([key, value]) => {
      if (value && typeof value === "string") {
        // Parse the insight text into individual insights
        const parsedInsights = parseInsights(value);
        insights.push(...parsedInsights);
      }
    });

    return insights;
  };

  const toggleDocExpansion = (filename: string) => {
    setExpandedDocs((prev) => ({
      ...prev,
      [filename]: !prev[filename],
    }));
  };

  const toggleInsights = (filename: string) => {
    setShowInsights((prev) => ({
      ...prev,
      [filename]: !prev[filename],
    }));
  };

  const toggleMoreInsights = (filename: string) => {
    setShowMoreInsights((prev) => ({
      ...prev,
      [filename]: !prev[filename],
    }));
  };

  if (!documents || documents.length === 0) {
    return null;
  }

  // Calculate files with insights available
  const filesWithInsights = documents.filter((doc) => {
    const docInsights = getDocumentInsights(doc.filename);
    return docInsights.length > 0;
  }).length;

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h4 className="text-gray-700 text-sm font-medium">
            Uploaded Files ({documents.length})
          </h4>
          {filesWithInsights > 0 && (
            <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-white">
              <span className="text-xs font-bold">{filesWithInsights}</span>
            </div>
          )}
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-xs text-purple-600 hover:text-purple-800 focus:outline-none"
        >
          {isExpanded ? "Hide" : "Show"}
        </button>
      </div>

      {isExpanded && (
        <div className="mt-2 space-y-3">
          {documents.map((doc, index) => {
            const docInsights = getDocumentInsights(doc.filename);
            const hasInsights = docInsights.length > 0;
            const isDocExpanded = expandedDocs[doc.filename];
            const areInsightsVisible = showInsights[doc.filename];
            const showMore = showMoreInsights[doc.filename];
            const maxInitialInsights = 2;
            const visibleInsights = showMore
              ? docInsights
              : docInsights.slice(0, maxInitialInsights);

            return (
              <div
                key={`${doc.filename}-${index}`}
                className="border-gray-200 bg-gray-50 rounded-md border"
              >
                {/* Main document row */}
                <div
                  className="hover:bg-gray-100 flex cursor-pointer items-center space-x-3 p-3"
                  onClick={() => toggleDocExpansion(doc.filename)}
                >
                  <div className="flex-shrink-0">
                    {getFileIcon(doc.file_type)}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-gray-900 truncate text-sm font-medium">
                      {doc.filename}
                    </p>
                    <p className="text-gray-500 text-xs">
                      {getFileTypeLabel(doc.file_type)} •{" "}
                      {formatDate(doc.upload_date)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                      Analyzed
                    </span>
                    <svg
                      className={`text-gray-500 h-4 w-4 transition-transform ${
                        isDocExpanded ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>

                {/* Expanded content - show insights by default */}
                {isDocExpanded && (
                  <div className="border-gray-200 border-t px-3 pb-3">
                    {hasInsights ? (
                      <div className="mt-3">
                        <div className="mb-3 flex items-center justify-between">
                          <h5 className="text-gray-700 text-xs font-medium">
                            Key Insights:
                          </h5>
                          <span className="text-gray-500 text-xs">
                            {formatDate(doc.upload_date)}
                          </span>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          {visibleInsights.map((insight, idx) => (
                            <div
                              key={idx}
                              className="inline-flex rounded-full border border-blue-200 bg-blue-50 px-3 py-1"
                            >
                              <span className="text-xs text-blue-800">
                                {insight}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* Show More/Less button */}
                        {docInsights.length > maxInitialInsights && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleMoreInsights(doc.filename);
                            }}
                            className="mt-3 text-xs font-medium text-blue-600 hover:text-blue-800 focus:outline-none"
                          >
                            {showMore
                              ? `Show Less (${docInsights.length - maxInitialInsights} hidden)`
                              : `Show More (${docInsights.length - maxInitialInsights} more insights)`}
                          </button>
                        )}
                      </div>
                    ) : (
                      <div className="mt-3 rounded-md border border-red-200 bg-red-50 p-3">
                        <div className="flex items-center space-x-2">
                          <svg
                            className="h-4 w-4 text-red-500"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clipRule="evenodd"
                            />
                          </svg>
                          <p className="text-xs font-medium text-red-800">
                            Analysis Failed
                          </p>
                        </div>
                        <p className="mt-1 text-xs text-red-700">
                          Unable to extract insights from this file. The content
                          may be too complex or the file format may not be fully
                          supported.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Skeleton loader components for plan sections
const PlanHeaderSkeleton = () => (
  <div className="animate-pulse space-y-3">
    <div className="bg-gray-200 h-6 w-3/4 rounded"></div>
    <div className="bg-gray-200 h-4 w-full rounded"></div>
    <div className="bg-gray-200 h-4 w-5/6 rounded"></div>
    <div className="flex gap-4">
      <div className="bg-gray-200 h-4 w-20 rounded"></div>
      <div className="bg-gray-200 h-4 w-16 rounded"></div>
      <div className="bg-gray-200 h-4 w-24 rounded"></div>
    </div>
  </div>
);

const PlanPhaseSkeleton = () => (
  <div className="border-gray-200 animate-pulse space-y-3 rounded-lg border p-4">
    <div className="flex items-center gap-2">
      <div className="bg-gray-200 h-4 w-32 rounded"></div>
      <div className="bg-gray-200 h-3 w-16 rounded"></div>
    </div>
    <div className="bg-gray-200 h-3 w-full rounded"></div>
    <div className="bg-gray-200 h-3 w-4/5 rounded"></div>
    <div className="bg-gray-200 h-3 w-3/4 rounded"></div>
  </div>
);

const ExampleSessionSkeleton = () => (
  <div className="border-gray-200 animate-pulse space-y-3 rounded-lg border p-4">
    <div className="flex items-center justify-between">
      <div className="bg-gray-200 h-4 w-40 rounded"></div>
      <div className="bg-gray-200 h-3 w-20 rounded"></div>
    </div>
    <div className="bg-gray-200 h-3 w-full rounded"></div>
    <div className="bg-gray-200 h-3 w-5/6 rounded"></div>
  </div>
);

// Plan section component with progressive loading
interface PlanSectionProps {
  title: string;
  icon: React.ReactNode;
  isLoading: boolean;
  isComplete: boolean;
  children: React.ReactNode;
  skeletonComponent: React.ReactNode;
}

const PlanSection: React.FC<PlanSectionProps> = ({
  title,
  icon,
  isLoading,
  isComplete,
  children,
  skeletonComponent,
}) => {
  return (
    <div className="mb-6">
      <div className="mb-3 flex items-center gap-2">
        {isComplete ? (
          <CheckCircle className="h-4 w-4 text-green-600" />
        ) : isLoading ? (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-purple-600 border-t-transparent" />
        ) : (
          icon
        )}
        <h3
          className={`text-sm font-semibold ${isComplete ? "text-green-800" : isLoading ? "text-purple-700" : "text-gray-500"}`}
        >
          {title}
        </h3>
        {isComplete && (
          <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800">
            Complete
          </span>
        )}
      </div>

      <div
        className={`transition-all duration-500 ${isComplete ? "opacity-100" : isLoading ? "opacity-60" : "opacity-30"}`}
      >
        {isComplete ? children : skeletonComponent}
      </div>
    </div>
  );
};

const OnboardingSidebar: React.FC<OnboardingSidebarProps> = ({
  sidebarData = null,
  selectedSport = null,
}) => {
  const [activeStep, setActiveStep] = useState(1);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [hoveredDocument, setHoveredDocument] = useState<string | null>(null);

  // State for tracking progressive plan loading
  const [loadedSections, setLoadedSections] = useState<Set<string>>(new Set());

  const steps = [
    { step: 1, title: "Welcome", content: "Tell us about yourself..." },
    { step: 2, title: "Gathering Info", content: "Let's chat..." },
    {
      step: 3,
      title: "Plan Summary",
      content:
        "Review your customized plan summary and let me know if you'd like any adjustments before I generate your full training plan.",
    },
    {
      step: 4,
      title: "Your Plan",
      content: "Your personalized training plan is ready!",
    },
  ];

  // Progressive loading effect for plan sections
  useEffect(() => {
    if (sidebarData?.generatedPlan) {
      // Reset loaded sections when a new plan is generated
      setLoadedSections(new Set());

      // Simulate progressive loading of sections
      const sections = ["header", "overview", "phases", "sessions"];
      sections.forEach((section, index) => {
        setTimeout(() => {
          setLoadedSections((prev) => new Set([...prev, section]));
        }, index * 800); // 800ms delay between sections
      });
    }
  }, [sidebarData?.generatedPlan]);

  useEffect(() => {
    let newStep = 1;

    console.log("=== ONBOARDING SIDEBAR STEP CALCULATION START ===");
    console.log("[OnboardingSidebar] Raw sidebarData received:", {
      sidebarData_exists: !!sidebarData,
      sidebarData_raw: sidebarData,
      sidebarData_type: typeof sidebarData,
      sidebarData_keys: sidebarData ? Object.keys(sidebarData) : null,
    });

    if (sidebarData) {
      console.log(
        "[OnboardingSidebar] useEffect - Processing sidebarData for step calculation:",
        sidebarData,
      );

      // Add detailed debugging for each condition
      const stepCalculationData = {
        hasPlan: !!sidebarData.generatedPlan,
        hasSummaryItems:
          sidebarData.summaryItems && sidebarData.summaryItems.length > 0,
        isReviewingStage: sidebarData.currentStage === "reviewing",
        hasGoals:
          sidebarData.goals?.exists ||
          (sidebarData.goals?.list && sidebarData.goals.list.length > 0),
        hasSelectedSports:
          sidebarData.selectedSports && sidebarData.selectedSports.length > 0,
        hasSportSuggestions:
          sidebarData.sportSuggestions &&
          sidebarData.sportSuggestions.length > 0,
        currentStage: sidebarData.currentStage,
        currentStageType: typeof sidebarData.currentStage,
        // Raw data for inspection
        rawGoals: sidebarData.goals,
        rawSelectedSports: sidebarData.selectedSports,
        rawSummaryItems: sidebarData.summaryItems,
        rawSportSuggestions: sidebarData.sportSuggestions,
        rawGeneratedPlan: sidebarData.generatedPlan,
      };

      console.log(
        "[OnboardingSidebar] Step calculation details:",
        stepCalculationData,
      );

      // Step 4: Full plan generated - highest priority
      if (
        sidebarData.generatedPlan ||
        sidebarData.currentStage === "plan_ready"
      ) {
        console.log(
          "[OnboardingSidebar] ✅ STEP 4: Full plan detected or plan_ready stage, setting step to 4.",
        );
        newStep = 4;
      }
      // Step 3: Plan summary ready - when AI has generated a plan summary from backend
      else if (sidebarData.currentStage === "plan_summary_ready") {
        console.log(
          "[OnboardingSidebar] ✅ STEP 3: Plan summary ready from backend.",
          {
            currentStage: sidebarData.currentStage,
            summaryItemsLength: sidebarData.summaryItems?.length || 0,
            hasGoals: sidebarData.goals?.exists,
            goalsCount: sidebarData.goals?.list?.length || 0,
            reasoning:
              "Backend generated plan summary, waiting for user confirmation",
          },
        );
        newStep = 3;
      }
      // Step 2: Information gathering in progress
      else if (
        // Goals exist
        sidebarData.goals?.exists ||
        (sidebarData.goals?.list && sidebarData.goals.list.length > 0) ||
        // Sports have been selected
        (sidebarData.selectedSports && sidebarData.selectedSports.length > 0) ||
        // Sport suggestions are available (indicates AI is engaged)
        (sidebarData.sportSuggestions &&
          sidebarData.sportSuggestions.length > 0) ||
        // Summary items are being collected (but not enough for plan summary yet)
        (sidebarData.summaryItems && sidebarData.summaryItems.length > 0) ||
        // We're in gathering or information collection stages
        sidebarData.currentStage === "gathering" ||
        sidebarData.currentStage === "greeting" ||
        sidebarData.currentStage === "info_collection" ||
        sidebarData.currentStage === "sports_selection" ||
        sidebarData.currentStage === "goal_setting" ||
        // Any stage that indicates we've moved beyond initial welcome
        (sidebarData.currentStage &&
          sidebarData.currentStage !== "initial" &&
          sidebarData.currentStage !== "welcome")
      ) {
        console.log(
          "[OnboardingSidebar] ✅ STEP 2: Information gathering in progress.",
          {
            goalsExist: sidebarData.goals?.exists,
            goalsListLength: sidebarData.goals?.list?.length || 0,
            selectedSportsLength: sidebarData.selectedSports?.length || 0,
            sportSuggestionsLength: sidebarData.sportSuggestions?.length || 0,
            summaryItemsLength: sidebarData.summaryItems?.length || 0,
            currentStage: sidebarData.currentStage,
            reasoning:
              "Interactive content detected, information gathering in progress",
          },
        );
        newStep = 2;
      }
      // Step 1: Default welcome state
      else {
        console.log(
          "[OnboardingSidebar] ❌ STEP 1: Defaulting to step 1 - no conditions met.",
          {
            currentStage: sidebarData.currentStage,
            hasData: !!sidebarData,
            reasoning: "No meaningful interaction detected yet",
          },
        );
        newStep = 1;
      }
    } else {
      console.log(
        "[OnboardingSidebar] ❌ No sidebarData provided, defaulting to step 1.",
      );
    }

    // Always update the step to ensure it reflects the current data state
    console.log(
      `[OnboardingSidebar] 🎯 FINAL STEP DECISION: Setting step to ${newStep} (current: ${activeStep})`,
    );
    console.log("=== ONBOARDING SIDEBAR STEP CALCULATION END ===");

    setActiveStep(newStep);
  }, [sidebarData]);

  // Scroll to bottom when content changes
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop =
        scrollContainerRef.current.scrollHeight;
    }
  }, [
    activeStep,
    sidebarData?.summaryItems,
    sidebarData?.goals,
    sidebarData?.selectedSports,
    sidebarData?.generatedPlan,
  ]);

  useEffect(() => {
    if (sidebarData) {
      console.log("[OnboardingSidebar] SidebarData details:", {
        currentStage: sidebarData.currentStage,
        goalsExist: sidebarData.goals?.exists,
        goalsList: sidebarData.goals?.list,
        summaryItemsCount: sidebarData.summaryItems?.length ?? 0,
        planExists: !!sidebarData.generatedPlan,
        planName: sidebarData.generatedPlan?.name,
        selectedSports: sidebarData.selectedSports,
        selectedSportsCount: sidebarData.selectedSports?.length ?? 0,
        sportSuggestions: sidebarData.sportSuggestions,
        sportSuggestionsCount: sidebarData.sportSuggestions?.length ?? 0,
        timestamp: new Date().toISOString(),
      });
    } else {
      console.log("[OnboardingSidebar] SidebarData is null/undefined");
    }
  }, [sidebarData]);

  const chatId = window.location.pathname.split("/").pop();

  const handleSkip = () => {
    const newUrl = `/chat/${chatId}`;
    router.push(newUrl);
  };

  // Development helper to trigger plan generation
  const handleDevTriggerPlan = () => {
    console.log("🚀 [DEV] Triggering plan generation...");

    // Create mock sidebar data that simulates a complete onboarding
    const mockSidebarData: SidebarStateData = {
      currentStage: "complete",
      goals: {
        exists: true,
        list: ["Run a 10K race", "Build strength", "Improve overall fitness"],
      },
      summaryItems: [
        {
          category: "Experience Level",
          details: "Beginner runner with some gym experience",
          isImportant: false,
        },
        {
          category: "Time Availability",
          details: "4 days per week, 45-60 minutes per session",
          isImportant: false,
        },
        {
          category: "Equipment Access",
          details: "Full gym access and outdoor running",
          isImportant: false,
        },
        {
          category: "Goals Timeline",
          details: "10K race in 6 months",
          isImportant: true,
        },
      ],
      selectedSports: ["Running", "Strength"],
      sportSuggestions: [],
      selectedSport: "Running",
      generatedPlan: {
        planId: "dev-plan-123",
        name: "Progressive 10K Training Foundation",
        description:
          "A comprehensive 12-week program combining running progression with complementary strength training to prepare you for your first 10K race.",
        duration: "12 weeks",
        level: "Beginner",
        planType: "Running",
        disciplines: ["Running", "Strength", "Recovery"],
        rationale:
          "This plan balances running volume progression with strength training to build a solid foundation while preventing injury. The 12-week timeline allows for gradual adaptation and peak performance for your 10K goal.",

        phases: [
          {
            phaseName: "Base Building",
            duration: "4 weeks",
            weeks: "4 weeks",
            focus: "Aerobic Base Development",
            description:
              "Focus on building aerobic base with easy runs and basic strength training. Emphasis on consistency and proper form development.",
            rationale:
              "Building a strong aerobic foundation is essential for endurance performance and injury prevention.",
            skillsTraining: "Form drills",
            fitnessTraining: "Easy pace running",
            strengthTraining: "Bodyweight exercises",
            mindsetTraining: "Consistency focus",
            nutritionRecommendations: "Hydration and balanced meals",
            recoveryRecommendations: "Stretching and sleep",
            resources: "Running shoes, safe running routes, gym access",
            disciplineSpecifics: {
              Running: {
                specificDetails: {
                  techniques_used: ["Easy pace running", "Form drills"],
                  resources: ["Running track", "Safe running routes"],
                  primary_metrics: ["Weekly mileage", "Heart rate"],
                  average_weekly_time: "3 hours",
                  sessions_per_week: 3,
                },
              },
              Strength: {
                specificDetails: {
                  techniques_used: [
                    "Bodyweight exercises",
                    "Basic compound movements",
                  ],
                  resources: ["Gym access", "Basic equipment"],
                  primary_metrics: ["Strength progression", "Movement quality"],
                  average_weekly_time: "2 hours",
                  sessions_per_week: 2,
                },
              },
            },
          },
        ],
      },
    };

    // Show a more helpful development guide
    const devMessage = `🚀 DEVELOPMENT PLAN TRIGGER

To test the plan generation and approval flow, send this message in chat:

"I want to run a 10K race in 6 months. I'm a beginner runner but I have gym access and can train 4 times per week for 45-60 minutes. I want to build both running endurance and strength."

TESTING THE DYNAMIC FLOW:
1. Send the above message → triggers info gathering & plan generation
2. AI creates plan → asks "What do you think?"
3. Test both paths:
   • Reply "Yes" or "looks good" → moves to session generation ✅
   • Reply "Make it more intense" → regenerates plan with feedback 🔄

The sidebar shows progressive loading during plan generation!`;

    console.log(devMessage);

    // Try to copy quick message to clipboard
    const quickMessage =
      "I want to run a 10K race in 6 months. I'm a beginner runner but I have gym access and can train 4 times per week for 45-60 minutes. I want to build both running endurance and strength.";

    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(quickMessage)
        .then(() => {
          alert(devMessage + "\n\n📋 Quick test message copied to clipboard!");
        })
        .catch(() => {
          alert(devMessage + "\n\n📋 Quick test message available in console.");
          console.log("📋 Quick test message:", quickMessage);
        });
    } else {
      alert(devMessage + "\n\n📋 Quick test message available in console.");
      console.log("📋 Quick test message:", quickMessage);
    }
  };

  const renderInformationSoFar = (summaryItems?: SummaryItem[]) => {
    console.log("[OnboardingSidebar] renderInformationSoFar called with:", {
      summaryItems,
      sidebarDataGoals: sidebarData?.goals,
      sidebarDataSelectedSports: sidebarData?.selectedSports,
      summaryItemsLength: summaryItems?.length,
    });

    if (
      (!sidebarData?.goals?.list || sidebarData.goals.list.length === 0) &&
      (!sidebarData?.selectedSports ||
        sidebarData.selectedSports.length === 0) &&
      (!summaryItems || summaryItems.length === 0) &&
      (!sidebarData?.keyInsights ||
        Object.keys(sidebarData.keyInsights).length === 0)
    ) {
      console.log(
        "[OnboardingSidebar] No goals, sports, summary items, or insights to render.",
        {
          goalsCheck:
            !sidebarData?.goals?.list || sidebarData.goals.list.length === 0,
          sportsCheck:
            !sidebarData?.selectedSports ||
            sidebarData.selectedSports.length === 0,
          summaryCheck: !summaryItems || summaryItems.length === 0,
          insightsCheck:
            !sidebarData?.keyInsights ||
            Object.keys(sidebarData.keyInsights).length === 0,
          actualGoalsList: sidebarData?.goals?.list,
          actualSelectedSports: sidebarData?.selectedSports,
          actualSummaryItems: summaryItems,
          actualKeyInsights: sidebarData?.keyInsights,
        },
      );
      return null;
    }

    const goals = sidebarData?.goals;

    // Trust the backend filtering - only filter out truly empty items
    const filteredSummaryItems = summaryItems?.filter((item) => {
      // Only skip items that are actually empty/null
      return item.details && item.details.trim().length > 0;
    });

    const hasGoals = goals?.exists && goals.list && goals.list.length > 0;
    const hasSports =
      sidebarData?.selectedSports && sidebarData.selectedSports.length > 0;
    const hasSummaryItems =
      filteredSummaryItems && filteredSummaryItems.length > 0;
    const hasInsights =
      sidebarData?.keyInsights &&
      Object.keys(sidebarData.keyInsights).length > 0;

    // Check which sports have been converted to goals to avoid redundancy
    const sportsConvertedToGoals = new Set<string>();
    if (hasGoals) {
      goals.list.forEach((goal: string) => {
        const improvementPattern = /^Improve at (.+)$/;
        const match = goal.match(improvementPattern);
        if (match && sidebarData?.selectedSports?.includes(match[1])) {
          sportsConvertedToGoals.add(match[1]);
        }
      });
    }

    // Filter out sports that have been converted to goals
    const sportsToShow =
      sidebarData?.selectedSports?.filter(
        (sport: string) => !sportsConvertedToGoals.has(sport),
      ) || [];

    const hasUnconvertedSports = sportsToShow.length > 0;

    console.log("[OnboardingSidebar] Rendering checks passed:", {
      hasGoals,
      hasSports,
      hasUnconvertedSports,
      hasInsights,
      sportsConvertedToGoals: Array.from(sportsConvertedToGoals),
      sportsToShow,
      goalsData: goals,
      sportsData: sidebarData?.selectedSports,
      summaryData: summaryItems,
      filteredSummaryData: filteredSummaryItems,
      insightsData: sidebarData?.keyInsights,
      filteringResults: {
        originalSummaryCount: summaryItems?.length || 0,
        filteredSummaryCount: filteredSummaryItems?.length || 0,
        itemsRemoved:
          (summaryItems?.length || 0) - (filteredSummaryItems?.length || 0),
      },
    });

    const importantCategoryKeywords = [
      "injury",
      "illness",
      "dietary",
      "event",
      "medical",
      "condition",
      // "sport" and "activity" are handled by hasSports section directly
    ];

    const getPillClassName = (isImportant: boolean, category?: string) => {
      let baseClasses = "rounded-lg border px-3 py-1.5 text-sm shadow-sm";

      // Sports/Activities get purple styling
      if (category === "sports") {
        return `${baseClasses} border-purple-300 bg-gray-100 text-gray-700`;
      }

      // Remove special styling for insights - use same as regular pills
      if (isImportant) {
        return `${baseClasses} border-purple-600 bg-gray-100 text-gray-700`;
      }
      return `${baseClasses} border-gray-300 bg-gray-100 text-gray-700`;
    };

    // Helper function to parse insights and extract individual data points
    const parseInsights = (insightText: string) => {
      // Split by common separators and filter out empty entries
      const insights = insightText
        .split(/[;|\n]/)
        .map((item) => item.trim())
        .filter((item) => item.length > 0 && item.length < 100); // Filter out very long items
      return insights;
    };

    // Get document name from uploaded documents for tooltip
    const getDocumentNameForInsight = () => {
      // For now, if we have insights and uploaded documents, use the first document name
      // This could be enhanced to map specific insights to specific documents
      if (
        sidebarData?.uploadedDocuments &&
        sidebarData.uploadedDocuments.length > 0
      ) {
        return sidebarData.uploadedDocuments[0].filename;
      }
      return "Uploaded Document";
    };

    // Prepare insights for display
    const insightsToDisplay: string[] = [];
    if (hasInsights && sidebarData.keyInsights) {
      Object.entries(sidebarData.keyInsights).forEach(([key, value]) => {
        if (value && typeof value === "string") {
          const parsedInsights = parseInsights(value);
          insightsToDisplay.push(...parsedInsights);
        }
      });
    }

    // Get the primary document name for the insights
    const primaryDocumentName = getDocumentNameForInsight();

    return (
      <React.Fragment>
        <h4 className="mb-3 mt-4 text-sm font-medium">
          Your Information So Far:
        </h4>
        <div className="flex flex-wrap gap-2">
          {hasGoals &&
            goals.list.map((goal: string, idx: number) => {
              const isImportant = false; // Goals are not important by default
              return (
                <div
                  key={`goal-pill-${idx}`}
                  className={getPillClassName(isImportant, "goals")}
                >
                  <span className="font-semibold">Goals: </span>
                  {goal}
                </div>
              );
            })}

          {hasSports &&
            sidebarData?.selectedSports?.map((sport: string, idx: number) => {
              return (
                <div
                  key={`sport-pill-${idx}`}
                  className={getPillClassName(false, "sports")}
                >
                  <span className="font-semibold">Sports/Activities: </span>
                  {sport}
                </div>
              );
            })}

          {/* Display insights from uploaded files as pills */}
          {insightsToDisplay.length > 0 &&
            insightsToDisplay
              .slice(0, 8)
              .map((insight: string, idx: number) => {
                // Limit to 8 insights
                const isRelatedToHoveredDoc =
                  hoveredDocument === null ||
                  hoveredDocument === primaryDocumentName;

                return (
                  <div
                    key={`insight-pill-${idx}`}
                    className={`${getPillClassName(false, "insight")} flex items-center gap-1.5 transition-opacity duration-200 ${
                      isRelatedToHoveredDoc ? "opacity-100" : "opacity-30"
                    }`}
                  >
                    <File size={14} className="text-gray-600 flex-shrink-0" />
                    <span className="truncate">{insight}</span>
                  </div>
                );
              })}

          {hasSummaryItems &&
            filteredSummaryItems?.map((section: SummaryItem, index: number) => {
              const categoryLower = section.category.toLowerCase();
              const isImportant = importantCategoryKeywords.some((keyword) =>
                categoryLower.includes(keyword),
              );
              return (
                <div
                  key={`summary-pill-${index}`}
                  className={getPillClassName(isImportant, section.category)}
                >
                  <span className="font-semibold">{section.category}: </span>
                  {section.details}
                </div>
              );
            })}
        </div>

        {/* Show simplified uploaded documents list with hover effects */}
        {sidebarData?.uploadedDocuments &&
          sidebarData.uploadedDocuments.length > 0 && (
            <div className="mt-4">
              <h5 className="mb-2 text-sm font-medium">
                Uploaded Files ({sidebarData.uploadedDocuments.length})
              </h5>
              <div className="space-y-2">
                {sidebarData.uploadedDocuments.map(
                  (doc: any, index: number) => (
                    <div
                      key={index}
                      className="text-gray-600 hover:text-gray-800 flex cursor-pointer items-center gap-2 text-sm transition-colors"
                      onMouseEnter={() => setHoveredDocument(doc.filename)}
                      onMouseLeave={() => setHoveredDocument(null)}
                    >
                      <File size={16} className="text-gray-500" />
                      <span>{doc.filename}</span>
                      <span className="text-gray-400 text-xs">
                        • {doc.file_type}
                      </span>
                      <span className="ml-auto rounded bg-green-100 px-2 py-0.5 text-xs text-green-800">
                        Analyzed
                      </span>
                    </div>
                  ),
                )}
              </div>
            </div>
          )}
      </React.Fragment>
    );
  };

  const renderGeneratedPlan = () => {
    const plan = sidebarData?.generatedPlan as PlanDetails | undefined;
    if (!plan) return null;

    console.log("[OnboardingSidebar] Rendering generated plan:", plan);

    const isHeaderLoaded = loadedSections.has("header");
    const isPhasesLoaded = loadedSections.has("phases");

    return (
      <div className="mt-6 space-y-6">
        {/* Header with celebration */}
        <div className="text-center">
          <div className="inline-flex items-center gap-2 rounded-full border border-green-200 bg-green-50 px-4 py-2 text-green-800">
            <Zap className="h-4 w-4" />
            <span className="text-sm font-medium">Your Plan is Ready!</span>
          </div>
        </div>

        {/* Plan Header Section */}
        <PlanSection
          title="Plan Overview"
          icon={<Target className="text-gray-500 h-4 w-4" />}
          isLoading={!isHeaderLoaded}
          isComplete={isHeaderLoaded}
          skeletonComponent={<PlanHeaderSkeleton />}
        >
          <div className="border-gray-200 space-y-3 rounded-lg border bg-white p-4">
            <div>
              <h4 className="text-gray-900 mb-2 text-lg font-bold">
                {plan.name}
              </h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {plan.description}
              </p>
            </div>

            <div className="flex flex-wrap gap-3 pt-2">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3 text-purple-600" />
                <span className="text-gray-700 text-xs font-medium">
                  {plan.duration}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Target className="h-3 w-3 text-blue-600" />
                <span className="text-gray-700 text-xs font-medium">
                  {plan.level}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3 text-green-600" />
                <span className="text-gray-700 text-xs font-medium">
                  {plan.planType}
                </span>
              </div>
            </div>
          </div>
        </PlanSection>

        {/* Training Phases Section */}
        {plan.phases && plan.phases.length > 0 && (
          <PlanSection
            title={`Training Phases (${plan.phases.length})`}
            icon={<Calendar className="text-gray-500 h-4 w-4" />}
            isLoading={!isPhasesLoaded}
            isComplete={isPhasesLoaded}
            skeletonComponent={
              <div className="space-y-3">
                {[...Array(plan.phases.length || 1)].map((_, i) => (
                  <PlanPhaseSkeleton key={i} />
                ))}
              </div>
            }
          >
            <div className="space-y-3">
              {plan.phases.map((phase, index) => (
                <div
                  key={index}
                  className="border-gray-200 rounded-lg border bg-white p-4 transition-colors hover:border-purple-200"
                >
                  <div className="mb-2 flex items-center justify-between">
                    <h5 className="text-gray-800 text-sm font-semibold">
                      Phase {index + 1}: {phase.phaseName}
                    </h5>
                    <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800">
                      {phase.duration}
                    </span>
                  </div>
                  <p className="text-gray-600 text-xs leading-relaxed">
                    {phase.description}
                  </p>
                  {/* You can add more details from the new schema here */}
                </div>
              ))}
            </div>
          </PlanSection>
        )}
      </div>
    );
  };

  const renderPlanSummary = () => {
    console.log("[OnboardingSidebar] renderPlanSummary called", {
      hasSidebarData: !!sidebarData,
      hasGeneratedPlan: !!sidebarData?.generatedPlan,
      currentStage: sidebarData?.currentStage,
    });

    // Only show plan summary if we have enough information but no full plan yet
    if (!sidebarData || sidebarData.generatedPlan) {
      console.log(
        "[OnboardingSidebar] renderPlanSummary: Early return - no sidebar data or plan already exists",
      );
      return null;
    }

    // Check if we have the essential information for a plan summary
    const hasGoals =
      sidebarData.goals?.exists &&
      sidebarData.goals.list &&
      sidebarData.goals.list.length > 0;
    const hasSelectedSports =
      sidebarData.selectedSports && sidebarData.selectedSports.length > 0;
    const hasSummaryItems =
      sidebarData.summaryItems && sidebarData.summaryItems.length >= 3; // At least 3 categories

    console.log("[OnboardingSidebar] renderPlanSummary conditions:", {
      hasGoals,
      hasSelectedSports,
      hasSummaryItems,
      goalsCount: sidebarData.goals?.list?.length || 0,
      selectedSportsCount: sidebarData.selectedSports?.length || 0,
      summaryItemsCount: sidebarData.summaryItems?.length || 0,
    });

    if (!hasGoals || !hasSelectedSports || !hasSummaryItems) {
      console.log(
        "[OnboardingSidebar] renderPlanSummary: Conditions not met, returning null",
      );
      return null;
    }

    console.log("[OnboardingSidebar] Rendering plan summary preview");

    // Extract key information for the summary
    const primarySports =
      sidebarData.selectedSports?.slice(0, 2).join(" & ") || ""; // Show up to 2 sports
    const mainGoals = sidebarData.goals?.list?.slice(0, 2) || []; // Show up to 2 goals

    // Find key summary items
    const experienceItem = sidebarData.summaryItems?.find(
      (item: SummaryItem) =>
        item.category.toLowerCase().includes("experience") ||
        item.category.toLowerCase().includes("level"),
    );
    const timeItem = sidebarData.summaryItems?.find(
      (item: SummaryItem) =>
        item.category.toLowerCase().includes("time") ||
        item.category.toLowerCase().includes("frequency") ||
        item.category.toLowerCase().includes("commitment"),
    );
    const equipmentItem = sidebarData.summaryItems?.find(
      (item: SummaryItem) =>
        item.category.toLowerCase().includes("equipment") ||
        item.category.toLowerCase().includes("access") ||
        item.category.toLowerCase().includes("location"),
    );

    return (
      <div className="mt-4 rounded-md border border-blue-200 bg-blue-50 p-4">
        <div className="mb-3 flex items-center justify-between">
          <h4 className="text-sm font-medium text-blue-800">
            📋 Plan Summary Preview
          </h4>
          <span className="rounded-full bg-blue-200 px-2 py-1 text-xs font-medium text-blue-800">
            Ready for Generation
          </span>
        </div>

        <div className="space-y-3 text-sm">
          <div>
            <strong className="font-semibold text-blue-900">
              Focus Areas:
            </strong>
            <p className="text-blue-800">{primarySports}</p>
          </div>

          <div>
            <strong className="font-semibold text-blue-900">
              Primary Goals:
            </strong>
            <ul className="mt-1 list-disc pl-5 text-blue-800">
              {mainGoals.map((goal: string, index: number) => (
                <li key={index}>{goal}</li>
              ))}
            </ul>
          </div>

          {experienceItem && (
            <div>
              <strong className="font-semibold text-blue-900">
                Experience:
              </strong>
              <p className="text-blue-800">{experienceItem.details}</p>
            </div>
          )}

          {timeItem && (
            <div>
              <strong className="font-semibold text-blue-900">Schedule:</strong>
              <p className="text-blue-800">{timeItem.details}</p>
            </div>
          )}

          {equipmentItem && (
            <div>
              <strong className="font-semibold text-blue-900">Setup:</strong>
              <p className="text-blue-800">{equipmentItem.details}</p>
            </div>
          )}
        </div>

        <div className="mt-4 rounded-md border border-blue-300 bg-blue-100 p-3">
          <p className="text-xs text-blue-700">
            <strong>Next Step:</strong> I&apos;ll create a personalized training
            plan based on this information. The plan will include specific
            phases, example workouts, and be tailored to your goals and
            schedule.
          </p>
          <p className="mt-2 text-xs font-medium text-blue-800">
            💬 Let me know if this looks good or if you&apos;d like to add
            anything else!
          </p>
        </div>
      </div>
    );
  };

  const renderContentOrLoading = () => {
    const currentStepDetails = steps.find((s) => s.step === activeStep);

    // Show initial welcome content only if we're on step 1 AND no meaningful data exists
    const showInitialContent =
      activeStep === 1 &&
      (!sidebarData ||
        ((!sidebarData.summaryItems || sidebarData.summaryItems.length === 0) &&
          (!sidebarData.goals?.list || sidebarData.goals.list.length === 0) &&
          (!sidebarData.selectedSports ||
            sidebarData.selectedSports.length === 0) &&
          (!sidebarData.sportSuggestions ||
            sidebarData.sportSuggestions.length === 0) &&
          !sidebarData.generatedPlan &&
          (!sidebarData.currentStage ||
            sidebarData.currentStage === "initial" ||
            sidebarData.currentStage === "welcome")));

    console.log("[OnboardingSidebar] Content render state:", {
      activeStep,
      currentStage: sidebarData?.currentStage,
      hasSummaryItems: (sidebarData?.summaryItems?.length ?? 0) > 0,
      hasSelectedSports: (sidebarData?.selectedSports?.length ?? 0) > 0,
      hasSportSuggestions: (sidebarData?.sportSuggestions?.length ?? 0) > 0,
      hasGoals:
        sidebarData?.goals?.exists ||
        (sidebarData?.goals?.list?.length ?? 0) > 0,
      hasPlan: !!sidebarData?.generatedPlan,
      showInitialContent,
    });

    if (showInitialContent) {
      return (
        <div className="mb-4">
          <p className="text-gray-500 mb-1 text-sm">Onboarding Step 1:</p>
          <h1 className="mb-6 text-3xl font-bold">Welcome to Athlea!</h1>

          <p className="mb-5">
            Athlea is your intelligent coaching companion — designed to help you
            move more, feel stronger, and stay consistent with your physical
            activity goals. Whether you&apos;re just getting started or already
            on your journey, Athlea adapts to you.
          </p>

          <h2 className="mb-4 text-xl font-bold">How Athlea Works:</h2>

          <ol className="mb-5 space-y-4">
            <li>
              <span className="font-bold">1. Set your goals.</span> During
              onboarding, you&apos;ll answer a few quick questions so we can
              understand your current habits, preferences, and goals. This helps
              Athlea personalise your experience right from the start.
            </li>
            <li>
              <span className="font-bold">2. Get your plan.</span> Based on your
              input, Athlea will create a tailored coaching plan that fits your
              lifestyle — with suggestions on movement, training sessions, and
              wellbeing tips.
            </li>
            <li>
              <span className="font-bold">3. Stay flexible.</span> Life changes
              — and so should your plan. As you interact with Athlea and
              complete sessions, we&apos;ll adjust your recommendations over
              time. The more you use it, the smarter it gets.
            </li>
            <li>
              <span className="font-bold">4. Full transparency.</span>{" "}
              You&apos;ll always be able to see what&apos;s influencing your
              coaching plan. We&apos;ll show you what we&apos;re tracking and
              why — no surprises.
            </li>
          </ol>

          <p className="font-medium">Now, let&apos;s get started!</p>
          <p>
            Tap &quot;Next&quot; to begin your journey — we&apos;re here to
            support you every step of the way.
          </p>
        </div>
      );
    }

    return (
      <div className="mb-4">
        <div className="text-gray-500 mb-2 text-sm">
          Onboarding Stage: {sidebarData?.currentStage ?? `Step ${activeStep}`}
        </div>
        <h2 className="mb-4 text-2xl font-bold">
          {currentStepDetails?.title ?? "Onboarding"}
        </h2>
        <p className="text-gray-600">
          {currentStepDetails?.content ??
            "Please follow the chat instructions."}
        </p>

        {/* Show loading state only if we have no data at all */}
        {!sidebarData ? (
          <div className="bg-gray-50 mt-4 rounded-md border p-4 text-center">
            <p className="text-gray-500">Loading onboarding status...</p>
          </div>
        ) : (
          <>
            {/* Show information from step 2 onwards */}
            {activeStep >= 2 &&
              renderInformationSoFar(sidebarData?.summaryItems)}

            {/* Show plan summary stage indicator when on step 3 with plan_summary_ready stage */}
            {activeStep === 3 &&
              sidebarData?.currentStage === "plan_summary_ready" && (
                <div className="mt-4 rounded-md border border-blue-200 bg-blue-50 p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <h4 className="text-sm font-medium text-blue-800">
                      📋 Plan Summary Ready
                    </h4>
                    <span className="rounded-full bg-blue-200 px-2 py-1 text-xs font-medium text-blue-800">
                      Review & Confirm
                    </span>
                  </div>
                  <p className="text-xs text-blue-700">
                    I&apos;ve created a personalized training plan summary based
                    on your information. Please review it in the chat above and
                    let me know if you&apos;d like any adjustments before I
                    generate your full detailed plan.
                  </p>
                  <div className="mt-3 flex items-center gap-2 text-xs text-blue-600">
                    <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                    <span>Waiting for your confirmation...</span>
                  </div>
                </div>
              )}

            {/* Show plan ready indicator when on step 4 with plan_ready stage */}
            {activeStep === 4 &&
              sidebarData?.currentStage === "plan_ready" &&
              sidebarData?.generatedPlan && (
                <div className="mt-4 rounded-md border border-green-200 bg-green-50 p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <h4 className="text-sm font-medium text-green-800">
                      🎉 Your Plan is Ready!
                    </h4>
                    <span className="rounded-full bg-green-200 px-2 py-1 text-xs font-medium text-green-800">
                      Complete
                    </span>
                  </div>
                  <p className="text-xs text-green-700">
                    Your personalized training plan has been generated and is
                    displayed below. You can now start your fitness journey with
                    a plan tailored specifically to your goals!
                  </p>
                  <div className="mt-3 flex items-center gap-2 text-xs text-green-600">
                    <CheckCircle className="h-3 w-3" />
                    <span>Ready to start training!</span>
                  </div>
                </div>
              )}

            {/* Show generated plan whenever it exists, now after the info */}
            {sidebarData?.generatedPlan && renderGeneratedPlan()}
            {/* Show calendar view only if we have a plan and are on step 4 */}
            {activeStep === 4 && sidebarData?.generatedPlan && (
              <OnboardingPlanCalendarView
                plan={sidebarData.generatedPlan as PlanDetails}
              />
            )}
          </>
        )}
      </div>
    );
  };

  return (
    <div className="relative flex h-full flex-col bg-white">
      <div
        ref={scrollContainerRef}
        className="flex flex-1 flex-col overflow-y-auto px-6 pb-24 pt-6"
      >
        <div className="mb-6">
          <Stepper value={activeStep - 1} className="w-full">
            {steps.map((stepInfo, index) => (
              <StepperItem
                key={index}
                step={index}
                // className="[&:not(:last-child)]:flex-1"
                completed={index < activeStep - 1}
              >
                <StepperTrigger>
                  <StepperIndicator className="data-[state=active]:border-purple-600 data-[state=completed]:border-purple-600 data-[state=active]:bg-purple-600 data-[state=completed]:bg-purple-600" />
                </StepperTrigger>
                {/* {index < steps.length - 1 && (
                  <StepperSeparator className="data-[state=completed]:bg-purple-600" />
                )} */}
              </StepperItem>
            ))}
          </Stepper>
        </div>

        {renderContentOrLoading()}
      </div>

      <div className="absolute bottom-6 right-6 flex gap-4">
        {/* Development Trigger - Remove in production */}
        {process.env.NODE_ENV === "development" && (
          <Button
            variant="secondary"
            onClick={handleDevTriggerPlan}
            className="bg-yellow-500 px-4 text-xs text-white hover:bg-yellow-600"
            title="Dev: Trigger Plan Generation"
          >
            🚀 Dev: Generate Plan
          </Button>
        )}

        <Button
          variant="default"
          // onClick={handleSkip}
          className="bg-purple-600 px-8 text-white hover:bg-purple-700"
        >
          Next Step
        </Button>
        <Button
          variant="outline"
          onClick={handleSkip}
          className="border-purple-600 px-8 text-purple-600 hover:bg-purple-100 hover:text-purple-700"
        >
          Skip
        </Button>
      </div>
    </div>
  );
};

export default OnboardingSidebar;
