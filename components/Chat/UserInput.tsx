import { getWebSocket } from "@/app/lib/websocketmanager";
import { useSuggestionContext } from "@/context/SuggestionContext";
import useAzurSpeechtoText from "@/hooks/useAzurSpeechtoText";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import {
  addSystemMessage,
  addToChatMessage,
  clearSelectedSuggestions,
  initChatWebSocket,
  sendChatMessageAsync,
  sendFileMessage,
  setAnswer,
  setUploading,
  setUserInput,
  setPendingResponse,
  clearChatMessages,
  clearContentAndChats,
  clearLogArray,
} from "@/store/slices/chatSlice";
import {
  createTrainingPlanAndSendFileMessage,
  createTrainingPlanAndSendMessage,
  setIsSidebarModalOpen,
  setLastActiveCoach,
  setUserInputHeight,
  clearJobId,
  handleCreateNewChatClick,
} from "@/store/slices/jobSlice";
import { loadNotifications } from "@/store/slices/notificationSlice";
import store, { RootState } from "@/store/store";
import { Message } from "@/types/chat";
import IconButton from "@mui/material/IconButton";
import InputBase from "@mui/material/InputBase";
import Paper from "@mui/material/Paper";
import {
  Mic,
  MicOff,
  Paperclip,
  SendHorizonalIcon,
  File,
  FileX2,
  X,
  Waves,
  Search,
  Send,
  Loader2,
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import {
  ChangeEvent,
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useSelector } from "react-redux";
import ServiceDropdown from "./ServiceDropdown";
import ImagePreview from "./ImagePreview";
import axios from "axios";
import FilePreviewComponent from "./FilePreview/FilePreviewComponent";
import { DropEvent, FileRejection, useDropzone } from "react-dropzone";
import { useGlossary } from "@/context/GlossaryContext";
import toast from "react-hot-toast";
import { useAnimationMessage } from "@/context/AnimationMessageContext";
import { useTrainingProfile } from "@/context/TrainingProfileContext";
import { useStats } from "@/context/StatsContext";
import { TimelineRange } from "../Stats/StatsTimeline";
import { MetricDataPoint } from "@/types/stats";
import { processMetricData, processMetricGroups } from "@/utils/statsUtils";
import { toggleCalendar } from "@/store/slices/calendarSlice";
import {
  connectAudioWebSocket,
  startRecording,
  disconnectAudioWebSocket,
  setIsMicOn,
  addChatMessage,
  setCurrentEmotions,
} from "@/store/slices/audioSlice";
import { useVoice } from "@humeai/voice-react";
import { fetchEmotionalProfile, updateEmotion } from "@/store/slices/userSlice";
import { useEmotional } from "@/context/EmotionalContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/app/lib/utlis";

// Define the suggestion type based on the expected data
interface Suggestion {
  label: string;
  value: string;
}

// Add props interface
interface UserInputProps {
  onSubmitRegularMessage?: (message: string) => void;
  onSubmitWaitingInput?: (message: string) => void;
  onSubmitHomeMessage?: (message: string) => void;
  isLoading?: boolean;
  isStreaming?: boolean;
  waitingForInput?: boolean;
  inputPrompt?: string;
  isHomePage?: boolean;
  isCreatingChat?: boolean;
  onFocusChange?: (isFocused: boolean) => void;
  suggestions?: Suggestion[]; // Add the suggestions prop here
}

const findLastQuestionOrTask = (messages: Message[]): Message | null => {
  for (let i = 0; i <= messages.length - 1; i++) {
    console.log("LAST+++MEssage===>", messages[i]);
    if (messages[i].type === "question" || messages[i].type === "task") {
      console.log("Found last question or task:", messages[i]);
      return messages[i];
    }
  }
  return null;
};

const UserInput: FC<UserInputProps> = ({
  onSubmitRegularMessage,
  onSubmitWaitingInput,
  onSubmitHomeMessage,
  isLoading,
  isStreaming,
  waitingForInput,
  inputPrompt,
  isHomePage,
  isCreatingChat,
  onFocusChange,
  suggestions,
}) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [inputWidth, setInputWidth] = useState(0);
  const { showTrainingProfile, showCurrentTrainingProfile } =
    useTrainingProfile();

  const {
    setShowStats,
    setAvailableRanges,
    setSelectedRange,
    setData,
    setMetricPanels,
    setMetrics,
    availableRanges,
    data,
  } = useStats();

  const {
    myTranscript,
    recognizingTranscript,
    isListening,
    pauseListening,
    resumeListening,
    stopListening,
  } = useAzurSpeechtoText();

  // hume ai voice
  const { connect, disconnect, messages, lastUserMessage, lastVoiceMessage } =
    useVoice();

  const { isOpen: isCalendarOpen } = useAppSelector(
    (state: RootState) => state.calendar,
  );
  const { showGlossary } = useGlossary();
  const { isAnimationMessageExpanded } = useAnimationMessage();
  const { showStats } = useStats(); // Add stats context

  const {
    answer,
    pendingResponse,
    userInput,
    selectedSuggestions,
    chatMessages,
    ai_voice_messages,
    hasErrorMessage,
  } = useAppSelector((state: RootState) => state.chat);
  console.log(
    "myTranscript",
    myTranscript,
    isListening,
    "recognizingTranscript",
    recognizingTranscript,
  );

  const currWebSocket = getWebSocket();

  const {
    selectedJobId,
    coaches,
    lastActiveCoach,
    temporaryCoaches,
    isSidebarModalOpen,
  } = useAppSelector((state: RootState) => state.job);
  const [localInput, setLocalInput] = useState(userInput);
  console.log(
    "humeMessage",
    messages,
    selectedJobId,
    lastUserMessage,
    lastVoiceMessage,
  );

  const user_id = useAppSelector(
    (state: RootState) => state.user.userData?.user_id,
  );
  const { colorMode } = useSelector((state: any) => state.colormode);
  const isMicOn = useAppSelector((state: RootState) => state.audio?.isMicOn);
  console.log(
    "isListening ",
    isMicOn,
    isListening,
    myTranscript,
    recognizingTranscript,
  );

  const updateEmotionalData = useCallback(
    async (currentEmotions: any) => {
      console.log("Fetching elevation for userId:", user_id);
      if (Object.keys(currentEmotions).length > 0) {
        try {
          await axios.post(`/api/users/${user_id}/update-emotion`, {
            emotions: currentEmotions,
          });
          dispatch(updateEmotion(currentEmotions));
        } catch (error) {
          console.error("falied to post emotion");
        }
      }
    },
    [user_id],
  );

  useEffect(() => {
    if (messages && messages.length > 0) {
      if (lastUserMessage) {
        dispatch(
          addChatMessage({
            timestamp: new Date().toISOString(),
            role: lastActiveCoach,
            content: lastUserMessage.message?.content,
            emotions: lastUserMessage.models?.prosody?.scores,
            type: lastUserMessage.type,
          }),
        );

        if (lastUserMessage.models?.prosody?.scores) {
          dispatch(setCurrentEmotions(lastUserMessage.models?.prosody?.scores));
          (async () => {
            await updateEmotionalData(lastUserMessage.models?.prosody?.scores);
            await dispatch(fetchEmotionalProfile(user_id)).unwrap();
          })();
        }
      }
    }
  }, [lastUserMessage]);

  useEffect(() => {
    if (messages && messages.length > 0) {
      if (lastVoiceMessage) {
        dispatch(
          addChatMessage({
            timestamp: new Date().toISOString(),
            role: lastActiveCoach,
            content: lastVoiceMessage.message?.content,
            emotions: lastVoiceMessage.models?.prosody?.scores,
            type: lastVoiceMessage.type,
          }),
        );

        if (lastVoiceMessage.models?.prosody?.scores) {
          dispatch(
            setCurrentEmotions(lastVoiceMessage.models?.prosody?.scores),
          );
        }
      }
    }
  }, [lastVoiceMessage]);

  const coachNameSafe = lastActiveCoach || "default";

  const [lastMessage, setLastMessage] = useState<Message | null>(null);

  const [attachment, setAttachment] = useState<any[]>([]);
  const [previewUrl, setPreviewUrl] = useState<any>(null);
  const [isImageSelection, setIsImageSelection] = useState<boolean>(false);
  const [isFileUploading, setIfileUploading] = useState<boolean>(false);
  const [uploadedFile, setUploadedFile] = useState<{
    file: File;
    fileId: string;
  } | null>(null);
  const [isErrorFile, setIsErrorFile] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [fileSize, setFileSize] = useState<number>(0);
  const [cumulativeTranscript, setCumulativeTranscript] = useState("");
  // const [isMicOn, setIsMicOn] = useState(false);
  const [isEnterPressed, setIsEnterPressed] = useState<boolean>(false);
  const [uploadStatus, setUploadStatus] = useState<{
    status: "idle" | "uploading" | "processing" | "completed" | "error";
    message?: string;
    progress?: number;
  }>({ status: "idle" });

  // State for tracking staged uploads (uploaded but not yet sent in a message)
  const [stagedUpload, setStagedUpload] = useState<{
    filename: string;
    timestamp: string;
  } | null>(null);

  const selectedCoach = useSelector(
    (state: RootState) => state.job.lastActiveCoach,
  );
  const { showEmotional, setShowEmotional } = useEmotional();

  // Determine if running within the LangGraph chat page context
  const isLangGraphChat = pathname?.includes("/chat/");

  const allowedTypes = [
    "text/plain",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "text/csv",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "image/jpeg",
    "image/png",
    "image/webp",
  ];

  const handleFileUpload = useCallback(
    async (file: File) => {
      console.log("File selected:", file?.name);

      if (!file) {
        console.error("No file selected");
        return;
      }

      // User ID check logic
      let attempts = 0;
      const maxAttempts = 10;
      let currentUserId = user_id;

      while (!currentUserId && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 500));
        currentUserId = store.getState().user.userData?.user_id;
        attempts++;
      }

      if (!currentUserId) {
        console.error("User ID is missing after waiting");
        toast.error("Unable to get user ID. Please try again.");
        return;
      }

      // File validation
      if (!allowedTypes.includes(file.type)) {
        console.error("File type not allowed:", file.type);
        dispatch(setAnswer(`Error: File type ${file.type} is not allowed`));
        return;
      }

      console.log("File type allowed:", file.type);
      setIsImageSelection(file.type.startsWith("image/"));
      setFileSize(fileSize + file.size);
      setAttachment([...attachment, file]);

      if (file && fileSize + file.size > 5 * 1024 * 1024) {
        console.error("Can not upload file more than 5MB");
        setIsErrorFile(true);
        dispatch(setAnswer(`Error: File size more than 5MB is not allowed`));
        return;
      }

      setIfileUploading(true);
      setUploadStatus({
        status: "uploading",
        message: "Uploading file...",
        progress: 20,
      });

      // Handle image preview
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onloadend = () => {
          console.log("Image preview generated");
          setPreviewUrl(reader.result);
        };
        reader.readAsDataURL(file);
      }

      // Check if we're in onboarding mode - if so, upload directly to Python backend
      const isOnboardingMode = pathname?.includes("/onboarding/");

      if (isOnboardingMode) {
        console.log("[ONBOARDING UPLOAD] Uploading directly to Python backend");
        setUploadStatus({
          status: "processing",
          message: "Analyzing your fitness data...",
          progress: 60,
        });

        try {
          dispatch(setUploading(true));

          const formData = new FormData();
          formData.append("file", file);
          formData.append("user_id", currentUserId);

          // Get thread ID from current pathname
          const threadId = pathname.split("/").pop();
          if (threadId) {
            formData.append("thread_id", threadId);
          }

          const pythonBackendUrl =
            process.env.NEXT_PUBLIC_PYTHON_LANGGRAPH_URL ||
            "http://localhost:8000";
          setUploadStatus({
            status: "processing",
            message: "Analyzing your fitness data...",
            progress: 60,
          });

          // Upload directly to Python backend onboarding endpoint
          const uploadResponse = await fetch(
            `${pythonBackendUrl}/api/onboarding/upload`,
            {
              method: "POST",
              body: formData,
            },
          );

          if (uploadResponse.ok) {
            const uploadResult = await uploadResponse.json();
            console.log(
              "File uploaded to Next.js backend successfully:",
              uploadResult,
            );

            setUploadedFile({ file, fileId: uploadResult.fileId });

            setUploadStatus({
              status: "completed",
              message: `${file.name} uploaded and will be analyzed during onboarding!`,
              progress: 100,
            });

            // Auto-clear success status after 3 seconds
            setTimeout(() => {
              setUploadStatus({ status: "idle" });
            }, 3000);

            // Reset uploading state
            setIfileUploading(false);
            dispatch(setUploading(false));

            // Stage the uploaded file for inclusion in the next user message
            // Instead of immediately sending a message, we'll include it when user sends their next message
            setStagedUpload({
              filename: file.name,
              timestamp: new Date().toISOString(),
            });

            console.log(
              "[ONBOARDING UPLOAD] File uploaded successfully and staged for next message",
              {
                filename: file.name,
                timestamp: new Date().toISOString(),
              },
            );

            console.log(
              "[ONBOARDING UPLOAD] File uploaded and added to chat messages",
            );
          } else {
            const errorData = await uploadResponse.json();
            console.error("Upload failed:", errorData.error);
            setUploadStatus({
              status: "error",
              message: `Upload failed: ${errorData.error}`,
            });
            toast.error(`Upload failed: ${errorData.error}`);

            // Reset uploading state on error
            setIfileUploading(false);
            dispatch(setUploading(false));
          }
        } catch (error) {
          console.error("Error uploading to Python backend:", error);
          setUploadStatus({
            status: "error",
            message: "Error uploading file. Please try again.",
          });
          toast.error("Error uploading file. Please try again.");

          // Reset uploading state on error
          setIfileUploading(false);
          dispatch(setUploading(false));
        }
      } else {
        // Original Next.js upload for non-onboarding contexts
        console.log("[REGULAR UPLOAD] Using Next.js upload endpoint");
        setUploadStatus({
          status: "uploading",
          message: "Processing file...",
          progress: 40,
        });

        const formData = new FormData();
        formData.append("file", file);
        formData.append("user_id", currentUserId);

        try {
          console.log("Starting file upload");
          dispatch(setUploading(true));

          const response = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          });

          console.log("Upload response received:", response.status);

          if (response.ok) {
            const result = await response.json();
            console.log("File uploaded successfully:", result);
            setUploadedFile({ file, fileId: result.fileId });

            setUploadStatus({
              status: "completed",
              message: `${file.name} uploaded successfully!`,
              progress: 100,
            });

            toast.success(`✅ ${file.name} uploaded successfully`);

            // Auto-clear success status after 3 seconds
            setTimeout(() => {
              setUploadStatus({ status: "idle" });
            }, 3000);

            // Step 1: Determine if file has analyzable stats
            try {
              console.log("Checking if file contains analyzable stats");
              const determineResponse = await fetch("/api/analyze/determine", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                  userId: currentUserId,
                  fileName: file.name,
                }),
              });

              if (!determineResponse.ok) {
                throw new Error("Failed to determine stats potential");
              }

              const determineResult = await determineResponse.json();
              console.log("Stats determination result:", determineResult);

              // Step 2: If file has analyzable stats, perform analysis
              if (determineResult.hasStats) {
                console.log(
                  "File contains analyzable stats, starting detailed analysis",
                );

                // Create new FormData for analysis
                const analysisFormData = new FormData();
                analysisFormData.append("file", file);
                // Optional: Add any additional context from determination
                analysisFormData.append(
                  "determinationResult",
                  JSON.stringify(determineResult),
                );
                analysisFormData.append("userId", currentUserId);
                analysisFormData.append("fileId", result.fileId);

                const analyzeResponse = await fetch("/api/analyze", {
                  method: "POST",
                  body: analysisFormData,
                });

                if (!analyzeResponse.ok) {
                  throw new Error("Failed to analyze stats");
                }

                const analysisResult = await analyzeResponse.json();
                console.log("Stats analysis completed:", analysisResult);

                if (analysisResult.success) {
                  try {
                    // Save stats to API
                    const saveResponse = await fetch(
                      `/api/stats?user_id=${currentUserId}`,
                      {
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                          analysis: analysisResult.analysis,
                          metadata: analysisResult.metadata,
                          rawData: analysisResult.rawData,
                          dateRange: analysisResult.dateRange,
                        }),
                      },
                    );

                    if (saveResponse.ok) {
                      // Create timeline range object
                      const newRange: TimelineRange = {
                        startDate: new Date(analysisResult.dateRange.startDate),
                        endDate: new Date(analysisResult.dateRange.endDate),
                        rawData: analysisResult.rawData,
                        analysis: {
                          metricGroups: analysisResult.analysis.metricGroups,
                        },
                        metadata: {
                          fileName: analysisResult.metadata.fileName,
                          fileType: analysisResult.metadata?.fileType,
                          fileSize: analysisResult.metadata?.fileSize,
                          image_url: analysisResult.metadata?.image_url,
                        },
                      };

                      // Update ranges if not exists
                      const foundRange = availableRanges.find(
                        (item: TimelineRange) =>
                          item.metadata?.fileName ===
                          analysisResult.metadata.fileName,
                      );

                      // Update ranges if not exists
                      if (!foundRange) {
                        setAvailableRanges((prev) => [...prev, newRange]);
                      }

                      // Set selected range
                      setSelectedRange(newRange);

                      // Update dashboard data if not exists
                      const foundStats = data.find(
                        (item: MetricDataPoint) =>
                          item.fileName === analysisResult.metadata.fileName,
                      );

                      if (!foundStats) {
                        setData([
                          ...data,
                          ...[
                            {
                              ...analysisResult.rawData[0],
                              ...analysisResult.metadata,
                            },
                          ],
                        ]);
                      }

                      // Process and set metrics
                      const processedPanels = processMetricData(
                        analysisResult.rawData,
                        analysisResult,
                      );
                      const processedMetrics =
                        processMetricGroups(analysisResult);
                      setMetricPanels(processedPanels);
                      setMetrics(processedMetrics);

                      // Show success message
                      toast.success(
                        "Stats analysis completed and saved successfully",
                      );

                      // Show stats view
                      setShowStats(true);
                    } else {
                      throw new Error("Failed to save analysis");
                    }
                  } catch (error) {
                    console.error("Failed to save stats:", error);
                    toast.error("Analysis completed but failed to save stats");
                  }
                }
              } else {
                console.log("File does not contain analyzable stats");
              }
            } catch (statsError) {
              console.error("Stats processing failed:", statsError);
              // Don't show error to user since the upload succeeded
            }
          } else {
            const errorData = await response.json();
            console.error("File upload failed:", errorData.error);
            toast.error(`Upload failed: ${errorData.error}`);
          }
        } catch (error) {
          console.error("Error uploading file:", error);
          toast.error("Error uploading file. Please try again.");
        }
      }

      // Common cleanup
      console.log("Upload process completed");
      dispatch(setUploading(false));
      setIfileUploading(false);
    },
    [
      dispatch,
      user_id,
      setIsImageSelection,
      setIfileUploading,
      setAttachment,
      setPreviewUrl,
      attachment,
      fileSize,
      setShowStats,
      setAvailableRanges,
      setSelectedRange,
      setData,
      setMetricPanels,
      setMetrics,
      availableRanges,
      data,
      processMetricData,
      processMetricGroups,
      pathname, // Add pathname dependency
    ],
  );
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        handleFileUpload(acceptedFiles[0]);
      }
    },
    [handleFileUpload],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "text/plain": [],
      "application/pdf": [],
      "application/msword": [],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [],
      "text/csv": [],
      "application/vnd.ms-excel": [],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
      "image/jpeg": [],
      "image/png": [],
      "image/webp": [],
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024,
    minSize: 0,
    onDrop,
    noClick: true, // Prevent opening file dialog on click
    noDragEventsBubbling: true, // Prevent drag events from bubbling up
  });

  useEffect(() => {
    if (attachment.length > 0) {
      const totalFileSize = attachment
        .map((item: File) => item.size)
        .reduce((prev: any, current: any) => prev + current);
      setFileSize(totalFileSize);
      if (totalFileSize > 5 * 1024 * 1024) {
        setIsErrorFile(true);
      } else {
        setIsErrorFile(false);
      }
    }
  }, [attachment]);

  const {
    setShowSuggestions,
    handleGenerateSuggestions,
    setIsLoadingSuggestions,
    autoShowSuggestions,
    setAutoShowSuggestions,
    setSuggestions,
  } = useSuggestionContext();

  const handleDelErroFile = useCallback(
    (index: number) => {
      let newAttachmnets = [...attachment];
      newAttachmnets = newAttachmnets.filter((_, idx: number) => idx !== index);
      setAttachment(newAttachmnets);
      setIsErrorFile(false);
    },
    [attachment],
  );

  const handleDeleteDoc = useCallback(
    async (docName: string, index: number) => {
      try {
        setIsDeleting(true);
        const { data } = await axios.delete(
          `/api/upload?docName=${docName}&userId=${user_id}`,
        );
        handleDelErroFile(index);
        // setAttachment([]);
        setPreviewUrl(null);
        setIsErrorFile(false);
        setIsDeleting(false);
      } catch (error) {
        setIsDeleting(false);
        setAttachment([]);
        setPreviewUrl(null);
      }
    },
    [uploadedFile, handleDelErroFile],
  );

  useEffect(() => {
    // Update local input when userInput in Redux changes
    setLocalInput(userInput);
  }, [userInput]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalInput(newValue);
      dispatch(setAnswer(newValue));
      dispatch(setUserInput(newValue));
      if (inputRef.current?.offsetHeight) {
        dispatch(setUserInputHeight(inputRef.current?.offsetHeight));
      }
    },
    [dispatch],
  );

  // useEffect(() => {
  //   let isMounted = true;
  //   setTimeout(() => {
  //     fetchMessage(isMounted);
  //   }, 2000);
  //   return () => {
  //     console.log("Cleanup: setting isMounted to false");
  //     isMounted = false;
  //   };
  // }, [selectedJobId]);

  useEffect(() => {
    if (pathname === "/") {
      // alert('mic off')
      if (isMicOn) {
        dispatch(setIsMicOn(false));
        // stopListening();
        setCumulativeTranscript("");
      }
    }
  }, [pathname]);

  const fetchMessage = async (isMounted: boolean) => {
    if (selectedJobId) {
      try {
        const response = await fetch(`/api/messages/${selectedJobId}`);
        if (response.ok) {
          const data = await response.json();
          if (isMounted && data.messageList) {
            const lastQuestionOrTask = findLastQuestionOrTask(data.messageList);
            setLastMessage(lastQuestionOrTask);
          } else {
            console.log("Component unmounted or no messageList in data");
          }
        } else {
          console.error(`Failed to fetch messages. Status: ${response.status}`);
        }
      } catch (error) {
        console.error("Error fetching messages:", error);
      }
    } else {
      console.log("No selectedJobId, skipping fetch");
    }
  };

  useEffect(() => {
    setShowSuggestions(false);
    setAutoShowSuggestions(false);
  }, [selectedJobId, setShowSuggestions, setAutoShowSuggestions]);

  const checkAndAddNote = async (message: string, date: string) => {
    try {
      const response = await axios.post("/api/coach-notes", {
        message,
        userId: user_id,
        date,
      });

      if (response.data.success) {
        toast.success("Training note added to calendar");
        // Open calendar if it's not already open
        if (!isCalendarOpen) {
          dispatch(toggleCalendar());
        }
      }
    } catch (error) {
      // Silently log error but don't show to user
      console.error("Error adding note:", error);
    }
  };

  // Add state for tracking selected sports
  const [selectedSports, setSelectedSports] = useState<Suggestion[]>([]);

  // State to hide suggestions after they've been submitted
  const [suggestionsSubmitted, setSuggestionsSubmitted] = useState(false);

  // Filter out already selected sports from suggestions
  // Also hide all suggestions if they've been submitted
  const availableSuggestions = !suggestionsSubmitted
    ? suggestions?.filter(
        (suggestion) =>
          !selectedSports.some(
            (selected) => selected.value === suggestion.value,
          ),
      ) || []
    : [];

  // Debug logging for suggestions
  console.log("[UserInput DEBUG] Suggestions state:", {
    original_suggestions: suggestions,
    original_suggestions_count: suggestions?.length || 0,
    selected_sports: selectedSports,
    selected_sports_count: selectedSports.length,
    available_suggestions: availableSuggestions,
    available_suggestions_count: availableSuggestions.length,
    will_show_suggestions: availableSuggestions.length > 0,
    suggestions_submitted: suggestionsSubmitted,
    suggestions_prop_type: typeof suggestions,
    suggestions_prop_value: JSON.stringify(suggestions),
  });

  // Function to handle sport selection
  const handleSportSelection = (suggestion: Suggestion) => {
    setSelectedSports((prev) => {
      const isAlreadySelected = prev.some(
        (selected) => selected.value === suggestion.value,
      );
      if (isAlreadySelected) {
        return prev; // Don't add duplicates
      }
      return [...prev, suggestion];
    });
  };

  // Function to remove a selected sport
  const handleRemoveSport = (suggestionValue: string) => {
    setSelectedSports((prev) =>
      prev.filter((selected) => selected.value !== suggestionValue),
    );
  };

  // Function to submit all selected sports
  const handleSubmitSports = () => {
    if (selectedSports.length > 0) {
      // Send all selected sports as a comma-separated string or JSON
      const sportValues = selectedSports.map((sport) => sport.value).join(", ");

      // Hide suggestions immediately and clear selections
      setSuggestionsSubmitted(true);
      setSelectedSports([]);
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));

      // Submit the sports
      if (onSubmitRegularMessage) {
        onSubmitRegularMessage(sportValues);
      }
    }
  };

  // Modified handleSubmitAnswer to include selected sports
  const handleSubmitAnswer = async () => {
    console.log("[UserInput] handleSubmitAnswer called");
    console.log("[UserInput] Current state:", {
      localInput: localInput,
      localInputLength: localInput.length,
      selectedSports: selectedSports,
      selectedSportsCount: selectedSports.length,
      isHomePage,
      waitingForInput,
      isLoading,
      isStreaming,
      onSubmitRegularMessage: !!onSubmitRegularMessage,
      onSubmitWaitingInput: !!onSubmitWaitingInput,
      onSubmitHomeMessage: !!onSubmitHomeMessage,
      stagedUpload: stagedUpload,
      hasErrorMessage,
    });

    // Prepare the message to send including selected sports
    let messageToSend = localInput.trim();
    console.log("[UserInput] Initial messageToSend:", messageToSend);

    // If there are selected sports and no text input, submit the sports
    if (selectedSports.length > 0 && !messageToSend) {
      messageToSend = selectedSports.map((sport) => sport.value).join(", ");
      console.log(
        "[UserInput] Using selected sports as message:",
        messageToSend,
      );
    }
    // If there are both selected sports and text input, combine them
    else if (selectedSports.length > 0 && messageToSend) {
      const sportValues = selectedSports.map((sport) => sport.value).join(", ");
      messageToSend = `${sportValues}. ${messageToSend}`;
      console.log("[UserInput] Combined sports and text:", messageToSend);
    }

    // Handle staged upload separately - send two messages if both exist
    const hasUserMessage = messageToSend.length > 0;
    const hasStagedUpload = stagedUpload !== null;

    console.log("[UserInput] Message analysis:", {
      hasUserMessage,
      hasStagedUpload,
      messageToSendLength: messageToSend.length,
      messageToSend:
        messageToSend.substring(0, 100) +
        (messageToSend.length > 100 ? "..." : ""),
    });

    if (!hasUserMessage && !hasStagedUpload) {
      console.log("[UserInput] No message or upload - showing error");
      toast.error("Please enter a message or select sports to continue.");
      return;
    }

    // Handle home page message
    if (isHomePage && onSubmitHomeMessage) {
      console.log("[UserInput] Handling home page message");

      // Combine file upload and user message into a single submission
      let combinedMessage = "";

      if (hasStagedUpload && hasUserMessage) {
        // Both file and text message exist - combine them
        combinedMessage = `📎 Uploaded file: ${stagedUpload.filename}\n\n${messageToSend}`;
        console.log(
          "[UserInput] Sending combined file and text message to home:",
          combinedMessage,
        );
        onSubmitHomeMessage(combinedMessage);
      } else if (hasStagedUpload) {
        // Only file upload exists
        combinedMessage = `📎 Uploaded file: ${stagedUpload.filename}`;
        console.log(
          "[UserInput] Sending file message to home:",
          combinedMessage,
        );
        onSubmitHomeMessage(combinedMessage);
      } else if (hasUserMessage) {
        // Only text message exists
        console.log("[UserInput] Sending user message to home:", messageToSend);
        onSubmitHomeMessage(messageToSend);
      }

      console.log("[UserInput] Clearing state after home message");
      setSuggestionsSubmitted(true);
      setSelectedSports([]);
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));
      // Clear staged upload after message is sent
      setStagedUpload(null);
      return;
    }

    // Handle waiting for input
    if (waitingForInput && onSubmitWaitingInput) {
      console.log("[UserInput] Handling waiting for input message");
      console.log(
        "[UserInput] waitingForInput:",
        waitingForInput,
        "onSubmitWaitingInput exists:",
        !!onSubmitWaitingInput,
      );

      // Combine file upload and user message into a single submission
      let combinedMessage = "";

      if (hasStagedUpload && hasUserMessage) {
        // Both file and text message exist - combine them
        combinedMessage = `📎 Uploaded file: ${stagedUpload.filename}\n\n${messageToSend}`;
        console.log(
          "[UserInput] Sending combined file and text message for waiting input:",
          combinedMessage,
        );
        onSubmitWaitingInput(combinedMessage);
      } else if (hasStagedUpload) {
        // Only file upload exists
        combinedMessage = `📎 Uploaded file: ${stagedUpload.filename}`;
        console.log(
          "[UserInput] Sending file message for waiting input:",
          combinedMessage,
        );
        onSubmitWaitingInput(combinedMessage);
      } else if (hasUserMessage) {
        // Only text message exists
        console.log(
          "[UserInput] Sending user message for waiting input:",
          messageToSend,
        );
        onSubmitWaitingInput(messageToSend);
      }

      console.log("[UserInput] Clearing state after waiting input message");
      setSuggestionsSubmitted(true);
      setSelectedSports([]);
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));
      // Clear staged upload after message is sent
      setStagedUpload(null);
      return;
    }

    // Handle regular message
    if (onSubmitRegularMessage) {
      console.log("[UserInput] Handling regular message");
      console.log(
        "[UserInput] onSubmitRegularMessage exists:",
        !!onSubmitRegularMessage,
      );

      // Clear file attachments immediately when submitting the message
      setAttachment([]);
      setPreviewUrl(null);
      setUploadedFile(null);
      setIsImageSelection(false);
      setIfileUploading(false);
      setIsErrorFile(false);

      // Combine file upload and user message into a single submission
      let combinedMessage = "";

      if (hasStagedUpload && hasUserMessage) {
        // Both file and text message exist - combine them
        combinedMessage = `📎 Uploaded file: ${stagedUpload.filename}\n\n${messageToSend}`;
        console.log(
          "[UserInput] Sending combined file and text message for regular:",
          combinedMessage,
        );
        onSubmitRegularMessage(combinedMessage);
      } else if (hasStagedUpload) {
        // Only file upload exists
        combinedMessage = `📎 Uploaded file: ${stagedUpload.filename}`;
        console.log(
          "[UserInput] Sending file message for regular:",
          combinedMessage,
        );
        onSubmitRegularMessage(combinedMessage);
      } else if (hasUserMessage) {
        // Only text message exists
        console.log(
          "[UserInput] Sending user message for regular:",
          messageToSend,
        );
        onSubmitRegularMessage(messageToSend);
      }

      console.log("[UserInput] Clearing state after regular message");
      setSuggestionsSubmitted(true);
      setSelectedSports([]);
      setLocalInput("");
      dispatch(setUserInput(""));
      dispatch(setAnswer(""));

      // Clear staged upload after message is sent
      setStagedUpload(null);
      return;
    }

    console.log(
      "[UserInput] No handler found - falling back to original logic",
    );
    console.log("[UserInput] Available handlers:", {
      isHomePage: isHomePage,
      onSubmitHomeMessage: !!onSubmitHomeMessage,
      waitingForInput: waitingForInput,
      onSubmitWaitingInput: !!onSubmitWaitingInput,
      onSubmitRegularMessage: !!onSubmitRegularMessage,
    });

    // Original logic below for when no custom handlers are provided
    setIsEnterPressed(true);

    // For fallback WebSocket logic, combine messages since we can't send two separate ones easily
    let fallbackMessage = messageToSend || answer.trim();
    if (hasStagedUpload && hasUserMessage) {
      const fileMessage = `📎 Uploaded file: ${stagedUpload.filename}`;
      fallbackMessage = `${fileMessage}\n\n${messageToSend}`;
    } else if (hasStagedUpload) {
      fallbackMessage = `📎 Uploaded file: ${stagedUpload.filename}`;
    }

    // Guard clause: Do nothing if there's no message and no file
    if (fallbackMessage.length === 0 && !uploadedFile) {
      setIsEnterPressed(false); // Reset enter press state
      return;
    }

    // Clear suggestions and input state from Redux
    dispatch(clearSelectedSuggestions());
    dispatch(setAnswer("")); // Clear Redux answer state
    dispatch(setUserInput("")); // Clear Redux user input state
    setLocalInput(""); // Clear local input state as well

    // Ensure the input field is cleared after Redux state updates
    setTimeout(() => {
      setLocalInput("");
    }, 50);

    // Pause microphone if active
    if (isMicOn) {
      // pauseListening(); // Keep this commented if not using Azure Speech directly here
    }

    try {
      // Original logic for non-LangGraph pages (e.g., WebSocket)
      console.log("UserInput: Sending message via WebSocket (original logic)");
      await checkAndAddNote(
        fallbackMessage,
        new Date().toISOString().split("T")[0],
      );
      if (currWebSocket && currWebSocket.readyState === WebSocket.OPEN) {
        if (uploadedFile) {
          await dispatch(
            sendFileMessage({
              message: fallbackMessage,
              fileName: uploadedFile.file.name,
              fileType: uploadedFile.file.type,
              fileId: uploadedFile.fileId,
            }),
          );
          setUploadedFile(null);
        } else {
          await dispatch(sendChatMessageAsync({ msg: fallbackMessage }));
        }
        // Profile update logic remains for non-LangGraph context
        try {
          const response = await axios.post("/api/update-profile", {
            message: fallbackMessage,
            userId: user_id,
          });
          if (
            response.data.updated &&
            Object.keys(response.data.cleanedProfile).length > 0
          ) {
            console.log("Profile updated:", response.data.cleanedProfile);
            toast.success("Profile has been updated");
            const updateMessage = Object.entries(response.data.cleanedProfile)
              .map(([domain, updates]) =>
                Object.entries(updates as Record<string, any>).map(
                  ([key, value]) => `${domain}.${key}: ${value}`,
                ),
              )
              .flat()
              .join(", ");
            const systemMessage = `Training profile updated: ${updateMessage}`;
            dispatch(
              addToChatMessage({
                msg: systemMessage,
                msg_from: "system",
                display: true,
                timestamp: new Date().toISOString(),
              }),
            );
            await dispatch(
              loadNotifications({ userId: user_id, unread_count: false }),
            ).unwrap();
          } else {
            console.log(
              "No fields were updated or profile update was not required.",
            );
          }
        } catch (error) {
          console.error("Error updating profile:", error);
        }
        // Suggestion logic remains for non-LangGraph context
        setIsLoadingSuggestions(true);
        setSuggestions([]);
        setTimeout(() => {
          handleCallMsgListAPIAfterSubmitANS();
        }, 2000);
      } else {
        console.error("Cannot send message: WebSocket is not connected.");
        toast.error("Cannot send message. Please check your connection.");
      }

      // Common cleanup logic (applies to all contexts)
      setCumulativeTranscript("");
      setAttachment([]);
      setPreviewUrl(null);
      setIsImageSelection(false);
      setIfileUploading(false);
      setUploadedFile(null); // Ensure uploaded file state is cleared
      setIsErrorFile(false);
      // Resume listening after a short delay if mic was on
      if (isMicOn) {
        setTimeout(() => {
          // resumeListening(); // Keep commented if not using Azure Speech directly
        }, 100);
      }

      // Clear selections and hide suggestions after successful submission
      setSuggestionsSubmitted(true);
      setSelectedSports([]);

      // Clear staged upload in fallback logic
      setStagedUpload(null);
    } catch (error) {
      console.error("Error in handleSubmitAnswer:", error);
      toast.error("An error occurred while sending the message.");
    } finally {
      setIsEnterPressed(false);
    }
  };

  useEffect(() => {
    if (pendingResponse && isListening) {
      // pauseListening();
    } else if (
      !pendingResponse &&
      isMicOn &&
      !isListening &&
      pathname !== "/"
    ) {
      // resumeListening();
    }
  }, [pendingResponse, isListening, isMicOn, pauseListening, resumeListening]);

  const handleCallMsgListAPIAfterSubmitANS = async () => {
    const response = await fetch(`/api/messages/${selectedJobId}`);
    if (response.ok) {
      const data = await response.json();
      if (data.messageList) {
        const lastQuestionOrTask = findLastQuestionOrTask(data.messageList);
        setLastMessage(lastQuestionOrTask);
      }
    }
  };

  useEffect(() => {
    console.log(
      `pendingResponse: ${pendingResponse}, lastMessage: ${JSON.stringify(lastMessage)}`,
    );
    if (pendingResponse) {
      console.log(
        "Setting isLoadingSuggestions to true due to pendingResponse",
      );
      setIsLoadingSuggestions(true);
    } else if (lastMessage) {
      console.log(
        "Setting isLoadingSuggestions to false due to new lastMessage",
      );
      setIsLoadingSuggestions(false);
    }
  }, [pendingResponse, lastMessage, setIsLoadingSuggestions]);

  console.log("Pending Response", pendingResponse);
  useEffect(() => {
    if (myTranscript) {
      setCumulativeTranscript((prevTranscript) => {
        const newTranscript = prevTranscript
          ? `${prevTranscript} ${myTranscript}`
          : myTranscript;
        dispatch(setAnswer(newTranscript));
        dispatch(setUserInput(newTranscript));
        return newTranscript;
      });
    }
  }, [myTranscript, dispatch]);

  useEffect(() => {
    if (!isListening) {
      // Comment out problematic function call - we can restore it when proper imports are fixed
      // dispatch(setAiVoiceMessages([]));
      setShowSuggestions(false);
    }
  }, [isListening, dispatch, setShowSuggestions]);

  const hasInputOrSuggestions = localInput.trim() || selectedSports.length > 0;

  const handleAttachmentClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  useEffect(() => {
    const generateSuggestionsForNewMessage = async () => {
      console.log(
        `Checking for new message. lastMessage: ${JSON.stringify(lastMessage)}, autoShowSuggestions: ${autoShowSuggestions}`,
      );
      if (
        lastMessage &&
        lastMessage.id &&
        (lastMessage.type === "question" || lastMessage.type === "task")
      ) {
        const lastGeneratedId = localStorage.getItem(
          "lastGeneratedSuggestionId",
        );
        console.log(`Last generated ID from localStorage: ${lastGeneratedId}`);

        if (
          (!lastGeneratedId || lastMessage.id.toString() !== lastGeneratedId) &&
          autoShowSuggestions
        ) {
          console.log("Generating suggestions for new message:", lastMessage);
          await handleGenerateSuggestions(lastMessage);
          localStorage.setItem(
            "lastGeneratedSuggestionId",
            lastMessage.id.toString(),
          );
          console.log(
            `Updated lastGeneratedSuggestionId in localStorage to ${lastMessage.id}`,
          );
        } else {
          console.log(
            "Skipping suggestion generation: same message ID or autoShowSuggestions is false",
          );
        }
      } else {
        console.log(
          "Cannot generate suggestions: invalid lastMessage type or no lastMessage",
        );
      }
    };

    generateSuggestionsForNewMessage();
  }, [lastMessage, autoShowSuggestions, handleGenerateSuggestions]);

  const shouldApplyExtraStyles = useMemo(() => {
    const otherStatesActive =
      isCalendarOpen ||
      isAnimationMessageExpanded ||
      showGlossary ||
      showStats ||
      showEmotional ||
      showCurrentTrainingProfile; // Add stats to other states

    return (
      (!isCalendarOpen &&
        !isAnimationMessageExpanded &&
        !showGlossary &&
        !showCurrentTrainingProfile &&
        !showStats && // Add stats to extra styles check
        !showTrainingProfile &&
        !showEmotional) ||
      (showTrainingProfile && otherStatesActive)
    );
  }, [
    isCalendarOpen,
    isAnimationMessageExpanded,
    showGlossary,
    showTrainingProfile,
    showStats, // Add stats to dependencies
    showCurrentTrainingProfile,
    showEmotional,
  ]);
  console.log("shouldApplyExtraStyles", shouldApplyExtraStyles);

  useEffect(() => {
    const updateInputWidth = () => {
      if (formRef.current) {
        setInputWidth(formRef.current.offsetWidth);
      }
    };

    // Initial measurement
    setTimeout(() => {
      updateInputWidth();
    }, 500);
    // Set up event listener for window resize
    window.addEventListener("resize", updateInputWidth);

    // Clean up
    return () => {
      window.removeEventListener("resize", updateInputWidth);
    };
  }, [shouldApplyExtraStyles]);

  // Also update the useEffect for calendar/animation changes
  useEffect(() => {
    setTimeout(() => {
      if (formRef.current && (isCalendarOpen || isAnimationMessageExpanded)) {
        setInputWidth(formRef.current.offsetWidth);
      }
    }, 500);
  }, [isCalendarOpen, isAnimationMessageExpanded]);

  const handleMicToggle = () => {
    if (isMicOn) {
      dispatch(setIsMicOn(false));
      // stopListening();
      setCumulativeTranscript("");
    } else {
      dispatch(setIsMicOn(true));
      if (!pendingResponse) {
        setCumulativeTranscript("");
        // resumeListening();
      }
    }
  };

  const handleMicPress = async () => {
    try {
      const sessionId = crypto.randomUUID();
      console.log("[UserInput] Starting mic press with sessionId:", sessionId);

      // Use the current selected coach instead of hardcoding
      const currentCoach = selectedCoach || "Cycling Coach"; // Fallback if no coach selected
      console.log("[UserInput] Current coach:", currentCoach);

      // Convert display name to backend format for WebSocket
      const backendCoachId =
        currentCoach.replace(" Coach", "").toLowerCase().split(" ").join("_") +
        "_coach";

      console.log("[UserInput] Using coach for WebSocket:", {
        displayName: currentCoach,
        backendId: backendCoachId,
      });

      // Keep the same display format in Redux
      dispatch(setLastActiveCoach(currentCoach));
      dispatch(setIsMicOn(true));
      connect()
        .then((value) => {
          console.log("hume conneted", value);
        })
        .catch((reason) => {
          console.log("hume error", reason);
        });

      console.log("[UserInput] Connecting WebSocket...");
      // await dispatch(
      //   connectAudioWebSocket({
      //     sessionId,
      //     userId: user_id,
      //     coach: backendCoachId, // Use the backend format for WebSocket
      //     onMessage: (message) => {
      //       console.log("[UserInput] WebSocket message received:", message);
      //     },
      //   }),
      // ).unwrap();
      console.log("[UserInput] WebSocket connected successfully");

      console.log("[UserInput] Navigating to audio page...");
      // if (pathname === "/") {
      // }
      router.push(`/audio/${sessionId}`);
    } catch (error) {
      console.error("[UserInput] Error in handleMicPress:", error);
      toast.error("Failed to start recording");
      dispatch(disconnectAudioWebSocket());
      dispatch(setIsMicOn(false));
    }
  };

  const [isInputFocused, setIsInputFocused] = useState<boolean>(false);
  const [showUploadHint, setShowUploadHint] = useState<boolean>(false);
  const [uploadHintDismissed, setUploadHintDismissed] = useState<boolean>(
    () => {
      if (typeof window !== "undefined") {
        return localStorage.getItem("uploadHintDismissed") === "true";
      }
      return false;
    },
  );

  // Enhanced logging for debugging input blocking
  useEffect(() => {
    console.log("🔍 [UserInput] State Debug:", {
      // Input states
      localInput: localInput,
      localInputLength: localInput.length,
      localInputTrimmed: localInput.trim(),
      hasInputOrSuggestions,

      // Loading states
      isLoading,
      isStreaming,
      isCreatingChat,
      pendingResponse,
      isFileUploading,

      // Waiting states
      waitingForInput,

      // Error states
      hasErrorMessage,

      // Page states
      isHomePage,
      isLangGraphChat,

      // Selected data
      selectedSports: selectedSports.length,
      selectedJobId,

      // Computed disabled states
      inputDisabled: isLoading || hasErrorMessage,
      submitDisabled:
        isLoading ||
        hasErrorMessage ||
        (!hasInputOrSuggestions && !waitingForInput),
      fileUploadDisabled: isFileUploading,

      // Other relevant states
      attachment: attachment.length,
      stagedUpload: !!stagedUpload,
      isInputFocused,

      // Timestamp for tracking
      timestamp: new Date().toISOString(),
    });
  }, [
    localInput,
    hasInputOrSuggestions,
    isLoading,
    isStreaming,
    isCreatingChat,
    pendingResponse,
    isFileUploading,
    waitingForInput,
    hasErrorMessage,
    isHomePage,
    isLangGraphChat,
    selectedSports.length,
    selectedJobId,
    attachment.length,
    stagedUpload,
    isInputFocused,
  ]);

  // Log when input gets focused/blurred
  useEffect(() => {
    console.log("🎯 [UserInput] Focus State Changed:", {
      isInputFocused,
      timestamp: new Date().toISOString(),
    });
  }, [isInputFocused]);

  // Hide upload hint when clicking anywhere on the page
  useEffect(() => {
    const handleClickAnywhere = () => {
      setShowUploadHint(false);
    };

    if (showUploadHint) {
      document.addEventListener("click", handleClickAnywhere);
      return () => {
        document.removeEventListener("click", handleClickAnywhere);
      };
    }
  }, [showUploadHint]);

  // Show upload hint when user has sports suggestions available but no files uploaded
  useEffect(() => {
    const hasSelectedSports = selectedSports.length > 0;
    const hasSportSuggestions = availableSuggestions.length > 0;
    const hasUploadedFiles = attachment.length > 0;
    const isOnboardingMode = pathname?.includes("/onboarding/");

    // Show hint if:
    // 1. In onboarding mode and ANY of these conditions:
    //    - There are sport suggestions available
    //    - The URL suggests we're past the initial stage (has a UUID thread ID)
    // 2. OR user has manually selected sports
    // AND no files have been uploaded yet
    const isOnboardingWithActivity =
      isOnboardingMode &&
      (hasSportSuggestions || (pathname && pathname.split("/").length >= 3)); // /onboarding/{uuid} suggests active session

    const shouldShowHint =
      (isOnboardingWithActivity || hasSelectedSports) &&
      !hasUploadedFiles &&
      !showUploadHint &&
      !uploadHintDismissed;

    if (shouldShowHint) {
      // Show hint after a brief delay to feel natural
      const timer = setTimeout(() => {
        setShowUploadHint(true);
      }, 2000);
      return () => clearTimeout(timer);
    } else if (hasUploadedFiles && showUploadHint) {
      // Hide hint once files are uploaded
      setShowUploadHint(false);
    }
  }, [
    selectedSports.length,
    availableSuggestions.length,
    attachment.length,
    showUploadHint,
    pathname,
    uploadHintDismissed,
  ]);

  const handleCloseUploadHint = () => {
    setShowUploadHint(false);
    setUploadHintDismissed(true);
    if (typeof window !== "undefined") {
      localStorage.setItem("uploadHintDismissed", "true");
    }
  };

  return (
    <div className="flex w-full flex-col items-center">
      {/* Selected Sports Pills */}
      {selectedSports.length > 0 && (
        <div className="mb-2 flex flex-wrap justify-center gap-2 px-4">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-gray-600 text-sm font-medium">Selected:</span>
            {selectedSports.map((sport) => (
              <div
                key={sport.value}
                className="flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm"
              >
                <span>{sport.label}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveSport(sport.value)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                  aria-label={`Remove ${sport.label}`}
                >
                  ×
                </button>
              </div>
            ))}
            {/* Submit selected sports button */}
            <Button
              type="button"
              variant="default"
              size="sm"
              className="rounded-full text-xs"
              onClick={handleSubmitSports}
            >
              Submit Selected ({selectedSports.length})
            </Button>
          </div>
        </div>
      )}

      {/* Staged Upload Indicator */}
      {stagedUpload && (
        <div className="mb-2 flex justify-center px-4">
          <div className="flex items-center gap-2 rounded-full border border-green-200 bg-green-50 px-4 py-2">
            <div className="flex h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
            <span className="text-sm font-medium text-green-800">
              📎 {stagedUpload.filename} ready to send
            </span>
            <button
              type="button"
              onClick={() => setStagedUpload(null)}
              className="ml-2 text-green-600 hover:text-green-800"
              aria-label="Remove staged upload"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Available Suggestions Container */}
      {availableSuggestions && availableSuggestions.length > 0 && (
        <div className="mb-2 flex flex-wrap justify-center gap-2 px-4">
          {availableSuggestions.map((suggestion) => (
            <Button
              key={suggestion.value}
              variant="outline"
              size="sm"
              className="h-auto rounded-full px-3 py-1 text-sm"
              onClick={() => handleSportSelection(suggestion)}
              onMouseDown={(e) => e.preventDefault()}
            >
              {suggestion.label}
            </Button>
          ))}
        </div>
      )}

      <form
        onSubmit={(e) => {
          console.log("[UserInput] Form submitted!");
          console.log("[UserInput] Form submission state:", {
            isLoading,
            hasInputOrSuggestions,
            hasErrorMessage,
            localInput,
            waitingForInput,
            buttonDisabled:
              isLoading ||
              hasErrorMessage ||
              (!hasInputOrSuggestions && !waitingForInput),
          });
          e.preventDefault();
          handleSubmitAnswer();
        }}
        className={cn(
          "flex w-full max-w-sm items-center gap-2 rounded-full border bg-white px-4 py-2 shadow-sm transition-all duration-200 sm:max-w-md md:max-w-lg lg:max-w-3xl",
          isInputFocused ? "shadow-md" : "shadow-sm",
        )}
        ref={formRef}
      >
        <Search className="text-gray-400 h-5 w-5" />
        <Input
          type="text"
          value={localInput}
          onChange={handleInputChange}
          placeholder={
            isHomePage
              ? "Ask about training, nutrition, or recovery..."
              : isLangGraphChat
                ? waitingForInput
                  ? inputPrompt || "Please provide the requested information..."
                  : "Ask a fitness question..."
                : selectedJobId
                  ? "Send message"
                  : "Ask about training, nutrition, or recovery..."
          }
          className="placeholder:text-gray-500 flex-1 border-none bg-transparent outline-none focus:ring-0"
          onFocus={() => {
            setIsInputFocused(true);
            if (onFocusChange) onFocusChange(true);
          }}
          onBlur={(e) => {
            const relatedTarget = e.relatedTarget;

            // Allow time for click events on suggestion pills to propagate
            // before deciding whether to remove focus
            setTimeout(() => {
              // Only lose focus if not clicking a suggestion pill
              const isClickingPill = document
                .querySelector(".suggestion-pills-container")
                ?.contains(relatedTarget as Node);

              if (
                !formRef.current?.contains(relatedTarget as Node) &&
                !isClickingPill
              ) {
                setIsInputFocused(false);
                if (onFocusChange) onFocusChange(false);
                if (!isLangGraphChat) {
                  dispatch(setIsSidebarModalOpen(true));
                }
              }
            }, 10);
          }}
          disabled={isLoading || hasErrorMessage}
        />

        <IconButton
          onClick={handleAttachmentClick}
          onMouseEnter={() => uploadHintDismissed && setShowUploadHint(true)}
          onMouseLeave={() => uploadHintDismissed && setShowUploadHint(false)}
          disabled={isFileUploading}
          aria-label="attach file"
          sx={{
            color: "#90a4ae",
            borderRadius: "10%",
            p: "6px",
            "&:hover": {
              backgroundColor: "transparent",
              color: "#1976d2",
            },
          }}
        >
          <Paperclip size={20} />

          {showUploadHint && (
            <div className="absolute -top-16 left-1/2 -translate-x-1/2 transform">
              <div className="relative flex items-center whitespace-nowrap rounded-lg bg-white px-3 py-2 text-xs text-slate-700 shadow-lg duration-300 animate-in fade-in-0 zoom-in-95">
                <span className="whitespace-nowrap">Try uploading</span>
                <button
                  type="button"
                  onClick={handleCloseUploadHint}
                  className="text-gray-400 hover:text-gray-600 ml-2 flex h-3 w-3 items-center justify-center focus:outline-none"
                >
                  <X size={12} />
                </button>
                {/* Tooltip Arrow */}
                <div className="absolute left-1/2 top-full -translate-x-1/2 border-4 border-transparent border-t-white"></div>
              </div>
            </div>
          )}
        </IconButton>

        <Button
          type="submit"
          size="sm"
          variant="ghost"
          disabled={
            isLoading ||
            hasErrorMessage ||
            (!hasInputOrSuggestions && !waitingForInput)
          }
          onClick={(e) => {
            console.log("[UserInput] Submit button clicked!");
            console.log("[UserInput] Button click state:", {
              isLoading,
              hasInputOrSuggestions,
              hasErrorMessage,
              localInput,
              waitingForInput,
              buttonDisabled:
                isLoading ||
                hasErrorMessage ||
                (!hasInputOrSuggestions && !waitingForInput),
              eventDefaultPrevented: e.defaultPrevented,
            });
          }}
        >
          {isHomePage && isCreatingChat ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : isLangGraphChat && isLoading && !waitingForInput ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-5 w-5" />
          )}
        </Button>

        <input
          {...getInputProps()}
          type="file"
          accept="*"
          ref={fileInputRef}
          style={{ display: "none" }}
          onChange={(e) => {
            if (e.target.files && e.target.files[0]) {
              handleFileUpload(e.target.files[0]);
            }
          }}
        />
      </form>

      {attachment.length > 0 && isImageSelection && previewUrl && (
        <div className="absolute bottom-16 left-0 rounded-md border">
          <ImagePreview previewUrl={previewUrl} />
          {isFileUploading && (
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#000000] border-t-transparent"></div>
            </div>
          )}
        </div>
      )}

      {attachment.length > 0 && !isImageSelection && (
        <div
          className="absolute bottom-16 left-0 flex flex-row overflow-scroll rounded-md"
          style={{ width: inputWidth > 0 ? inputWidth + 42 : "auto" }}
        >
          {attachment.map((attachedfile: File, index: number) => (
            <FilePreviewComponent
              key={index}
              attachment={attachedfile}
              colorMode={colorMode}
              isFileUploading={isFileUploading}
              isErrorFile={isErrorFile}
              isDeleting={isDeleting}
              isLastIndex={
                attachment.length > 0 && attachment.length - 1 === index
                  ? true
                  : false
              }
              onRemove={() => {
                if (isErrorFile) {
                  handleDelErroFile(index);
                } else if (attachedfile) {
                  handleDeleteDoc(attachedfile?.name, index);
                }
              }}
              inputWidth={inputWidth}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default UserInput;
