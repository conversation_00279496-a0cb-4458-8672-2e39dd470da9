import React, { useEffect, useState, useRef } from "react";

type WorkflowTransition = {
  type: string;
  timestamp: string;
  activePaths?: string[];
  context?: any;
  runId?: string;
  status?: string;
  message?: string;
  stepId?: string;
};

type WorkflowViewerProps = {
  workflowId: string;
  runId?: string;
  watchWorkflow: (
    workflowId: string,
    runId: string | undefined,
    callback: (data: any) => void,
  ) => () => void;
  className?: string;
};

const MastraWorkflowViewer: React.FC<WorkflowViewerProps> = ({
  workflowId,
  runId,
  watchWorkflow,
  className = "",
}) => {
  const [transitions, setTransitions] = useState<WorkflowTransition[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const transitionsEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when transitions change
  useEffect(() => {
    transitionsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [transitions]);

  // Set up SSE when component mounts
  useEffect(() => {
    if (!workflowId) return;

    setTransitions([]);
    setError(null);
    setIsConnected(false);

    // Start watching the workflow
    const cleanupFn = watchWorkflow(workflowId, runId, (data) => {
      if (data.type === "connection" && data.status === "established") {
        setIsConnected(true);
        setTransitions((prev) => [...prev, data]);
      } else if (data.type === "error") {
        setError(data.message);
        setTransitions((prev) => [...prev, data]);
      } else {
        setTransitions((prev) => [...prev, data]);
      }
    });

    // Clean up SSE when component unmounts
    return cleanupFn;
  }, [workflowId, runId, watchWorkflow]);

  return (
    <div className={`flex flex-col ${className}`}>
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-medium">Workflow Transitions</h3>
        <div className="flex items-center">
          <span
            className={`mr-2 h-3 w-3 rounded-full ${
              isConnected ? "bg-green-500" : "bg-gray-400"
            }`}
          ></span>
          <span className="text-gray-600 text-sm">
            {isConnected ? "Connected" : "Disconnected"}
          </span>
        </div>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-100 p-3 text-sm text-red-700">
          Error: {error}
        </div>
      )}

      <div className="border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 flex-1 overflow-y-auto rounded-md border p-3">
        {transitions.length === 0 ? (
          <div className="text-gray-500 flex h-full items-center justify-center">
            Waiting for workflow transitions...
          </div>
        ) : (
          <div className="space-y-2">
            {transitions.map((transition, index) => (
              <div
                key={index}
                className="border-gray-200 dark:border-gray-700 dark:bg-gray-700 rounded-md border bg-white p-3"
              >
                <div className="flex items-center justify-between">
                  <span
                    className={`rounded-full px-2 py-0.5 text-xs ${getTypeColor(
                      transition.type,
                    )}`}
                  >
                    {transition.type}
                  </span>
                  <span className="text-gray-500 text-xs">
                    {new Date(transition.timestamp).toLocaleTimeString()}
                  </span>
                </div>

                {/* Transition details based on type */}
                {transition.type === "transition" && (
                  <div className="mt-2 text-sm">
                    {transition.activePaths && (
                      <div className="mt-1">
                        <strong>Active Paths:</strong>{" "}
                        {transition.activePaths.join(", ")}
                      </div>
                    )}
                    {transition.stepId && (
                      <div className="mt-1">
                        <strong>Step ID:</strong> {transition.stepId}
                      </div>
                    )}
                    {transition.context && (
                      <div className="mt-2">
                        <strong>Context:</strong>
                        <pre className="bg-gray-50 dark:bg-gray-800 mt-1 max-h-32 overflow-y-auto rounded-md p-2 text-xs">
                          {JSON.stringify(transition.context, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                )}

                {/* Connection status */}
                {transition.type === "connection" && (
                  <div className="mt-2 text-sm">
                    <strong>Status:</strong> {transition.status}
                    {transition.runId && (
                      <div className="mt-1">
                        <strong>Run ID:</strong> {transition.runId}
                      </div>
                    )}
                  </div>
                )}

                {/* Error message */}
                {transition.type === "error" && transition.message && (
                  <div className="mt-2 text-sm text-red-600">
                    <strong>Error:</strong> {transition.message}
                  </div>
                )}

                {/* Completion status */}
                {transition.type === "complete" && (
                  <div className="mt-2 text-sm">
                    <strong>Status:</strong>{" "}
                    <span
                      className={
                        transition.status === "completed"
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {transition.status}
                    </span>
                  </div>
                )}
              </div>
            ))}
            <div ref={transitionsEndRef} />
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to get background color based on transition type
function getTypeColor(type: string): string {
  switch (type) {
    case "connection":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    case "transition":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
    case "error":
      return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
    case "complete":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
  }
}

export default MastraWorkflowViewer;
