import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps,
} from "recharts";
import { Card, CardContent } from "../ui/cards";

interface DatasetType {
  key: string;
  unit?: string;
  color: string;
  description?: string;
}

interface DataType {
  [key: string]: any;
}

interface CustomTooltipProps extends TooltipProps<number, string> {
  dataset: DatasetType;
  formatDate: (date: string) => string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({
  active,
  payload,
  label,
  dataset,
  formatDate,
}) => {
  if (!active || !payload || !payload.length) return null;

  const firstPayload = payload[0];
  const value =
    typeof firstPayload.value === "number"
      ? firstPayload.value.toFixed(2)
      : firstPayload.value;

  const labelStr = label ? label.toString() : "";

  return (
    <div className="border-gray-200 rounded-lg border bg-white/90 p-3 shadow-lg backdrop-blur-sm">
      <p className="text-gray-600 mb-2 font-medium">{formatDate(labelStr)}</p>
      <div className="flex items-center gap-2">
        <span className="text-sm">
          <span className="ml-1 font-medium">
            {value} {dataset?.unit || ""}
          </span>
        </span>
      </div>
    </div>
  );
};

interface SingleLineChartProps {
  dataset: DatasetType;
  data: DataType[];
  formatDate: (date: string) => string;
}

const SingleLineChart: React.FC<SingleLineChartProps> = ({
  dataset,
  data,
  formatDate,
}) => {
  const values = data
    .map((item) => item[dataset.key])
    .filter((val): val is number => typeof val === "number");

  const minValue = values.length > 0 ? Math.floor(Math.min(...values)) : 0;
  const maxValue = values.length > 0 ? Math.ceil(Math.max(...values)) : 0;
  const padding = (maxValue - minValue) * 0.1;

  return (
    <div className="mx-2 h-[300px] w-full">
      <ResponsiveContainer width={400} height="100%">
        <LineChart
          data={data}
          margin={{ top: 10, right: 0, bottom: 10, left: 0 }}
        >
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="Date"
            tick={{ fontSize: 12 }}
            tickFormatter={formatDate}
            interval="preserveStartEnd"
            axisLine={false}
          />
          <YAxis
            domain={[minValue - padding, maxValue + padding]}
            tickCount={5}
            width={40}
            axisLine={false}
            dx={-10}
          />
          <Tooltip
            content={
              <CustomTooltip dataset={dataset} formatDate={formatDate} />
            }
          />
          <Line
            type="monotone"
            dataKey={dataset.key}
            stroke={dataset.color}
            dot={false}
            strokeWidth={2}
            connectNulls={true}
            isAnimationActive={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

interface MetricType {
  id: string;
  datasets: DatasetType[];
}

interface CombinedChartsProps {
  metrics: MetricType[];
  data: DataType[];
  formatDate: (date: string) => string;
}

export const CombinedCharts: React.FC<CombinedChartsProps> = ({
  metrics,
  data,
  formatDate,
}) => {
  return (
    <div className="mt-8 space-y-8">
      {metrics.map((metric) => (
        <Card key={metric.id} style={{ maxWidth: 500 }}>
          <CardContent className="p-0">
            {metric.datasets.map((dataset) => {
              const values = data
                .map((item) => item[dataset.key])
                .filter((val): val is number => typeof val === "number");

              const minValue = values.length > 0 ? Math.min(...values) : 0;
              const maxValue = values.length > 0 ? Math.max(...values) : 0;
              const average =
                values.length > 0
                  ? values.reduce((a, b) => a + b, 0) / values.length
                  : 0;

              return (
                <div key={dataset.key} className="space-y-4 p-6">
                  <SingleLineChart
                    dataset={dataset}
                    data={data}
                    formatDate={formatDate}
                  />
                  <p className="text-gray-500 text-sm">
                    {`Measurement of ${dataset.description || dataset.key.replace(/_/g, " ")
                      }`}
                  </p>
                  <p className="text-gray-500 text-sm">
                    {`Values ranging from ${minValue.toFixed(
                      2,
                    )} to ${maxValue.toFixed(
                      2,
                    )} with average ${average.toFixed(2)} ${dataset.unit || ""
                      }`}
                  </p>
                </div>
              );
            })}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
