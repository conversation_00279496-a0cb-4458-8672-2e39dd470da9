import React, { useState, useRef, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import {
  Lo<PERSON>,
  ChevronLeft,
  ChevronRight,
  Heart, // Heart Rate icon
  Activity, // Activity icon
  Moon, // Sleep icon
  Battery, // Recovery icon
  LucideBlocks, // Blocks icon
  Dumbbell, // Added for Training icon
} from "lucide-react";
import { Card, CardContent, CardHeader } from "../ui/cards";
import MetricVisualizer, { MetricPanel } from "./MetricVisualizer";




const getCategoryIcon = (category: string) => {
  const iconProps = { className: "h-6 w-6 mr-2" };

  switch (category) {
    case "Heart Rate":
      return <Heart {...iconProps} />;
    case "Activity":
      return <Activity {...iconProps} />;
    case "Sleep":
      return <Moon {...iconProps} />;
    case "Recovery":
      return <Battery {...iconProps} />;
    case "Training":
      return <Dumbbell {...iconProps} />;
    default:
      return <LucideBlocks {...iconProps} />;
  }
};

interface StatsRowProps {
  category: string;
  panels: MetricPanel[];
  renderPanel?: (panel: MetricPanel) => React.ReactNode;
}

const StatsRow: React.FC<StatsRowProps> = ({
  category,
  panels,
  renderPanel,
}) => {
  const domainColors: { [key: string]: string } = {
    cycling: "#37C6C4",
    general: "#6580F4",
    strength: "#D343DB",
    running: "#F7B500",
    nutrition: "#63D571",
    recovery: "#FD8C5B",
    phase: "#A1A1AA", // Example color for phase
  };
  console.log('panels', panels, domainColors);
  const [scrollPosition, setScrollPosition] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const scroll = (direction: "left" | "right") => {
    const container = containerRef.current;
    if (!container) return;

    const scrollAmount = 300;
    const newPosition =
      direction === "left"
        ? scrollPosition - scrollAmount
        : scrollPosition + scrollAmount;

    container.scrollTo({
      left: newPosition,
      behavior: "smooth",
    });

    setScrollPosition(newPosition);
  };

  const showLeftArrow = scrollPosition > 0;
  const showRightArrow = useMemo(() => {
    if (!containerRef.current) return false;
    return (
      scrollPosition <
      containerRef.current.scrollWidth - containerRef.current.clientWidth
    );
  }, [scrollPosition]);

  return (
    <div className="relative px-4">
      <div className="mb-4 flex items-center">
        {getCategoryIcon(category)}
        <h2 className="text-xl font-medium">{category}</h2>
      </div>

      <div className="relative">
        {showLeftArrow && (
          <button
            onClick={() => scroll("left")}
            className="absolute left-0 top-1/2 z-10 -translate-y-1/2 transform rounded-full bg-white p-2 shadow-lg"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
        )}

        <div
          ref={containerRef}
          className="no-scrollbar flex gap-4 overflow-x-auto scroll-smooth"
          onScroll={(e) =>
            setScrollPosition((e.target as HTMLDivElement).scrollLeft)
          }
        >
          {panels.map((panel, index) => {
            const color = panel.type ? domainColors[panel.type] : 'blue-500'
            return (
              <div
                key={index}
                className={`flex-shrink-0 ${category === "Training" ? `border-l-4` : ""
                  } mx-2 mb-4 rounded-md shadow-lg`}
                style={{ borderColor: color }}
              >
                {renderPanel ? (
                  renderPanel(panel)
                ) : (
                  <MetricVisualizer metric={panel} />
                )}
              </div>
            )
          })}
        </div>

        {showRightArrow && (
          <button
            onClick={() => scroll("right")}
            className="absolute right-0 top-1/2 z-10 -translate-y-1/2 transform rounded-full bg-white p-2 shadow-lg"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        )}
      </div>
    </div>
  );
};

export default StatsRow;
