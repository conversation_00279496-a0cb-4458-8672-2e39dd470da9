import React from "react";
import { <PERSON>, CardContent, CardHeader } from "../ui/cards";
import { Badge } from "../ui/badge";
import { Activity, Timer, TrendingUp, Weight } from "lucide-react";
import {
  DailyStrengthStats,
  DailyNutritionStats,
  DailyCyclingStats,
  DailyRunningStats,
  DailyRecoveryStats,
} from "@/types/statsTypes";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { selectDailyStats } from "@/store/slices/statsSlice";

interface StatBoxProps {
  label: string;
  value: number | string;
  unit?: string;
  icon?: React.ReactNode;
}

const StatBox: React.FC<StatBoxProps> = ({ label, value, unit, icon }) => (
  <div className="bg-gray-50 flex items-center justify-between rounded-lg p-3">
    <div className="flex items-center space-x-2">
      {icon}
      <div>
        <p className="text-gray-500 text-sm">{label}</p>
        <p className="text-lg font-semibold">
          {value} {unit}
        </p>
      </div>
    </div>
  </div>
);

interface TrainingMetricVisualizerProps {
  stats: any;
  type: "cycling" | "running" | "strength" | "nutrition" | "recovery";
}

const TrainingMetricVisualizer: React.FC<TrainingMetricVisualizerProps> = ({
  stats,
  type,
}) => {
  const dailyTrainingStats = useAppSelector(selectDailyStats);
  console.log("dailyTrainingStats", dailyTrainingStats, type);

  const renderStrengthStats = (data: DailyStrengthStats) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <StatBox
            label="Duration"
            value={data.duration || 0}
            unit="minutes"
            icon={<Timer className="text-gray-400 h-5 w-5" />}
          />
          <StatBox
            label="Intensity"
            value={data.intensity || "Medium"}
            icon={<Activity className="text-gray-400 h-5 w-5" />}
          />
        </div>

        <div>
          <h4 className="mb-2 ml-3 font-medium">Body Part Distribution</h4>
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-4">
            {Object.entries(data.bodyPartDistribution).map(([part, count]) => (
              <div key={part} className="bg-gray-50 rounded-lg p-2 text-center">
                <div className="text-sm font-medium">{part}</div>
                <div className="text-lg">{count}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderCyclingStats = (data: DailyCyclingStats) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <StatBox
            label="Duration"
            value={data.duration || 0}
            unit="minutes"
            icon={<Timer className="text-gray-400 h-5 w-5" />}
          />
          <StatBox
            label="Average RPM"
            value={
              data.segmentBreakdown.reduce(
                (acc, segment) => acc + segment.rpm,
                0,
              ) / data.segmentBreakdown.length
            }
            unit="rpm"
            icon={<Activity className="text-gray-400 h-5 w-5" />}
          />
        </div>

        <div>
          <h4 className="mb-2 ml-3 font-medium">Zone Distribution</h4>
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-5">
            {Object.entries(data.zoneDistribution).map(([zone, time]) => (
              <div key={zone} className="bg-gray-50 rounded-lg p-2 text-center">
                <div className="text-sm font-medium">{zone}</div>
                <div className="text-lg">{time} min</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderRunningStats = (data: DailyRunningStats) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <StatBox
            label="Duration"
            value={data.duration || 0}
            unit="minutes"
            icon={<Timer className="text-gray-400 h-5 w-5" />}
          />
          <StatBox
            label="Total Distance"
            value={data.totalDistance}
            unit="km"
            icon={<Activity className="text-gray-400 h-5 w-5" />}
          />
        </div>

        <div>
          <h4 className="mb-2 ml-3 font-medium">Zone Distribution</h4>
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-5">
            {Object.entries(data.zoneDistribution).map(([zone, time]) => (
              <div key={zone} className="bg-gray-50 rounded-lg p-2 text-center">
                <div className="text-sm font-medium">{zone}</div>
                <div className="text-lg">{time} min</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderRecoveryStats = (data: DailyRecoveryStats) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <StatBox
            label="Duration"
            value={data.duration || 0}
            unit="minutes"
            icon={<Timer className="text-gray-400 h-5 w-5" />}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="mb-2 ml-3 font-medium">Intensity Distribution</h4>
            <div className="grid gap-2">
              {Object.entries(data.intensityDistribution).map(
                ([intensity, count]) => (
                  <div
                    key={intensity}
                    className="bg-gray-50 rounded-lg p-2 text-center"
                  >
                    <div className="text-sm font-medium">{intensity}</div>
                    <div className="text-lg">{count}</div>
                  </div>
                ),
              )}
            </div>
          </div>

          <div>
            <h4 className="mb-2 ml-3 font-medium">Type Distribution</h4>
            <div className="grid gap-2">
              {Object.entries(data.typeDistribution).map(([type, count]) => (
                <div
                  key={type}
                  className="bg-gray-50 rounded-lg p-2 text-center"
                >
                  <div className="text-sm font-medium">{type}</div>
                  <div className="text-lg">{count}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderNutritionStats = (data: DailyNutritionStats) => {
    console.log("Nutrition Data:", {
      fullData: data,
      mealBreakdown: data?.mealBreakdown,
      totalCalories: data?.dailyTotalCalories,
    });

    if (!data?.mealBreakdown) {
      return (
        <div className="space-y-4">
          <StatBox
            label="Total Daily Calories"
            value={data?.dailyTotalCalories || 0}
            unit="kcal"
            icon={<Activity className="text-gray-400 h-5 w-5" />}
          />
          <div className="text-gray-500">No meal breakdown available</div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <StatBox
            label="Total Daily Calories"
            value={data.dailyTotalCalories}
            unit="kcal"
            icon={<Activity className="text-gray-400 h-5 w-5" />}
          />
        </div>
        <div>
          <h4 className="mb-2 ml-3 font-medium">Meal Breakdown</h4>
          <div className="grid gap-2">
            {Object.entries(data.mealBreakdown).map(([meal, details]) => (
              <div key={meal} className="bg-gray-50 rounded-lg p-2">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium capitalize">{meal}</div>
                  <div className="text-lg">{details.totalCalories} kcal</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  switch (type) {
    case "strength":
      console.log("Rendering strength stats:", stats);
      if (!dailyTrainingStats.strength) {
        return (
          <div className="text-gray-500">
            No visualization available for {type} stats
          </div>
        );
      }
      return renderStrengthStats(dailyTrainingStats.strength);
    case "cycling":
      console.log("Rendering cycling stats:", stats);
      if (!dailyTrainingStats.cycling) {
        return (
          <div className="text-gray-500">
            No visualization available for {type} stats
          </div>
        );
      }
      return renderCyclingStats(dailyTrainingStats.cycling);
    case "running":
      console.log("Rendering running stats:", stats);
      if (!dailyTrainingStats.running) {
        return (
          <div className="text-gray-500">
            No visualization available for {type} stats
          </div>
        );
      }
      return renderRunningStats(dailyTrainingStats.running);
    case "recovery":
      console.log("Rendering recovery stats:", stats);
      if (!dailyTrainingStats.recovery) {
        return (
          <div className="text-gray-500">
            No visualization available for {type} stats
          </div>
        );
      }
      return renderRecoveryStats(dailyTrainingStats.recovery);
    case "nutrition":
      console.log("Rendering nutrition stats:", stats);
      if (!dailyTrainingStats.nutrition) {
        return (
          <div className="text-gray-500">
            No visualization available for {type} stats
          </div>
        );
      }
      return renderNutritionStats(dailyTrainingStats.nutrition);
    default:
      return (
        <div className="text-gray-500">
          No visualization available for {type} stats
        </div>
      );
  }
};

export default TrainingMetricVisualizer;
