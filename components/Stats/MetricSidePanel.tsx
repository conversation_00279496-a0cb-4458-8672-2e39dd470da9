import React, { useState, useEffect } from "react";
import { ChevronFirstIcon, ChevronLastIcon } from "lucide-react";

// Define a type for the metric names
type MetricName =
  | "Heart_Rate_Avg"
  | "Heart_Rate_Max"
  | "Heart_Rate_Min"
  | "Resting_Heart_Rate"
  | "HRV"
  | "Steps"
  | "Calories_Burned"
  | "Active_Calories"
  | "Distance_km"
  | "Floor_Climbed"
  | "Activity_Minutes"
  | "Sleep_Hours"
  | "Deep_Sleep_Hours"
  | "REM_Sleep_Hours"
  | "Light_Sleep_Hours"
  | "Recovery_Score"
  | "Stress_Level"
  | "Body_Battery"
  | "VO2_Max";

const ALL_METRICS: Record<string, readonly MetricName[]> = {
  "Heart Rate": [
    "Heart_Rate_Avg",
    "Heart_Rate_Max",
    "Heart_Rate_Min",
    "Resting_Heart_Rate",
    "HRV",
  ],
  Activity: [
    "Steps",
    "Calories_Burned",
    "Active_Calories",
    "Distance_km",
    "Floor_Climbed",
    "Activity_Minutes",
  ],
  Sleep: [
    "Sleep_Hours",
    "Deep_Sleep_Hours",
    "REM_Sleep_Hours",
    "Light_Sleep_Hours",
  ],
  Recovery: ["Recovery_Score", "Stress_Level", "Body_Battery", "VO2_Max"],
} as const;

interface MetricsSidePanelProps {
  availableMetrics: Set<string>;
  selectedMetrics: Set<string>;
  onMetricsChange: (metrics: Set<string>) => void;
  onCollapse?: (collapsed: boolean) => void;
}

export const MetricsSidePanel: React.FC<MetricsSidePanelProps> = ({
  availableMetrics,
  selectedMetrics,
  onMetricsChange,
  onCollapse,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    onCollapse?.(isCollapsed);
  }, [isCollapsed, onCollapse]);

  const handleMetricToggle = (metric: MetricName) => {
    const newSelectedMetrics = new Set(selectedMetrics);
    if (newSelectedMetrics.has(metric)) {
      newSelectedMetrics.delete(metric);
    } else {
      newSelectedMetrics.add(metric);
    }
    onMetricsChange(newSelectedMetrics);
  };

  const handleCategoryToggle = (categoryMetrics: readonly MetricName[]) => {
    const availableCategoryMetrics = categoryMetrics.filter((metric) =>
      availableMetrics.has(metric),
    );
    const allSelected = availableCategoryMetrics.every((metric) =>
      selectedMetrics.has(metric),
    );

    const newSelectedMetrics = new Set(selectedMetrics);
    availableCategoryMetrics.forEach((metric) => {
      if (allSelected) {
        newSelectedMetrics.delete(metric);
      } else {
        newSelectedMetrics.add(metric);
      }
    });

    onMetricsChange(newSelectedMetrics);
  };

  if (isCollapsed) {
    return (
      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white shadow">
        <button
          onClick={() => setIsCollapsed(false)}
          className="hover:bg-gray-100 rounded-full p-1 transition-colors"
        >
          <ChevronLastIcon className="text-gray-500 h-5 w-5" />
        </button>
      </div>
    );
  }

  return (
    <div className="flex h-full w-64 flex-col rounded-lg bg-white shadow">
      {/* Header without border */}
      <div className="pl-4 pr-4 pt-4">
        <div className="flex items-center justify-between">
          <div className="text-lg font-medium">Available Metrics</div>
          <button
            onClick={() => setIsCollapsed(true)}
            className="hover:bg-gray-100 rounded-full p-1 transition-colors"
          >
            <ChevronFirstIcon className="text-gray-500 h-5 w-5" />
          </button>
        </div>

        {selectedMetrics.size > 0 && (
          <button
            onClick={() => onMetricsChange(new Set())}
            className="mt-2 text-sm text-blue-600 hover:text-blue-800"
          >
            Clear All Filters
          </button>
        )}
      </div>

      {/* Scrollable Content */}
      <div className="scrollbar-hide flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {(
            Object.entries(ALL_METRICS) as [string, readonly MetricName[]][]
          ).map(([category, metrics]) => {
            const availableCategoryMetrics = metrics.filter((metric) =>
              availableMetrics.has(metric),
            );
            const selectedCategoryCount = metrics.filter((metric) =>
              selectedMetrics.has(metric),
            ).length;
            const isFullySelected =
              availableCategoryMetrics.length > 0 &&
              selectedCategoryCount === availableCategoryMetrics.length;

            return (
              <div key={category}>
                <button
                  onClick={() => handleCategoryToggle(metrics)}
                  disabled={availableCategoryMetrics.length === 0}
                  className={`
                      mb-2 w-full rounded-md px-2 py-1 text-left transition-colors
                      ${
                        availableCategoryMetrics.length === 0
                          ? "cursor-not-allowed text-slate-400"
                          : "cursor-pointer hover:bg-blue-50"
                      }
                      ${isFullySelected ? "bg-blue-50 text-blue-800" : ""}
                    `}
                >
                  <span className="text-sm font-medium">{category}</span>
                </button>
                <div className="space-y-1 pl-4">
                  {metrics.map((metric) => (
                    <button
                      key={metric}
                      onClick={() => handleMetricToggle(metric)}
                      disabled={!availableMetrics.has(metric)}
                      className={`
                          w-full rounded-md px-2 py-1 text-left text-sm transition-colors
                          ${
                            !availableMetrics.has(metric)
                              ? "cursor-not-allowed text-slate-400"
                              : "cursor-pointer hover:bg-blue-50"
                          }
                          ${
                            selectedMetrics.has(metric)
                              ? "bg-blue-50 text-blue-800"
                              : ""
                          }
                        `}
                    >
                      <div className="flex items-center justify-between">
                        <span>{metric.replace(/_/g, " ")}</span>
                        {availableMetrics.has(metric) && (
                          <span className="text-green-500">●</span>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MetricsSidePanel;
