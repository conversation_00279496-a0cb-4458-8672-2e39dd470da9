"use client";

import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { createContext, useContext } from "react";
import { CheckIcon } from "@radix-ui/react-icons";

// Types
type StepperContextValue = {
  activeStep: number;
  setActiveStep: (step: number) => void;
  orientation: "horizontal" | "vertical";
};

type StepItemContextValue = {
  step: number;
  state: StepState;
  isDisabled: boolean;
  isLoading: boolean;
};

type StepState = "active" | "completed" | "inactive" | "loading";

// Contexts
const StepperContext = React.createContext<{
  value: number;
  onChange: (value: number) => void;
}>({
  value: 0,
  onChange: () => {},
});

const StepperItemContext = React.createContext<number>(0);

const useStepper = () => {
  const context = useContext(StepperContext);
  if (!context) {
    throw new Error("useStepper must be used within a Stepper");
  }
  return context;
};

const useStepItem = () => {
  const context = useContext(StepperItemContext);
  if (!context) {
    throw new Error("useStepItem must be used within a StepperItem");
  }
  return context;
};

// Components
interface StepperProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultValue?: number;
  value?: number;
  onValueChange?: (value: number) => void;
  orientation?: "horizontal" | "vertical";
}

const Stepper = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value: number;
    onChange?: (value: number) => void;
  }
>(({ value = 0, onChange, className, children, ...props }, ref) => {
  const handleChange = React.useCallback(
    (value: number) => {
      onChange?.(value);
    },
    [onChange],
  );

  return (
    <StepperContext.Provider value={{ value, onChange: handleChange }}>
      <div
        ref={ref}
        className={cn("flex w-full items-center gap-4", className)}
        {...props}
      >
        {children}
      </div>
    </StepperContext.Provider>
  );
});
Stepper.displayName = "Stepper";

// StepperItem
interface StepperItemProps extends React.HTMLAttributes<HTMLDivElement> {
  step?: number;
  value?: number;
  completed?: boolean;
  disabled?: boolean;
  loading?: boolean;
}

const StepperItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    step?: number;
    value?: number;
    completed?: boolean;
  }
>(({ step, value, completed, className, children, ...props }, ref) => {
  // Use step if provided, otherwise use value (for backward compatibility)
  const stepValue = step !== undefined ? step : value !== undefined ? value : 0;
  const { value: activeValue } = React.useContext(StepperContext);
  const isActive = stepValue === activeValue;
  const isCompleted = completed || stepValue < activeValue;

  return (
    <StepperItemContext.Provider value={stepValue}>
      <div
        ref={ref}
        // className={cn(
        //   "flex items-center",
        //   isActive && "active",
        //   isCompleted && "completed",
        //   className,
        // )}
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
        }}
        data-state={
          isActive ? "active" : isCompleted ? "completed" : "inactive"
        }
        data-active={isActive}
        data-completed={isCompleted}
        {...props}
      >
        {children}
      </div>
    </StepperItemContext.Provider>
  );
});
StepperItem.displayName = "StepperItem";

// StepperTrigger
interface StepperTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  asChild?: boolean;
}

const StepperTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ className, children, ...props }, ref) => {
  const { value, onChange } = React.useContext(StepperContext);
  const step = React.useContext(StepperItemContext);

  return (
    <button
      ref={ref}
      className={cn("flex items-center", className)}
      onClick={() => onChange(step)}
      {...props}
      style={{ width: "100%" }}
    >
      {children}
    </button>
  );
});
StepperTrigger.displayName = "StepperTrigger";

// StepperIndicator
interface StepperIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  asChild?: boolean;
}

const StepperIndicator = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { value } = React.useContext(StepperContext);
  const step = React.useContext(StepperItemContext);
  const isActive = step === value;
  const isCompleted = step < value;

  return (
    <>
      {/* <span
      // ref={ref}
      className={cn(
        "relative flex h-8 w-8 items-center justify-center rounded-full border border-input bg-background text-sm font-medium transition-colors",
        isActive && "border-purple-600 bg-purple-600 text-white",
        isCompleted && "border-purple-600 bg-purple-600 text-white",
        className,
      )}
      {...props}
    >
      {isCompleted ? (
        <CheckIcon className="h-4 w-4" />
      ) : (
        <span>{step + 1}</span>
      )}
    </span> */}

      <div
        ref={ref}
        className={cn(
          "h-[5px] w-full bg-border",
          isActive && "border-purple-600 bg-purple-600",
          className,
        )}
        {...props}
        // style={{flex:1, height:'5px',width:'5rem',backgroundColor:isActive?'purple':'#ccc'}}
      />
    </>
  );
});
StepperIndicator.displayName = "StepperIndicator";

// StepperTitle
const StepperTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3 ref={ref} className={cn("text-sm font-medium", className)} {...props} />
));
StepperTitle.displayName = "StepperTitle";

// StepperDescription
const StepperDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
StepperDescription.displayName = "StepperDescription";

// StepperSeparator
const StepperSeparator = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("h-0.5 w-full bg-border", className)}
      {...props}
    />
  );
});
StepperSeparator.displayName = "StepperSeparator";

export {
  Stepper,
  StepperDescription,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
};
