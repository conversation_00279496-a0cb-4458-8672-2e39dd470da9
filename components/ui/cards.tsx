import { cn } from "@/app/lib/utlis";
import * as React from "react";

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { rounded?: boolean }
>(({ className, rounded = true, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "text-gray-950 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-50 border border-slate-200 bg-white shadow-sm",
      rounded && "rounded-lg",
      className,
    )}
    {...props}
  />
));
Card.displayName = "Card";

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  rounded?: boolean;
}

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, rounded = true, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex flex-col space-y-1.5 p-6",
        rounded && "rounded-t-lg",
        className,
      )}
      {...props}
    />
  ),
);
CardHeader.displayName = "CardHeader";

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  removePadding?: boolean;
}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, removePadding = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(removePadding ? "" : "p-6 pt-0", className)}
      {...props}
    />
  ),
);
CardContent.displayName = "CardContent";

export { Card, CardHeader, CardContent };
