import React from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectPlanGenerationState,
  selectShowPlanSidebar,
  closePlanSidebar,
  PlanGenerationState,
} from "../store/slices/planGenerationSlice";
import { Loader2, CheckCircle, XCircle, X } from "lucide-react"; // Assuming lucide-react
import { ScrollArea } from "@/components/ui/scroll-area"; // Assuming shadcn/ui
import { Button } from "@/components/ui/button"; // Assuming shadcn/ui
import ReactMarkdown from "react-markdown"; // For rendering plan markdown

const PlanGenerationSidebar: React.FC = () => {
  const { status, requiredDomains, domainStatus, finalPlan, error } =
    useSelector(selectPlanGenerationState) as PlanGenerationState;
  const showSidebar = useSelector(selectShowPlanSidebar);
  const dispatch = useDispatch();

  if (!showSidebar) {
    return null;
  }

  const getStatusIcon = (domain: string) => {
    const current = domainStatus[domain];
    if (!current || current.status === "pending")
      return <span className="text-gray-400">⏳</span>;
    if (current.status === "active")
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    if (current.status === "complete")
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (current.status === "error")
      return <XCircle className="h-4 w-4 text-red-500" />;
    return null;
  };

  const handleClose = () => {
    dispatch(closePlanSidebar());
  };

  return (
    <div className="border-gray-300 fixed right-0 top-0 z-40 h-full w-96 translate-x-0 transform border-l bg-white shadow-lg transition-transform">
      <div className="border-gray-300 flex h-14 items-center justify-between border-b px-4">
        <h2 className="text-lg font-semibold">Plan Generation</h2>
        <Button variant="ghost" size="icon" onClick={handleClose}>
          <X className="h-5 w-5" />
        </Button>
      </div>

      <ScrollArea className="h-[calc(100vh-3.5rem)] p-4">
        {status === "initializing" && (
          <div className="text-gray-600 flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Initializing plan...</span>
          </div>
        )}

        {error && (
          <div className="mb-4 rounded border border-red-200 bg-red-50 p-3 text-red-700">
            <p className="font-semibold">Error:</p>
            <p>{error}</p>
          </div>
        )}

        {requiredDomains.length > 0 && (
          <div className="mb-4">
            <h3 className="mb-2 font-medium">Required Coaches:</h3>
            <ul className="space-y-1 text-sm">
              {requiredDomains.map((domain: string) => (
                <li key={domain} className="flex items-center justify-between">
                  <span className="capitalize">
                    {domain.replace("_coach", "")}
                  </span>
                  {getStatusIcon(domain)}
                </li>
              ))}
            </ul>
          </div>
        )}

        {status === "aggregating" && !finalPlan && !error && (
          <div className="text-gray-600 flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Aggregating contributions...</span>
          </div>
        )}

        {finalPlan && (
          <div className="mt-4 space-y-2">
            <h3 className="text-lg font-semibold">Generated Plan:</h3>
            <div className="prose prose-sm bg-gray-50 max-w-none rounded border p-3">
              {/* Use ReactMarkdown or similar to render the plan */}
              <ReactMarkdown>{finalPlan}</ReactMarkdown>
            </div>
          </div>
        )}

        {/* Optional: Display individual contributions */}
        {/* {Object.entries(domainStatus).map(([domain, status]) => (
                    status.status === 'complete' && status.contribution && (
                        <details key={domain} className="mb-2 text-xs">
                            <summary className="cursor-pointer font-medium capitalize">{domain.replace('_coach', '')} Contribution</summary>
                            <pre className="mt-1 whitespace-pre-wrap bg-gray-100 p-2 rounded">{status.contribution}</pre>
                        </details>
                    )
                ))} */}
      </ScrollArea>
    </div>
  );
};

export default PlanGenerationSidebar;
