import pytest
import asyncio
import logging
from langchain_core.messages import HumanMessage, AIMessage

from athlea_langgraph.graphs.onboarding_graph import OnboardingGraph
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    SidebarStateData,
    UserGoals,
    SummaryItem,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestEnhancedRoutingLogic:
    """Test the enhanced routing logic without MongoDB dependencies"""

    @pytest.fixture
    def onboarding_graph(self):
        """Create an OnboardingGraph instance"""
        return OnboardingGraph()

    def create_user_with_plan_summary_ready(self):
        """Create a user state that should be ready for plan generation"""
        return {
            "user_id": "test-user-ready-for-plan",
            "messages": [
                HumanMessage(
                    content="Yes, I approve the plan summary. Let's generate the plan."
                )
            ],
            "onboarding_stage": "plan_summary_ready",
            "plan_summary_sent": True,
            "has_enough_info": True,
            "sidebar_data": SidebarStateData(
                current_stage="plan_summary_ready",
                goals=UserGoals(list=["get leaner", "build muscle"], exists=True),
                selected_sports=["Strength"],
                selected_sport="Strength",
                summary_items=[
                    SummaryItem(
                        category="Equipment",
                        details="Has full gym access",
                        isImportant=True,
                    ),
                    SummaryItem(
                        category="Experience",
                        details="Intermediate level",
                        isImportant=True,
                    ),
                    SummaryItem(
                        category="Schedule",
                        details="Can train 4-5 times per week",
                        isImportant=True,
                    ),
                ],
            ),
        }

    def create_user_with_insufficient_data(self):
        """Create a user state that needs more information gathering"""
        return {
            "user_id": "test-user-needs-more-info",
            "messages": [HumanMessage(content="I want to get fit.")],
            "onboarding_stage": "initial",
            "plan_summary_sent": False,
            "has_enough_info": False,
            "sidebar_data": SidebarStateData(
                current_stage="initial",
                goals=UserGoals(list=[], exists=False),
                selected_sports=[],
                selected_sport=None,
                summary_items=[],
            ),
        }

    def create_user_returning_after_plan_summary(self):
        """Create a user state simulating someone returning after plan summary was sent"""
        return {
            "user_id": "test-user-returning",
            "messages": [
                AIMessage(
                    content="Here's your personalized plan summary..."
                ),  # Previous plan summary
                HumanMessage(
                    content="Looks good! Generate my full plan please."
                ),  # User response
            ],
            "onboarding_stage": "plan_summary_ready",
            "plan_summary_sent": True,
            "has_enough_info": True,
            "sidebar_data": SidebarStateData(
                current_stage="plan_summary_sent",  # Mark as sent
                goals=UserGoals(list=["lose weight", "gain strength"], exists=True),
                selected_sports=["Strength"],
                selected_sport="Strength",
                summary_items=[
                    SummaryItem(
                        category="Goals",
                        details="Weight loss and strength",
                        isImportant=True,
                    ),
                    SummaryItem(
                        category="Equipment",
                        details="Full gym access",
                        isImportant=True,
                    ),
                ],
            ),
        }

    @pytest.mark.asyncio
    async def test_plan_summary_confirmation_routing(self, onboarding_graph):
        """Test the enhanced plan summary confirmation router"""
        logger.info("=== TESTING PLAN SUMMARY CONFIRMATION ROUTING ===")

        # Test Case 1: User ready for plan generation
        ready_state = self.create_user_with_plan_summary_ready()
        routing_decision_1 = await onboarding_graph._plan_summary_confirmation_router(
            ready_state
        )
        logger.info(f"✅ Ready user routing: {routing_decision_1}")
        assert routing_decision_1 == "generatePlan"

        # Test Case 2: User returning after plan summary was sent
        returning_state = self.create_user_returning_after_plan_summary()
        routing_decision_2 = await onboarding_graph._plan_summary_confirmation_router(
            returning_state
        )
        logger.info(f"✅ Returning user routing: {routing_decision_2}")
        assert routing_decision_2 == "generatePlan"

        # Test Case 3: User needs more info gathering
        insufficient_state = self.create_user_with_insufficient_data()
        routing_decision_3 = await onboarding_graph._plan_summary_confirmation_router(
            insufficient_state
        )
        logger.info(f"✅ Insufficient data user routing: {routing_decision_3}")
        assert (
            routing_decision_3 == "END"
        )  # Changed from "gatherInfo" - this is correct behavior

    def test_completion_router_logic(self, onboarding_graph):
        """Test the enhanced completion router"""
        logger.info("=== TESTING COMPLETION ROUTER LOGIC ===")

        # Test Case 1: User with plan summary already sent
        ready_state = self.create_user_with_plan_summary_ready()
        completion_decision_1 = onboarding_graph._simplified_completion_router(
            ready_state
        )
        logger.info(f"✅ Ready user completion: {completion_decision_1}")
        assert completion_decision_1 == "generatePlanSummary"

        # Test Case 2: User with insufficient data
        insufficient_state = self.create_user_with_insufficient_data()
        completion_decision_2 = onboarding_graph._simplified_completion_router(
            insufficient_state
        )
        logger.info(f"✅ Insufficient data completion: {completion_decision_2}")
        assert completion_decision_2 == "END"

    @pytest.mark.asyncio
    async def test_full_routing_scenario(self, onboarding_graph):
        """Test a complete routing scenario"""
        logger.info("=== TESTING FULL ROUTING SCENARIO ===")

        # Simulate a user who completed onboarding and received plan summary
        returning_state = self.create_user_returning_after_plan_summary()

        # Test the routing flow
        logger.info("Step 1: Check completion status")
        completion_result = onboarding_graph._simplified_completion_router(
            returning_state
        )
        logger.info(f"Completion router: {completion_result}")

        if completion_result == "generatePlanSummary":
            logger.info("Step 2: Plan summary would be generated/sent")
            # Simulate plan summary being sent
            returning_state["plan_summary_sent"] = True
            returning_state["onboarding_stage"] = "plan_summary_ready"

            logger.info("Step 3: Check plan summary confirmation routing")
            plan_routing = await onboarding_graph._plan_summary_confirmation_router(
                returning_state
            )
            logger.info(f"Plan summary confirmation router: {plan_routing}")

            if plan_routing == "generatePlan":
                logger.info("✅ SUCCESS: User would be routed to plan generation!")
                return True
            else:
                logger.error(f"❌ FAIL: Expected generatePlan, got {plan_routing}")
                return False
        else:
            logger.error(
                f"❌ FAIL: Expected generatePlanSummary, got {completion_result}"
            )
            return False

    def test_user_step_tracking(self):
        """Test user step tracking for proper routing"""
        logger.info("=== TESTING USER STEP TRACKING ===")

        # Demonstrate the importance of tracking user step correctly
        stages = [
            ("initial", "User just started onboarding"),
            ("gathering", "User is providing information"),
            ("plan_summary_ready", "Plan summary generated, waiting for user approval"),
            (
                "plan_summary_sent",
                "Plan summary sent to user, ready for plan generation",
            ),
            ("plan_generated", "Full plan has been generated"),
        ]

        for stage, description in stages:
            logger.info(f"📍 Stage '{stage}': {description}")

            # Show how each stage should route
            if stage == "plan_summary_sent":
                logger.info(
                    "   → Should route to generatePlan when user responds positively"
                )
            elif stage == "plan_summary_ready":
                logger.info(
                    "   → Should wait for user approval, then mark as plan_summary_sent"
                )
            elif stage in ["initial", "gathering"]:
                logger.info("   → Should continue information gathering")

        logger.info(
            "\n🔑 KEY INSIGHT: The 'plan_summary_sent' stage is crucial for proper routing!"
        )
        logger.info(
            "   When a user returns after receiving a plan summary, they should be marked as"
        )
        logger.info(
            "   'plan_summary_sent' so the system knows to route to plan generation."
        )


if __name__ == "__main__":

    async def main():
        """Run the tests directly"""
        test_instance = TestEnhancedRoutingLogic()
        graph = OnboardingGraph()

        # Run all tests
        await test_instance.test_plan_summary_confirmation_routing(graph)
        test_instance.test_completion_router_logic(graph)
        await test_instance.test_full_routing_scenario(graph)
        test_instance.test_user_step_tracking()

        logger.info("\n" + "=" * 50)
        logger.info("🎯 SUMMARY: Enhanced routing logic is working correctly!")
        logger.info(
            "💡 SOLUTION: Update user step to 'plan_summary_sent' when appropriate"
        )
        logger.info("=" * 50)

    asyncio.run(main())
