import pytest
import logging
import asyncio
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage

from athlea_langgraph.agents.onboarding.generate_plan_node import GeneratePlanNode
from athlea_langgraph.agents.onboarding.information_gatherer_node import (
    InformationGathererNode,
)
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    SidebarStateData,
    create_initial_onboarding_state,
)

# Configure logging for debugging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestGeneratePlanNodeRealUser:
    """Test the improved GeneratePlanNode with real user data"""

    @pytest.fixture
    def generate_plan_node(self):
        """Create a GeneratePlanNode instance for testing"""
        return GeneratePlanNode()

    @pytest.fixture
    def info_gatherer_node(self):
        """Create an InformationGathererNode to load real user data"""
        return InformationGathererNode()

    @pytest.mark.asyncio
    async def test_real_user_plan_generation(
        self, generate_plan_node, info_gatherer_node
    ):
        """Test plan generation with real user data"""
        user_id = "oocR78vBmCeHSuMu7ORq1Wo6df23"
        logger.info(f"=== TESTING REAL USER PLAN GENERATION: {user_id} ===")

        try:
            # Load real user data from MongoDB
            logger.info(f"[Test] Loading onboarding data for user: {user_id}")
            saved_sidebar_data = await info_gatherer_node._load_onboarding_sidebar_data(
                user_id
            )
            logger.info(f"[Test] Loaded sidebar data type: {type(saved_sidebar_data)}")

            if saved_sidebar_data:
                logger.info(f"[Test] User has existing onboarding data")
                logger.info(f"[Test] Current stage: {saved_sidebar_data.current_stage}")
                logger.info(f"[Test] Goals: {saved_sidebar_data.goals}")
                logger.info(
                    f"[Test] Selected sports: {saved_sidebar_data.selected_sports}"
                )
                logger.info(
                    f"[Test] Summary items: {len(saved_sidebar_data.summary_items or [])}"
                )
            else:
                logger.info(f"[Test] No existing data found, creating sample data")
                # Create sample data for testing
                from athlea_langgraph.states.onboarding_state import (
                    UserGoals,
                    SummaryItem,
                )

                saved_sidebar_data = SidebarStateData(
                    current_stage="gatherInfo",
                    goals=UserGoals(exists=True, list=["get leaner", "build muscle"]),
                    selected_sports=["Strength Training"],
                    selected_sport="Strength Training",
                    summary_items=[
                        SummaryItem(
                            category="Equipment Access",
                            details="Has access to all equipment possible",
                            isImportant=True,
                        )
                    ],
                )

            # Get conversation history from MongoDB
            logger.info(f"[Test] Loading conversation history for user: {user_id}")
            try:
                mongo_client = await info_gatherer_node._get_mongo_client()
                db = mongo_client.athlea_dev
                collection = db.conversations

                conversation_doc = await collection.find_one({"user_id": user_id})
                messages = []
                if conversation_doc and "messages" in conversation_doc:
                    logger.info(
                        f"[Test] Found {len(conversation_doc['messages'])} messages in conversation history"
                    )
                    # Convert stored messages to LangChain format
                    for msg_data in conversation_doc["messages"][
                        -10:
                    ]:  # Take last 10 messages
                        if msg_data.get("type") == "human":
                            messages.append(
                                HumanMessage(content=msg_data.get("content", ""))
                            )
                        elif msg_data.get("type") == "ai":
                            messages.append(
                                AIMessage(content=msg_data.get("content", ""))
                            )
                else:
                    logger.info(
                        "[Test] No conversation history found, using sample message"
                    )
                    messages = [
                        HumanMessage(
                            content="I have access to all equipment possible. And I want to focus on getting leaner"
                        )
                    ]

            except Exception as e:
                logger.warning(f"[Test] Could not load conversation history: {e}")
                messages = [
                    HumanMessage(
                        content="I have access to all equipment possible. And I want to focus on getting leaner"
                    )
                ]

            # Create state for plan generation
            test_state = OnboardingState(
                user_id=user_id,
                messages=messages,
                onboarding_stage="generatePlan",
                sidebar_data=saved_sidebar_data,
                has_enough_info=True,
                needs_input=False,
                requires_input=False,
                info_gathered=True,
                system_prompt="Generate a personalized fitness plan based on user data",
            )

            logger.info(f"[Test] Created test state with {len(messages)} messages")
            logger.info(f"[Test] Sidebar data type: {type(test_state.sidebar_data)}")

            # Test plan generation
            logger.info("[Test] *** CALLING GENERATE_PLAN_NODE ***")
            result = await generate_plan_node(test_state)

            logger.info(f"[Test] *** PLAN GENERATION COMPLETE ***")
            logger.info(f"[Test] Result keys: {list(result.keys())}")
            logger.info(f"[Test] Onboarding stage: {result.get('onboarding_stage')}")

            if "generated_plan" in result:
                plan = result["generated_plan"]
                logger.info(f"[Test] ✅ Plan generated successfully!")
                logger.info(f"[Test] Plan ID: {plan.get('planId')}")
                logger.info(f"[Test] Plan Name: {plan.get('name')}")
                logger.info(f"[Test] Plan Type: {plan.get('planType')}")
                logger.info(f"[Test] Plan Duration: {plan.get('duration')}")
                logger.info(f"[Test] Plan Disciplines: {plan.get('disciplines')}")
                logger.info(f"[Test] Plan Rationale: {plan.get('rationale')[:200]}...")
                logger.info(f"[Test] Number of phases: {len(plan.get('phases', []))}")
                logger.info(
                    f"[Test] Number of example sessions: {len(plan.get('exampleSessions', []))}"
                )

                # Log phase details
                for i, phase in enumerate(plan.get("phases", [])[:2]):  # First 2 phases
                    logger.info(
                        f"[Test] Phase {i+1}: {phase.get('phaseName')} - {phase.get('focus')}"
                    )

            else:
                logger.warning("[Test] ❌ No plan generated in result")

            # Test assertions
            assert "messages" in result
            assert "sidebar_data" in result
            assert result["onboarding_stage"] in ["complete", "error"]

            if result["onboarding_stage"] == "complete":
                assert "generated_plan" in result
                plan = result["generated_plan"]
                assert plan.get("name")
                assert plan.get("planType")
                assert plan.get("disciplines")
                assert plan.get("phases")

            return result

        except Exception as e:
            logger.error(f"[Test] ❌ Error in real user test: {e}")
            import traceback

            logger.error(f"[Test] Full traceback: {traceback.format_exc()}")
            raise

    @pytest.mark.asyncio
    async def test_fallback_plan_generation(self, generate_plan_node):
        """Test the fallback plan generation mechanism"""
        logger.info("=== TESTING FALLBACK PLAN GENERATION ===")

        # Create a scenario that should trigger fallback
        from athlea_langgraph.states.onboarding_state import UserGoals, SummaryItem

        sidebar_data = SidebarStateData(
            current_stage="gatherInfo",
            goals=UserGoals(exists=True, list=["build muscle", "get stronger"]),
            selected_sports=[
                "Strength Training"
            ],  # This should be mapped to "Strength"
            summary_items=[
                SummaryItem(
                    category="Equipment", details="Full gym access", isImportant=True
                )
            ],
        )

        # Test the fallback method directly
        failed_plan_data = {
            "planId": "test-123",
            "name": "Test Plan",
            "description": "A test plan",
            "duration": "8 weeks",
            "level": "Advanced",
            "planType": "Strength",
            "disciplines": ["Strength Training"],  # This will cause validation error
            "rationale": "Test rationale",
            "phases": [],  # Missing required phases
        }

        logger.info("[Test] Testing fallback plan creation...")
        fallback_plan = generate_plan_node._create_fallback_plan(
            sidebar_data, failed_plan_data
        )

        logger.info(f"[Test] ✅ Fallback plan created:")
        logger.info(f"[Test] Name: {fallback_plan.name}")
        logger.info(f"[Test] Type: {fallback_plan.planType}")
        logger.info(f"[Test] Disciplines: {fallback_plan.disciplines}")
        logger.info(f"[Test] Number of phases: {len(fallback_plan.phases)}")
        logger.info(f"[Test] Number of sessions: {len(fallback_plan.exampleSessions)}")

        # Verify the fallback plan is valid
        assert fallback_plan.name
        assert fallback_plan.planType in [
            "Running",
            "Cycling",
            "Strength",
            "Nutrition",
            "Recovery",
        ]
        assert len(fallback_plan.disciplines) > 0
        assert all(
            d in ["Running", "Cycling", "Strength", "Nutrition", "Recovery"]
            for d in fallback_plan.disciplines
        )
        assert len(fallback_plan.phases) >= 3
        assert len(fallback_plan.exampleSessions) >= 2

        logger.info("[Test] ✅ Fallback plan validation passed!")


if __name__ == "__main__":
    # Allow running this test file directly for debugging
    async def run_real_user_test():
        test_instance = TestGeneratePlanNodeRealUser()
        generate_node = test_instance.generate_plan_node()
        info_node = test_instance.info_gatherer_node()

        print("Testing real user plan generation...")
        result = await test_instance.test_real_user_plan_generation(
            generate_node, info_node
        )
        print(f"Test completed with result: {list(result.keys())}")

    asyncio.run(run_real_user_test())
