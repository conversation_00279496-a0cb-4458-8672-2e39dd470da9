import asyncio
import logging
from unittest.mock import AsyncMock, patch, MagicMock

from athlea_langgraph.agents.onboarding.generate_plan_summary_node import (
    GeneratePlanSummaryNode,
)
from athlea_langgraph.agents.onboarding.generate_plan_node import GeneratePlanNode
from athlea_langgraph.states.onboarding_state import (
    SidebarStateData,
    UserGoals,
    SummaryItem,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_plan_summary_persistence():
    """Test that generatePlanSummary saves stage correctly"""
    print("Testing plan summary persistence...")

    # Create test node
    node = GeneratePlanSummaryNode()
    node.dev_mode = False
    node.mongodb_uri = "mongodb://test"

    # Create test state
    sidebar_data = SidebarStateData(
        current_stage="gathering",
        goals=UserGoals(exists=True, list=["Build running endurance", "Get stronger"]),
        summary_items=[
            SummaryItem(
                category="Equipment Access",
                details="Has access to full gym and running routes",
                isImportant=True,
            )
        ],
        selected_sports=["Running", "Strength Training"],
        selected_sport="Running",
    )

    test_state = {
        "user_id": "test-user-123",
        "thread_id": "test-thread-456",
        "sidebar_data": sidebar_data.model_dump(),
        "onboarding_stage": "gathering",
    }

    # Mock MongoDB operations
    with patch("pymongo.MongoClient") as mock_mongo_client:
        mock_collection = MagicMock()
        mock_db = MagicMock()
        mock_client = MagicMock()

        mock_client["AthleaUserData"] = mock_db
        mock_db["users"] = mock_collection
        mock_mongo_client.return_value = mock_client

        # Mock the update operation to return success
        mock_result = MagicMock()
        mock_result.modified_count = 1
        mock_result.upserted_id = None
        mock_collection.update_one.return_value = mock_result

        # Mock LLM streaming to avoid API calls
        async def mock_astream(messages):
            chunks = [
                MagicMock(content="## Test Plan Summary\n"),
                MagicMock(content="This is a test plan summary.\n"),
                MagicMock(
                    content="Are you ready for me to generate your detailed training plan?"
                ),
            ]
            for chunk in chunks:
                yield chunk

        node.llm.astream = mock_astream

        # Call the node
        result = await node(test_state)

        # Verify the stage was updated
        assert result["onboarding_stage"] == "plan_summary_ready"
        assert result["plan_summary_sent"] == True

        # Verify MongoDB save methods were called
        assert mock_collection.update_one.called

        # Check that the correct data was saved
        calls = mock_collection.update_one.call_args_list
        assert len(calls) >= 1  # Should have at least one call for sidebar data

        # Verify the sidebar data update call
        sidebar_call = calls[0]
        update_data = sidebar_call[1]["$set"]

        assert "onboarding_sidebar_data" in update_data
        assert (
            update_data["onboarding_sidebar_data"]["current_stage"]
            == "plan_summary_ready"
        )

        print("✅ Plan summary persistence test passed!")


async def test_plan_generation_persistence():
    """Test that generatePlan saves stage correctly"""
    print("Testing plan generation persistence...")

    # Create test node
    node = GeneratePlanNode()
    node.dev_mode = False
    node.mongodb_uri = "mongodb://test"

    # Create test state
    sidebar_data = SidebarStateData(
        current_stage="gathering",
        goals=UserGoals(exists=True, list=["Build running endurance", "Get stronger"]),
        summary_items=[
            SummaryItem(
                category="Equipment Access",
                details="Has access to full gym and running routes",
                isImportant=True,
            )
        ],
        selected_sports=["Running", "Strength Training"],
        selected_sport="Running",
    )

    test_state = {
        "user_id": "test-user-123",
        "thread_id": "test-thread-456",
        "sidebar_data": sidebar_data.model_dump(),
        "onboarding_stage": "gathering",
    }

    # Mock MongoDB operations
    with patch("pymongo.MongoClient") as mock_mongo_client:
        mock_collection = MagicMock()
        mock_db = MagicMock()
        mock_client = MagicMock()

        mock_client["AthleaUserData"] = mock_db
        mock_db["users"] = mock_collection
        mock_mongo_client.return_value = mock_client

        # Mock the update operation to return success
        mock_result = MagicMock()
        mock_result.modified_count = 1
        mock_result.upserted_id = None
        mock_collection.update_one.return_value = mock_result

        # Mock LLM structured output to avoid API calls
        mock_plan_data = {
            "planId": "test-plan-123",
            "name": "Test Training Plan",
            "description": "A test plan for verification",
            "duration": "12 weeks",
            "level": "Intermediate",
            "planType": "Running",
            "disciplines": ["Running"],
            "rationale": "Test rationale",
            "phases": [
                {
                    "phaseName": "Test Phase",
                    "duration": "4 weeks",
                    "weeks": "1-4",
                    "focus": "Test focus",
                    "description": "Test description",
                    "rationale": "Test rationale",
                    "disciplineSpecifics": {},
                }
            ],
            "exampleSessions": [
                {
                    "SessionName": "Test Session",
                    "sessionType": "Running",
                    "Duration": "45 minutes",
                    "SessionDescription": "Test session description",
                }
            ],
        }

        # Mock the structured output LLM
        mock_structured_llm = AsyncMock()
        mock_structured_llm.ainvoke.return_value = mock_plan_data
        node.llm.with_structured_output = MagicMock(return_value=mock_structured_llm)

        # Call the node
        result = await node(test_state)

        # Verify the stage was updated
        assert result["onboarding_stage"] == "complete"
        assert "generated_plan" in result

        # Verify MongoDB save methods were called
        assert mock_collection.update_one.called

        # Check that the correct data was saved
        calls = mock_collection.update_one.call_args_list
        assert len(calls) >= 1  # Should have at least one call for sidebar data

        # Verify the sidebar data update call
        sidebar_call = calls[0]
        update_data = sidebar_call[1]["$set"]

        assert "onboarding_sidebar_data" in update_data
        assert update_data["onboarding_sidebar_data"]["current_stage"] == "complete"

        print("✅ Plan generation persistence test passed!")


async def main():
    print("🧪 Testing Stage Persistence...")

    print("\n1. Testing plan summary persistence...")
    await test_plan_summary_persistence()

    print("\n2. Testing plan generation persistence...")
    await test_plan_generation_persistence()

    print("\n✅ All stage persistence tests passed!")


if __name__ == "__main__":
    asyncio.run(main())
