import pytest
import asyncio
import logging
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage

from athlea_langgraph.agents.onboarding.information_gatherer_node import (
    InformationGathererNode,
)
from athlea_langgraph.graphs.onboarding_graph import OnboardingGraph
from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    SidebarStateData,
    create_initial_onboarding_state,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

USER_ID = "oocR78vBmCeHSuMu7ORq1Wo6df23"


class TestRealUserRouting:
    """Test the enhanced routing logic with real user data"""

    @pytest.fixture
    def onboarding_graph(self):
        """Create an OnboardingGraph instance"""
        return OnboardingGraph()

    @pytest.fixture
    def information_gatherer_node(self):
        """Create an InformationGathererNode instance"""
        return InformationGathererNode()

    @pytest.mark.asyncio
    async def test_load_real_user_data(self, information_gatherer_node):
        """Test loading real user data and see current state"""
        logger.info(f"=== TESTING REAL USER DATA LOAD FOR {USER_ID} ===")

        try:
            # Load the user's saved onboarding data
            saved_data = await information_gatherer_node._load_onboarding_sidebar_data(
                USER_ID
            )

            logger.info(f"✅ Successfully loaded data for user {USER_ID}")
            logger.info(f"Current stage: {saved_data.current_stage}")
            logger.info(
                f"Goals exist: {saved_data.goals.exists if saved_data.goals else False}"
            )
            logger.info(f"Selected sports: {saved_data.selected_sports}")
            logger.info(
                f"Summary items count: {len(saved_data.summary_items) if saved_data.summary_items else 0}"
            )
            logger.info(f"Generated plan exists: {bool(saved_data.generated_plan)}")

            # Check if user should be at plan generation stage
            has_enough_info = (
                saved_data.goals
                and saved_data.goals.exists
                and saved_data.selected_sports
                and len(saved_data.selected_sports) > 0
                and saved_data.summary_items
                and len(saved_data.summary_items) > 2
            )

            logger.info(f"Has enough info for plan generation: {has_enough_info}")

            return saved_data

        except Exception as e:
            logger.error(f"❌ Failed to load data for user {USER_ID}: {e}")
            return None

    @pytest.mark.asyncio
    async def test_routing_logic_with_real_user(self, onboarding_graph):
        """Test the routing logic with real user data"""
        logger.info(f"=== TESTING ROUTING LOGIC FOR {USER_ID} ===")

        # Create initial state with user ID
        initial_state = create_initial_onboarding_state(USER_ID)

        # Add a sample message that might trigger plan generation
        initial_state["messages"] = [
            HumanMessage(content="Yes, let's proceed with the plan.")
        ]

        # Test the plan summary confirmation router
        try:
            routing_decision = await onboarding_graph._plan_summary_confirmation_router(
                initial_state
            )
            logger.info(f"Routing decision: {routing_decision}")

            # Test completion router
            completion_decision = onboarding_graph._simplified_completion_router(
                initial_state
            )
            logger.info(f"Completion router decision: {completion_decision}")

            return routing_decision

        except Exception as e:
            logger.error(f"❌ Routing test failed: {e}")
            return None

    @pytest.mark.asyncio
    async def test_full_graph_execution(self, onboarding_graph):
        """Test full graph execution with real user data"""
        logger.info(f"=== TESTING FULL GRAPH EXECUTION FOR {USER_ID} ===")

        try:
            # Get the compiled graph
            graph = onboarding_graph.create_graph()

            # Create initial state
            initial_state = create_initial_onboarding_state(USER_ID)
            initial_state["messages"] = [
                HumanMessage(content="I'm ready to generate my plan now.")
            ]

            # Invoke the graph
            logger.info("Invoking onboarding graph...")
            result = await graph.ainvoke(initial_state)

            logger.info(f"✅ Graph execution completed")
            logger.info(
                f"Final onboarding stage: {result.get('onboarding_stage', 'unknown')}"
            )
            logger.info(f"Message count: {len(result.get('messages', []))}")

            # Check sidebar data
            sidebar_data = result.get("sidebar_data")
            if sidebar_data:
                logger.info(f"Final sidebar stage: {sidebar_data.current_stage}")
                logger.info(f"Has generated plan: {bool(sidebar_data.generated_plan)}")

                # Check if we should update the user step to "plan_summary_sent"
                if sidebar_data.current_stage == "plan_summary_ready":
                    logger.info(
                        "🔄 User should be marked as 'plan_summary_sent' for proper routing"
                    )

            return result

        except Exception as e:
            logger.error(f"❌ Graph execution failed: {e}")
            return None

    @pytest.mark.asyncio
    async def test_user_step_update(self, information_gatherer_node):
        """Test updating user step to plan_summary_sent"""
        logger.info(f"=== TESTING USER STEP UPDATE FOR {USER_ID} ===")

        try:
            # Load current data
            current_data = (
                await information_gatherer_node._load_onboarding_sidebar_data(USER_ID)
            )

            if current_data:
                # Update the stage to plan_summary_sent
                updated_data = SidebarStateData(
                    current_stage="plan_summary_sent",  # Mark as plan summary sent
                    goals=current_data.goals,
                    summary_items=current_data.summary_items,
                    selected_sports=current_data.selected_sports,
                    selected_sport=current_data.selected_sport,
                    generated_plan=current_data.generated_plan,
                    sport_suggestions=current_data.sport_suggestions,
                    uploaded_documents=current_data.uploaded_documents,
                    key_insights=current_data.key_insights,
                )

                # Save the updated data
                await information_gatherer_node._save_onboarding_sidebar_data(
                    USER_ID, updated_data
                )

                logger.info(f"✅ Updated user {USER_ID} stage to 'plan_summary_sent'")

                # Verify the update
                verified_data = (
                    await information_gatherer_node._load_onboarding_sidebar_data(
                        USER_ID
                    )
                )
                logger.info(f"Verified stage: {verified_data.current_stage}")

                return True
            else:
                logger.error("No existing data found for user")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to update user step: {e}")
            return False


if __name__ == "__main__":

    async def main():
        """Run the tests directly"""
        test_instance = TestRealUserRouting()

        # Test loading real user data
        info_node = InformationGathererNode()
        await test_instance.test_load_real_user_data(info_node)

        # Test routing logic
        graph = OnboardingGraph()
        await test_instance.test_routing_logic_with_real_user(graph)

        # Test user step update
        await test_instance.test_user_step_update(info_node)

        # Test full graph execution
        await test_instance.test_full_graph_execution(graph)

    asyncio.run(main())
