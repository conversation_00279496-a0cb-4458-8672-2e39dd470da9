import asyncio
import logging
from unittest.mock import patch, MagicMock

from athlea_langgraph.agents.onboarding.generate_plan_summary_node import (
    GeneratePlanSummaryNode,
)
from athlea_langgraph.agents.onboarding.generate_plan_node import GeneratePlanNode
from athlea_langgraph.states.onboarding_state import (
    SidebarStateData,
    UserGoals,
    SummaryItem,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_persistence_methods():
    """Test that the MongoDB persistence methods work correctly"""
    print("🧪 Testing MongoDB Persistence Methods...")

    # Test GeneratePlanSummaryNode persistence
    print("\n1. Testing GeneratePlanSummaryNode persistence methods...")
    summary_node = GeneratePlanSummaryNode()
    summary_node.dev_mode = False
    summary_node.mongodb_uri = "mongodb://test"

    # Test GeneratePlanNode persistence
    print("2. Testing GeneratePlanNode persistence methods...")
    plan_node = GeneratePlanNode()
    plan_node.dev_mode = False
    plan_node.mongodb_uri = "mongodb://test"

    # Create test sidebar data
    sidebar_data = SidebarStateData(
        current_stage="plan_summary_ready",
        goals=UserGoals(exists=True, list=["Build running endurance", "Get stronger"]),
        summary_items=[
            SummaryItem(
                category="Equipment Access",
                details="Has access to full gym and running routes",
                isImportant=True,
            )
        ],
        selected_sports=["Running", "Strength Training"],
        selected_sport="Running",
    )

    # Mock MongoDB operations
    with patch("pymongo.MongoClient") as mock_mongo_client:
        mock_collection = MagicMock()
        mock_db = MagicMock()
        mock_client = MagicMock()

        mock_client["AthleaUserData"] = mock_db
        mock_db["users"] = mock_collection
        mock_mongo_client.return_value = mock_client

        # Mock the update operation to return success
        mock_result = MagicMock()
        mock_result.modified_count = 1
        mock_result.upserted_id = None
        mock_collection.update_one.return_value = mock_result

        # Test GeneratePlanSummaryNode persistence methods
        print("   Testing _save_onboarding_sidebar_data...")
        await summary_node._save_onboarding_sidebar_data(
            "test-user-123", sidebar_data, "test-thread-456"
        )

        print("   Testing _save_onboarding_stage...")
        await summary_node._save_onboarding_stage(
            "test-user-123", "plan_summary_ready", "test-thread-456"
        )

        # Test GeneratePlanNode persistence methods
        print("   Testing GeneratePlanNode _save_onboarding_sidebar_data...")

        # Update sidebar data for plan completion
        completed_sidebar_data = SidebarStateData(
            current_stage="complete",
            goals=sidebar_data.goals,
            summary_items=sidebar_data.summary_items,
            selected_sports=sidebar_data.selected_sports,
            selected_sport=sidebar_data.selected_sport,
        )

        await plan_node._save_onboarding_sidebar_data(
            "test-user-123", completed_sidebar_data, "test-thread-456"
        )

        print("   Testing GeneratePlanNode _save_onboarding_stage...")
        await plan_node._save_onboarding_stage(
            "test-user-123", "complete", "test-thread-456"
        )

        # Verify MongoDB operations were called
        assert mock_collection.update_one.called
        calls = mock_collection.update_one.call_args_list
        assert len(calls) >= 4  # Should have at least 4 calls

        # Verify the calls contain the correct data
        for call in calls:
            update_data = call[1]["$set"]
            # Each call should have either sidebar data or stage data
            has_sidebar_data = "onboarding_sidebar_data" in update_data
            has_stage_data = "onboarding_stage" in update_data
            assert (
                has_sidebar_data or has_stage_data
            ), f"Call missing expected data: {update_data.keys()}"

        print("✅ All persistence methods working correctly!")

        # Print summary of what was saved
        print("\n📊 Summary of MongoDB operations:")
        for i, call in enumerate(calls):
            update_data = call[1]["$set"]
            if "onboarding_sidebar_data" in update_data:
                stage = update_data["onboarding_sidebar_data"]["current_stage"]
                print(f"   Call {i+1}: Saved sidebar_data with stage '{stage}'")
            if "onboarding_stage" in update_data:
                stage = update_data["onboarding_stage"]
                print(f"   Call {i+1}: Saved onboarding_stage '{stage}'")


if __name__ == "__main__":
    asyncio.run(test_persistence_methods())
