name: Sync Issue to Azure Boards Work Item

on:
  issues:
    types: [opened, edited, closed, reopened]

jobs:
  sync-issue:
    runs-on: ubuntu-latest
    steps:
      - name: Sync Issue to Azure Boards
        uses: danhellem/github-actions-issue-to-work-item@master
        env:
          ADO_PERSONAL_ACCESS_TOKEN: "${{ secrets.ADO_PERSONAL_ACCESS_TOKEN }}"
          GH_PERSONAL_ACCESS_TOKEN: "${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}"
          ado_organization: "https://dev.azure.com/athlea-ai"
          ado_project: "Athlea"
          # Modify the area path and iteration path as needed for your project
          ado_area_path: "Athlea"
          ado_iteration_path: "Athlea\\MVP"
          ado_wit: "User Story"
          ado_new_state: "New"
          ado_active_state: "Active"
          ado_close_state: "Closed"
          ado_bypassrules: "true"
