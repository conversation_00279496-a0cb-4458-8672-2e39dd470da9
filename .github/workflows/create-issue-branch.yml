name: Create Issue Branch

on:
  issues:
    types: [opened]

permissions:
  contents: write
  issues: write

jobs:
  create_issue_branch:
    runs-on: ubuntu-latest
    steps:
      - name: Create Issue Branch
        uses: robvanderleek/create-issue-branch@v1.9.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          branch-name: "issue/${{ github.event.issue.number }}-${{ github.event.issue.title }}"
