"""
FastAPI application for streaming memory-enhanced coaching responses.

This module provides SSE endpoints that are compatible with the Next.js
frontend chat interface, including proper error handling and CORS support.
"""

import asyncio
import json
import logging
import os
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

from fastapi import Body, FastAPI, HTTPException, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.types import Command
from pydantic import BaseModel, Field

from athlea_langgraph.states.onboarding_state import (
    OnboardingState,
    create_initial_onboarding_state,
)

from athlea_langgraph.api.streaming import (
    StreamingEventType,
    create_coaching_streamer,
    stream_coaching_response,
    stream_specialized_coach,
    ComprehensiveCoachingStreamer,
)

from athlea_langgraph.graph_factory import GraphType, get_graph_by_type

# Import the new advanced caching system
from athlea_langgraph.utils.advanced_user_data_cache import (
    fetch_user_data_ultra_fast,
    get_advanced_cache,
    CacheConfig,
    CacheStrategy,
    AdvancedUserDataCache,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic here
    logger.info("Initializing coaching agents...")

    # Check MongoDB configuration
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        logger.error("MONGODB_URI environment variable not set")
        # This will prevent the app from starting if MONGODB_URI is missing
        raise ValueError("MONGODB_URI environment variable is required for startup")
    logger.info("MONGODB_URI configured, proceeding with startup.")

    # Auto-sync prompts to LangSmith on startup (can be disabled with DISABLE_PROMPT_SYNC=true)
    if os.getenv("DISABLE_PROMPT_SYNC", "false").lower() != "true":
        try:
            from athlea_langgraph.services.langsmith_prompt_sync import (
                auto_sync_prompts_on_startup,
                start_prompt_watcher,
            )

            await auto_sync_prompts_on_startup()

            # Start background prompt watcher for development
            if (
                os.getenv("LANGGRAPH_DEV") == "true"
                or os.getenv("ENVIRONMENT") == "development"
            ):
                await start_prompt_watcher(watch_interval=300)  # Check every 5 minutes
                logger.info("🔄 Prompt watcher started for development mode")

        except Exception as e:
            logger.warning(f"📝 Prompt sync initialization failed: {e}")
    else:
        logger.info(
            "📝 Prompt sync disabled via DISABLE_PROMPT_SYNC environment variable"
        )

    yield


app = FastAPI(
    title="Athlea Coaching API",
    description="Memory-enhanced coaching agents with streaming support",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class CoachingRequest(BaseModel):
    """Request model for coaching endpoints."""

    message: str
    threadId: Optional[str] = None
    userId: str
    userProfile: Optional[Dict[str, Any]] = None


class SpecializedCoachRequest(BaseModel):
    """Request model for specialized coach endpoints."""

    message: str
    coachType: str
    threadId: Optional[str] = None
    userId: str


class OnboardingRequest(BaseModel):
    """Request model for onboarding sessions."""

    message: str
    user_id: str
    thread_id: Optional[str] = None
    user_profile: Optional[Dict[str, Any]] = None
    conversation_history: Optional[List[Union[Dict[str, Any], BaseMessage]]] = Field(
        default_factory=list,
        description="Previous conversation messages - can be dicts with 'role' and 'content' or BaseMessage objects",
    )
    resume_from_interrupt: Optional[bool] = False


async def handle_streaming_errors(generator):
    """Wrapper to handle errors in streaming generators."""
    try:
        async for chunk in generator:
            yield chunk
    except Exception as e:
        logger.error(f"Streaming error: {e}")
        error_event = f'event: {StreamingEventType.ERROR}\ndata: {{"message": "An error occurred during processing. Please try again."}}\n\n'
        yield error_event


@app.get("/api/coaching")
async def stream_coaching_get(
    message: str = Query(..., description="User message"),
    threadId: str = Query(..., description="Thread ID"),
    userId: str = Query(..., description="User ID"),
    singleCoach: Optional[str] = Query(None, description="Specific coach type"),
    userInput: Optional[str] = Query(
        None, description="Resume input for continuing interrupted sessions"
    ),
):
    """
    GET endpoint for streaming coaching responses (Next.js compatibility).

    This endpoint matches the frontend's expectation for GET requests with query parameters.
    """
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        # Use ultra-fast caching for user data retrieval
        logger.info(f"🚀 Ultra-fast fetch: userId={userId}")
        user_data = await fetch_user_data_ultra_fast(userId, mongodb_uri)

        # Log cache performance
        cache_source = user_data.get("cache_source", "unknown")
        logger.info(f"⚡ Data source for userId {userId}: {cache_source}")

        # Log user data retrieval status
        if user_data.get("training_profile"):
            logger.info(
                f"✅ Successfully fetched training profile for userId: {userId}"
            )
        else:
            logger.warning(f"⚠️  No training profile found for userId: {userId}")

        if user_data.get("current_plan"):
            logger.info(f"✅ Successfully fetched current plan for userId: {userId}")
        else:
            logger.warning(f"⚠️  No current plan found for userId: {userId}")

        if singleCoach:
            # Route to specific coach with user data
            generator = stream_specialized_coach(
                user_message=message,
                coach_type=singleCoach,
                thread_id=threadId,
                user_id=userId,
                mongodb_uri=mongodb_uri,
                user_data=user_data,  # Pass ultra-fast cached data
                user_input=userInput,  # Pass resume input if available
            )
        else:
            # General coaching with user data
            generator = stream_coaching_response(
                user_message=message,
                thread_id=threadId,
                user_id=userId,
                mongodb_uri=mongodb_uri,
                user_data=user_data,  # Pass ultra-fast cached data
                user_input=userInput,  # Pass resume input if available
            )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
                "X-Cache-Source": cache_source,  # Add cache source to headers
            },
        )

    except Exception as e:
        logger.error(f"Error in coaching endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/coaching")
async def stream_coaching_post(request: CoachingRequest):
    """
    POST endpoint for streaming coaching responses.

    Alternative endpoint that accepts JSON payloads.
    """
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    thread_id = request.threadId or f"thread_{request.userId}_{hash(request.message)}"

    try:
        # Use ultra-fast caching for user data retrieval
        logger.info(f"🚀 Ultra-fast fetch: userId={request.userId}")
        user_data = await fetch_user_data_ultra_fast(request.userId, mongodb_uri)

        # Log cache performance
        cache_source = user_data.get("cache_source", "unknown")
        logger.info(f"⚡ Data source for userId {request.userId}: {cache_source}")

        # Log user data retrieval status
        if user_data.get("training_profile"):
            logger.info(
                f"✅ Successfully fetched training profile for userId: {request.userId}"
            )
        else:
            logger.warning(f"⚠️  No training profile found for userId: {request.userId}")

        if user_data.get("current_plan"):
            logger.info(
                f"✅ Successfully fetched current plan for userId: {request.userId}"
            )
        else:
            logger.warning(f"⚠️  No current plan found for userId: {request.userId}")

        generator = stream_coaching_response(
            user_message=request.message,
            thread_id=thread_id,
            user_id=request.userId,
            mongodb_uri=mongodb_uri,
            user_data=user_data,  # Pass ultra-fast cached data
        )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
                "X-Cache-Source": cache_source,  # Add cache source to headers
            },
        )

    except Exception as e:
        logger.error(f"Error in coaching POST endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/coaching/specialized")
async def stream_specialized_coaching(
    message: str = Query(..., description="User message"),
    coachType: str = Query(..., description="Coach type"),
    threadId: str = Query(..., description="Thread ID"),
    userId: str = Query(..., description="User ID"),
):
    """
    GET endpoint for specialized coaching with specific coach types.
    """
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        # Use ultra-fast caching for user data retrieval
        logger.info(
            f"🚀 Ultra-fast fetch for specialized coaching: userId={userId}, coachType={coachType}"
        )
        user_data = await fetch_user_data_ultra_fast(userId, mongodb_uri)

        # Log cache performance
        cache_source = user_data.get("cache_source", "unknown")
        logger.info(
            f"⚡ Data source for specialized coach ({coachType}) - userId {userId}: {cache_source}"
        )

        # Log user data retrieval status
        if user_data.get("training_profile"):
            logger.info(
                f"✅ Successfully fetched training profile for userId: {userId}"
            )
        else:
            logger.warning(f"⚠️  No training profile found for userId: {userId}")

        if user_data.get("current_plan"):
            logger.info(f"✅ Successfully fetched current plan for userId: {userId}")
        else:
            logger.warning(f"⚠️  No current plan found for userId: {userId}")

        generator = stream_specialized_coach(
            user_message=message,
            coach_type=coachType,
            thread_id=threadId,
            user_id=userId,
            mongodb_uri=mongodb_uri,
            user_data=user_data,  # Pass ultra-fast cached data
        )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
                "X-Cache-Source": cache_source,  # Add cache source to headers
            },
        )

    except Exception as e:
        logger.error(f"Error in specialized coaching endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/coaching/specialized")
async def stream_specialized_coaching_post(request: SpecializedCoachRequest):
    """
    POST endpoint for specialized coaching.
    """
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    thread_id = request.threadId or f"thread_{request.userId}_{hash(request.message)}"

    try:
        # Use ultra-fast caching for user data retrieval
        logger.info(
            f"🚀 Ultra-fast fetch for specialized coaching POST: userId={request.userId}, coachType={request.coachType}"
        )
        user_data = await fetch_user_data_ultra_fast(request.userId, mongodb_uri)

        # Log cache performance
        cache_source = user_data.get("cache_source", "unknown")
        logger.info(
            f"⚡ Data source for specialized coach ({request.coachType}) - userId {request.userId}: {cache_source}"
        )

        # Log user data retrieval status
        if user_data.get("training_profile"):
            logger.info(
                f"✅ Successfully fetched training profile for userId: {request.userId}"
            )
        else:
            logger.warning(f"⚠️  No training profile found for userId: {request.userId}")

        if user_data.get("current_plan"):
            logger.info(
                f"✅ Successfully fetched current plan for userId: {request.userId}"
            )
        else:
            logger.warning(f"⚠️  No current plan found for userId: {request.userId}")

        generator = stream_specialized_coach(
            user_message=request.message,
            coach_type=request.coachType,
            thread_id=thread_id,
            user_id=request.userId,
            mongodb_uri=mongodb_uri,
            user_data=user_data,  # Pass ultra-fast cached data
        )

        return StreamingResponse(
            handle_streaming_errors(generator),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
                "X-Cache-Source": cache_source,  # Add cache source to headers
            },
        )

    except Exception as e:
        logger.error(f"Error in specialized coaching POST endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/health")
async def health_check():
    """Health check endpoint with cache system status."""
    mongodb_uri = os.getenv("MONGODB_URI")

    # Basic health info
    basic_health = {
        "status": "healthy",
        "mongodb_configured": bool(mongodb_uri),
        "timestamp": datetime.now().isoformat(),
    }

    # Enhanced health check with cache system
    if mongodb_uri:
        try:
            cache = get_advanced_cache(mongodb_uri)
            cache_health = await cache.health_check()
            basic_health["cache_system"] = cache_health
        except Exception as e:
            basic_health["cache_system"] = {"error": str(e), "status": "unhealthy"}

    return basic_health


@app.get("/api/cache/stats")
async def get_cache_statistics():
    """Get comprehensive cache performance statistics."""
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        cache = get_advanced_cache(mongodb_uri)
        stats = await cache.get_cache_stats()
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "statistics": stats,
        }
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving cache stats: {str(e)}"
        )


@app.post("/api/cache/warm")
async def warm_cache_endpoint(
    user_ids: List[str] = Body(..., description="List of user IDs to warm cache for")
):
    """Warm cache for multiple users."""
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    if not user_ids:
        raise HTTPException(status_code=400, detail="user_ids list cannot be empty")

    if len(user_ids) > 100:  # Limit batch size
        raise HTTPException(status_code=400, detail="Maximum 100 user IDs per request")

    try:
        cache = get_advanced_cache(mongodb_uri)
        results = await cache.warm_cache_for_users(user_ids)

        successful = sum(results.values())
        total = len(user_ids)

        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "results": results,
            "summary": {
                "total_users": total,
                "successful": successful,
                "failed": total - successful,
                "success_rate": successful / total if total > 0 else 0,
            },
        }
    except Exception as e:
        logger.error(f"Error warming cache: {e}")
        raise HTTPException(status_code=500, detail=f"Error warming cache: {str(e)}")


@app.delete("/api/cache/user/{user_id}")
async def invalidate_user_cache_endpoint(user_id: str):
    """Invalidate all cached data for a specific user."""
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        cache = get_advanced_cache(mongodb_uri)
        success = await cache.invalidate_user_cache(user_id)

        if success:
            return {
                "success": True,
                "message": f"Cache invalidated for user {user_id}",
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to invalidate cache")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error invalidating cache for user {user_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error invalidating cache: {str(e)}"
        )


@app.get("/api/cache/health")
async def cache_health_check():
    """Detailed health check specifically for the caching system."""
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        cache = get_advanced_cache(mongodb_uri)
        health_status = await cache.health_check()

        # Determine HTTP status code based on health
        if health_status["overall"] == "healthy":
            status_code = 200
        elif health_status["overall"] == "degraded":
            status_code = 200  # Still OK, but degraded
        else:
            status_code = 503  # Service unavailable

        return {"timestamp": datetime.now().isoformat(), **health_status}

    except Exception as e:
        logger.error(f"Error checking cache health: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "overall": "unhealthy",
            "error": str(e),
        }


@app.post("/api/cache/test")
async def test_cache_performance(
    user_id: str = Body(..., description="User ID to test caching performance"),
    iterations: int = Body(10, description="Number of test iterations", ge=1, le=100),
):
    """Test cache performance for a specific user."""
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        cache = get_advanced_cache(mongodb_uri)

        # Run performance test
        response_times = []
        cache_sources = []

        for i in range(iterations):
            start_time = time.time()
            user_data = await fetch_user_data_ultra_fast(user_id, mongodb_uri)
            elapsed = (time.time() - start_time) * 1000  # Convert to milliseconds

            response_times.append(elapsed)
            cache_sources.append(user_data.get("cache_source", "unknown"))

        # Calculate statistics
        avg_response_time = sum(response_times) / len(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)

        # Count cache sources
        from collections import Counter

        source_counts = Counter(cache_sources)

        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "test_config": {"user_id": user_id, "iterations": iterations},
            "performance": {
                "avg_response_time_ms": round(avg_response_time, 2),
                "min_response_time_ms": round(min_response_time, 2),
                "max_response_time_ms": round(max_response_time, 2),
                "all_response_times_ms": [round(t, 2) for t in response_times],
            },
            "cache_behavior": {
                "source_distribution": dict(source_counts),
                "cache_efficiency": source_counts.get("advanced_redis_cache", 0)
                / iterations,
            },
        }

    except Exception as e:
        logger.error(f"Error testing cache performance: {e}")
        raise HTTPException(status_code=500, detail=f"Error testing cache: {str(e)}")


@app.get("/api/coaches")
async def get_available_coaches():
    """Get list of available specialized coaches."""
    return {
        "coaches": [
            {
                "id": "strength",
                "name": "Strength Coach",
                "description": "Strength training and muscle building",
            },
            {
                "id": "running",
                "name": "Running Coach",
                "description": "Running technique and endurance",
            },
            {
                "id": "cardio",
                "name": "Cardio Coach",
                "description": "Cardiovascular fitness and conditioning",
            },
            {
                "id": "cycling",
                "name": "Cycling Coach",
                "description": "Cycling performance and technique",
            },
            {
                "id": "nutrition",
                "name": "Nutrition Coach",
                "description": "Diet and nutrition guidance",
            },
            {
                "id": "recovery",
                "name": "Recovery Coach",
                "description": "Recovery and rehabilitation",
            },
            {
                "id": "mental",
                "name": "Mental Coach",
                "description": "Mental training and mindset",
            },
        ]
    }


@app.post("/api/domain-coach")
async def domain_coach_endpoint(request: Request):
    """
    Simple domain coach endpoint that matches the Next.js frontend expectations.

    Expected format:
    - data: {content}
    - data: [DONE]
    """
    try:
        body = await request.json()
        messages = body.get("messages", [])

        if not messages:
            raise HTTPException(status_code=400, detail="No messages provided")

        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            raise HTTPException(status_code=400, detail="No user message found")

        # Generate thread ID
        thread_id = f"simple_coach_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        user_id = "simple_coach_user"  # Default user for simple coach

        async def generate_simple_stream():
            """Generate simple streaming response compatible with frontend."""
            try:
                # Stream the coaching response
                async for event in stream_coaching_response(
                    user_message=user_message,
                    thread_id=thread_id,
                    user_id=user_id,
                    mongodb_uri=os.getenv("MONGODB_URI"),
                ):
                    # Parse SSE event to extract content
                    if "data:" in event:
                        lines = event.strip().split("\n")
                        for line in lines:
                            if line.startswith("data:"):
                                data_str = line.split(":", 1)[1].strip()
                                try:
                                    data = json.loads(data_str)
                                    # Extract content from various event types
                                    content = ""
                                    if isinstance(data, dict):
                                        if "content" in data:
                                            content = data["content"]
                                        elif "token" in data:
                                            content = data["token"]
                                        elif (
                                            "message" in data
                                            and isinstance(data["message"], dict)
                                            and "content" in data["message"]
                                        ):
                                            content = data["message"]["content"]
                                    elif isinstance(data, str):
                                        content = data

                                    if content:
                                        yield f"data: {content}\n\n"

                                except json.JSONDecodeError:
                                    # If it's not JSON, treat as plain text
                                    if data_str and data_str != "":
                                        yield f"data: {data_str}\n\n"

                    # Check for completion
                    if (
                        StreamingEventType.COMPLETE in event
                        or StreamingEventType.ERROR in event
                    ):
                        break

                # Send completion signal
                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error(f"Error in simple streaming: {e}")
                yield f"data: Sorry, there was an error processing your request.\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            generate_simple_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        logger.error(f"Error in domain coach endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def _merge_sidebar_data(
    existing_data: Dict[str, Any], new_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Intelligently merge existing sidebar data with new data, preserving the most complete information.
    """
    merged = {}

    # Always prefer existing data for these fields if they're more complete
    preserve_if_existing = [
        "summary_items",
        "uploaded_documents",
        "key_insights",
        "selected_sports",
        "goals",
        "sport_suggestions",
    ]

    for field in preserve_if_existing:
        existing_value = existing_data.get(field)
        new_value = new_data.get(field)

        # Use existing if it has more content
        if existing_value and new_value:
            if isinstance(existing_value, list) and isinstance(new_value, list):
                merged[field] = (
                    existing_value
                    if len(existing_value) >= len(new_value)
                    else new_value
                )
            elif isinstance(existing_value, dict) and isinstance(new_value, dict):
                merged[field] = (
                    existing_value
                    if len(existing_value) >= len(new_value)
                    else new_value
                )
            else:
                merged[field] = existing_value  # Prefer existing for other types
        elif existing_value:
            merged[field] = existing_value
        elif new_value:
            merged[field] = new_value

    # For other fields, prefer new data (like current_stage, generated_plan)
    for field, value in new_data.items():
        if field not in preserve_if_existing:
            merged[field] = value

    # Add any remaining fields from existing data
    for field, value in existing_data.items():
        if field not in merged:
            merged[field] = value

    logger.info(
        f"[MERGE] Merged sidebar data - preserved {len(preserve_if_existing)} key fields"
    )
    return merged


async def stream_onboarding_response(
    request: OnboardingRequest,
) -> AsyncGenerator[str, None]:
    """
    Stream onboarding conversation responses with real-time updates.

    This function handles the onboarding flow using LangGraph, including:
    - Message processing and conversation management
    - Real-time token streaming
    - Sidebar data updates as user information is gathered
    - State persistence across conversation turns
    """
    import json  # Import json at the function level to avoid scope issues
    import time
    import asyncio

    try:
        # Use ultra-fast caching for user data if available
        mongodb_uri = os.getenv("MONGODB_URI")
        user_data = None

        if mongodb_uri:
            try:
                logger.info(
                    f"🚀 Ultra-fast fetch for onboarding: userId={request.user_id}"
                )
                user_data = await fetch_user_data_ultra_fast(
                    request.user_id, mongodb_uri
                )

                # Log cache performance
                cache_source = user_data.get("cache_source", "unknown")
                logger.info(
                    f"⚡ Data source for onboarding - userId {request.user_id}: {cache_source}"
                )

                # Log user data retrieval status
                if user_data.get("training_profile"):
                    logger.info(
                        f"✅ Successfully fetched training profile for onboarding - userId: {request.user_id}"
                    )
                else:
                    logger.info(
                        f"ℹ️  No training profile found for onboarding - userId: {request.user_id}"
                    )

                if user_data.get("current_plan"):
                    logger.info(
                        f"✅ Successfully fetched current plan for onboarding - userId: {request.user_id}"
                    )
                else:
                    logger.info(
                        f"ℹ️  No current plan found for onboarding - userId: {request.user_id}"
                    )

            except Exception as e:
                logger.warning(
                    f"⚠️  Error fetching user data for onboarding - userId: {request.user_id}: {e}"
                )
                user_data = None
        else:
            logger.warning("⚠️  MONGODB_URI not set, proceeding without user data fetch")

        # --- Load existing onboarding data from MongoDB for resume functionality ---
        onboarding_data = None
        if request.user_id:
            try:
                # Load onboarding-specific data from MongoDB
                from pymongo import MongoClient
                import asyncio

                def _fetch_onboarding_data():
                    mongo_client = MongoClient(mongodb_uri)
                    db = mongo_client["AthleaUserData"]
                    users_collection = db["users"]

                    user_doc = users_collection.find_one(
                        {"user_id": request.user_id},
                        {
                            "onboarding_stage": 1,
                            "onboarding_sidebar_data": 1,
                            "onboarding_thread_id": 1,
                            "_id": 0,
                        },
                    )

                    mongo_client.close()
                    return user_doc

                loop = asyncio.get_event_loop()
                user_doc = await loop.run_in_executor(None, _fetch_onboarding_data)

                if user_doc:
                    onboarding_data = {
                        "onboarding_stage": user_doc.get("onboarding_stage"),
                        "sidebar_data": user_doc.get("onboarding_sidebar_data"),
                        "thread_id": user_doc.get("onboarding_thread_id"),
                    }

                    logger.info(
                        f"✅ ONBOARDING: [MONGODB] Loaded onboarding data for user {request.user_id}: "
                        f"stage={onboarding_data.get('onboarding_stage')}, "
                        f"has_sidebar_data={bool(onboarding_data.get('sidebar_data'))}"
                    )

                    # Log more details about the sidebar data
                    if onboarding_data.get("sidebar_data"):
                        sidebar = onboarding_data["sidebar_data"]
                        logger.info(
                            f"📊 ONBOARDING: [SIDEBAR_DATA] current_stage={sidebar.get('current_stage')}, "
                            f"goals_count={len(sidebar.get('goals', {}).get('list', []))}, "
                            f"sports_count={len(sidebar.get('selected_sports', []))}, "
                            f"summary_items_count={len(sidebar.get('summary_items', []))}"
                        )
                else:
                    logger.info(
                        f"ℹ️ ONBOARDING: [MONGODB] No onboarding data found for user {request.user_id}"
                    )

            except Exception as e:
                logger.warning(
                    f"⚠️ ONBOARDING: [MONGODB] Error loading onboarding data: {e}"
                )
                onboarding_data = None

        # Get onboarding graph
        graph = get_graph_by_type(GraphType.ONBOARDING, checkpointer_type="memory")

        # Prepare configuration
        thread_id = request.thread_id or f"onboarding_{request.user_id}"
        config = {"configurable": {"thread_id": thread_id}}

        logger.info(f"🚀 ONBOARDING: [STREAM_START] Starting onboarding stream")
        logger.info(
            f"📊 ONBOARDING: [STREAM_START] Request details: user_id={request.user_id}, thread_id={thread_id}, message_length={len(request.message)}"
        )

        # Add timeout for the entire streaming operation
        timeout_seconds = 120  # 2 minutes timeout
        start_time = time.time()

        # Send initial heartbeat
        yield f"event: heartbeat\ndata: {json.dumps({'status': 'starting'})}\n\n"

        # Handle resume from interrupt or new conversation
        if request.resume_from_interrupt:
            logger.info(
                "🔄 ONBOARDING: [RESUME] Resuming from interrupt with user input"
            )

            # Get existing state to preserve sidebar data
            try:
                snapshot = await graph.aget_state(config)
                existing_sidebar_data = None
                if snapshot.values and "sidebar_data" in snapshot.values:
                    existing_sidebar_data = snapshot.values["sidebar_data"]
                    logger.info(
                        f"[RESUME] Found existing sidebar data: {bool(existing_sidebar_data)}"
                    )
                    if existing_sidebar_data:
                        # Log what data we're preserving
                        if hasattr(existing_sidebar_data, "model_dump"):
                            sidebar_dict = existing_sidebar_data.model_dump()
                        elif hasattr(existing_sidebar_data, "dict"):
                            sidebar_dict = existing_sidebar_data.dict()
                        else:
                            sidebar_dict = existing_sidebar_data
                        logger.info(
                            f"[RESUME] Preserving: {len(sidebar_dict.get('summary_items', []))} summary items, {len(sidebar_dict.get('selected_sports', []))} sports, {len(sidebar_dict.get('goals', {}).get('list', []))} goals"
                        )
            except Exception as e:
                logger.warning(f"[RESUME] Error getting existing state: {e}")
                existing_sidebar_data = None

            # Send agent start for Athlea
            yield f"data: {json.dumps({'type': 'agent_start', 'agent': 'Athlea'})}\n\n"

            # Resume from interrupt with user input
            input_state = Command(resume=HumanMessage(content=request.message))
        else:
            # Create input state for new or continuing conversation
            input_state = {
                "messages": [HumanMessage(content=request.message)],
                "user_id": request.user_id,
                "user_input": request.message,
                "conversation_history": request.conversation_history
                or [],  # Add conversation history
            }

            # Add loaded onboarding data to state if available
            if onboarding_data:
                main_stage = onboarding_data.get("onboarding_stage")
                sidebar_data = onboarding_data.get("sidebar_data")
                sidebar_stage = (
                    sidebar_data.get("current_stage") if sidebar_data else None
                )

                # CRITICAL: Synchronize stage fields to prevent infinite loops
                # If stages are mismatched, prefer the sidebar stage (it's more up-to-date)
                if main_stage and sidebar_stage and main_stage != sidebar_stage:
                    logger.warning(
                        f"🚨 ONBOARDING: [STAGE_MISMATCH] Main stage: '{main_stage}' != Sidebar stage: '{sidebar_stage}'"
                    )
                    logger.info(
                        f"🔧 ONBOARDING: [STAGE_SYNC] Using sidebar stage: '{sidebar_stage}'"
                    )
                    # Use sidebar stage as source of truth
                    input_state["onboarding_stage"] = sidebar_stage
                    input_state["plan_summary_sent"] = (
                        sidebar_stage == "plan_summary_ready"
                    )
                elif main_stage:
                    input_state["onboarding_stage"] = main_stage
                    input_state["plan_summary_sent"] = (
                        main_stage == "plan_summary_ready"
                    )

                logger.info(
                    f"🔧 ONBOARDING: [INPUT_STATE] Set onboarding_stage={input_state.get('onboarding_stage')}, "
                    f"plan_summary_sent={input_state.get('plan_summary_sent')}"
                )

                if sidebar_data:
                    # Ensure sidebar current_stage matches the resolved stage
                    if input_state.get("onboarding_stage") and isinstance(
                        sidebar_data, dict
                    ):
                        sidebar_data = sidebar_data.copy()  # Don't modify original
                        sidebar_data["current_stage"] = input_state["onboarding_stage"]
                        logger.info(
                            f"🔧 ONBOARDING: [STAGE_SYNC] Synchronized sidebar current_stage to '{input_state['onboarding_stage']}'"
                        )

                    input_state["sidebar_data"] = sidebar_data
                    logger.info(
                        f"🔧 ONBOARDING: [INPUT_STATE] Added sidebar_data with current_stage={sidebar_data.get('current_stage')}"
                    )

            # FALLBACK: Check conversation history for plan summary if not detected from DB
            if (
                not input_state.get("plan_summary_sent")
                and request.conversation_history
            ):
                logger.info(
                    f"🔍 ONBOARDING: [FALLBACK] Checking conversation history for plan summary indicators"
                )

                # Look for plan summary in the last few messages
                plan_summary_indicators = [
                    "are you ready for me to generate",
                    "ready for me to generate your detailed",
                    "training plan?",
                    "plan summary",
                    "approach:",
                    "weekly structure:",
                    "key focus:",
                    "progression:",
                    "phases:",
                    "this structured plan is tailored",
                    "let me know if this looks good",
                ]

                # Check last 5 messages for plan summary
                recent_messages = (
                    request.conversation_history[-5:]
                    if len(request.conversation_history) > 5
                    else request.conversation_history
                )

                for msg in recent_messages:
                    if isinstance(msg, dict) and msg.get("role") == "assistant":
                        content = msg.get("content", "").lower()
                        if any(
                            indicator in content
                            for indicator in plan_summary_indicators
                        ):
                            logger.info(
                                f"🎯 ONBOARDING: [FALLBACK] Detected plan summary in conversation history!"
                            )
                            logger.info(
                                f"🎯 ONBOARDING: [FALLBACK] Message preview: '{content[:100]}...'"
                            )
                            input_state["plan_summary_sent"] = True
                            input_state["onboarding_stage"] = "plan_summary_ready"

                            # Also update sidebar data if not present
                            if not input_state.get("sidebar_data"):
                                input_state["sidebar_data"] = {
                                    "current_stage": "plan_summary_ready"
                                }
                            elif isinstance(input_state["sidebar_data"], dict):
                                input_state["sidebar_data"][
                                    "current_stage"
                                ] = "plan_summary_ready"

                            logger.info(
                                f"🔧 ONBOARDING: [FALLBACK] Set plan_summary_sent=True and onboarding_stage=plan_summary_ready"
                            )
                            break

            logger.info(f"🔧 ONBOARDING: [INPUT_STATE] Created base input state")
            logger.info(f"🔧 ONBOARDING: [INPUT_STATE] User ID: {request.user_id}")
            logger.info(
                f"🔧 ONBOARDING: [INPUT_STATE] Message: {request.message[:50]}..."
            )
            logger.info(
                f"🔧 ONBOARDING: [INPUT_STATE] Conversation history length: {len(request.conversation_history or [])}"
            )
            logger.info(
                f"🔧 ONBOARDING: [INPUT_STATE] Onboarding stage: {input_state.get('onboarding_stage', 'Not set')}"
            )

            # Add user profile if available - prioritize MongoDB data
            if user_data and (
                user_data.get("training_profile") or user_data.get("current_plan")
            ):
                # Use MongoDB data
                merged_profile = {}

                if user_data.get("training_profile"):
                    merged_profile.update(user_data["training_profile"])
                    logger.info(
                        f"✅ ONBOARDING: [INPUT_STATE] Using MongoDB training profile data for onboarding - userId: {request.user_id}"
                    )

                if user_data.get("current_plan"):
                    merged_profile["current_plan"] = user_data["current_plan"]
                    logger.info(
                        f"✅ ONBOARDING: [INPUT_STATE] Using MongoDB current plan data for onboarding - userId: {request.user_id}"
                    )

                # Add formatted versions for prompt consumption
                if user_data.get("training_profile_formatted"):
                    merged_profile["training_profile_formatted"] = user_data[
                        "training_profile_formatted"
                    ]

                if user_data.get("current_plan_formatted"):
                    merged_profile["current_plan_formatted"] = user_data[
                        "current_plan_formatted"
                    ]

                input_state["user_profile"] = merged_profile
                logger.info(
                    f"✅ ONBOARDING: [INPUT_STATE] Added user profile with {len(merged_profile)} fields"
                )

            elif request.user_profile:
                # Fallback to request profile if provided
                input_state["user_profile"] = request.user_profile
                logger.info(
                    f"✅ ONBOARDING: [INPUT_STATE] Using request user profile data for onboarding - userId: {request.user_id}"
                )
            else:
                logger.info(
                    f"ℹ️ ONBOARDING: [INPUT_STATE] No user profile data available"
                )

        logger.info(f"🔧 ONBOARDING: [STREAM] Created input state")

        # Track streaming state
        event_count = 0
        agent_starts_sent = set()
        token_counts_by_node = {}
        event_types_seen = set()
        last_event_time = time.time()
        stall_timeout = 30  # 30 seconds without events is considered a stall
        last_sidebar_data = (
            None  # Track the last sidebar data sent instead of just a boolean
        )

        # Node mappings for onboarding - all nodes stream as "Athlea"
        onboarding_node_mappings = {
            "gatherInfo": "Athlea",
            "informationGathererNode": "Athlea",
            "checkCompletion": "Athlea",
            "needInput": "Athlea",
            "generatePlan": "Athlea",
        }

        logger.info(f"🗺️ ONBOARDING: [STREAM_MAPPINGS] Available node mappings:")
        for node_name, agent_domain in onboarding_node_mappings.items():
            logger.info(f"  📌 {node_name} -> {agent_domain}")

        try:
            # Use astream_events with version="v2" for proper token streaming
            logger.info(
                f"🎯 ONBOARDING: [STREAM] Starting graph execution with token streaming..."
            )

            async for event in graph.astream_events(input_state, config, version="v2"):
                # Check for overall timeout
                elapsed_time = time.time() - start_time
                if elapsed_time > timeout_seconds:
                    logger.error(
                        f"❌ ONBOARDING: [TIMEOUT] Stream exceeded {timeout_seconds}s timeout"
                    )
                    yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': 'Request timeout - please try again'})}\n\n"
                    break

                # Check for stall (no events for too long)
                time_since_last_event = time.time() - last_event_time
                if time_since_last_event > stall_timeout:
                    logger.error(
                        f"❌ ONBOARDING: [STALL] No events for {stall_timeout}s"
                    )
                    yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': 'Processing stalled - please try again'})}\n\n"
                    break

                last_event_time = time.time()
                event_count += 1
                event_type = event.get("event", "unknown")
                event_name = event.get("name", "unknown")
                event_types_seen.add(event_type)

                # Send periodic heartbeats
                if event_count % 50 == 0:
                    yield f"event: heartbeat\ndata: {json.dumps({'status': 'processing', 'events': event_count})}\n\n"

                if event_count % 10 == 0:  # Log every 10th event to avoid spam
                    logger.info(
                        f"📈 ONBOARDING: [STREAM] Processed {event_count} events so far. Event types seen: {sorted(event_types_seen)}"
                    )

                try:
                    # Log event details
                    logger.debug(
                        f"🔄 ONBOARDING: [EVENT] #{event_count} Type: {event_type}, Name: {event_name}"
                    )

                    # Handle on_chat_model_stream events - this is where individual tokens come from
                    if event_type == "on_chat_model_stream":
                        chunk_data = event.get("data", {})
                        chunk = chunk_data.get("chunk")

                        # Get the node name to determine which agent is streaming
                        tags = event.get("tags", [])
                        metadata = event.get("metadata", {})

                        # Skip internal LLM calls (routers, check_completion, etc.)
                        if "internal" in tags or "no_stream" in tags:
                            logger.debug(
                                f"⏭️ ONBOARDING: [SKIP] Skipping internal LLM stream with tags: {tags}"
                            )
                            continue

                        # Enhanced logging for debugging plan generation
                        logger.info(
                            f"🔍 ONBOARDING: [STREAM_TOKEN] Event #{event_count} - Event: {event_name}, Tags: {tags}, Metadata: {metadata}"
                        )

                        # Log chunk content for debugging
                        if chunk and hasattr(chunk, "content"):
                            content_preview = (
                                chunk.content[:100] if chunk.content else "NO_CONTENT"
                            )
                            logger.info(
                                f"🔍 ONBOARDING: [CHUNK_CONTENT] Event #{event_count} - Content preview: '{content_preview}...'"
                            )

                        # Skip internal processing streams - only stream final responses to user
                        if "internal_processing" in tags:
                            logger.debug(
                                f"🔇 ONBOARDING: [FILTER] Skipping internal processing stream with tags: {tags}"
                            )
                            continue

                        # Enhanced filtering logic - allow plan generation and user-facing content
                        should_stream = False
                        filter_reason = ""

                        # Allow events tagged with final_response (original logic)
                        if "final_response" in tags:
                            should_stream = True
                            filter_reason = "final_response tag"
                        # Allow plan generation events
                        elif "plan_generation" in tags:
                            should_stream = True
                            filter_reason = "plan_generation tag"
                        # Allow onboarding completion events
                        elif "onboarding_complete" in tags:
                            should_stream = True
                            filter_reason = "onboarding_complete tag"
                        # Allow user-facing response events
                        elif "user_response" in tags:
                            should_stream = True
                            filter_reason = "user_response tag"
                        # Allow events from specific nodes that generate plans or gather information
                        elif metadata.get("langgraph_node") in [
                            "generatePlan",
                            "plan_review",
                            "generatePlanSummary",
                            "adaptivePlanGeneration",
                            "onboarding_completion",
                            "checkCompletion",
                            "gatherInfo",
                            "information_gatherer",
                            "processUserInput",
                            "analyzeUserResponse",
                        ]:
                            should_stream = True
                            filter_reason = f"plan generation node: {metadata.get('langgraph_node')}"
                        # Allow events from any node that contains "plan" in the name
                        elif (
                            metadata.get("langgraph_node")
                            and "plan" in metadata.get("langgraph_node", "").lower()
                        ):
                            should_stream = True
                            filter_reason = (
                                f"plan-related node: {metadata.get('langgraph_node')}"
                            )
                        # FALLBACK: Allow events from nodes that might contain plan content based on event name
                        elif event_name and "plan" in event_name.lower():
                            should_stream = True
                            filter_reason = f"plan-related event: {event_name}"
                        # FALLBACK: If we detect plan content in the chunk, allow it through
                        elif chunk and hasattr(chunk, "content") and chunk.content:
                            content_lower = chunk.content.lower()
                            if any(
                                keyword in content_lower
                                for keyword in [
                                    "training plan",
                                    "personalized plan",
                                    "your plan",
                                    "plan generated",
                                    "duration:",
                                    "level:",
                                    "disciplines:",
                                    "training approach",
                                    "training phases",
                                    "example sessions",
                                ]
                            ):
                                should_stream = True
                                filter_reason = "detected plan content in chunk"

                        if not should_stream:
                            logger.info(
                                f"🔇 ONBOARDING: [FILTER] Skipping non-streamable event with tags: {tags}, node: {metadata.get('langgraph_node', 'unknown')} (event #{event_count})"
                            )
                            continue
                        else:
                            logger.info(
                                f"✅ ONBOARDING: [FILTER] Allowing event to stream - reason: {filter_reason}, tags: {tags}, node: {metadata.get('langgraph_node', 'unknown')}"
                            )

                        logger.info(
                            f"✅ ONBOARDING: [STREAM_ACCEPT] Accepting token stream with tags: {tags}"
                        )

                        # Detect node name using same logic as coaching
                        node_name = None
                        detection_method = None

                        # Method 1: Check metadata for langgraph node
                        if metadata.get("langgraph_node"):
                            node_name = metadata["langgraph_node"]
                            detection_method = "metadata/langgraph_node"

                        # Method 2: Check other metadata fields
                        elif metadata.get("node"):
                            node_name = metadata["node"]
                            detection_method = "metadata/node"

                        # Method 3: Check tags for node names
                        elif tags:
                            for tag in tags:
                                if tag in onboarding_node_mappings:
                                    node_name = tag
                                    detection_method = "tags"
                                    break

                        # Method 4: Fall back to event name
                        if not node_name:
                            node_name = event.get("name", "unknown")
                            detection_method = "event_name (fallback)"

                        logger.info(
                            f"🎯 ONBOARDING: [NODE_DETECTION] Detected node: '{node_name}' via {detection_method}"
                        )

                        # Map to agent domain (all onboarding nodes -> "Athlea")
                        agent_domain = onboarding_node_mappings.get(node_name, "Athlea")

                        # Track first token for agent start events
                        if agent_domain not in agent_starts_sent:
                            agent_starts_sent.add(agent_domain)
                            logger.info(
                                f"🆕 ONBOARDING: [NODE_TRACKING] First token from node: '{node_name}' -> agent: '{agent_domain}'"
                            )
                            yield f"data: {json.dumps({'type': 'agent_start', 'agent': agent_domain})}\n\n"
                            logger.info(
                                f"📤 ONBOARDING: [SSE_SEND] Agent start: '{agent_domain}' (triggered by node: '{node_name}')"
                            )

                        # Stream the token if we have chunk content
                        if chunk and hasattr(chunk, "content") and chunk.content:
                            logger.debug(
                                f"📤 ONBOARDING: [TOKEN_STREAM] Sending token: '{chunk.content[:50]}...' from node: '{node_name}'"
                            )
                            yield f"data: {json.dumps({'type': 'token', 'content': chunk.content, 'agent': agent_domain})}\n\n"

                            # Update token tracking
                            if node_name not in token_counts_by_node:
                                token_counts_by_node[node_name] = 0
                            token_counts_by_node[node_name] += 1

                            # Log progress every 10 tokens
                            if token_counts_by_node[node_name] % 10 == 0:
                                logger.info(
                                    f"📊 ONBOARDING: [TOKEN_PROGRESS] Node: '{node_name}' has sent {token_counts_by_node[node_name]} tokens to agent: '{agent_domain}'"
                                )
                        else:
                            logger.debug(
                                f"⚠️ ONBOARDING: [EMPTY_TOKEN] Received chunk with no content from node: '{node_name}'"
                            )

                    # Handle on_chain_start events for agent routing (backup method)
                    elif (
                        event_type == "on_chain_start"
                        and event_name in onboarding_node_mappings
                    ):
                        agent_domain = onboarding_node_mappings[event_name]

                        logger.info(
                            f"🚀 ONBOARDING: [AGENT_START] Chain start detected - Agent starting: '{event_name}' -> '{agent_domain}'"
                        )

                        if agent_domain not in agent_starts_sent:
                            agent_starts_sent.add(agent_domain)
                            yield f"data: {json.dumps({'type': 'agent_start', 'agent': agent_domain})}\n\n"
                            logger.info(
                                f"📤 ONBOARDING: [SSE_SEND] Agent start: '{agent_domain}' (via chain_start for '{event_name}')"
                            )

                    # Handle on_chain_end events to check for state updates
                    elif event_type == "on_chain_end":
                        # Check for sidebar data updates
                        try:
                            current_snapshot = await graph.aget_state(config)
                            if current_snapshot and current_snapshot.values:
                                sidebar_data = current_snapshot.values.get(
                                    "sidebar_data"
                                )

                                if sidebar_data:
                                    # Convert Pydantic model to dict if needed
                                    if hasattr(sidebar_data, "model_dump"):
                                        sidebar_dict = sidebar_data.model_dump()
                                    elif hasattr(sidebar_data, "dict"):
                                        sidebar_dict = sidebar_data.dict()
                                    else:
                                        sidebar_dict = sidebar_data

                                    # If we're resuming and have existing sidebar data, merge it
                                    if (
                                        request.resume_from_interrupt
                                        and "existing_sidebar_data" in locals()
                                        and existing_sidebar_data
                                    ):
                                        if hasattr(existing_sidebar_data, "model_dump"):
                                            existing_dict = (
                                                existing_sidebar_data.model_dump()
                                            )
                                        elif hasattr(existing_sidebar_data, "dict"):
                                            existing_dict = existing_sidebar_data.dict()
                                        else:
                                            existing_dict = existing_sidebar_data

                                        logger.info(
                                            f"[RESUME] Merging streaming sidebar data with existing data"
                                        )
                                        sidebar_dict = _merge_sidebar_data(
                                            existing_dict, sidebar_dict
                                        )

                                    # Check if sidebar data has actually changed
                                    current_sidebar_json = json.dumps(
                                        sidebar_dict, sort_keys=True
                                    )
                                    last_sidebar_json = json.dumps(
                                        last_sidebar_data or {}, sort_keys=True
                                    )

                                    if current_sidebar_json != last_sidebar_json:
                                        logger.info(
                                            f"📋 ONBOARDING: [SIDEBAR_UPDATE] Sending sidebar data update (data changed)"
                                        )
                                        logger.info(
                                            f"📋 ONBOARDING: [SIDEBAR_DATA] Goals: {sidebar_dict.get('goals', {})}"
                                        )
                                        logger.info(
                                            f"📋 ONBOARDING: [SIDEBAR_DATA] Selected Sports: {sidebar_dict.get('selected_sports', [])}"
                                        )
                                        logger.info(
                                            f"📋 ONBOARDING: [SIDEBAR_DATA] Summary Items Count: {len(sidebar_dict.get('summary_items', []))}"
                                        )

                                        yield f"data: {json.dumps({'type': 'sidebar_update', 'sidebarData': sidebar_dict})}\n\n"
                                        last_sidebar_data = sidebar_dict

                                # Check for need_input
                                if current_snapshot.values.get(
                                    "needs_input"
                                ) or current_snapshot.values.get("requires_input"):
                                    logger.info(
                                        f"🛑 ONBOARDING: [NEED_INPUT] Input needed from user"
                                    )
                                    input_prompt = current_snapshot.values.get(
                                        "input_prompt",
                                        "Please provide more information.",
                                    )
                                    yield f"data: {json.dumps({'type': 'need_input', 'prompt': input_prompt})}\n\n"
                                    break

                        except Exception as e:
                            logger.warning(
                                f"⚠️ ONBOARDING: [STATE_CHECK] Error checking state: {e}"
                            )

                        # Check for completion
                        if event_name in ["__graph__", "generatePlan"]:
                            logger.info(
                                f"✅ ONBOARDING: [COMPLETE] Graph execution completed: '{event_name}'"
                            )
                            break

                except Exception as e:
                    logger.error(
                        f"❌ ONBOARDING: [EVENT_ERROR] Error processing event #{event_count}: {e}"
                    )
                    # Don't break on individual event errors - continue processing
                    continue

            # Check final state after graph completion
            try:
                final_snapshot = await graph.aget_state(config)
                if final_snapshot and final_snapshot.values:
                    final_state = final_snapshot.values

                    # Send final sidebar update if available
                    if final_state.get("sidebar_data"):
                        sidebar_data = final_state["sidebar_data"]
                        if hasattr(sidebar_data, "model_dump"):
                            sidebar_dict = sidebar_data.model_dump()
                        elif hasattr(sidebar_data, "dict"):
                            sidebar_dict = sidebar_data.dict()
                        else:
                            sidebar_dict = sidebar_data

                        # If we're resuming and have existing sidebar data, merge it
                        if (
                            request.resume_from_interrupt
                            and "existing_sidebar_data" in locals()
                            and existing_sidebar_data
                        ):
                            if hasattr(existing_sidebar_data, "model_dump"):
                                existing_dict = existing_sidebar_data.model_dump()
                            elif hasattr(existing_sidebar_data, "dict"):
                                existing_dict = existing_sidebar_data.dict()
                            else:
                                existing_dict = existing_sidebar_data

                            logger.info(
                                f"[RESUME] Merging final sidebar data with existing data"
                            )
                            sidebar_dict = _merge_sidebar_data(
                                existing_dict, sidebar_dict
                            )

                        # Check if this final sidebar data is different from what we've sent
                        current_sidebar_json = json.dumps(sidebar_dict, sort_keys=True)
                        last_sidebar_json = json.dumps(
                            last_sidebar_data or {}, sort_keys=True
                        )

                        if current_sidebar_json != last_sidebar_json:
                            logger.info(
                                f"📋 ONBOARDING: [FINAL_SIDEBAR] Sending final sidebar data (different from previous)"
                            )
                            logger.info(
                                f"📋 ONBOARDING: [FINAL_SIDEBAR_DATA] Goals: {sidebar_dict.get('goals', {})}"
                            )
                            logger.info(
                                f"📋 ONBOARDING: [FINAL_SIDEBAR_DATA] Selected Sports: {sidebar_dict.get('selected_sports', [])}"
                            )
                            logger.info(
                                f"📋 ONBOARDING: [FINAL_SIDEBAR_DATA] Summary Items Count: {len(sidebar_dict.get('summary_items', []))}"
                            )

                            yield f"data: {json.dumps({'type': 'sidebar_update', 'sidebarData': sidebar_dict})}\n\n"

                    # Check if we need input instead of completing
                    if final_state.get("needs_input") or final_state.get(
                        "requires_input"
                    ):
                        logger.info(
                            f"🛑 ONBOARDING: [FINAL_NEED_INPUT] Final state indicates input needed"
                        )
                        input_prompt = final_state.get(
                            "input_prompt", "Please provide more information."
                        )
                        yield f"data: {json.dumps({'type': 'need_input', 'prompt': input_prompt})}\n\n"
                        return  # Don't send complete event

            except Exception as e:
                logger.warning(
                    f"⚠️ ONBOARDING: [FINAL_STATE] Error checking final state: {e}"
                )

            # Send completion event
            logger.info(f"📤 ONBOARDING: [SSE_SEND] Completion event sent")
            yield f"data: {json.dumps({'type': 'complete', 'message': 'Response completed'})}\n\n"

            # Final summary logging
            logger.info(
                f"🏁 ONBOARDING: [STREAM_END] Completed streaming, total events processed: {event_count}"
            )
            logger.info(
                f"📊 ONBOARDING: [STREAM_SUMMARY] Agent starts sent: {list(agent_starts_sent)}"
            )

            # Log token counts per node
            if token_counts_by_node:
                logger.info(f"📈 ONBOARDING: [TOKEN_SUMMARY] Token counts by node:")
                for node_name, count in token_counts_by_node.items():
                    agent_domain = onboarding_node_mappings.get(node_name, "Athlea")
                    logger.info(f"  🎯 {node_name} -> {agent_domain}: {count} tokens")
            else:
                logger.warning(
                    f"⚠️ ONBOARDING: [TOKEN_SUMMARY] No tokens were produced by any nodes!"
                )

        except asyncio.CancelledError:
            logger.warning("⚠️ ONBOARDING: [STREAM_CANCELLED] Stream was cancelled")
            yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': 'Stream cancelled'})}\n\n"
            raise

        except Exception as e:
            logger.error(f"❌ ONBOARDING: [STREAM_ERROR] Fatal error in streaming: {e}")
            logger.exception("Full traceback:")

            # Send error event
            yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': f'Backend streaming error: {str(e)}'})}\n\n"

        finally:
            # Always send a final heartbeat to indicate stream is closing
            total_time = time.time() - start_time
            logger.info(
                f"⏱️ ONBOARDING: [STREAM_COMPLETE] Total streaming time: {total_time:.2f}s"
            )
            yield f"event: heartbeat\ndata: {json.dumps({'status': 'completed', 'total_time': total_time})}\n\n"

    except Exception as e:
        logger.error(
            f"❌ ONBOARDING: [OUTER_ERROR] Error in stream_onboarding_response: {e}"
        )
        yield f"event: error\ndata: {json.dumps({'type': 'error', 'message': f'Error starting onboarding stream: {str(e)}'})}\n\n"


@app.post("/api/onboarding")
async def stream_onboarding_session(request: OnboardingRequest):
    """
    Stream an onboarding session with real-time updates.

    Supports:
    - New onboarding conversations
    - Resume from interrupts (HITL)
    - Real-time streaming of responses
    - Sidebar data updates
    - Plan generation
    """
    try:
        # Validate request
        if not request.user_id:
            raise HTTPException(status_code=400, detail="user_id is required")

        if not request.message:
            raise HTTPException(status_code=400, detail="message is required")

        # Return streaming response
        return StreamingResponse(
            handle_streaming_errors(stream_onboarding_response(request)),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in stream_onboarding_session: {e}")
        raise HTTPException(
            status_code=500, detail="Internal server error during onboarding"
        )


@app.get("/api/onboarding/status/{user_id}")
async def get_onboarding_status(user_id: str, thread_id: Optional[str] = None):
    """
    Get the current onboarding status for a user.

    Returns:
    - Current onboarding stage
    - Whether onboarding is complete
    - Last conversation state
    """
    try:
        # Get graph
        graph = get_graph_by_type(GraphType.ONBOARDING, checkpointer_type="memory")

        # Prepare config
        config_thread_id = thread_id or f"onboarding_{user_id}"
        config = {"configurable": {"thread_id": config_thread_id}}

        # Get current state
        snapshot = await graph.aget_state(config)

        if not snapshot.values:
            return {
                "status": "not_started",
                "stage": "initial",
                "has_enough_info": False,
                "needs_input": True,
                "message": "Onboarding not started",
            }

        state = snapshot.values
        return {
            "status": (
                "in_progress"
                if not state.get("onboarding_stage") == "complete"
                else "completed"
            ),
            "stage": state.get("onboarding_stage", "unknown"),
            "has_enough_info": state.get("has_enough_info", False),
            "needs_input": state.get("needs_input", True),
            "requires_input": state.get("requires_input", False),
            "sidebar_data": state.get("sidebar_data"),
            "generated_plan": state.get("generated_plan"),
            "user_id": user_id,
            "thread_id": config_thread_id,
        }

    except Exception as e:
        logger.error(f"Error getting onboarding status: {e}")
        raise HTTPException(
            status_code=500, detail="Error retrieving onboarding status"
        )


@app.post("/api/onboarding/reset/{user_id}")
async def reset_onboarding(user_id: str, thread_id: Optional[str] = None):
    """
    Reset onboarding for a user (clear conversation state).
    """
    try:
        # Get graph
        graph = get_graph_by_type(GraphType.ONBOARDING, checkpointer_type="memory")

        # Prepare config
        config_thread_id = thread_id or f"onboarding_{user_id}"
        config = {"configurable": {"thread_id": config_thread_id}}

        # Clear state by creating a new initial state
        initial_state = create_initial_onboarding_state(user_id)

        # Update the state to reset
        await graph.aupdate_state(config, initial_state)

        return {
            "status": "reset",
            "message": "Onboarding has been reset",
            "user_id": user_id,
            "thread_id": config_thread_id,
        }

    except Exception as e:
        logger.error(f"Error resetting onboarding: {e}")
        raise HTTPException(status_code=500, detail="Error resetting onboarding")


@app.post("/api/onboarding/upload")
async def upload_onboarding_file(request: Request):
    """
    Handle file uploads during onboarding process.

    This endpoint receives files directly from the frontend during onboarding
    and stores them for later analysis during the conversation.
    """
    import tempfile
    import os
    import uuid
    from fastapi import Form, File, UploadFile

    try:
        # Parse the multipart form data
        form = await request.form()

        # Extract form fields
        file_data = form.get("file")
        user_id = form.get("user_id")
        thread_id = form.get("thread_id")

        if not file_data:
            raise HTTPException(status_code=400, detail="No file provided")

        if not user_id:
            raise HTTPException(status_code=400, detail="user_id is required")

        # Validate file type
        allowed_types = [
            "text/plain",
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/csv",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "image/jpeg",
            "image/png",
            "image/webp",
        ]

        if file_data.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_data.content_type} not allowed",
            )

        # Check file size (5MB limit)
        file_content = await file_data.read()
        if len(file_content) > 5 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File size exceeds 5MB limit")

        # Generate unique file ID
        file_id = str(uuid.uuid4())

        # Create directory for onboarding files if it doesn't exist
        onboarding_dir = os.path.join(
            tempfile.gettempdir(), "athlea_onboarding", user_id
        )
        os.makedirs(onboarding_dir, exist_ok=True)

        # Save file with original extension but preserve original filename in metadata
        file_extension = (
            os.path.splitext(file_data.filename)[1] if file_data.filename else ""
        )
        file_path = os.path.join(onboarding_dir, f"{file_id}{file_extension}")

        with open(file_path, "wb") as f:
            f.write(file_content)

        # Save metadata file with original filename mapping
        metadata_path = os.path.join(onboarding_dir, f"{file_id}.meta")
        metadata = {
            "original_filename": file_data.filename,
            "file_id": file_id,
            "content_type": file_data.content_type,
            "file_size": len(file_content),
            "upload_timestamp": datetime.now().isoformat(),
            "thread_id": thread_id,
        }

        with open(metadata_path, "w") as f:
            import json

            json.dump(metadata, f)

        logger.info(f"📁 ONBOARDING: [UPLOAD] File uploaded successfully")
        logger.info(f"   📋 User ID: {user_id}")
        logger.info(f"   📋 Thread ID: {thread_id}")
        logger.info(f"   📋 File ID: {file_id}")
        logger.info(f"   📋 Filename: {file_data.filename}")
        logger.info(f"   📋 Content Type: {file_data.content_type}")
        logger.info(f"   📋 File Size: {len(file_content)} bytes")
        logger.info(f"   📋 Saved Path: {file_path}")

        # Store file metadata for later retrieval during onboarding conversation
        file_metadata = {
            "file_id": file_id,
            "original_filename": file_data.filename,
            "content_type": file_data.content_type,
            "file_size": len(file_content),
            "file_path": file_path,
            "user_id": user_id,
            "thread_id": thread_id,
            "upload_timestamp": datetime.now().isoformat(),
        }

        # TODO: Store metadata in database or cache for retrieval during conversation
        # For now, we'll rely on the file system and the onboarding system will
        # detect files in the user's onboarding directory

        return {
            "success": True,
            "fileId": file_id,
            "filename": file_data.filename,
            "message": f"File {file_data.filename} uploaded successfully for onboarding analysis",
            "metadata": {
                "contentType": file_data.content_type,
                "fileSize": len(file_content),
                "uploadTime": datetime.now().isoformat(),
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ ONBOARDING: [UPLOAD_ERROR] Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")


@app.delete("/api/onboarding/clear-files/{user_id}")
async def clear_onboarding_files(user_id: str):
    """
    Clear all onboarding files for a user from the local filesystem.

    This removes files from the /tmp/athlea_onboarding/{user_id}/ directory
    to ensure uploaded files don't persist across onboarding resets.
    """
    try:
        import tempfile
        import os
        import shutil

        if not user_id:
            raise HTTPException(status_code=400, detail="user_id is required")

        # Get the onboarding directory path
        onboarding_dir = os.path.join(
            tempfile.gettempdir(), "athlea_onboarding", user_id
        )

        files_deleted = 0
        directories_deleted = 0

        if os.path.exists(onboarding_dir):
            logger.info(
                f"📁 ONBOARDING: [CLEAR_FILES] Clearing directory: {onboarding_dir}"
            )

            # Count files before deletion
            for root, dirs, files in os.walk(onboarding_dir):
                files_deleted += len(files)
                directories_deleted += len(dirs)

            # Remove the entire directory and its contents
            shutil.rmtree(onboarding_dir)
            directories_deleted += 1  # Count the root directory itself

            logger.info(
                f"📁 ONBOARDING: [CLEAR_FILES] Successfully deleted {files_deleted} files and {directories_deleted} directories"
            )
        else:
            logger.info(
                f"📁 ONBOARDING: [CLEAR_FILES] Directory does not exist: {onboarding_dir}"
            )

        return {
            "success": True,
            "message": "Onboarding files cleared successfully",
            "user_id": user_id,
            "files_deleted": files_deleted,
            "directories_deleted": directories_deleted,
            "directory_path": onboarding_dir,
        }

    except Exception as e:
        logger.error(
            f"❌ ONBOARDING: [CLEAR_FILES_ERROR] Error clearing files for user {user_id}: {e}"
        )
        raise HTTPException(
            status_code=500, detail=f"Error clearing onboarding files: {str(e)}"
        )


@app.get("/api/test-sse")
async def test_sse():
    """Simple test SSE endpoint to debug streaming issues."""

    async def generate_test_stream():
        """Generate test SSE events."""
        import asyncio
        import json

        for i in range(5):
            yield f"event: test\ndata: {json.dumps({'message': f'Test message {i+1}', 'timestamp': str(datetime.now())})}\n\n"
            await asyncio.sleep(1)

        yield f"event: complete\ndata: {json.dumps({'message': 'Test stream complete'})}\n\n"

    return StreamingResponse(
        generate_test_stream(),
        media_type="text/event-stream",
        headers={
            "Connection": "keep-alive",
            "Cache-Control": "no-cache, no-transform",
            "Content-Encoding": "none",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
        },
    )


@app.get("/api/onboarding")
async def stream_onboarding_get(
    message: str = Query(..., description="User message"),
    threadId: str = Query(..., description="Thread ID"),
    userId: str = Query(..., description="User ID"),
    resume: Optional[str] = Query(None, description="Resume flag"),
):
    """
    GET endpoint for streaming onboarding responses (Next.js compatibility).

    This endpoint loads the full conversation history from MongoDB to make
    proper routing decisions based on the complete conversation context.
    """
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        raise HTTPException(status_code=500, detail="MONGODB_URI not configured")

    try:
        logger.info(
            f"🚀 ONBOARDING GET: Starting onboarding stream for user {userId}, thread {threadId}"
        )

        # Load full conversation history from MongoDB
        conversation_history = []
        try:
            # Import MongoDB client
            from pymongo import MongoClient

            client = MongoClient(mongodb_uri)
            # Use explicit database name instead of get_default_database()
            db = client["AthleaUserData"]  # Use the correct database name
            messages_collection = db["trainingPlans"]  # Use the correct collection name

            logger.info(f"📚 ONBOARDING GET: Searching for thread ID: {threadId}")

            # Find the conversation document - use jobId field instead of threadId
            conversation_doc = messages_collection.find_one({"jobId": threadId})

            logger.info(
                f"📚 ONBOARDING GET: MongoDB query result: {conversation_doc is not None}"
            )

            if conversation_doc:
                logger.info(
                    f"📚 ONBOARDING GET: Document keys: {list(conversation_doc.keys())}"
                )
                if "messages" in conversation_doc:
                    message_list = conversation_doc["messages"]
                    logger.info(
                        f"📚 ONBOARDING GET: Found {len(message_list)} messages in messages field"
                    )
                else:
                    logger.warning(
                        f"📚 ONBOARDING GET: No 'messages' field in document"
                    )
            else:
                # Let's also check what documents exist in the collection
                sample_docs = list(messages_collection.find().limit(3))
                logger.info(
                    f"📚 ONBOARDING GET: No document found for jobId {threadId}"
                )
                logger.info(
                    f"📚 ONBOARDING GET: Sample documents in collection: {[doc.get('jobId', 'NO_JOB_ID') for doc in sample_docs]}"
                )

            if conversation_doc and "messages" in conversation_doc:
                # Extract message list
                message_list = conversation_doc["messages"]
                logger.info(
                    f"📚 ONBOARDING GET: Loaded {len(message_list)} messages from MongoDB for thread {threadId}"
                )

                # Convert to conversation history format
                for msg in message_list:
                    if isinstance(msg, dict):
                        # Handle different message types from trainingPlans collection
                        msg_type = msg.get("type", "")
                        msg_content = msg.get("text", "") or msg.get("content", "")

                        # Map message types to role
                        if msg_type in ["question", "assistant_message"]:
                            role = "assistant"
                        elif msg_type in ["answer", "user_message", "user"]:
                            role = "human"
                        else:
                            # Default to human for unknown types
                            role = "human"

                        if msg_content:  # Only add messages with content
                            conversation_history.append(
                                {
                                    "role": role,
                                    "content": msg_content,
                                }
                            )

                logger.info(
                    f"📚 ONBOARDING GET: Converted {len(conversation_history)} messages to conversation history format"
                )
            else:
                logger.info(
                    f"📚 ONBOARDING GET: No existing conversation found for thread {threadId}"
                )

            client.close()

        except Exception as e:
            logger.warning(f"⚠️ ONBOARDING GET: Error loading conversation history: {e}")
            # Continue without history if loading fails

        # Create onboarding request with full conversation history
        request = OnboardingRequest(
            message=message,
            user_id=userId,
            thread_id=threadId,
            conversation_history=conversation_history,
            resume_from_interrupt=resume == "true" if resume else False,
        )

        # Return streaming response
        return StreamingResponse(
            handle_streaming_errors(stream_onboarding_response(request)),
            media_type="text/event-stream",
            headers={
                "Connection": "keep-alive",
                "Cache-Control": "no-cache, no-transform",
                "Content-Encoding": "none",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        logger.error(f"Error in onboarding GET endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    import warnings
    import logging

    # Comprehensive deprecation warning suppression
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", message=".*websockets.legacy.*")
    warnings.filterwarnings("ignore", message=".*WebSocketServerProtocol.*")

    # Suppress uvicorn's websockets warnings at the logging level
    websockets_logger = logging.getLogger("websockets.legacy")
    websockets_logger.setLevel(logging.ERROR)
    uvicorn_logger = logging.getLogger("uvicorn.protocols.websockets")
    uvicorn_logger.setLevel(logging.ERROR)

    # Load environment variables
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")

    logger.info(f"Starting Athlea Coaching API on {host}:{port}")

    uvicorn.run(
        "athlea_langgraph.api.main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info",
        ws="auto",  # Let uvicorn choose the best WebSocket implementation
    )
