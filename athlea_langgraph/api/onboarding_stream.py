"""
Streaming Onboarding API Endpoint

FastAPI endpoint that streams onboarding responses to the frontend.
Provides real-time streaming of the onboarding conversation flow.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Async<PERSON>enerator, Dict, Optional

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage
from langgraph.types import Command
from pydantic import BaseModel, Field

from ..graph_factory import get_graph_factory
from ..states.onboarding_state import OnboardingState, create_initial_onboarding_state

logger = logging.getLogger(__name__)

app = FastAPI()


class OnboardingRequest(BaseModel):
    """Request model for onboarding sessions."""

    message: str = Field(..., description="User's message during onboarding")
    user_id: str = Field(..., description="User identifier")
    thread_id: Optional[str] = Field(
        None, description="Thread identifier for conversation continuity"
    )
    user_profile: Optional[Dict[str, Any]] = Field(
        None, description="User profile information"
    )
    conversation_history: Optional[list] = Field(
        default_factory=list, description="Previous conversation messages"
    )
    resume_from_interrupt: Optional[bool] = Field(
        False, description="Whether to resume from a previous interrupt"
    )


class StreamingOnboardingResponse:
    """Handles streaming of onboarding responses."""

    def __init__(self, request: OnboardingRequest):
        self.request = request
        self.graph = None

    async def initialize_graph(self):
        """Initialize the onboarding graph."""
        try:
            graph_factory = get_graph_factory()
            self.graph = await graph_factory.create_graph(
                "onboarding", checkpointer_type="memory"
            )
            logger.info("Initialized onboarding graph successfully")
        except Exception as e:
            logger.error(f"Failed to initialize onboarding graph: {e}")
            raise HTTPException(
                status_code=500, detail="Failed to initialize onboarding system"
            )

    async def stream_response(self) -> AsyncGenerator[str, None]:
        """Stream the onboarding response with real-time updates."""
        try:
            # Initialize graph if not already done
            if not self.graph:
                await self.initialize_graph()

            # Send initial status
            yield self._format_stream_message(
                "status",
                {
                    "type": "session_started",
                    "message": "Starting onboarding session...",
                    "user_id": self.request.user_id,
                },
            )

            # Prepare configuration
            thread_id = self.request.thread_id or f"onboarding_{self.request.user_id}"
            config = {"configurable": {"thread_id": thread_id}}

            # Handle resume from interrupt or new conversation
            if self.request.resume_from_interrupt:
                yield self._format_stream_message(
                    "status",
                    {"type": "resuming", "message": "Resuming onboarding session..."},
                )

                # Get existing state to preserve sidebar data
                snapshot = await self.graph.aget_state(config)
                existing_sidebar_data = None
                if snapshot.values and "sidebar_data" in snapshot.values:
                    existing_sidebar_data = snapshot.values["sidebar_data"]
                    logger.info(
                        f"[RESUME] Preserving existing sidebar data: {bool(existing_sidebar_data)}"
                    )

                # Resume from interrupt with user input
                resume_result = await self.graph.ainvoke(
                    Command(resume=HumanMessage(content=self.request.message)), config
                )

                # Preserve existing sidebar data if it was more complete
                if existing_sidebar_data and "sidebar_data" in resume_result:
                    current_sidebar = resume_result["sidebar_data"]
                    preserved_sidebar = self._merge_sidebar_data(
                        existing_sidebar_data, current_sidebar
                    )
                    resume_result["sidebar_data"] = preserved_sidebar
                    logger.info(
                        f"[RESUME] Merged sidebar data to preserve existing information"
                    )

                # Stream the result
                await self._stream_graph_result(resume_result)
            else:
                # Create initial state or get existing state
                try:
                    # Try to get existing state first
                    snapshot = await self.graph.aget_state(config)

                    if snapshot.values:
                        # Existing conversation - add new message
                        state = snapshot.values
                        logger.info("Continuing existing onboarding conversation")
                    else:
                        # New conversation - create initial state
                        state = create_initial_onboarding_state(self.request.user_id)
                        logger.info("Created new onboarding conversation")

                    # Add the new user message
                    state["user_input"] = self.request.message
                    state["messages"] = state.get("messages", []) + [
                        HumanMessage(content=self.request.message)
                    ]

                    # Add user profile if provided
                    if self.request.user_profile:
                        state["user_profile"] = self.request.user_profile

                    yield self._format_stream_message(
                        "status",
                        {"type": "processing", "message": "Processing your message..."},
                    )

                    # Execute the graph
                    result = await self.graph.ainvoke(state, config)

                    # Stream the result
                    await self._stream_graph_result(result)

                except Exception as e:
                    logger.error(f"Error during graph execution: {e}")
                    yield self._format_stream_message(
                        "error",
                        {
                            "type": "execution_error",
                            "message": "An error occurred during processing",
                            "error": str(e),
                        },
                    )

        except Exception as e:
            logger.error(f"Error in stream_response: {e}")
            yield self._format_stream_message(
                "error",
                {
                    "type": "stream_error",
                    "message": "An error occurred during streaming",
                    "error": str(e),
                },
            )

    async def _stream_graph_result(self, result: Dict[str, Any]):
        """Stream the graph execution result with adaptive weekly planning support."""
        try:
            # Log the result keys for debugging
            logger.info(f"🔍 STREAMING: Result keys: {list(result.keys())}")

            # Log detailed info about key fields
            if "generated_plan" in result:
                logger.info(
                    f"📋 STREAMING: Generated plan exists: {bool(result['generated_plan'])}"
                )
                if result["generated_plan"]:
                    logger.info(
                        f"📋 STREAMING: Plan details - Name: {result['generated_plan'].get('name', 'N/A')}, Type: {type(result['generated_plan'])}"
                    )

            if "onboarding_stage" in result:
                logger.info(
                    f"🎯 STREAMING: Onboarding stage: {result.get('onboarding_stage')}"
                )

            if "sidebar_data" in result:
                logger.info(
                    f"📊 STREAMING: Sidebar data exists: {bool(result['sidebar_data'])}"
                )

            # Stream onboarding stage update
            if "onboarding_stage" in result:
                yield self._format_stream_message(
                    "stage_update",
                    {
                        "stage": result["onboarding_stage"],
                        "message": f"Moved to {result['onboarding_stage']} stage",
                        "timestamp": self._get_current_timestamp(),
                    },
                )

            # Stream sidebar data updates
            if "sidebar_data" in result and result["sidebar_data"]:
                # Convert sidebar_data to frontend format
                frontend_sidebar_data = self._convert_sidebar_data_to_frontend(
                    result["sidebar_data"]
                )
                yield self._format_stream_message(
                    "sidebar_update",
                    {
                        "sidebarData": frontend_sidebar_data,
                        "timestamp": self._get_current_timestamp(),
                    },
                )

            # Stream adaptive weekly planning updates
            async for update in self._stream_adaptive_planning_updates(result):
                yield update

            # Stream messages if available
            if "messages" in result and result["messages"]:
                last_message = result["messages"][-1]

                if hasattr(last_message, "content"):
                    response_content = last_message.content

                    # Send agent start before streaming content
                    yield self._format_stream_message(
                        "agent_start",
                        {
                            "agent": "Athlea",
                            "timestamp": self._get_current_timestamp(),
                        },
                    )

                    # Stream response as tokens (same format as plan messages)
                    chunk_size = 50  # Characters per chunk
                    for i in range(0, len(response_content), chunk_size):
                        chunk = response_content[i : i + chunk_size]
                        yield self._format_stream_message(
                            "token",
                            {
                                "agent": "Athlea",
                                "content": chunk,
                                "timestamp": self._get_current_timestamp(),
                            },
                        )
                        # Small delay for streaming effect
                        await asyncio.sleep(0.02)

            # Stream plan if generated with progressive section updates
            if "generated_plan" in result and result["generated_plan"]:
                plan = result["generated_plan"]
                logger.info(f"🎯 STREAMING: Plan generated, sending as chat message")

                # Send the plan as a chat message using the token event type
                # that the frontend already knows how to handle

                # First, send agent_start event
                yield self._format_stream_message(
                    "agent_start",
                    {
                        "agent": "Athlea",
                        "timestamp": self._get_current_timestamp(),
                    },
                )

                # Format the plan as a nice message
                plan_message = f"""# 🎯 Your Personalized Training Plan: {plan.get('name', 'Custom Plan')}

## Overview
**Duration:** {plan.get('duration', 'N/A')}
**Level:** {plan.get('level', 'N/A')}
**Type:** {plan.get('planType', 'N/A')}

**Description:** {plan.get('description', 'Your custom training plan')}

## Training Approach
{plan.get('rationale', 'This plan is designed to help you achieve your fitness goals.')}

"""

                # Add disciplines if present
                if plan.get("disciplines"):
                    plan_message += "## Disciplines\n"
                    for discipline in plan.get("disciplines", []):
                        plan_message += f"- {discipline}\n"
                    plan_message += "\n"

                # Add phases if present
                if plan.get("phases"):
                    plan_message += "## Training Phases\n"
                    for i, phase in enumerate(plan.get("phases", []), 1):
                        plan_message += (
                            f"\n### Phase {i}: {phase.get('name', 'Phase')}\n"
                        )
                        plan_message += (
                            f"**Duration:** {phase.get('duration', 'N/A')}\n"
                        )
                        plan_message += f"**Focus:** {phase.get('focus', 'N/A')}\n"
                        if phase.get("description"):
                            plan_message += f"\n{phase.get('description')}\n"

                # Add example sessions if present
                if plan.get("exampleSessions"):
                    plan_message += "\n## Example Training Sessions\n"
                    for session in plan.get("exampleSessions", []):
                        plan_message += f"\n### {session.get('name', 'Session')}\n"
                        plan_message += f"**Type:** {session.get('type', 'N/A')}\n"
                        plan_message += (
                            f"**Duration:** {session.get('duration', 'N/A')}\n"
                        )
                        if session.get("description"):
                            plan_message += f"\n{session.get('description')}\n"
                        if session.get("mainSet"):
                            plan_message += (
                                f"\n**Main Set:** {session.get('mainSet')}\n"
                            )

                plan_message += "\n---\n\n✅ Your plan has been generated! You can now start your training journey."

                # Stream the plan message as tokens
                # Split into chunks for streaming effect
                chunk_size = 50  # Characters per chunk
                for i in range(0, len(plan_message), chunk_size):
                    chunk = plan_message[i : i + chunk_size]
                    yield self._format_stream_message(
                        "token",
                        {
                            "agent": "Athlea",
                            "content": chunk,
                            "timestamp": self._get_current_timestamp(),
                        },
                    )
                    # Small delay for streaming effect
                    await asyncio.sleep(0.02)

                logger.info(f"✅ STREAMING: Plan message sent as tokens")

            # Handle interrupts (need more input)
            if result.get("needs_input") or result.get("requires_input"):
                yield self._format_stream_message(
                    "needs_input",
                    {
                        "type": "interrupt",
                        "message": "I need more information to continue",
                        "stage": result.get("onboarding_stage", "unknown"),
                    },
                )

            # Send completion status with enhanced adaptive planning information
            completion_message = "Processing completed"
            completion_type = "general_completion"

            if result.get("onboarding_stage") == "complete":
                completion_message = "Onboarding completed successfully!"
                completion_type = "onboarding_complete"

                # Also send a final sidebar update with the complete plan
                if "sidebar_data" in result and result["sidebar_data"]:
                    # Ensure the sidebar data includes the generated plan
                    final_sidebar_data = result["sidebar_data"]
                    if "generated_plan" in result and result["generated_plan"]:
                        if isinstance(final_sidebar_data, dict):
                            final_sidebar_data["generated_plan"] = result[
                                "generated_plan"
                            ]
                            final_sidebar_data["current_stage"] = "complete"

                    # Convert to frontend format
                    frontend_sidebar_data = self._convert_sidebar_data_to_frontend(
                        final_sidebar_data
                    )

                    yield self._format_stream_message(
                        "sidebar_update",
                        {
                            "sidebarData": frontend_sidebar_data,
                            "timestamp": self._get_current_timestamp(),
                        },
                    )

            elif result.get("has_enough_info"):
                completion_message = "Information gathering completed"
                completion_type = "info_complete"
            elif result.get("generated_sessions"):
                completion_message = "Weekly training sessions generated!"
                completion_type = "sessions_complete"
            elif result.get("coach_coordination_notes"):
                completion_message = "Coach coordination completed"
                completion_type = "coordination_complete"

            yield self._format_stream_message(
                "status",
                {
                    "status_type": completion_type,  # Renamed to avoid conflict with main type field
                    "message": completion_message,
                    "stage": result.get("onboarding_stage", "unknown"),
                    "timestamp": self._get_current_timestamp(),
                    "has_sessions": bool(result.get("generated_sessions")),
                    "has_coordination": bool(result.get("coach_coordination_notes")),
                },
            )

            # Always send a complete event at the end
            if result.get("onboarding_stage") == "complete":
                yield self._format_stream_message(
                    "complete",
                    {
                        "message": "Stream completed",
                        "timestamp": self._get_current_timestamp(),
                    },
                )

        except Exception as e:
            logger.error(f"Error streaming graph result: {e}")
            yield self._format_stream_message(
                "error",
                {
                    "type": "result_stream_error",
                    "message": "Error streaming result",
                    "error": str(e),
                },
            )

    async def _stream_adaptive_planning_updates(self, result: Dict[str, Any]):
        """Stream adaptive weekly planning specific updates."""
        # Stream weekly plan generation progress
        if "generated_sessions" in result and result["generated_sessions"]:
            yield self._format_stream_message(
                "weekly_sessions_generated",
                {
                    "sessions": result["generated_sessions"],
                    "message": "Weekly training sessions have been generated!",
                    "timestamp": self._get_current_timestamp(),
                },
            )

        # Stream current week information
        if "current_week" in result:
            yield self._format_stream_message(
                "current_week_update",
                {
                    "week": result["current_week"],
                    "message": f"Planning week {result['current_week']}",
                    "timestamp": self._get_current_timestamp(),
                },
            )

        # Stream coach coordination updates
        if "coach_coordination_notes" in result and result["coach_coordination_notes"]:
            yield self._format_stream_message(
                "coach_coordination",
                {
                    "coordination_notes": result["coach_coordination_notes"],
                    "message": "Coaches have coordinated your training plan",
                    "timestamp": self._get_current_timestamp(),
                },
            )

        # Stream fitness profile updates
        if "fitness_profile" in result and result["fitness_profile"]:
            yield self._format_stream_message(
                "fitness_profile_created",
                {
                    "profile": {
                        "user_id": result["fitness_profile"].user_id,
                        "equipment_available": result[
                            "fitness_profile"
                        ].equipment_available,
                        "time_constraints": result["fitness_profile"].time_constraints,
                    },
                    "message": "Your fitness profile has been created",
                    "timestamp": self._get_current_timestamp(),
                },
            )

        # Stream adaptation triggers
        if "adaptation_triggers" in result and result["adaptation_triggers"]:
            yield self._format_stream_message(
                "adaptation_triggers",
                {
                    "triggers": result["adaptation_triggers"],
                    "message": "Adaptive learning triggers activated",
                    "timestamp": self._get_current_timestamp(),
                },
            )

        # Stream weekly coordination notes
        if (
            "weekly_coordination_notes" in result
            and result["weekly_coordination_notes"]
        ):
            yield self._format_stream_message(
                "weekly_coordination",
                {
                    "notes": result["weekly_coordination_notes"],
                    "message": "Weekly coordination completed",
                    "timestamp": self._get_current_timestamp(),
                },
            )

        # Stream pause/resume state
        if "paused_at" in result and result["paused_at"]:
            yield self._format_stream_message(
                "training_paused",
                {
                    "paused_at": (
                        result["paused_at"].isoformat() if result["paused_at"] else None
                    ),
                    "message": "Training has been paused",
                    "timestamp": self._get_current_timestamp(),
                },
            )

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format for real-time updates."""
        from datetime import datetime, timezone

        return datetime.now(timezone.utc).isoformat()

    def _convert_sidebar_data_to_frontend(self, sidebar_data: Any) -> Dict[str, Any]:
        """Convert Python sidebar_data to frontend format with camelCase fields."""
        if not sidebar_data:
            return sidebar_data

        # Convert Pydantic model to dict if needed
        if hasattr(sidebar_data, "model_dump"):
            # Pydantic v2
            sidebar_dict = sidebar_data.model_dump()
        elif hasattr(sidebar_data, "dict"):
            # Pydantic v1 (fallback)
            sidebar_dict = sidebar_data.dict()
        elif isinstance(sidebar_data, dict):
            sidebar_dict = sidebar_data
        else:
            # If it's neither, return as is
            return sidebar_data

        # Create a copy to avoid modifying the original
        frontend_data = {}

        # Map Python field names to frontend field names
        field_mapping = {
            "current_stage": "currentStage",
            "summary_items": "summaryItems",
            "generated_plan": "generatedPlan",
            "sport_suggestions": "sportSuggestions",
            "selected_sport": "selectedSport",
            "selected_sports": "selectedSports",
            "weekly_plan": "weeklyPlan",
            "uploaded_documents": "uploadedDocuments",
            "key_insights": "keyInsights",
        }

        for python_field, frontend_field in field_mapping.items():
            if python_field in sidebar_dict:
                frontend_data[frontend_field] = sidebar_dict[python_field]

        # Handle the goals field specially since it's a nested object
        if "goals" in sidebar_dict and sidebar_dict["goals"]:
            frontend_data["goals"] = sidebar_dict["goals"]

        # Copy over any other fields that don't need conversion
        for key, value in sidebar_dict.items():
            if key not in field_mapping and key != "goals" and key not in frontend_data:
                frontend_data[key] = value

        return frontend_data

    def _format_stream_message(self, event_type: str, data: Dict[str, Any]) -> str:
        """Format a message for streaming with enhanced metadata."""
        # Format message to match frontend expectations
        # Frontend expects: {"type": "event_type", ...data_fields}
        formatted_message = {
            "type": event_type,
            **data,  # Spread the data fields directly into the message
            "event_id": self._generate_event_id(),
            "server_timestamp": self._get_current_timestamp(),
        }
        return json.dumps(formatted_message) + "\n"

    def _generate_event_id(self) -> str:
        """Generate unique event ID for streaming."""
        import uuid

        return str(uuid.uuid4())[:8]

    def _merge_sidebar_data(
        self, existing_data: Dict[str, Any], new_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Intelligently merge existing sidebar data with new data, preserving the most complete information.
        """
        merged = {}

        # Always prefer existing data for these fields if they're more complete
        preserve_if_existing = [
            "summary_items",
            "uploaded_documents",
            "key_insights",
            "selected_sports",
            "goals",
            "sport_suggestions",
        ]

        for field in preserve_if_existing:
            existing_value = existing_data.get(field)
            new_value = new_data.get(field)

            # Use existing if it has more content
            if existing_value and new_value:
                if isinstance(existing_value, list) and isinstance(new_value, list):
                    merged[field] = (
                        existing_value
                        if len(existing_value) >= len(new_value)
                        else new_value
                    )
                elif isinstance(existing_value, dict) and isinstance(new_value, dict):
                    merged[field] = (
                        existing_value
                        if len(existing_value) >= len(new_value)
                        else new_value
                    )
                else:
                    merged[field] = existing_value  # Prefer existing for other types
            elif existing_value:
                merged[field] = existing_value
            elif new_value:
                merged[field] = new_value

        # For other fields, prefer new data (like current_stage, generated_plan)
        for field, value in new_data.items():
            if field not in preserve_if_existing:
                merged[field] = value

        # Add any remaining fields from existing data
        for field, value in existing_data.items():
            if field not in merged:
                merged[field] = value

        logger.info(
            f"[MERGE] Merged sidebar data - preserved {len(preserve_if_existing)} key fields"
        )
        return merged


@app.post("/onboarding/stream")
async def stream_onboarding_session(request: OnboardingRequest):
    """
    Stream an onboarding session with real-time updates.

    Supports:
    - New onboarding conversations
    - Resume from interrupts (HITL)
    - Real-time streaming of responses
    - Sidebar data updates
    - Plan generation
    """
    try:
        # Validate request
        if not request.user_id:
            raise HTTPException(status_code=400, detail="user_id is required")

        if not request.message:
            raise HTTPException(status_code=400, detail="message is required")

        # Create streaming response handler
        response_handler = StreamingOnboardingResponse(request)

        # Return streaming response
        return StreamingResponse(
            response_handler.stream_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in stream_onboarding_session: {e}")
        raise HTTPException(
            status_code=500, detail="Internal server error during onboarding"
        )


@app.get("/onboarding/status/{user_id}")
async def get_onboarding_status(user_id: str, thread_id: Optional[str] = None):
    """
    Get the current onboarding status for a user.

    Returns:
    - Current onboarding stage
    - Whether onboarding is complete
    - Last conversation state
    """
    try:
        # Get graph
        graph_factory = get_graph_factory()
        graph = await graph_factory.create_graph(
            "onboarding", checkpointer_type="memory"
        )

        # Prepare config
        config_thread_id = thread_id or f"onboarding_{user_id}"
        config = {"configurable": {"thread_id": config_thread_id}}

        # Get current state
        snapshot = await graph.aget_state(config)

        if not snapshot.values:
            return {
                "status": "not_started",
                "stage": "initial",
                "has_enough_info": False,
                "needs_input": True,
                "message": "Onboarding not started",
            }

        state = snapshot.values

        # Enhanced status with adaptive weekly planning information
        status_info = {
            "status": (
                "in_progress"
                if not state.get("onboarding_stage") == "complete"
                else "completed"
            ),
            "stage": state.get("onboarding_stage", "unknown"),
            "has_enough_info": state.get("has_enough_info", False),
            "needs_input": state.get("needs_input", True),
            "requires_input": state.get("requires_input", False),
            "sidebar_data": state.get("sidebar_data"),
            "generated_plan": state.get("generated_plan"),
            "user_id": user_id,
            "thread_id": config_thread_id,
            "last_updated": datetime.now().isoformat(),
        }

        # Add adaptive weekly planning specific status
        status_info["adaptive_planning"] = {
            "has_sessions": bool(state.get("generated_sessions")),
            "has_fitness_profile": bool(state.get("fitness_profile")),
            "has_coordination_notes": bool(state.get("coach_coordination_notes")),
            "current_week": state.get("current_week", 1),
            "paused_at": (
                state.get("paused_at").isoformat() if state.get("paused_at") else None
            ),
            "adaptation_triggers": state.get("adaptation_triggers", []),
            "training_ready": bool(
                state.get("generated_plan")
                and state.get("onboarding_stage") == "complete"
            ),
        }

        # Add session count if sessions exist
        if state.get("generated_sessions"):
            sessions = state["generated_sessions"]
            if isinstance(sessions, dict):
                session_count = sum(
                    len(day_sessions) for day_sessions in sessions.values()
                )
                status_info["adaptive_planning"][
                    "total_sessions_generated"
                ] = session_count
                status_info["adaptive_planning"]["training_days"] = len(
                    [day for day, sessions in sessions.items() if sessions]
                )

        return status_info

    except Exception as e:
        logger.error(f"Error getting onboarding status: {e}")
        raise HTTPException(
            status_code=500, detail="Error retrieving onboarding status"
        )


@app.post("/onboarding/reset/{user_id}")
async def reset_onboarding(user_id: str, thread_id: Optional[str] = None):
    """
    Reset onboarding for a user (clear conversation state and MongoDB data).
    """
    try:
        # Get graph
        graph_factory = get_graph_factory()
        graph = await graph_factory.create_graph(
            "onboarding", checkpointer_type="memory"
        )

        # Prepare config
        config_thread_id = thread_id or f"onboarding_{user_id}"
        config = {"configurable": {"thread_id": config_thread_id}}

        # Clear state by creating a new initial state
        initial_state = create_initial_onboarding_state(user_id)

        # Update the state to reset
        await graph.aupdate_state(config, initial_state)

        # CRITICAL: Also clear MongoDB persistent data to prevent stage conflicts
        await _clear_mongodb_onboarding_data(user_id)

        return {
            "status": "reset",
            "message": "Onboarding has been reset",
            "user_id": user_id,
            "thread_id": config_thread_id,
        }

    except Exception as e:
        logger.error(f"Error resetting onboarding: {e}")
        raise HTTPException(status_code=500, detail="Error resetting onboarding")


async def _clear_mongodb_onboarding_data(user_id: str):
    """Clear onboarding data from MongoDB to prevent conflicts"""
    try:
        import os
        from pymongo import MongoClient
        import asyncio

        mongodb_uri = os.getenv("MONGODB_URI")
        if not mongodb_uri:
            logger.warning("[Reset] No MONGODB_URI configured, skipping MongoDB clear")
            return

        # Skip in dev mode
        dev_mode = os.getenv("ONBOARDING_DEV_MODE", "false").lower() == "true"
        if dev_mode:
            logger.info(f"[DEV MODE] Skipping MongoDB clear for user: {user_id}")
            return

        mongo_client = MongoClient(mongodb_uri)
        db = mongo_client["AthleaUserData"]
        users_collection = db["users"]

        logger.info(f"[Reset] Clearing MongoDB onboarding data for user: {user_id}")

        # CRITICAL: Clear ALL onboarding-related fields in MongoDB to prevent stage conflicts
        fields_to_clear = {
            "onboarding_sidebar_data": "",
            "onboarding_stage": "",  # This is the main field causing the infinite loop
            "onboarding_thread_id": "",
            "onboarding_last_updated": "",
            "onboarding_stage_updated": "",
        }

        logger.info(f"[Reset] Clearing MongoDB fields: {list(fields_to_clear.keys())}")

        result = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: users_collection.update_one(
                {"user_id": user_id},
                {"$unset": fields_to_clear},
            ),
        )

        if result.modified_count > 0:
            logger.info(
                f"[Reset] ✅ Successfully cleared MongoDB onboarding data for user: {user_id}"
            )
        else:
            logger.info(
                f"[Reset] No MongoDB onboarding data found to clear for user: {user_id}"
            )

        mongo_client.close()

    except Exception as e:
        logger.error(
            f"[Reset] Error clearing MongoDB onboarding data for user {user_id}: {e}"
        )
        # Don't raise the exception as this shouldn't fail the entire reset operation


@app.get("/onboarding/health")
async def health_check():
    """Health check endpoint for onboarding service."""
    try:
        # Test graph initialization
        graph_factory = get_graph_factory()
        graph = await graph_factory.create_graph(
            "onboarding", checkpointer_type="memory"
        )

        return {
            "status": "healthy",
            "service": "onboarding_stream",
            "message": "Onboarding service is operational",
            "graph_initialized": graph is not None,
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "onboarding_stream",
            "message": "Onboarding service is not operational",
            "error": str(e),
        }


@app.middleware("http")
async def add_cors_header(request: Request, call_next):
    """Add CORS headers to all responses."""
    response = await call_next(request)
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    return response
