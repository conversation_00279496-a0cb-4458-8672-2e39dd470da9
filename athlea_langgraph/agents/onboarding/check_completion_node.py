import logging
from typing import Any, Dict

from langchain_core.messages import BaseMessage, SystemMessage, HumanMessage, AIMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import OnboardingState
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class CheckCompletionNode:
    """Node for intelligently checking if enough information has been gathered"""

    def __init__(self):
        # Low temperature for more consistent analysis
        self.llm = create_azure_chat_openai(temperature=0.1)
        self.completion_analysis_prompt = None  # Will be loaded lazily

        # Fallback prompt for dynamic analysis
        self.fallback_analysis_prompt = """You are an expert fitness coach analyst. Analyze this conversation between a fitness coach and a user during onboarding.

Your task is to intelligently determine:
1. What essential information has been provided by the user
2. What critical information is still missing for creating a personalized fitness plan
3. Whether the user seems ready to proceed with plan generation

INFORMATION CATEGORIES for fitness planning:
- **Fitness Goals**: What they want to achieve (can be general like "get fit" or specific like "run 10K")
- **Experience Level**: Their background/skill level in chosen activities
- **Time Availability**: How much time they can commit to training  
- **Equipment/Environment**: What they have access to for training
- **Priorities/Focus**: What matters most to them or any constraints

INTELLIGENT ANALYSIS GUIDELINES:
1. **Be contextually smart**: If someone says "I want to run a 10K as a beginner focused on not getting injured", that's sufficient goal information - don't demand specific finish times for beginners
2. **Consider experience level**: Beginners need less detail than advanced athletes
3. **Look for implicit information**: Experience level often implied by how they describe their goals
4. **Recognize readiness signals**: Phrases like "I'm ready to start", "let's do this", "I'm committed" indicate readiness
5. **Don't be perfectionist**: 80%+ information coverage is usually sufficient to create a good plan
6. **Quality over quantity**: Good information in 4 categories beats sparse info in 5 categories

SUFFICIENT INFORMATION THRESHOLDS:
- **Beginner users**: Goals + Experience + Time OR Equipment = SUFFICIENT (3/5 categories with good quality)
- **Intermediate users**: Need more specificity across categories  
- **Advanced users**: Need detailed information across most categories

USER READINESS INDICATORS:
- Expressing eagerness to start ("ready to begin", "let's do this")
- Providing comprehensive information in recent messages
- Asking about next steps or when to start
- Confirming details and showing commitment

Respond ONLY with a JSON object in this exact format:
{
  "has_enough_info": boolean,
  "completion_percentage": number (0-100),
  "found_categories": ["category1", "category2"],
  "missing_categories": ["category3", "category4"],
  "next_to_ask": "category_name",
  "next_question": "specific question to ask",
  "user_ready": boolean,
  "reasoning": "brief explanation of your analysis"
}

Where:
- has_enough_info: true if you have sufficient information to create a personalized plan (be smart about this!)
- completion_percentage: realistic estimate based on information quality and user experience level
- found_categories: categories where sufficient information was provided
- missing_categories: categories that would be helpful but aren't strictly required yet
- next_to_ask: the most important missing category (only if has_enough_info is false)
- next_question: a natural question to gather more info (only if has_enough_info is false)
- user_ready: whether the user seems ready to proceed with plan generation
- reasoning: your contextual analysis of why this is or isn't sufficient information

Be intelligent, contextual, and practical - focus on having enough good information to help the user, not perfect information."""

        # Schema for structured LLM response
        self.completion_analysis_schema = {
            "title": "CompletionAnalysis",
            "description": "Analysis of conversation completeness for fitness plan generation",
            "type": "object",
            "properties": {
                "has_enough_info": {
                    "type": "boolean",
                    "description": "Whether sufficient information has been gathered to proceed with plan generation",
                },
                "completion_percentage": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 100,
                    "description": "Estimated percentage of required information gathered",
                },
                "found_categories": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Categories where sufficient information was provided",
                },
                "missing_categories": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Categories that still need more information",
                },
                "next_to_ask": {
                    "type": "string",
                    "description": "The most important missing category to focus on next",
                },
                "next_question": {
                    "type": "string",
                    "description": "A natural, conversational question to ask for the missing information",
                },
                "user_ready": {
                    "type": "boolean",
                    "description": "Whether the user seems ready to proceed with plan generation",
                },
                "reasoning": {
                    "type": "string",
                    "description": "Brief explanation of the analysis and decisions made",
                },
            },
            "required": [
                "has_enough_info",
                "completion_percentage",
                "found_categories",
                "missing_categories",
                "next_to_ask",
                "next_question",
                "user_ready",
                "reasoning",
            ],
        }

    async def _get_completion_analysis_prompt(self) -> str:
        """Load the completion analysis prompt lazily"""
        if self.completion_analysis_prompt is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/completion_check"
                )
                self.completion_analysis_prompt = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding completion analysis prompt from file"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load onboarding completion analysis prompt: {e}"
                )
                self.completion_analysis_prompt = self.fallback_analysis_prompt

        return self.completion_analysis_prompt

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """
        Intelligently analyze conversation completeness and provide detailed feedback.
        Returns specific guidance on what information is missing and what to ask next.
        """
        logger.info("[Node: checkCompletion] Starting intelligent completion analysis")

        # Get both current turn messages and full conversation history
        current_messages = state.get("messages", [])
        conversation_history = state.get("conversation_history", [])

        # Convert conversation history to LangChain message format if needed
        history_messages = []
        if conversation_history:
            logger.info(
                f"[Node: checkCompletion] Found {len(conversation_history)} messages in conversation history"
            )
            for msg in conversation_history:
                if isinstance(msg, dict):
                    role = msg.get("role", "")
                    content = msg.get("content", "")
                    if role == "human" and content:
                        history_messages.append(HumanMessage(content=content))
                    elif role == "assistant" and content:
                        history_messages.append(AIMessage(content=content))
                elif hasattr(msg, "content"):
                    # Already a LangChain message
                    history_messages.append(msg)

        # Combine conversation history with current turn messages for complete context
        messages = history_messages + current_messages

        logger.info(
            f"[Node: checkCompletion] Analyzing {len(history_messages)} history + {len(current_messages)} current = {len(messages)} total messages"
        )

        # Filter out system messages for analysis
        user_messages = [msg for msg in messages if not isinstance(msg, SystemMessage)]

        if not user_messages:
            logger.info("[Node: checkCompletion] No user messages found")
            return {
                "has_enough_info": False,
                "completion_feedback": {
                    "missing_categories": [
                        "fitness_goals",
                        "experience_level",
                        "time_commitment",
                        "equipment_access",
                        "priorities_focus",
                    ],
                    "next_to_ask": "fitness_goals",
                    "next_question": "What are your main athletic goals right now, and which sports or activities are you focusing on?",
                    "completion_percentage": 0,
                    "user_ready": False,
                    "reasoning": "No conversation content to analyze",
                },
            }

        try:
            # Get the dynamic analysis prompt
            analysis_prompt = await self._get_completion_analysis_prompt()

            # Create conversation context for analysis
            conversation_text = "\n".join(
                [
                    f"{'User' if isinstance(msg, HumanMessage) else 'Coach'}: {msg.content}"
                    for msg in user_messages
                ]
            )

            # Configure LLM with structured output for consistent analysis
            structured_llm = self.llm.with_structured_output(
                self.completion_analysis_schema
            ).with_config({"tags": ["internal", "check_completion", "no_stream"]})

            # Perform intelligent analysis
            logger.info(
                "[Node: checkCompletion] Calling LLM for intelligent completion analysis"
            )

            analysis_result = await structured_llm.ainvoke(
                [
                    SystemMessage(content=analysis_prompt),
                    HumanMessage(
                        content=f"Analyze this conversation:\n\n{conversation_text}"
                    ),
                ]
            )

            logger.info(f"[Node: checkCompletion] === INTELLIGENT ANALYSIS RESULTS ===")
            logger.info(
                f"[Node: checkCompletion] Completion: {analysis_result.get('completion_percentage', 0):.0f}%"
            )
            logger.info(
                f"[Node: checkCompletion] Has enough info: {analysis_result.get('has_enough_info', False)}"
            )
            logger.info(
                f"[Node: checkCompletion] Found categories: {analysis_result.get('found_categories', [])}"
            )
            logger.info(
                f"[Node: checkCompletion] Missing categories: {analysis_result.get('missing_categories', [])}"
            )
            logger.info(
                f"[Node: checkCompletion] Next to ask: {analysis_result.get('next_to_ask', 'unknown')}"
            )
            logger.info(
                f"[Node: checkCompletion] User ready: {analysis_result.get('user_ready', False)}"
            )
            logger.info(
                f"[Node: checkCompletion] Reasoning: {analysis_result.get('reasoning', 'No reasoning provided')}"
            )

            # Create detailed completion feedback for the information gatherer
            completion_feedback = {
                "missing_categories": analysis_result.get("missing_categories", []),
                "found_categories": analysis_result.get("found_categories", []),
                "next_to_ask": analysis_result.get("next_to_ask"),
                "next_question": analysis_result.get("next_question"),
                "completion_percentage": analysis_result.get(
                    "completion_percentage", 0
                ),
                "user_ready": analysis_result.get("user_ready", False),
                "reasoning": analysis_result.get("reasoning", ""),
                "analysis_type": "llm_based",  # Flag to indicate this is intelligent analysis
            }

            return {
                "has_enough_info": analysis_result.get("has_enough_info", False),
                "completion_feedback": completion_feedback,
            }

        except Exception as error:
            logger.error(
                f"[Node: checkCompletion] Error during intelligent analysis: {error}"
            )

            # Fallback to basic analysis
            logger.info("[Node: checkCompletion] Falling back to basic analysis")
            return {
                "has_enough_info": False,
                "completion_feedback": {
                    "missing_categories": [
                        "fitness_goals",
                        "experience_level",
                        "time_commitment",
                        "equipment_access",
                        "priorities_focus",
                    ],
                    "next_to_ask": "fitness_goals",
                    "next_question": "What are your main athletic goals right now, and which sports or activities are you focusing on?",
                    "completion_percentage": 25,  # Conservative estimate
                    "user_ready": False,
                    "reasoning": f"Analysis failed ({str(error)}), using conservative fallback",
                    "analysis_type": "fallback",
                },
            }


# Create the node instance
check_completion_node = CheckCompletionNode()
