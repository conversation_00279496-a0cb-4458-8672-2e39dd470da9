import logging
from typing import Any, Dict, List
import asyncio
import os
from datetime import datetime

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.runnables import RunnableLambda
from pymongo import MongoClient

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import (
    COMMON_SPORT_SUGGESTIONS,
    VALID_SPORT_VALUES,
    OnboardingState,
    SidebarStateData,
    SportSuggestion,
    SummaryItem,
    UserGoals,
)
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)

# Add this after the imports section and before the VALID_SPORT_VALUES
# Required categories for completion check (matching the completion check node)
REQUIRED_ONBOARDING_CATEGORIES = {
    "fitness_goals": {
        "name": "Specific Fitness Goals",
        "keywords": [
            "goals",
            "objectives",
            "targets",
            "improve",
            "build",
            "lose",
            "gain",
        ],
        "ask_prompt": "What are your main athletic goals right now, and which sports or activities are you focusing on?",
    },
    "experience_level": {
        "name": "Experience Level",
        "keywords": [
            "experience",
            "beginner",
            "intermediate",
            "advanced",
            "years",
            "new to",
            "familiar",
        ],
        "ask_prompt": "Could you tell me about your experience level in your chosen sports? Are you a beginner, intermediate, or advanced athlete?",
    },
    "time_commitment": {
        "name": "Time Availability",
        "keywords": [
            "days",
            "week",
            "hours",
            "minutes",
            "time",
            "schedule",
            "available",
            "dedicate",
        ],
        "ask_prompt": "How many days per week can you dedicate to training, and roughly how much time do you have for each session?",
    },
    "equipment_access": {
        "name": "Equipment Access",
        "keywords": [
            "gym",
            "home",
            "equipment",
            "weights",
            "treadmill",
            "bike",
            "outdoors",
            "access",
        ],
        "ask_prompt": "What equipment do you have access to? Do you train at a gym, at home, outdoors, or a combination?",
    },
    "priorities_seasonality": {
        "name": "Priorities/Focus Areas",
        "keywords": [
            "priority",
            "focus",
            "season",
            "important",
            "primary",
            "main",
            "constraints",
        ],
        "ask_prompt": "What should be the main focus of your training? Are there any seasonal considerations or specific areas you'd like to prioritize?",
    },
}


class InformationGathererNode:
    """Node for gathering user information during onboarding"""

    def __init__(self, dev_mode: bool = None):
        """Initialize the Information Gatherer Node with Azure OpenAI client"""
        logger.info("Initializing InformationGathererNode")

        # Initialize Azure OpenAI LLM
        self.llm = create_azure_chat_openai(temperature=0.3)

        # MongoDB client and configuration
        self.mongodb_uri = os.getenv("MONGODB_URI")
        self._mongo_client = None

        # Development mode flag - FORCE PRODUCTION MODE TO SAVE DATA
        # Temporarily hardcode to False to ensure data is saved
        self.dev_mode = False
        logger.info(
            "[FORCED PROD MODE] Onboarding persistence enabled (dev_mode hardcoded to False)"
        )

        # Initialize prompt placeholders
        self.system_prompt = None
        self.goal_extraction_prompt = None
        self.summary_extraction_prompt = None

        # Fallback prompts
        self.fallback_system_prompt = """You are Athlea, a comprehensive AI fitness coach specializing in multi-sport athlete development.

Analyze the conversation history. The last message is the most recent user input.

**Important: If the user has uploaded fitness files, prioritize analyzing that data to understand their workout routine instead of asking manual questions.**

**Initial Greeting (If history is very short, e.g., 1-2 messages):**
- Adopt an enthusiastic, welcoming tone (under 150 words).
- Briefly introduce yourself as their AI fitness coach for multi-sport athletes.
- Explain that the onboarding process will personalize their experience.
- Mention that they can upload their fitness data/workout files for automatic analysis.
- Ask an open-ended question about their athletic goals and the sports/activities they pursue.
- End with an encouraging statement.

**Ongoing Information Gathering (If history is longer):**
- Your goal is to gather information needed to create a personalized fitness plan by asking questions sequentially.
- **File Upload Priority**: If uploaded fitness data is available, use it to automatically determine workout routines and experience levels instead of asking manual questions.
- Required Information Categories (adapt based on sports mentioned):
    1.  **Primary Fitness Goal(s):** Specific, measurable goals for EACH sport/activity mentioned.
    2.  **Experience Level:** Overall and potentially per sport if relevant (can be determined from uploaded data).
    3.  **Time Commitment:** Days per week, duration per session, time of day preference.
    4.  **Equipment Access:** Specifics relevant to their sports.
    5.  **Priorities/Connections:** How goals/sports relate, seasonal constraints, main focus.

- **Response Rules for Ongoing Gathering:**
    - **If uploaded fitness data is available:** Use that data to understand their current routine and focus questions on goals and preferences rather than asking about their existing workout routine.
    - **If ANY information IS missing:**
        1. Briefly acknowledge the user's last message.
        2. Concisely summarize what you've learned *so far* across all relevant categories/sports.
        3. Ask a natural, conversational question to gather the *next single piece* of missing information. Be specific based on the sport if applicable:
           - Tennis: Ask about specific skills (backhand, serve, etc.).
           - Swimming: Inquire about seasonal availability and specific strokes.
           - Running: Explore distance preferences and performance targets.
           - Strength Training: Ask about specific lifts and strength goals.
           - Other Sports: Ask relevant goal/performance questions.
    - **If ALL relevant information SEEMS present:** Provide a polite closing statement indicating you have the necessary details and the process will continue. Example: "Thanks! It sounds like I have a good understanding of your goals and background across your sports."
    - **If the user asks a question or goes off-topic:** Briefly acknowledge/answer it, then gently guide them back by asking for the next piece of required information or provide the closing statement if all info seems present.
- Maintain a collaborative, detail-oriented tone."""

        self.fallback_goal_extraction_prompt = """Analyze the user messages in the provided conversation history. Identify and extract ALL specific fitness goals mentioned by the user, noting the sport/activity if specified.

IMPORTANT:
- Focus ONLY on what the USER has explicitly stated as their goal(s).
- Do not include general statements or questions from the coach.
- Look for phrases like "I want to...", "My goal for [sport] is...", etc.

Examples of fitness goals:
- Build muscle
- Lose weight
- Run a faster 5k (Running)
- Improve backhand consistency (Tennis)
- Increase bench press max (Strength Training)
- Swim 1500m freestyle efficiently (Swimming)

Return ONLY the identified goals, separated by commas. If the sport is clearly mentioned with the goal, include it in parentheses.
For example: "Build muscle, Lose weight, Run a faster 5k (Running), Improve backhand consistency (Tennis)"

If no specific fitness goals are mentioned by the user, respond ONLY with: "NO_GOALS"."""

        self.fallback_summary_extraction_prompt = """You are a fitness coach assistant analyzing a conversation history between a coach and a user during onboarding.
Your task is to extract ALL key pieces of information the USER has provided relevant to creating a fitness plan, especially for a multi-sport athlete.

CRITICAL: Focus on what the USER has stated, not what the coach suggests or asks about.

Look for these key categories:
1. **Sports/Activities**: Any sport or physical activity the user mentions they do, practice, or are interested in
2. **Fitness Goals**: Specific objectives like weight loss, muscle building, performance improvement
3. **Experience Level**: How long they've been doing activities, their skill level
4. **Time Availability**: How often they can train, when they prefer to train
5. **Equipment Access**: What equipment or facilities they have available
6. **Physical Considerations**: Injuries, limitations, medical conditions
7. **Preferences**: Training style preferences, likes/dislikes
8. **Equipment Access**: Available equipment (gym, home setup, etc.)

Output ONLY a valid JSON object. This object MUST contain a single key named "summaryList".
The value of "summaryList" MUST be an array of objects. Each object MUST have: { "category": string, "details": string, "isImportant": boolean }

- "category": A concise category name from the list above or similar
- "details": A SINGLE string containing the specific detail(s) for that category
- "isImportant": Set to true for: Sports/Activities, Medical Conditions, Injuries, Dietary Restrictions, Major Events, Equipment Access. Set to false for preferences and general information.

Example Output:
{
"summaryList": [
{ "category": "Sports/Activities", "details": "Cycling, Running, Strength Training", "isImportant": true },
{ "category": "Fitness Goals", "details": "Improve overall fitness and performance", "isImportant": false },
{ "category": "Experience Level", "details": "Intermediate level across all activities", "isImportant": false },
{ "category": "Time Availability", "details": "3-4 times per week, evenings preferred", "isImportant": false }
]
}

IMPORTANT:
- Extract information from the ENTIRE conversation history
- Focus ONLY on information provided by the USER
- If the user mentions ANY sports or activities, always include them in a "Sports/Activities" category
- ONLY include categories where the user has provided SPECIFIC, MEANINGFUL information
- DO NOT create summary items with "Not specified", "Unknown", or similar placeholder text
- DO NOT create empty or very short details (minimum 10 characters of meaningful content)
- If the user has not provided ANY relevant information yet, respond ONLY with: NO_INFO_SHARED"""

        # Define structured output schemas for reliable data extraction

        # Schema for summary extraction
        self.summary_wrapper_schema = {
            "title": "UserInformationSummaryWrapper",
            "description": "A wrapper object containing a list of categorized information provided by the user.",
            "type": "object",
            "properties": {
                "summaryList": {
                    "type": "array",
                    "description": "The list of categorized summary items.",
                    "items": {
                        "type": "object",
                        "properties": {
                            "category": {
                                "type": "string",
                                "description": "The category of information (e.g., Fitness Goals, Availability).",
                            },
                            "details": {
                                "type": "string",
                                "description": "Specific detail(s) provided by the user for this category. Must be meaningful content (minimum 10 characters). Do NOT use 'Not specified', 'Unknown', or similar placeholder text.",
                            },
                            "isImportant": {
                                "type": "boolean",
                                "description": "Whether this information is critical for plan generation (e.g., medical, injuries, primary sport).",
                            },
                        },
                        "required": ["category", "details", "isImportant"],
                    },
                },
            },
            "required": ["summaryList"],
        }

        # Schema for goal extraction
        self.goals_extraction_schema = {
            "title": "GoalExtractionResult",
            "description": "Structured extraction of user fitness goals from conversation",
            "type": "object",
            "properties": {
                "goals": {
                    "type": "array",
                    "description": "List of specific fitness goals mentioned by the user",
                    "items": {
                        "type": "string",
                        "description": "A specific fitness goal (e.g., 'Build muscle', 'Run a 5k')",
                    },
                },
                "exists": {
                    "type": "boolean",
                    "description": "Whether any fitness goals were found in the conversation",
                },
            },
            "required": ["goals", "exists"],
        }

        # Schema for sport extraction
        self.sports_extraction_schema = {
            "title": "SportExtractionResult",
            "description": "Structured extraction of sports/activities mentioned by the user",
            "type": "object",
            "properties": {
                "sports": {
                    "type": "array",
                    "description": "List of sports/activities mentioned by the user",
                    "items": {
                        "type": "string",
                        "enum": [
                            "Running",
                            "Cycling",
                            "Strength Training",
                            "General Fitness",
                            "Swimming",
                            "Tennis",
                        ],
                        "description": "Standardized sport/activity name",
                    },
                },
                "found": {
                    "type": "boolean",
                    "description": "Whether any sports/activities were detected in the conversation",
                },
            },
            "required": ["sports", "found"],
        }

        # Create goal extraction runnable with internal processing tag to exclude from streaming
        self.extract_goals = RunnableLambda(func=self._extract_goals_func).with_config(
            {
                "run_name": "extractGoals",
                "tags": ["internal_processing", "extractGoals"],
            }
        )

        # Create summary extraction runnable with internal processing tag to exclude from streaming
        self.extract_summary = RunnableLambda(self._extract_summary_func).with_config(
            {
                "run_name": "extractSummary",
                "tags": ["internal_processing", "extractSummary"],
            }
        )

        # Create sport extraction runnable with internal processing tag to exclude from streaming
        self.extract_sports = RunnableLambda(
            func=self._extract_sports_func
        ).with_config(
            {
                "run_name": "extractSports",
                "tags": ["internal_processing", "extractSports"],
            }
        )

    async def _get_mongo_client(self):
        """Get or create MongoDB client for persistence"""
        if not self.mongodb_uri:
            logger.warning("[MongoDB] No MONGODB_URI configured, skipping persistence")
            return None

        if not self._mongo_client:
            self._mongo_client = MongoClient(self.mongodb_uri)

        return self._mongo_client

    async def _save_onboarding_sidebar_data(
        self, user_id: str, sidebar_data: SidebarStateData, thread_id: str = None
    ):
        """Persist onboarding sidebar data to MongoDB user document for resume functionality"""
        try:
            # Skip persistence in development mode
            if self.dev_mode:
                logger.info(
                    f"[DEV MODE] Skipping save of onboarding data for user: {user_id}"
                )
                return

            if not user_id or not sidebar_data:
                logger.warning(
                    "[MongoDB] Missing user_id or sidebar_data for persistence"
                )
                return

            mongo_client = await self._get_mongo_client()
            if not mongo_client:
                return

            # Convert Pydantic model to dict for MongoDB storage
            sidebar_dict = (
                sidebar_data.model_dump()
                if hasattr(sidebar_data, "model_dump")
                else sidebar_data.model_dump()
            )

            # Add timestamp for resume logic
            sidebar_dict["last_updated"] = datetime.now().isoformat()

            # Add thread_id if provided for resume functionality
            if thread_id:
                sidebar_dict["thread_id"] = thread_id

            logger.info(
                f"[MongoDB] Saving onboarding sidebar data for user: {user_id}, thread_id: {thread_id}"
            )

            db = mongo_client["AthleaUserData"]
            users_collection = db["users"]

            # Update user document with onboarding sidebar data and thread_id
            update_data = {
                "onboarding_sidebar_data": sidebar_dict,
                "onboarding_last_updated": sidebar_dict["last_updated"],
            }

            # Also save thread_id at the top level for easy access
            if thread_id:
                update_data["onboarding_thread_id"] = thread_id

            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": update_data},
                    upsert=True,
                ),
            )

            if result.modified_count > 0 or result.upserted_id:
                logger.info(
                    f"[MongoDB] Successfully saved onboarding data for user: {user_id}"
                )

                # NEW: Also persist key data to the user's main training profile
                await self._persist_onboarding_to_training_profile(
                    user_id, sidebar_data
                )

            else:
                logger.warning(
                    f"[MongoDB] No changes made when saving onboarding data for user: {user_id}"
                )

        except Exception as error:
            logger.error(
                f"[MongoDB] Error saving onboarding sidebar data for user {user_id}: {error}"
            )

    async def _persist_onboarding_to_training_profile(
        self, user_id: str, sidebar_data: SidebarStateData
    ):
        """
        Persist key onboarding data to the user's main training profile.

        This saves uploaded file names, key insights, goals, and selected sports
        to the user's permanent profile for future reference.
        """
        try:
            logger.info(
                f"[Profile Update] Persisting onboarding data to training profile for user: {user_id}"
            )

            # Build the profile update data
            profile_updates = {}

            # Add uploaded files information
            if (
                sidebar_data.uploaded_documents
                and len(sidebar_data.uploaded_documents) > 0
            ):
                uploaded_files_info = []
                for doc in sidebar_data.uploaded_documents:
                    file_info = f"{doc.get('filename', 'Unknown')} ({doc.get('file_type', 'unknown')})"
                    uploaded_files_info.append(file_info)

                profile_updates["onboarding_uploaded_files"] = ", ".join(
                    uploaded_files_info
                )
                logger.info(
                    f"[Profile Update] Adding uploaded files: {profile_updates['onboarding_uploaded_files']}"
                )

            # Add key insights from uploaded files
            if sidebar_data.key_insights and len(sidebar_data.key_insights) > 0:
                insights_summary = []
                for key, value in sidebar_data.key_insights.items():
                    insights_summary.append(f"{key}: {value}")

                profile_updates["onboarding_file_insights"] = "; ".join(
                    insights_summary[:5]
                )  # Limit to first 5 insights
                logger.info(
                    f"[Profile Update] Adding file insights: {profile_updates['onboarding_file_insights'][:100]}..."
                )

            # Add goals from onboarding
            if (
                sidebar_data.goals
                and sidebar_data.goals.list
                and len(sidebar_data.goals.list) > 0
            ):
                profile_updates["onboarding_goals"] = ", ".join(sidebar_data.goals.list)
                logger.info(
                    f"[Profile Update] Adding goals: {profile_updates['onboarding_goals']}"
                )

            # Add selected sports/activities
            if sidebar_data.selected_sports and len(sidebar_data.selected_sports) > 0:
                profile_updates["primary_sports"] = ", ".join(
                    sidebar_data.selected_sports
                )
                logger.info(
                    f"[Profile Update] Adding sports: {profile_updates['primary_sports']}"
                )

            # Add onboarding completion status
            profile_updates["onboarding_completed"] = "yes"
            profile_updates["onboarding_completion_date"] = datetime.now().isoformat()

            # If we have data to update, call the update-profile API
            if profile_updates:
                try:
                    # Import required for HTTP requests
                    import aiohttp
                    import json

                    # Get the Next.js backend URL - try environment variable first, then fallback
                    backend_url = os.getenv(
                        "NEXT_PUBLIC_BACKEND_URL", "http://localhost:3000"
                    )
                    if backend_url.endswith("/"):
                        backend_url = backend_url.rstrip("/")

                    update_url = f"{backend_url}/api/update-profile"

                    # Prepare the request payload
                    payload = {
                        "message": f"Onboarding completed with the following information: {'; '.join([f'{k}: {v}' for k, v in profile_updates.items()])}",
                        "userId": user_id,
                        "action": "update",
                        "source": "onboarding",
                    }

                    logger.info(
                        f"[Profile Update] Calling update-profile API at {update_url}"
                    )

                    # Make async HTTP request to update-profile endpoint
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            update_url,
                            json=payload,
                            headers={"Content-Type": "application/json"},
                            timeout=aiohttp.ClientTimeout(total=30),
                        ) as response:
                            if response.status == 200:
                                result = await response.json()
                                if result.get("updated"):
                                    logger.info(
                                        f"[Profile Update] Successfully updated training profile for user {user_id}"
                                    )
                                    logger.info(
                                        f"[Profile Update] Updated fields: {result.get('cleanedProfile', {})}"
                                    )
                                else:
                                    logger.info(
                                        f"[Profile Update] No profile update needed: {result.get('message', 'Unknown reason')}"
                                    )
                            else:
                                error_text = await response.text()
                                logger.error(
                                    f"[Profile Update] Failed to update profile. Status: {response.status}, Error: {error_text}"
                                )

                except ImportError:
                    logger.error(
                        "[Profile Update] aiohttp not available, cannot update training profile"
                    )
                except Exception as api_error:
                    logger.error(
                        f"[Profile Update] Error calling update-profile API: {api_error}"
                    )

                    # Fallback: Direct MongoDB update if API call fails
                    logger.info(
                        "[Profile Update] Attempting direct MongoDB update as fallback"
                    )
                    try:
                        mongo_client = await self._get_mongo_client()
                        if mongo_client:
                            db = mongo_client["AthleaUserData"]
                            users_collection = db["users"]

                            # Update the training_profile.general section directly
                            update_operation = {}
                            for key, value in profile_updates.items():
                                update_operation[f"training_profile.general.{key}"] = (
                                    value
                                )

                            result = await asyncio.get_event_loop().run_in_executor(
                                None,
                                lambda: users_collection.update_one(
                                    {"user_id": user_id},
                                    {"$set": update_operation},
                                    upsert=True,
                                ),
                            )

                            if result.modified_count > 0 or result.upserted_id:
                                logger.info(
                                    f"[Profile Update] Successfully updated training profile via direct MongoDB update"
                                )
                            else:
                                logger.warning(
                                    f"[Profile Update] Direct MongoDB update made no changes"
                                )

                    except Exception as mongo_error:
                        logger.error(
                            f"[Profile Update] Fallback MongoDB update also failed: {mongo_error}"
                        )
            else:
                logger.info(
                    "[Profile Update] No onboarding data to persist to training profile"
                )

        except Exception as error:
            logger.error(
                f"[Profile Update] Error persisting onboarding data to training profile: {error}"
            )

    async def _load_onboarding_sidebar_data(self, user_id: str) -> SidebarStateData:
        """Load existing onboarding sidebar data from MongoDB for resume functionality"""
        try:
            # Skip loading in development mode - always return fresh state
            if self.dev_mode:
                logger.info(
                    f"[DEV MODE] Skipping load of onboarding data for user: {user_id} - returning fresh state"
                )
                return SidebarStateData()

            if not user_id:
                logger.warning("[MongoDB] Missing user_id for loading onboarding data")
                return SidebarStateData()

            mongo_client = await self._get_mongo_client()
            if not mongo_client:
                logger.warning("[MongoDB] No MongoDB client available for data loading")
                return SidebarStateData()

            logger.info(
                f"[MongoDB] Loading onboarding sidebar data for user: {user_id}"
            )

            db = mongo_client["AthleaUserData"]
            users_collection = db["users"]

            # Load user document with detailed logging
            user_doc = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: users_collection.find_one(
                    {"user_id": user_id},
                    {"onboarding_sidebar_data": 1, "onboarding_last_updated": 1},
                ),
            )

            logger.info(f"[MongoDB] Query result for user {user_id}: {bool(user_doc)}")

            if user_doc:
                logger.info(f"[MongoDB] User document keys: {list(user_doc.keys())}")
                sidebar_data_exists = user_doc.get("onboarding_sidebar_data")
                logger.info(
                    f"[MongoDB] Sidebar data exists: {bool(sidebar_data_exists)}"
                )

                if sidebar_data_exists:
                    logger.info(
                        f"[MongoDB] Sidebar data type: {type(sidebar_data_exists)}"
                    )
                    logger.info(
                        f"[MongoDB] Sidebar data keys: {list(sidebar_data_exists.keys()) if isinstance(sidebar_data_exists, dict) else 'Not a dict'}"
                    )

                    # Check specific fields to validate data
                    if isinstance(sidebar_data_exists, dict):
                        goals = sidebar_data_exists.get("goals", {})
                        selected_sports = sidebar_data_exists.get("selected_sports", [])
                        summary_items = sidebar_data_exists.get("summary_items", [])
                        logger.info(f"[MongoDB] Found goals: {goals}")
                        logger.info(
                            f"[MongoDB] Found selected_sports: {selected_sports}"
                        )
                        logger.info(
                            f"[MongoDB] Found summary_items count: {len(summary_items)}"
                        )

            if user_doc and user_doc.get("onboarding_sidebar_data"):
                sidebar_dict = user_doc["onboarding_sidebar_data"]
                logger.info(
                    f"[MongoDB] Found existing onboarding data for user: {user_id}"
                )

                # Convert back to Pydantic model with error handling
                try:
                    logger.info(
                        f"[DEBUG] Converting sidebar_dict to SidebarStateData: type={type(sidebar_dict)}"
                    )
                    logger.info(
                        f"[DEBUG] sidebar_dict keys: {list(sidebar_dict.keys()) if isinstance(sidebar_dict, dict) else 'not a dict'}"
                    )

                    result = SidebarStateData(**sidebar_dict)
                    logger.info(
                        f"[DEBUG] Successfully converted to SidebarStateData, type: {type(result)}"
                    )
                    return result
                except Exception as conversion_error:
                    logger.error(
                        f"[MongoDB] Error converting sidebar data to Pydantic model: {conversion_error}"
                    )
                    logger.error(f"[MongoDB] Sidebar dict structure: {sidebar_dict}")
                    logger.error(
                        f"[MongoDB] Returning raw dict instead of SidebarStateData due to conversion error"
                    )
                    return sidebar_dict  # Return the raw dict if conversion fails
            else:
                logger.info(
                    f"[MongoDB] No existing onboarding data found for user: {user_id}"
                )
                return SidebarStateData()

        except Exception as error:
            logger.error(
                f"[MongoDB] Error loading onboarding sidebar data for user {user_id}: {error}"
            )
            return SidebarStateData()

    def cleanup(self):
        """Clean up MongoDB connection"""
        if self._mongo_client:
            try:
                self._mongo_client.close()
                self._mongo_client = None
                logger.info("[MongoDB] Connection closed")
            except Exception as e:
                logger.error(f"[MongoDB] Error closing connection: {e}")

    async def _get_system_prompt(self) -> str:
        """Load the system prompt lazily"""
        if self.system_prompt is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/information_gathering"
                )
                self.system_prompt = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding information gathering prompt from file"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load onboarding information gathering prompt: {e}"
                )
                self.system_prompt = self.fallback_system_prompt
        return self.system_prompt

    async def _get_goal_extraction_prompt(self) -> str:
        """Load the goal extraction prompt lazily"""
        if self.goal_extraction_prompt is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/goal_extraction"
                )
                self.goal_extraction_prompt = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding goal extraction prompt from file"
                )
            except Exception as e:
                logger.error(f"Failed to load onboarding goal extraction prompt: {e}")
                self.goal_extraction_prompt = self.fallback_goal_extraction_prompt
        return self.goal_extraction_prompt

    async def _get_summary_extraction_prompt(self) -> str:
        """Load the summary extraction prompt lazily"""
        if self.summary_extraction_prompt is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/summary_extraction"
                )
                self.summary_extraction_prompt = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded onboarding summary extraction prompt from file"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load onboarding summary extraction prompt: {e}"
                )
                self.summary_extraction_prompt = self.fallback_summary_extraction_prompt
        return self.summary_extraction_prompt

    async def _extract_goals_func(self, messages: List[BaseMessage]) -> Dict[str, Any]:
        """Extract goals from conversation history using structured output"""
        logger.info("[extractGoals] Starting structured goal extraction")

        # Filter for user messages only for explicit goals
        user_messages = [msg for msg in messages if isinstance(msg, HumanMessage)]
        if not user_messages:
            logger.info("[extractGoals] No user messages found in history.")
            return {"goals": [], "exists": False}

        try:
            # Configure LLM with structured output to exclude from main streaming
            internal_llm = self.llm.with_config(
                {
                    "tags": ["internal_processing", "extractGoals"],
                    "run_name": "internal_goal_extraction",
                }
            )
            model_with_structured_output = internal_llm.with_structured_output(
                self.goals_extraction_schema
            )

            # Get the goal extraction prompt (loads lazily if needed)
            goal_prompt = await self._get_goal_extraction_prompt()

            # Update prompt to work with structured output and include sports as goals
            structured_goal_prompt = f"""
{goal_prompt}

ADDITIONAL GUIDANCE:
- If the user mentions sports/activities they practice (e.g., "I do cycling, running, strength training"), 
  treat these as implicit fitness goals for those activities
- Look for both explicit goals ("I want to lose weight") and implicit goals through sports mentioned
- Sports mentioned should be converted to improvement goals (e.g., "Cycling" → "Improve at Cycling")

IMPORTANT: Return a JSON object with the following structure:
{{
    "goals": ["goal1", "goal2", ...],
    "exists": true/false
}}

Where:
- "goals": Array of specific fitness goals mentioned by the user OR sports converted to improvement goals
- "exists": Boolean indicating if any goals were found

If no goals are found, return: {{"goals": [], "exists": false}}
"""

            response = await model_with_structured_output.ainvoke(
                [
                    SystemMessage(content=structured_goal_prompt),
                    *user_messages,
                ]
            )

            logger.info(f"[extractGoals] Structured response: {response}")

            # Handle structured response
            if isinstance(response, dict):
                goals = response.get("goals", [])
                exists = response.get("exists", False)

                logger.info(f"[extractGoals] Extracted {len(goals)} goals: {goals}")
                return {"goals": goals, "exists": exists}
            else:
                logger.error(f"[extractGoals] Unexpected response format: {response}")
                return {"goals": [], "exists": False}

        except Exception as error:
            logger.error(
                f"[extractGoals] Error during structured goal extraction: {error}"
            )
            return {"goals": [], "exists": False}

    async def _extract_sports_func(self, messages: List[BaseMessage]) -> List[str]:
        """Extract sports/activities mentioned by the user using structured output"""
        logger.info(
            "[extractSports] Starting structured sport extraction from conversation"
        )

        # Filter out system messages for cleaner analysis
        history_for_llm = [
            msg for msg in messages if not isinstance(msg, SystemMessage)
        ]
        if not history_for_llm:
            logger.info("[extractSports] No non-system messages.")
            return []

        try:
            # Configure LLM with structured output to exclude from main streaming
            internal_llm = self.llm.with_config(
                {
                    "tags": ["internal_processing", "extractSports"],
                    "run_name": "internal_sport_extraction",
                }
            )
            model_with_structured_output = internal_llm.with_structured_output(
                self.sports_extraction_schema
            )

            sport_extraction_prompt = """Analyze the conversation history and identify ALL UNIQUE sports, activities, or fitness disciplines that the USER has explicitly mentioned they are interested in, practice, or want to improve at.

CRITICAL DEDUPLICATION RULES:
- Return each sport/activity ONLY ONCE, even if mentioned multiple times
- If the user mentions the same sport in different ways (e.g., "running" and "jogging"), return only ONE standardized version
- Do NOT include sports that are already selected/mentioned in previous turns

IMPORTANT GUIDELINES:
- Focus ONLY on what the USER has stated, not what the coach suggests
- Look for explicit mentions like "I do running", "I want to improve my tennis", "I lift weights", etc.
- Match to these standardized sport categories (choose the MOST SPECIFIC match):
  • Running (includes jogging, running, marathons, 5ks, trail running, etc.)
  • Cycling (includes biking, cycling, road cycling, mountain biking, etc.)
  • Strength Training (includes weightlifting, gym work, resistance training, building muscle, powerlifting, etc.)
  • Swimming (includes pool swimming, open water, specific strokes, etc.)
  • Tennis (includes tennis, court sports)
  • General Fitness (ONLY if user mentions general fitness/exercise without ANY specific sports)

DEDUPLICATION EXAMPLES:
- If user says "I run and jog" → Return only ["Running"]
- If user says "I lift weights and do strength training" → Return only ["Strength Training"]  
- If user says "I bike and cycle" → Return only ["Cycling"]
- If user mentions "tennis" multiple times → Return only ["Tennis"] once

Return a JSON object with:
{
    "sports": ["Running", "Strength Training", ...],
    "found": true/false
}

Where:
- "sports": Array of UNIQUE standardized sport names (no duplicates, only from valid categories above)
- "found": Boolean indicating if any NEW sports were detected

If no NEW sports are mentioned, return: {"sports": [], "found": false}
"""

            response = await model_with_structured_output.ainvoke(
                [
                    SystemMessage(content=sport_extraction_prompt),
                    *history_for_llm,
                ]
            )

            logger.info(f"[extractSports] Structured response: {response}")

            # Handle structured response
            if isinstance(response, dict):
                sports = response.get("sports", [])
                found = response.get("found", False)

                # Validate sports against VALID_SPORT_VALUES (extra safety)
                validated_sports = []
                for sport in sports:
                    if sport in VALID_SPORT_VALUES:
                        validated_sports.append(sport)
                    else:
                        logger.warning(
                            f"[extractSports] Invalid sport filtered out: {sport}"
                        )

                logger.info(
                    f"[extractSports] Extracted {len(validated_sports)} validated sports: {validated_sports}"
                )
                return validated_sports
            else:
                logger.error(f"[extractSports] Unexpected response format: {response}")
                return []

        except Exception as error:
            logger.error(
                f"[extractSports] Error during structured sport extraction: {error}"
            )
            return []

    async def _extract_summary_func(
        self, messages: List[BaseMessage]
    ) -> List[SummaryItem]:
        """Extract structured summary from conversation history"""
        history_for_llm = [
            msg for msg in messages if not isinstance(msg, SystemMessage)
        ]
        if not history_for_llm:
            logger.info("[extractSummary] No non-system messages.")
            return []

        # Use structured output with the LLM, configured to exclude from main streaming
        internal_structured_llm = self.llm.with_config(
            {
                "tags": ["internal_processing", "extractSummary"],
                "run_name": "internal_summary_extraction",
            }
        )
        model_with_structured_output = internal_structured_llm.with_structured_output(
            self.summary_wrapper_schema
        )

        logger.info(
            f"[extractSummary] Calling LLM for structured summary with {len(history_for_llm)} messages"
        )

        try:
            # Get the summary extraction prompt (loads lazily if needed)
            summary_prompt = await self._get_summary_extraction_prompt()

            response = await model_with_structured_output.ainvoke(
                [
                    SystemMessage(content=summary_prompt),
                    *history_for_llm,
                ]
            )

            # Handle NO_INFO_SHARED string response
            if isinstance(response, str) and response == "NO_INFO_SHARED":
                logger.info("[extractSummary] LLM indicated no info shared.")
                return []

            # Check if response has summaryList
            if isinstance(response, dict) and "summaryList" in response:
                summary_list = response["summaryList"]
                logger.info(f"[extractSummary] Extracted summaryList: {summary_list}")

                # Convert to SummaryItem objects
                return [
                    SummaryItem(
                        category=item["category"],
                        details=item["details"],
                        is_important=item["isImportant"],
                    )
                    for item in summary_list
                ]
            else:
                logger.error(
                    f"[extractSummary] LLM response was not the expected wrapper object: {response}"
                )
                return []

        except Exception as error:
            if "NO_INFO_SHARED" in str(error):
                logger.info("[extractSummary] Caught NO_INFO_SHARED response in error.")
                return []
            logger.error(
                f"[extractSummary] Error calling/parsing LLM for structured summary: {error}"
            )
            return []

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Main node logic for information gathering"""
        logger.info("[Node: informationGathererNode] Entering node.")

        # Get both current turn messages and full conversation history
        current_messages = state.get("messages", [])
        conversation_history = state.get("conversation_history", [])

        # Convert conversation history to LangChain message format if needed
        history_messages = []
        if conversation_history:
            logger.info(
                f"[Node: informationGathererNode] Found {len(conversation_history)} messages in conversation history"
            )
            for msg in conversation_history:
                if isinstance(msg, dict):
                    role = msg.get("role", "")
                    content = msg.get("content", "")
                    if role == "human" and content:
                        history_messages.append(HumanMessage(content=content))
                    elif role == "assistant" and content:
                        history_messages.append(AIMessage(content=content))
                elif hasattr(msg, "content"):
                    # Already a LangChain message
                    history_messages.append(msg)

        # Combine conversation history with current turn messages for complete context
        messages = history_messages + current_messages

        logger.info(
            f"[Node: informationGathererNode] Using {len(history_messages)} history messages + {len(current_messages)} current messages = {len(messages)} total messages"
        )

        current_sidebar_data = state.get("sidebar_data") or SidebarStateData()
        user_input = state.get("user_input")
        user_id = state.get("user_id")

        # --- Check for uploaded file data and integrate into onboarding ---
        uploaded_file_data = {}
        if user_id:
            try:
                uploaded_file_data = await self._get_uploaded_file_data(user_id)
                if uploaded_file_data.get("has_data"):
                    logger.info(
                        f"[File Integration] Found uploaded fitness data for user {user_id}"
                    )
                    logger.info(
                        f"[File Integration] Detected sports: {uploaded_file_data.get('sports_detected', [])}"
                    )
                    logger.info(
                        f"[File Integration] Workout routine: {uploaded_file_data.get('workout_routine', 'N/A')[:100]}..."
                    )
                else:
                    logger.info(
                        f"[File Integration] No uploaded fitness data found for user {user_id}"
                    )
            except Exception as e:
                logger.error(f"[File Integration] Error checking uploaded files: {e}")

        # --- Load existing onboarding data from MongoDB for resume functionality ---
        if user_id:
            try:
                saved_sidebar_data = await self._load_onboarding_sidebar_data(user_id)
                if saved_sidebar_data:
                    logger.info(
                        f"[MongoDB] Loading existing onboarding data for resume - user: {user_id}"
                    )

                    # **COMPLETE DATA RESTORATION**: Replace current_sidebar_data with saved data entirely
                    # This ensures all information loads together as a single, complete dataset
                    logger.info(f"[MongoDB] BEFORE restoration - current data summary:")

                    # Add comprehensive logging to debug the current state
                    logger.info(
                        f"[DEBUG] current_sidebar_data type: {type(current_sidebar_data)}"
                    )
                    logger.info(
                        f"[DEBUG] saved_sidebar_data type: {type(saved_sidebar_data)}"
                    )

                    # Safe logging that handles both dict and SidebarStateData cases
                    try:
                        if hasattr(current_sidebar_data, "goals"):
                            goals_list = (
                                current_sidebar_data.goals.list
                                if current_sidebar_data.goals
                                else []
                            )
                        elif isinstance(current_sidebar_data, dict):
                            goals_data = current_sidebar_data.get("goals", {})
                            goals_list = (
                                goals_data.get("list", [])
                                if isinstance(goals_data, dict)
                                else []
                            )
                        else:
                            goals_list = []
                        logger.info(f"  - Goals: {goals_list}")
                    except Exception as e:
                        logger.error(f"[DEBUG] Error accessing goals: {e}")
                        logger.info(f"  - Goals: [ERROR ACCESSING]")

                    try:
                        if hasattr(current_sidebar_data, "selected_sports"):
                            selected_sports = current_sidebar_data.selected_sports or []
                        elif isinstance(current_sidebar_data, dict):
                            selected_sports = current_sidebar_data.get(
                                "selected_sports", []
                            )
                        else:
                            selected_sports = []
                        logger.info(f"  - Selected sports: {selected_sports}")
                    except Exception as e:
                        logger.error(f"[DEBUG] Error accessing selected_sports: {e}")
                        logger.info(f"  - Selected sports: [ERROR ACCESSING]")

                    try:
                        if hasattr(current_sidebar_data, "summary_items"):
                            summary_items = current_sidebar_data.summary_items or []
                        elif isinstance(current_sidebar_data, dict):
                            summary_items = current_sidebar_data.get(
                                "summary_items", []
                            )
                        else:
                            summary_items = []
                        logger.info(f"  - Summary items: {len(summary_items)}")
                    except Exception as e:
                        logger.error(f"[DEBUG] Error accessing summary_items: {e}")
                        logger.info(f"  - Summary items: [ERROR ACCESSING]")

                    try:
                        if hasattr(current_sidebar_data, "uploaded_documents"):
                            uploaded_docs = (
                                current_sidebar_data.uploaded_documents or []
                            )
                        elif isinstance(current_sidebar_data, dict):
                            uploaded_docs = current_sidebar_data.get(
                                "uploaded_documents", []
                            )
                        else:
                            uploaded_docs = []
                        logger.info(f"  - Uploaded docs: {len(uploaded_docs)}")
                    except Exception as e:
                        logger.error(f"[DEBUG] Error accessing uploaded_documents: {e}")
                        logger.info(f"  - Uploaded docs: [ERROR ACCESSING]")

                    try:
                        if hasattr(current_sidebar_data, "key_insights"):
                            key_insights = current_sidebar_data.key_insights or {}
                        elif isinstance(current_sidebar_data, dict):
                            key_insights = current_sidebar_data.get("key_insights", {})
                        else:
                            key_insights = {}
                        logger.info(f"  - Key insights: {len(key_insights)}")
                    except Exception as e:
                        logger.error(f"[DEBUG] Error accessing key_insights: {e}")
                        logger.info(f"  - Key insights: [ERROR ACCESSING]")

                    # Replace with complete saved data
                    # Handle both dict and SidebarStateData cases
                    if isinstance(saved_sidebar_data, SidebarStateData):
                        current_sidebar_data = saved_sidebar_data
                    else:
                        current_sidebar_data = SidebarStateData(**saved_sidebar_data)

                    logger.info(
                        f"[MongoDB] AFTER restoration - complete data restored:"
                    )

                    # Safe logging after restoration
                    logger.info(
                        f"[DEBUG] After restoration - current_sidebar_data type: {type(current_sidebar_data)}"
                    )
                    try:
                        goals_list = (
                            current_sidebar_data.goals.list
                            if current_sidebar_data.goals
                            else []
                        )
                        logger.info(f"  - Goals: {goals_list}")
                    except Exception as e:
                        logger.error(
                            f"[DEBUG] Error accessing goals after restoration: {e}"
                        )
                        logger.info(f"  - Goals: [ERROR ACCESSING]")

                    try:
                        selected_sports = current_sidebar_data.selected_sports or []
                        logger.info(f"  - Selected sports: {selected_sports}")
                    except Exception as e:
                        logger.error(
                            f"[DEBUG] Error accessing selected_sports after restoration: {e}"
                        )
                        logger.info(f"  - Selected sports: [ERROR ACCESSING]")

                    try:
                        summary_items = current_sidebar_data.summary_items or []
                        logger.info(f"  - Summary items: {len(summary_items)}")
                    except Exception as e:
                        logger.error(
                            f"[DEBUG] Error accessing summary_items after restoration: {e}"
                        )
                        logger.info(f"  - Summary items: [ERROR ACCESSING]")

                    try:
                        uploaded_docs = current_sidebar_data.uploaded_documents or []
                        logger.info(f"  - Uploaded docs: {len(uploaded_docs)}")
                    except Exception as e:
                        logger.error(
                            f"[DEBUG] Error accessing uploaded_documents after restoration: {e}"
                        )
                        logger.info(f"  - Uploaded docs: [ERROR ACCESSING]")

                    try:
                        key_insights = current_sidebar_data.key_insights or {}
                        logger.info(f"  - Key insights: {len(key_insights)}")
                    except Exception as e:
                        logger.error(
                            f"[DEBUG] Error accessing key_insights after restoration: {e}"
                        )
                        logger.info(f"  - Key insights: [ERROR ACCESSING]")

                    try:
                        current_stage = current_sidebar_data.current_stage
                        logger.info(f"  - Current stage: {current_stage}")
                    except Exception as e:
                        logger.error(
                            f"[DEBUG] Error accessing current_stage after restoration: {e}"
                        )
                        logger.info(f"  - Current stage: [ERROR ACCESSING]")

                    logger.info(
                        f"[MongoDB] ✅ Complete onboarding data restored as single dataset"
                    )
                else:
                    logger.info(
                        f"[MongoDB] No existing onboarding data found for user: {user_id}"
                    )
            except Exception as e:
                logger.error(f"[MongoDB] Error loading saved onboarding data: {e}")

        # --- Calculate is_first_turn early for logging ---
        history = [msg for msg in messages if not isinstance(msg, SystemMessage)]
        is_first_turn = len(history) <= 1

        # --- Preserve existing sidebar data ---
        logger.info("[DEBUG] *** PRESERVING EXISTING SIDEBAR DATA ***")
        logger.info(
            f"[DEBUG] Final current_sidebar_data type: {type(current_sidebar_data)}"
        )

        # Safe logging with comprehensive error handling
        try:
            if hasattr(current_sidebar_data, "goals"):
                goals_list = (
                    current_sidebar_data.goals.list
                    if current_sidebar_data.goals
                    else []
                )
            elif isinstance(current_sidebar_data, dict):
                goals_data = current_sidebar_data.get("goals", {})
                goals_list = (
                    goals_data.get("list", []) if isinstance(goals_data, dict) else []
                )
            else:
                goals_list = []
            logger.info(f"[DEBUG] Existing goals: {goals_list}")
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing existing goals: {e}")
            logger.info(f"[DEBUG] Existing goals: [ERROR ACCESSING]")

        try:
            if hasattr(current_sidebar_data, "selected_sports"):
                selected_sports = current_sidebar_data.selected_sports or []
            elif isinstance(current_sidebar_data, dict):
                selected_sports = current_sidebar_data.get("selected_sports", [])
            else:
                selected_sports = []
            logger.info(f"[DEBUG] Existing selected sports: {selected_sports}")
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing existing selected_sports: {e}")
            logger.info(f"[DEBUG] Existing selected sports: [ERROR ACCESSING]")

        try:
            if hasattr(current_sidebar_data, "summary_items"):
                summary_items = current_sidebar_data.summary_items or []
            elif isinstance(current_sidebar_data, dict):
                summary_items = current_sidebar_data.get("summary_items", [])
            else:
                summary_items = []
            logger.info(f"[DEBUG] Existing summary items: {len(summary_items)}")
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing existing summary_items: {e}")
            logger.info(f"[DEBUG] Existing summary items: [ERROR ACCESSING]")

        # --- Sport Selection Logic ---
        # Get current sport data from the state and preserve it - safe access
        try:
            if hasattr(current_sidebar_data, "selected_sport"):
                current_selected_sport = current_sidebar_data.selected_sport
            elif isinstance(current_sidebar_data, dict):
                current_selected_sport = current_sidebar_data.get("selected_sport")
            else:
                current_selected_sport = None
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing selected_sport: {e}")
            current_selected_sport = None

        try:
            if hasattr(current_sidebar_data, "selected_sports"):
                current_selected_sports = list(
                    current_sidebar_data.selected_sports or []
                )
            elif isinstance(current_sidebar_data, dict):
                current_selected_sports = list(
                    current_sidebar_data.get("selected_sports", [])
                )
            else:
                current_selected_sports = []
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing selected_sports: {e}")
            current_selected_sports = []

        try:
            if hasattr(current_sidebar_data, "sport_suggestions"):
                previous_suggestions = current_sidebar_data.sport_suggestions
            elif isinstance(current_sidebar_data, dict):
                previous_suggestions = current_sidebar_data.get("sport_suggestions")
            else:
                previous_suggestions = None
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing sport_suggestions: {e}")
            previous_suggestions = None
        sport_selected_this_turn = False

        # --- Initial State Logging ---
        logger.info("[DEBUG] *** Initial Sport State ***")
        logger.info(f"[DEBUG] Sidebar - Selected Sport: {current_selected_sport}")
        logger.info(
            f"[DEBUG] Sidebar - Selected Sports List: {current_selected_sports}"
        )
        logger.info(
            f"[DEBUG] Sidebar - Has previous suggestions: {bool(previous_suggestions)}"
        )

        # --- User Input Processing ---
        # Handle sport selection from user input (exact match or comma-separated)
        if previous_suggestions and user_input:
            logger.info(f"[DEBUG] Processing user_input for sports: '{user_input}'")
            parsed_sports = []

            # Split by comma and strip whitespace
            sport_parts = [sport.strip() for sport in user_input.split(",")]
            for sport in sport_parts:
                if sport in VALID_SPORT_VALUES:
                    parsed_sports.append(sport)
                elif sport:  # Log if a non-empty part is not a valid sport
                    logger.warning(
                        f"[DEBUG] Parsed sport value '{sport}' is not in VALID_SPORT_VALUES"
                    )

            if parsed_sports:
                sport_selected_this_turn = True
                logger.info(f"[DEBUG] User selected sports via input: {parsed_sports}")
                # Add all newly selected sports, avoiding duplicates
                for sport in parsed_sports:
                    if sport not in current_selected_sports:
                        current_selected_sports.append(sport)
                # Set the most recently selected one for potential context
                current_selected_sport = parsed_sports[-1]
            else:
                logger.info(
                    "[DEBUG] User input did not contain any valid sport selections."
                )

        # --- LLM Extraction for Sports ---
        # Extract goals, summary, and sports using LLM - ALWAYS run all extractions for consistency
        logger.info("[DEBUG] *** STARTING COMPLETE DATA EXTRACTION ***")
        logger.info(
            f"[DEBUG] Message count: {len(messages)}, Is first turn: {is_first_turn}"
        )
        logger.info(
            f"[DEBUG] Has uploaded file data: {uploaded_file_data.get('has_data', False)}"
        )

        goal_extraction_result = await self.extract_goals.ainvoke(messages)
        summary_items_result = await self.extract_summary.ainvoke(messages)
        sports_extraction_result = await self.extract_sports.ainvoke(messages)

        logger.info(f"[DEBUG] LLM Extraction - Goals: {goal_extraction_result}")
        logger.info(f"[DEBUG] LLM Extraction - Summary Items: {summary_items_result}")
        logger.info(f"[DEBUG] LLM Extraction - Sports: {sports_extraction_result}")
        logger.info("[DEBUG] *** EXTRACTION COMPLETE ***")

        # --- Enhanced Goal Logic: Merge with existing goals ---
        new_goals = goal_extraction_result["goals"]
        goals_exist = goal_extraction_result["exists"]

        # Start with existing goals and merge new ones - safe access
        try:
            if hasattr(current_sidebar_data, "goals") and current_sidebar_data.goals:
                existing_goals = list(current_sidebar_data.goals.list)
            elif isinstance(current_sidebar_data, dict):
                goals_data = current_sidebar_data.get("goals", {})
                existing_goals = list(
                    goals_data.get("list", []) if isinstance(goals_data, dict) else []
                )
            else:
                existing_goals = []
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing existing goals for merge: {e}")
            existing_goals = []
        final_goals = existing_goals.copy()

        # Add new goals that don't already exist
        for new_goal in new_goals:
            if new_goal not in final_goals:
                final_goals.append(new_goal)
                logger.info(f"[DEBUG] Added new goal: {new_goal}")

        # If no explicit goals were found but we have sports, convert sports to goals
        if not goals_exist and sports_extraction_result:
            logger.info(
                "[DEBUG] No explicit goals found, using sports as implicit goals"
            )
            for sport in sports_extraction_result:
                goal = f"Improve at {sport}"
                if goal not in final_goals:
                    final_goals.append(goal)
                    logger.info(f"[DEBUG] Created goal from sport: {goal}")

        # Update goals existence flag
        final_goals_exist = len(final_goals) > 0

        logger.info(
            f"[DEBUG] Final merged goals: {final_goals} (exists: {final_goals_exist})"
        )

        # --- Enhanced Sports Logic: Merge with existing sports ---
        # Update sports selection based on LLM extraction if any are found
        if sports_extraction_result and len(sports_extraction_result) > 0:
            logger.info(
                f"[Node: informationGathererNode] LLM detected sports: {sports_extraction_result}"
            )
            sport_selected_this_turn = True
            # Add all detected sports to the list, avoiding duplicates
            for sport in sports_extraction_result:
                if sport not in current_selected_sports:
                    current_selected_sports.append(sport)
                    logger.info(f"[DEBUG] Added new sport from LLM: {sport}")
                else:
                    logger.info(f"[DEBUG] Sport already exists, skipping: {sport}")
            # Update the 'current' sport to be the first one detected by the LLM this turn
            if (
                not current_selected_sport
                or current_selected_sport not in current_selected_sports
            ):
                current_selected_sport = sports_extraction_result[0]

        # --- Integrate Sports from Uploaded Files ---
        if uploaded_file_data.get("has_data") and uploaded_file_data.get(
            "sports_detected"
        ):
            file_detected_sports = uploaded_file_data.get("sports_detected", [])
            logger.info(
                f"[File Integration] Adding sports from uploaded files: {file_detected_sports}"
            )
            sport_selected_this_turn = True

            # Add file-detected sports to the list, avoiding duplicates
            for sport in file_detected_sports:
                if sport not in current_selected_sports:
                    current_selected_sports.append(sport)
                    logger.info(f"[DEBUG] Added sport from uploaded file: {sport}")
                else:
                    logger.info(f"[DEBUG] File sport already exists, skipping: {sport}")

            # Set current sport to first file-detected sport if not already set
            if not current_selected_sport and file_detected_sports:
                current_selected_sport = file_detected_sports[0]

        # --- Enhanced Summary Logic: Merge with existing summary items ---
        try:
            if hasattr(current_sidebar_data, "summary_items"):
                existing_summary_items = list(current_sidebar_data.summary_items or [])
            elif isinstance(current_sidebar_data, dict):
                existing_summary_items = list(
                    current_sidebar_data.get("summary_items", [])
                )
            else:
                existing_summary_items = []
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing summary_items for merge: {e}")
            existing_summary_items = []
        new_summary_items = list(summary_items_result)

        # Skip creating "Uploaded File Data" summary item - file info is shown separately via key_insights and uploaded_documents

        # Filter out "not specified" items from new summary items
        filtered_new_summary_items = self._filter_not_specified_items(new_summary_items)
        logger.info(
            f"[Filter] Filtered {len(new_summary_items)} down to {len(filtered_new_summary_items)} items (removed empty/not specified)"
        )

        # Merge summary items, avoiding duplicates by category
        existing_categories = {item.category.lower() for item in existing_summary_items}
        final_summary_items = existing_summary_items.copy()

        for new_item in filtered_new_summary_items:
            if new_item.category.lower() not in existing_categories:
                final_summary_items.append(new_item)
                logger.info(f"[DEBUG] Added new summary item: {new_item.category}")
            else:
                logger.info(
                    f"[DEBUG] Summary category already exists, skipping: {new_item.category}"
                )

        # Only add sports to summary if we DON'T have selected sports (to avoid redundancy)
        # When we have selected sports, they're displayed as individual pills already
        if current_selected_sports:
            logger.info(
                "[DEBUG] Selected sports exist, skipping sports summary item to avoid redundancy"
            )
            # Remove any existing sports summary items to prevent duplication
            final_summary_items = [
                item
                for item in final_summary_items
                if not (
                    "sport" in item.category.lower()
                    or "activit" in item.category.lower()
                )
            ]
            logger.info(
                f"[DEBUG] Filtered out sports summary items, remaining: {len(final_summary_items)} items"
            )

        logger.info(
            f"[DEBUG] Final merged summary items: {len(final_summary_items)} items"
        )

        # --- Suggestion Visibility Logic ---
        # The rule is simple: if any sports have been selected, EVER, do not show suggestions.
        # This is more robust than checking for the "first turn".
        show_suggestions = len(current_selected_sports) == 0

        logger.info(f"[Node: informationGathererNode] *** SUGGESTION LOGIC ***")
        logger.info(
            f"[Node: informationGathererNode] is_first_turn flag (for context): {is_first_turn}"
        )
        logger.info(
            f"[Node: informationGathererNode] Current selected sports count: {len(current_selected_sports)}"
        )
        logger.info(
            f"[Node: informationGathererNode] Final decision (show_suggestions): {show_suggestions}"
        )

        # --- AI Response Generation ---
        # Generate conversational response
        logger.info(
            f"[Node: informationGathererNode] Is first turn check? {is_first_turn}"
        )

        # Get the system prompt (loads lazily if needed) and create dynamic version
        base_system_prompt = await self._get_system_prompt()
        dynamic_system_prompt = base_system_prompt

        # Add dynamic uploaded file context if available
        if uploaded_file_data.get("has_data"):
            file_context = await self._create_dynamic_file_context(
                uploaded_file_data, is_first_turn
            )
            dynamic_system_prompt += file_context
            logger.info(
                "[File Integration] Added dynamic uploaded file context to AI prompt"
            )

        # Add memory context if available
        memory_context = state.get("memory_context")
        if memory_context:
            memory_guidance = f"\n\n**Previous Context:** Based on previous interactions:\n{memory_context}\n\nUse this context to avoid repeating questions and build upon previous conversations."
            dynamic_system_prompt += memory_guidance
            logger.info(
                "[Node: informationGathererNode] Added memory context to prompt"
            )

        # ADD CRITICAL MISSING INFORMATION ANALYSIS
        if not is_first_turn:
            # Analyze what information is still missing using existing robust function
            from athlea_langgraph.states.onboarding_state import (
                SidebarStateData,
                UserGoals,
            )

            temp_sidebar_data = SidebarStateData(
                goals=UserGoals(list=final_goals, exists=len(final_goals) > 0),
                summary_items=final_summary_items,
                selected_sports=current_selected_sports,
            )

            missing_analysis = self._analyze_missing_categories(
                history, temp_sidebar_data
            )
            found_categories = missing_analysis["found_categories"]
            missing_categories = missing_analysis["missing_categories"]
            completion_percentage = missing_analysis["completion_percentage"]
            next_to_ask = missing_analysis.get("next_to_ask")

            if missing_categories:
                critical_guidance = f"""

**CRITICAL MISSING INFORMATION ANALYSIS:**
Progress: {completion_percentage:.0f}% complete
Found: {', '.join(found_categories)}
Still Missing: {', '.join(missing_categories)}

**NEXT PRIORITY TO GATHER:**
{REQUIRED_ONBOARDING_CATEGORIES.get(next_to_ask, {}).get('name', 'Unknown') if next_to_ask else 'Continue gathering details'}
{REQUIRED_ONBOARDING_CATEGORIES.get(next_to_ask, {}).get('ask_prompt', '') if next_to_ask else ''}

**CRITICAL INSTRUCTIONS:**
- DO NOT move to plan generation until you have gathered information for ALL required categories
- Focus specifically on the missing categories above
- Ask targeted questions to fill these gaps
- Be conversational but ensure you get specific details for each missing area
- The user has uploaded files with good data, but you still need to gather their personal preferences, availability, and goals
"""
                dynamic_system_prompt += critical_guidance
                logger.info(
                    f"[Node: informationGathererNode] Added critical missing info guidance - {completion_percentage:.0f}% complete, missing: {missing_categories}"
                )
            else:
                logger.info(
                    "[Node: informationGathererNode] All required categories found, ready for plan generation"
                )

        # Use completion feedback from the check completion node to guide the conversation
        completion_feedback = state.get("completion_feedback")

        if completion_feedback and not is_first_turn:
            next_question = completion_feedback.get("next_question")
            missing_categories = completion_feedback.get("missing_categories", [])
            completion_percentage = completion_feedback.get("completion_percentage", 0)

            if next_question and missing_categories:
                smart_guidance = f"""

**GUIDANCE FROM COMPLETION ANALYSIS:**
The user has provided {completion_percentage:.0f}% of required information.
Missing: {', '.join(missing_categories)}

**NEXT QUESTION TO ASK:**
{next_question}

Use this question naturally in your response. Acknowledge what they've shared and ask for the missing information.
"""
                dynamic_system_prompt += smart_guidance
                logger.info(
                    f"[Node: informationGathererNode] Using completion feedback - asking about: {completion_feedback.get('next_to_ask', 'unknown')}"
                )
            elif current_selected_sport:
                sport_guidance = f"\n\n**Current Focus:** The user has selected **{current_selected_sport}** as a primary focus. Continue gathering information for their fitness plan."
                dynamic_system_prompt += sport_guidance
        elif current_selected_sport and not is_first_turn:
            sport_guidance = f"\n\n**Current Focus:** The user has selected **{current_selected_sport}** as a primary focus. Continue gathering information for their fitness plan."
            dynamic_system_prompt += sport_guidance

        # Get conversational response using streaming
        conversation_history_with_prompt = [
            SystemMessage(content=dynamic_system_prompt),
            *history,
        ]

        # Check if we have enough information for plan generation BEFORE generating more questions
        logger.info(
            "[Node: informationGathererNode] Checking completion status before generating response..."
        )

        # Perform inline completion check to avoid generating conflicting messages
        should_transition_to_plan = await self._check_completion_status(
            history, final_goals, final_summary_items, current_selected_sports
        )

        if should_transition_to_plan and not is_first_turn:
            logger.info(
                "[Node: informationGathererNode] ✅ Sufficient information detected - creating transition message"
            )

            # Create a transition message instead of asking more questions
            transition_message = await self._create_transition_to_plan_message(
                final_goals, final_summary_items, current_selected_sports, history
            )
            ai_response = AIMessage(content=transition_message)
            ai_response_content = transition_message

            logger.info(
                f"[Node: informationGathererNode] Created transition message: {len(ai_response_content)} chars"
            )
        else:
            logger.info(
                "[Node: informationGathererNode] Calling main LLM for streaming conversation turn..."
            )

            # Use streaming instead of ainvoke to support token streaming
            ai_response_content = ""
            try:
                logger.info(
                    "[Node: informationGathererNode] 🚀 Starting LLM streaming..."
                )

                # Configure LLM with tags for streaming detection
                streaming_llm = self.llm.with_config(
                    {"tags": ["final_response", "informationGathererNode"]}
                )

                # Stream the response and collect content
                chunk_count = 0
                async for chunk in streaming_llm.astream(
                    conversation_history_with_prompt
                ):
                    chunk_count += 1
                    if hasattr(chunk, "content") and chunk.content:
                        ai_response_content += chunk.content

                    # Log progress every 10 chunks to avoid spam
                    if chunk_count % 10 == 0:
                        logger.info(
                            f"[Node: informationGathererNode] Processed {chunk_count} chunks, content length: {len(ai_response_content)}"
                        )

                logger.info(
                    f"[Node: informationGathererNode] ✅ Streaming completed! Total chunks: {chunk_count}, final content length: {len(ai_response_content)}"
                )

                # Create AIMessage with accumulated content
                ai_response = AIMessage(content=ai_response_content)

                logger.info(
                    f"[Node: informationGathererNode] Streamed AI Response length: {len(ai_response_content)} chars"
                )
            except Exception as error:
                logger.error(
                    f"[Node: informationGathererNode] ❌ Error calling streaming LLM: {error}"
                )
                ai_response = AIMessage(
                    content="Sorry, I encountered an issue. Let's try continuing."
                )
                ai_response_content = ai_response.content

        # Prepare state update with merged data
        updated_sidebar_data = SidebarStateData(
            current_stage="greeting" if is_first_turn else "gathering",
            goals=UserGoals(
                list=final_goals,
                exists=final_goals_exist,
            ),
            summary_items=final_summary_items,
            generated_plan=(
                current_sidebar_data.generated_plan
                if hasattr(current_sidebar_data, "generated_plan")
                else (
                    current_sidebar_data.get("generated_plan")
                    if isinstance(current_sidebar_data, dict)
                    else None
                )
            ),  # Preserve existing plan safely
            sport_suggestions=COMMON_SPORT_SUGGESTIONS if show_suggestions else None,
            selected_sport=current_selected_sport,
            selected_sports=current_selected_sports,
            uploaded_documents=(
                uploaded_file_data.get("uploaded_documents", [])
                if uploaded_file_data.get("has_data")
                else []
            ),
            key_insights=(
                uploaded_file_data.get("key_insights", {})
                if uploaded_file_data.get("has_data")
                else {}
            ),
        )

        logger.info(
            f"[Node: informationGathererNode] Goals: {final_goals}, SummaryItems: {len(final_summary_items)}, Selected Sport: {current_selected_sport}"
        )

        # Determine if we have enough info to proceed to plan generation
        has_enough_info = should_transition_to_plan and not is_first_turn

        logger.info(
            f"[Node: informationGathererNode] Setting has_enough_info: {has_enough_info} (should_transition_to_plan: {should_transition_to_plan}, is_first_turn: {is_first_turn})"
        )

        updates = {
            "messages": [ai_response] if ai_response else [],
            "sidebar_data": updated_sidebar_data,
            "has_enough_info": has_enough_info,
        }

        # --- Save updated onboarding sidebar data to MongoDB for resume functionality ---
        if user_id and updated_sidebar_data:
            try:
                # Get thread_id from state if available
                thread_id = state.get("thread_id")
                await self._save_onboarding_sidebar_data(
                    user_id, updated_sidebar_data, thread_id
                )
            except Exception as e:
                logger.error(f"[MongoDB] Error saving updated onboarding data: {e}")

        logger.info(f"[DEBUG] *** FINAL UPDATES BEING RETURNED ***")
        logger.info(
            f"[DEBUG] sidebar_data.sport_suggestions: {updates['sidebar_data'].sport_suggestions}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.selected_sports: {updates['sidebar_data'].selected_sports}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.selected_sport: {updates['sidebar_data'].selected_sport}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.goals.list: {updates['sidebar_data'].goals.list}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.goals.exists: {updates['sidebar_data'].goals.exists}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.summary_items count: {len(updates['sidebar_data'].summary_items)}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.summary_items: {[item.category + ': ' + item.details for item in updates['sidebar_data'].summary_items]}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.key_insights count: {len(updates['sidebar_data'].key_insights or {})}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.key_insights: {updates['sidebar_data'].key_insights}"
        )
        logger.info(
            f"[DEBUG] sidebar_data.current_stage: {updates['sidebar_data'].current_stage}"
        )

        logger.info(
            "[Node: informationGathererNode] 🎯 RETURNING UPDATES - NODE EXECUTION COMPLETE"
        )
        logger.info(
            f"[Node: informationGathererNode] Updates keys: {list(updates.keys())}"
        )
        logger.info(
            f"[Node: informationGathererNode] Messages count: {len(updates.get('messages', []))}"
        )

        return updates

    async def _create_dynamic_file_context(
        self, uploaded_file_data: Dict[str, Any], is_first_turn: bool
    ) -> str:
        """
        Create dynamic file context based on actual uploaded file content for natural AI responses.

        Args:
            uploaded_file_data: Dictionary containing analyzed file data
            is_first_turn: Whether this is the first conversation turn

        Returns:
            Dynamic context string tailored to the specific file content found
        """
        try:
            context_parts = []

            # Start with file discovery context
            uploaded_docs = uploaded_file_data.get("uploaded_documents", [])
            files_summary = uploaded_file_data.get("files_summary", "")

            if uploaded_docs:
                file_types = [doc.get("file_type", "unknown") for doc in uploaded_docs]
                unique_file_types = list(set(file_types))

                # Create file discovery context
                if len(uploaded_docs) == 1:
                    context_parts.append(
                        f"\n\n**File Upload Detected:**\nThe user has uploaded 1 file: {uploaded_docs[0].get('filename', 'fitness data file')} ({uploaded_docs[0].get('file_type', 'unknown')} file)"
                    )
                else:
                    context_parts.append(
                        f"\n\n**File Uploads Detected:**\nThe user has uploaded {len(uploaded_docs)} files including: {', '.join([doc.get('filename', 'file') for doc in uploaded_docs[:3]])}{'...' if len(uploaded_docs) > 3 else ''}"
                    )

                # Add file type insights
                type_descriptions = {
                    "fitness_data": "fitness tracking data (GPX/TCX/FIT files)",
                    "csv": "structured data/spreadsheets",
                    "pdf": "documents or reports",
                    "image": "photos or screenshots",
                    "spreadsheet": "workout logs or data sheets",
                }

                type_context = []
                for ftype in unique_file_types:
                    if ftype in type_descriptions:
                        type_context.append(type_descriptions[ftype])

                if type_context:
                    context_parts.append(
                        f"File types suggest: {', '.join(type_context)}"
                    )

            # Add analyzed content insights
            sports_detected = uploaded_file_data.get("sports_detected", [])
            key_insights = uploaded_file_data.get("key_insights", {})
            files_summary = uploaded_file_data.get("files_summary", "")

            # Create specific insights based on what was found
            insights = []

            if sports_detected:
                if len(sports_detected) == 1:
                    insights.append(f"Primary activity detected: {sports_detected[0]}")
                else:
                    insights.append(
                        f"Multiple activities detected: {', '.join(sports_detected[:3])}"
                    )

            # Add insights summary (WITHOUT including the actual data)
            if key_insights:
                insights.append(
                    f"Analyzed {len(key_insights)} data points from uploaded files"
                )

                # Give general guidance about what types of insights are available
                insight_categories = []
                for key in key_insights.keys():
                    key_lower = key.lower()
                    if any(
                        term in key_lower for term in ["goal", "target", "objective"]
                    ):
                        insight_categories.append("goals")
                    elif any(
                        term in key_lower for term in ["experience", "level", "skill"]
                    ):
                        insight_categories.append("experience level")
                    elif any(
                        term in key_lower
                        for term in ["frequency", "schedule", "routine"]
                    ):
                        insight_categories.append("training frequency")
                    elif any(
                        term in key_lower
                        for term in ["performance", "time", "pace", "distance"]
                    ):
                        insight_categories.append("performance metrics")
                    elif any(term in key_lower for term in ["equipment", "gear"]):
                        insight_categories.append("equipment")

                if insight_categories:
                    unique_categories = list(set(insight_categories))
                    insights.append(
                        f"Available data includes: {', '.join(unique_categories[:3])}"
                    )

                # Important: Do NOT include the actual key insights data in the context
                # The data is already stored in sidebar_data and will be displayed there

            if insights:
                context_parts.append(f"\n**Analysis Results:**")
                for insight in insights:
                    context_parts.append(f"{insight}")

            # Create dynamic conversation guidance
            context_parts.append(f"\n**Dynamic Conversation Approach:**")

            # Tailor approach based on key insights found
            insight_keys = list(key_insights.keys()) if key_insights else []
            insight_values = list(key_insights.values()) if key_insights else []

            # Check for experience level indicators in key insights
            has_experience_info = any(
                "experience" in key.lower() or "level" in key.lower()
                for key in insight_keys
            )
            has_performance_info = any(
                "time" in key.lower()
                or "pace" in key.lower()
                or "distance" in key.lower()
                or "weight" in key.lower()
                for key in insight_keys
            )
            has_frequency_info = any(
                "frequency" in key.lower()
                or "schedule" in key.lower()
                or "routine" in key.lower()
                for key in insight_keys
            )

            if has_experience_info:
                # Look for experience level in values
                experience_text = " ".join(insight_values).lower()
                if "beginner" in experience_text or "new" in experience_text:
                    context_parts.append(
                        "- Use encouraging, supportive tone for newer athlete"
                    )
                elif (
                    "advanced" in experience_text
                    or "experienced" in experience_text
                    or "competitive" in experience_text
                ):
                    context_parts.append(
                        "- Use more technical language appropriate for experienced athlete"
                    )
                else:
                    context_parts.append(
                        "- Use balanced approach suitable for developing athlete"
                    )

            # Create conversation starters based on general insights (WITHOUT specific data)
            starters = []

            # Use general categories for natural conversation starters
            if key_insights:
                # Check for sport-related insights
                has_sport_data = any(
                    any(
                        sport in str(value).lower()
                        for sport in [
                            "running",
                            "cycling",
                            "swimming",
                            "tennis",
                            "strength",
                        ]
                    )
                    for value in key_insights.values()
                )

                # Check for performance data
                has_performance_data = any(
                    any(
                        perf in key.lower()
                        for perf in ["time", "distance", "pace", "best", "performance"]
                    )
                    for key in key_insights.keys()
                )

                # Check for training frequency data
                has_frequency_data = any(
                    any(
                        freq in key.lower()
                        for freq in ["frequency", "schedule", "times", "routine"]
                    )
                    for key in key_insights.keys()
                )

                if has_sport_data:
                    starters.append(
                        '"I can see from your uploaded data that you have some great training information"'
                    )
                elif has_performance_data:
                    starters.append(
                        '"Your uploaded data shows some interesting performance metrics"'
                    )
                elif has_frequency_data:
                    starters.append(
                        '"I can see you have detailed training schedule information"'
                    )

            # Fallback to sports detected if no key insights
            if not starters and sports_detected:
                if len(sports_detected) == 1:
                    starters.append(
                        f'"I can see you\'re focused on {sports_detected[0]}"'
                    )
                else:
                    starters.append(
                        f"\"I notice you're cross-training with {' and '.join(sports_detected[:2])}\""
                    )

            if starters:
                context_parts.append(
                    f"- Natural conversation starters based on your data: {' or '.join(starters[:2])}"
                )

            # Question guidance based on what we know vs don't know
            context_parts.append("- Questions to focus on:")

            if not any("goal" in key.lower() for key in insight_keys):
                context_parts.append("  • Specific goals and aspirations")

            if not has_frequency_info:
                context_parts.append("  • Preferred training schedule and timing")

            if not any(
                "equipment" in key.lower() or "location" in key.lower()
                for key in insight_keys
            ):
                context_parts.append("  • Available equipment and training environment")

            context_parts.append("  • What they want to improve or achieve next")
            context_parts.append("  • Any challenges or limitations they face")

            # Add context about building on uploaded data
            if key_insights:
                context_parts.append(f"\n**Key Information Available:**")
                context_parts.append(
                    f"You have {len(key_insights)} specific data points about this user."
                )
                context_parts.append(
                    "Use this information to ask targeted questions and avoid asking for information you already have."
                )
                context_parts.append(
                    "Reference specific insights naturally in your responses."
                )

            final_context = "\n".join(context_parts)

            logger.info(
                f"[Dynamic File Context] Created enhanced context with {len(context_parts)} sections and {len(key_insights)} key insights"
            )
            return final_context

        except Exception as e:
            logger.error(f"[Dynamic File Context] Error creating dynamic context: {e}")
            # Fallback to basic context
            return f"""

**Uploaded Fitness Data Available:**
The user has uploaded fitness files. Use this data to:
1. Acknowledge their uploaded information naturally
2. Ask about goals and preferences rather than activities you can see
3. Reference specific aspects from their data when relevant
"""

    async def _get_uploaded_file_data(self, user_id: str) -> Dict[str, Any]:
        """
        Retrieve and analyze uploaded file data for the user to determine workout routine automatically.

        Returns:
            Dict containing analyzed file data with keys:
            - has_data: boolean indicating if any uploaded files exist
            - files_summary: string summary of all uploaded files
            - workout_routine: extracted workout routine information
            - sports_detected: list of sports detected from files
            - experience_indicators: indicators of user's experience level
            - uploaded_documents: list of document metadata for display
        """
        try:
            if not user_id:
                return {"has_data": False}

            # Check MongoDB collections first
            mongo_client = await self._get_mongo_client()
            documents = []
            stats = []

            if mongo_client:
                db = mongo_client["AthleaUserData"]
                documents_collection = db["documents"]
                stats_collection = db["stats"]

                # Retrieve uploaded documents (using sync methods)
                documents = list(documents_collection.find({"userId": user_id}))

                # Retrieve analyzed stats (using sync methods)
                stats = list(stats_collection.find({"user_id": user_id}))

            # Check local onboarding files directory
            import tempfile
            import os
            from datetime import datetime

            onboarding_files = []
            onboarding_dir = os.path.join(
                tempfile.gettempdir(), "athlea_onboarding", user_id
            )

            if os.path.exists(onboarding_dir):
                logger.info(
                    f"[File Data] Checking onboarding directory: {onboarding_dir}"
                )
                for filename in os.listdir(onboarding_dir):
                    file_path = os.path.join(onboarding_dir, filename)
                    if os.path.isfile(file_path) and not filename.endswith(".meta"):
                        # Look for corresponding metadata file
                        file_base = os.path.splitext(filename)[0]
                        metadata_path = os.path.join(
                            onboarding_dir, f"{file_base}.meta"
                        )

                        original_filename = filename  # Default fallback
                        upload_date = datetime.fromtimestamp(
                            os.stat(file_path).st_mtime
                        ).isoformat()

                        # Try to load metadata if available
                        if os.path.exists(metadata_path):
                            try:
                                import json

                                with open(metadata_path, "r") as f:
                                    metadata = json.load(f)
                                    original_filename = metadata.get(
                                        "original_filename", filename
                                    )
                                    upload_date = metadata.get(
                                        "upload_timestamp", upload_date
                                    )
                                    logger.info(
                                        f"[File Data] Loaded metadata for {filename}: original name = {original_filename}"
                                    )
                            except Exception as e:
                                logger.warning(
                                    f"[File Data] Could not load metadata for {filename}: {e}"
                                )

                        onboarding_file = {
                            "file_path": file_path,
                            "filename": original_filename,
                            "file_size": os.stat(file_path).st_size,
                            "upload_date": upload_date,
                            "source": "onboarding_upload",
                        }
                        onboarding_files.append(onboarding_file)
                        logger.info(
                            f"[File Data] Found onboarding file: {original_filename}"
                        )

            if not documents and not stats and not onboarding_files:
                logger.info(f"[File Data] No uploaded files found for user {user_id}")
                return {"has_data": False}

            logger.info(
                f"[File Data] Found {len(documents)} documents, {len(stats)} stats entries, and {len(onboarding_files)} onboarding files for user {user_id}"
            )

            # Extract document metadata for display
            uploaded_documents = []
            processed_filenames = set()  # Track unique filenames to avoid duplicates

            # Process documents from documents collection
            for doc in documents:
                filename = doc.get("fileName", "Unknown File")
                if filename not in processed_filenames:
                    file_type = self._determine_file_type(filename)
                    upload_date = doc.get("createdAt", "Unknown Date")

                    # Convert datetime to string if needed
                    if hasattr(upload_date, "isoformat"):
                        upload_date = upload_date.isoformat()
                    elif hasattr(upload_date, "strftime"):
                        upload_date = upload_date.strftime("%Y-%m-%d %H:%M:%S")

                    uploaded_documents.append(
                        {
                            "filename": filename,
                            "file_type": file_type,
                            "upload_date": upload_date,
                            "source": "document",
                        }
                    )
                    processed_filenames.add(filename)
                    logger.info(f"[File Data] Added document: {filename} ({file_type})")

            # Process stats files
            for stat in stats:
                filename = stat.get("fileName", "Stats Analysis")
                if filename not in processed_filenames:
                    file_type = self._determine_file_type(filename)
                    upload_date = stat.get(
                        "uploadDate",
                        stat.get("metadata", {}).get("processedAt", "Unknown Date"),
                    )

                    # Convert datetime to string if needed
                    if hasattr(upload_date, "isoformat"):
                        upload_date = upload_date.isoformat()
                    elif hasattr(upload_date, "strftime"):
                        upload_date = upload_date.strftime("%Y-%m-%d %H:%M:%S")

                    uploaded_documents.append(
                        {
                            "filename": filename,
                            "file_type": file_type,
                            "upload_date": upload_date,
                            "source": "stats",
                        }
                    )
                    processed_filenames.add(filename)
                    logger.info(
                        f"[File Data] Added stats file: {filename} ({file_type})"
                    )

            # Process onboarding files - only show files with recognizable names (not UUIDs)
            for onboarding_file in onboarding_files:
                filename = onboarding_file["filename"]

                # Check if this looks like a UUID-based file ID (32-36 chars with dashes)
                filename_base = filename.split(".")[0] if "." in filename else filename
                is_uuid_like = (
                    len(filename_base) >= 32
                    and len(filename_base) <= 36
                    and "-" in filename_base
                    and len(filename_base.replace("-", "")) >= 30
                )

                # Only show files that don't look like UUIDs and haven't been processed
                if filename not in processed_filenames and not is_uuid_like:
                    file_type = self._determine_file_type(filename)

                    uploaded_documents.append(
                        {
                            "filename": filename,
                            "file_type": file_type,
                            "upload_date": onboarding_file["upload_date"],
                            "source": "onboarding_upload",
                        }
                    )
                    processed_filenames.add(filename)
                    logger.info(
                        f"[File Data] Added onboarding file: {filename} ({file_type})"
                    )
                elif is_uuid_like:
                    logger.info(
                        f"[File Data] Skipping UUID-like filename from display: {filename}"
                    )

            # Analyze the data to extract workout routine information
            analysis_result = await self._analyze_uploaded_fitness_data(
                documents, stats, onboarding_files
            )

            result = {
                "has_data": True,
                "uploaded_documents": uploaded_documents,
                **analysis_result,
            }

            logger.info(
                f"[File Data] Returning data with {len(uploaded_documents)} document entries"
            )
            return result

        except Exception as e:
            logger.error(
                f"[File Data] Error retrieving uploaded file data for user {user_id}: {e}"
            )
            return {"has_data": False, "error": str(e)}

    def _determine_file_type(self, filename: str) -> str:
        """Determine file type based on filename extension."""
        if not filename or "." not in filename:
            return "unknown"

        extension = filename.lower().split(".")[-1]

        # Document types
        if extension in ["pdf"]:
            return "pdf"
        elif extension in ["doc", "docx"]:
            return "document"
        elif extension in ["txt"]:
            return "text"

        # Spreadsheet/data types
        elif extension in ["csv"]:
            return "csv"
        elif extension in ["xlsx", "xls"]:
            return "spreadsheet"

        # Image types
        elif extension in ["jpg", "jpeg", "png", "gif", "webp"]:
            return "image"

        # Fitness/stats data (common fitness app exports)
        elif extension in ["gpx", "tcx", "fit"]:
            return "fitness_data"

        else:
            return "unknown"

    async def _analyze_uploaded_fitness_data(
        self,
        documents: List[Dict],
        stats: List[Dict],
        onboarding_files: List[Dict] = None,
    ) -> Dict[str, Any]:
        """
        Analyze uploaded documents and stats to extract workout routine and fitness information.

        Args:
            documents: List of uploaded documents from MongoDB
            stats: List of analyzed stats from MongoDB
            onboarding_files: List of onboarding files from local filesystem

        Returns:
            Dict containing analyzed information about user's fitness routine
        """
        try:
            # Compile all content for analysis
            content_for_analysis = []

            # Add document content
            for doc in documents:
                if "content" in doc:
                    content_for_analysis.append(
                        f"File: {doc.get('fileName', 'Unknown')}\nContent: {doc['content']}"
                    )
                elif "chunkContent" in doc:
                    content_for_analysis.append(
                        f"File: {doc.get('fileName', 'Unknown')}\nContent: {doc['chunkContent']}"
                    )

            # Add stats analysis content
            for stat in stats:
                if "analysis" in stat:
                    analysis_content = str(stat["analysis"])
                    content_for_analysis.append(f"Stats Analysis: {analysis_content}")
                if "rawData" in stat:
                    raw_data_content = str(stat["rawData"])
                    content_for_analysis.append(f"Raw Data: {raw_data_content}")

            # Add onboarding files content
            if onboarding_files:
                for onboarding_file in onboarding_files:
                    file_path = onboarding_file["file_path"]
                    filename = onboarding_file["filename"]

                    try:
                        # Read file content based on file type
                        file_content = await self._read_onboarding_file_content(
                            file_path
                        )
                        if file_content:
                            # Check if content is meaningful or just an error message
                            is_error_message = any(
                                error_phrase in file_content.lower()
                                for error_phrase in [
                                    "not available",
                                    "cannot read",
                                    "error reading",
                                    "could not be read",
                                    "parsing capabilities",
                                    "content extraction not supported",
                                ]
                            )

                            content_for_analysis.append(
                                f"File: {filename}\nContent: {file_content}"
                            )

                            if is_error_message:
                                logger.warning(
                                    f"[File Data] Content extraction issue for {filename}: {file_content[:100]}..."
                                )
                            else:
                                logger.info(
                                    f"[File Data] Successfully read content from onboarding file: {filename}"
                                )
                        else:
                            logger.warning(
                                f"[File Data] No content extracted from onboarding file: {filename}"
                            )
                    except Exception as e:
                        logger.error(
                            f"[File Data] Error reading onboarding file {filename}: {e}"
                        )
                        # Add basic file info even if we can't read content
                        content_for_analysis.append(
                            f"File: {filename}\nContent: File uploaded but content could not be read: {str(e)}"
                        )

            if not content_for_analysis:
                return {
                    "files_summary": "No analyzable content found in uploaded files",
                    "workout_routine": "Unable to determine from uploaded data",
                    "sports_detected": [],
                    "experience_indicators": "Unknown",
                }

            # Create analysis prompt for LLM
            analysis_prompt = """You are analyzing uploaded fitness data to extract key information for an AI fitness coach conversation.

Analyze the following uploaded file content and dynamically extract the most relevant and useful information that would be valuable for a fitness coach to know about this user.

Content to analyze:
{content}

Extract key information as flexible key-value pairs. Be selective and only include information that is actually present and relevant. Examples of potentially useful information:
- Sports/activities practiced
- Training frequency or schedule patterns
- Performance metrics (times, distances, weights, etc.)
- Experience level indicators
- Equipment mentioned
- Training locations or preferences
- Goals mentioned in the data
- Notable achievements or patterns
- Any other relevant fitness-related insights

Provide your analysis in the following JSON format:
{{
    "key_insights": {{
        "Dynamic Key 1": "Specific value or insight found",
        "Dynamic Key 2": "Another specific finding",
        "Dynamic Key 3": "etc..."
    }},
    "files_summary": "Brief summary of what types of files were analyzed"
}}

IMPORTANT: 
- Only include key-value pairs for information that is actually present in the data
- Use descriptive, natural language keys (e.g., "Primary Sport", "Weekly Training Frequency", "Best 5K Time")
- Keep values concise but informative
- Don't make up information that isn't in the data
- If no useful information is found, return an empty key_insights object
"""

            # Prepare content for LLM
            combined_content = "\n\n".join(content_for_analysis)

            # Use the LLM to analyze the fitness data
            # Configure LLM to exclude from streaming (internal processing only)
            internal_llm = self.llm.with_config(
                {
                    "tags": ["internal_processing", "file_analysis"],
                    "run_name": "internal_file_analysis",
                }
            )

            analysis_messages = [
                SystemMessage(content=analysis_prompt.format(content=combined_content))
            ]

            response = await internal_llm.ainvoke(analysis_messages)
            analysis_text = response.content

            # Try to parse JSON response with robust error handling
            try:
                import json
                import re

                # Extract JSON from response if it's wrapped in text
                if "```json" in analysis_text:
                    json_start = analysis_text.find("```json") + 7
                    json_end = analysis_text.find("```", json_start)
                    analysis_text = analysis_text[json_start:json_end].strip()
                elif "{" in analysis_text:
                    json_start = analysis_text.find("{")
                    json_end = analysis_text.rfind("}") + 1
                    analysis_text = analysis_text[json_start:json_end]

                # Clean up common JSON formatting issues
                # Remove trailing commas before closing braces/brackets
                analysis_text = re.sub(r",\s*}", "}", analysis_text)
                analysis_text = re.sub(r",\s*]", "]", analysis_text)

                # Try to fix missing commas between key-value pairs
                # This is a simple heuristic and may not catch all cases
                analysis_text = re.sub(r'"\s*\n\s*"', '",\n    "', analysis_text)

                # Clean up any potential multi-line string issues
                analysis_text = analysis_text.replace("\n", " ").replace("\r", " ")

                # Try parsing the cleaned JSON
                try:
                    parsed_analysis = json.loads(analysis_text)
                except json.JSONDecodeError as first_error:
                    # If that fails, try a more aggressive cleanup
                    logger.warning(
                        f"[File Data] First JSON parse attempt failed: {first_error}"
                    )

                    # Remove all newlines and extra whitespace
                    cleaned_text = re.sub(r"\s+", " ", analysis_text)

                    # Try to fix common quote issues
                    cleaned_text = re.sub(r'(?<!\\)"(?![,\]}])', '\\"', cleaned_text)

                    try:
                        parsed_analysis = json.loads(cleaned_text)
                        logger.info(
                            f"[File Data] Successfully parsed JSON after cleanup"
                        )
                    except json.JSONDecodeError as second_error:
                        logger.warning(
                            f"[File Data] Second JSON parse attempt also failed: {second_error}"
                        )
                        # Log the problematic JSON for debugging
                        logger.debug(
                            f"[File Data] Problematic JSON text: {analysis_text[:500]}..."
                        )
                        raise second_error
                logger.info(f"[File Data] Successfully analyzed uploaded fitness data")

                # Extract key insights and convert to the format expected by the rest of the system
                raw_key_insights = parsed_analysis.get("key_insights", {})
                files_summary = parsed_analysis.get(
                    "files_summary",
                    f"Analyzed {len(content_for_analysis)} uploaded files",
                )

                # Convert complex key_insights data structures to strings for Pydantic validation
                key_insights = {}
                for key, value in raw_key_insights.items():
                    if isinstance(value, list):
                        # Convert lists to comma-separated strings
                        key_insights[key] = ", ".join(str(item) for item in value)
                    elif isinstance(value, dict):
                        # Convert dictionaries to readable string format
                        formatted_items = []
                        for k, v in value.items():
                            formatted_items.append(f"{k}: {v}")
                        key_insights[key] = "; ".join(formatted_items)
                    else:
                        # Keep strings and other simple types as-is
                        key_insights[key] = str(value)

                # Convert key insights to legacy format for compatibility with existing code
                sports_detected = []
                workout_routine = ""
                experience_indicators = ""
                performance_metrics = ""

                # Extract sports from key insights with proper type checking
                for key, value in key_insights.items():
                    # Ensure key and value are strings
                    if not isinstance(key, str):
                        continue
                    if not isinstance(value, str):
                        value = str(value)  # Convert to string if it's not

                    key_lower = key.lower()
                    value_lower = value.lower()

                    if any(
                        sport_term in key_lower
                        for sport_term in ["sport", "activity", "discipline"]
                    ):
                        # Try to extract standard sport names from the value
                        sport_mappings = {
                            "running": "Running",
                            "cycling": "Cycling",
                            "strength": "Strength Training",
                            "weight": "Strength Training",
                            "swimming": "Swimming",
                            "tennis": "Tennis",
                            "fitness": "General Fitness",
                        }
                        for term, sport in sport_mappings.items():
                            if term in value_lower and sport not in sports_detected:
                                sports_detected.append(sport)

                    elif any(
                        freq_term in key_lower
                        for freq_term in [
                            "frequency",
                            "schedule",
                            "routine",
                            "training",
                        ]
                    ):
                        workout_routine += f"{key}: {value}. "

                    elif any(
                        exp_term in key_lower
                        for exp_term in ["experience", "level", "skill"]
                    ):
                        experience_indicators += f"{key}: {value}. "

                    elif any(
                        perf_term in key_lower
                        for perf_term in [
                            "time",
                            "distance",
                            "weight",
                            "pace",
                            "performance",
                            "best",
                            "pr",
                        ]
                    ):
                        performance_metrics += f"{key}: {value}. "

                return {
                    "files_summary": files_summary,
                    "key_insights": key_insights,  # Store the original flexible insights
                    "sports_detected": sports_detected,
                    "workout_routine": workout_routine.strip()
                    or "Training routine information extracted from files",
                    "experience_indicators": experience_indicators.strip()
                    or "Experience level indicators found in data",
                    "performance_metrics": performance_metrics.strip()
                    or "Performance data available in uploaded files",
                }

            except json.JSONDecodeError as e:
                logger.warning(
                    f"[File Data] Could not parse JSON from LLM response: {e}"
                )
                # Fallback to basic analysis
                return {
                    "files_summary": f"Analyzed {len(content_for_analysis)} uploaded files",
                    "workout_routine": (
                        analysis_text[:500] + "..."
                        if len(analysis_text) > 500
                        else analysis_text
                    ),
                    "sports_detected": self._extract_sports_from_text(combined_content),
                    "experience_indicators": "Analysis available in workout_routine field",
                    "performance_metrics": "See files_summary for details",
                    "training_focus": "Multiple areas detected",
                }

        except Exception as e:
            logger.error(f"[File Data] Error analyzing uploaded fitness data: {e}")
            return {
                "files_summary": "Error analyzing uploaded files",
                "workout_routine": "Unable to analyze due to processing error",
                "sports_detected": [],
                "experience_indicators": "Unknown due to error",
            }

    def _extract_sports_from_text(self, text: str) -> List[str]:
        """Extract sports/activities from text content using keyword matching."""
        detected_sports = []
        text_lower = text.lower()

        # Define sport keywords to look for
        sport_keywords = {
            "running": ["running", "run", "jog", "marathon", "5k", "10k", "sprint"],
            "cycling": ["cycling", "bike", "bicycle", "ride", "cycling"],
            "swimming": ["swimming", "swim", "pool", "freestyle", "backstroke"],
            "strength training": [
                "weight",
                "lifting",
                "bench",
                "squat",
                "deadlift",
                "strength",
            ],
            "tennis": ["tennis", "serve", "backhand", "forehand", "court"],
            "soccer": ["soccer", "football", "goal", "kick", "pitch"],
            "basketball": ["basketball", "shoot", "dribble", "court", "hoop"],
            "yoga": ["yoga", "pose", "meditation", "flexibility"],
            "hiking": ["hiking", "hike", "trail", "mountain", "outdoor"],
            "crossfit": ["crossfit", "wod", "box", "amrap", "emom"],
        }

        for sport, keywords in sport_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_sports.append(sport)

        return detected_sports

    async def _read_onboarding_file_content(self, file_path: str) -> str:
        """
        Read content from an uploaded onboarding file.

        Args:
            file_path: Path to the file to read

        Returns:
            String content of the file, or empty string if unable to read
        """
        try:
            import os

            if not os.path.exists(file_path):
                logger.error(f"[File Reader] File does not exist: {file_path}")
                return ""

            # Get file extension to determine how to read it
            _, ext = os.path.splitext(file_path.lower())

            if ext == ".pdf":
                return await self._read_pdf_content(file_path)
            elif ext in [".txt", ".csv"]:
                return await self._read_text_content(file_path)
            elif ext in [".doc", ".docx"]:
                return await self._read_word_content(file_path)
            else:
                logger.warning(
                    f"[File Reader] Unsupported file type for content extraction: {ext}"
                )
                return (
                    f"File type {ext} uploaded but content extraction not supported yet"
                )

        except Exception as e:
            logger.error(f"[File Reader] Error reading file {file_path}: {e}")
            return ""

    async def _read_text_content(self, file_path: str) -> str:
        """Read content from text/CSV files."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                # Limit content size to prevent overwhelming the LLM
                if len(content) > 10000:  # 10KB limit
                    content = content[:10000] + "\n... [Content truncated due to size]"
                return content
        except Exception as e:
            logger.error(f"[File Reader] Error reading text file {file_path}: {e}")
            return ""

    async def _read_pdf_content(self, file_path: str) -> str:
        """Read content from PDF files using LangChain's PDF loader."""
        try:
            # Try to import LangChain's PDF loader first
            try:
                from langchain_community.document_loaders import PyPDFLoader

                logger.info(
                    f"[File Reader] Using LangChain PyPDFLoader for {file_path}"
                )
                loader = PyPDFLoader(file_path)
                pages = loader.load()

                content = ""
                for page_num, page in enumerate(pages):
                    if page_num >= 10:  # Limit to first 10 pages
                        content += "\n... [Additional pages truncated]"
                        break

                    page_content = page.page_content.strip()
                    if page_content:  # Only add non-empty pages
                        content += f"\n--- Page {page_num + 1} ---\n{page_content}"

                    # Limit total content size
                    if len(content) > 10000:  # 10KB limit
                        content = (
                            content[:10000] + "\n... [Content truncated due to size]"
                        )
                        break

                if content.strip():
                    logger.info(
                        f"[File Reader] Successfully extracted {len(content)} characters from PDF"
                    )
                    return content.strip()
                else:
                    logger.warning(
                        f"[File Reader] No text content extracted from PDF {file_path}"
                    )
                    return "PDF file uploaded but no readable text content was found"

            except ImportError:
                logger.warning(
                    "[File Reader] LangChain PDF loader not available, falling back to PyPDF2"
                )
                # Fallback to PyPDF2 if LangChain is not available
                try:
                    import PyPDF2
                except ImportError:
                    logger.warning(
                        "[File Reader] PyPDF2 also not available, cannot read PDF content"
                    )
                    return "PDF file uploaded but neither LangChain nor PyPDF2 libraries are available for content extraction"

                content = ""
                with open(file_path, "rb") as f:
                    pdf_reader = PyPDF2.PdfReader(f)
                    for page_num, page in enumerate(pdf_reader.pages):
                        if page_num >= 10:  # Limit to first 10 pages
                            content += "\n... [Additional pages truncated]"
                            break
                        page_text = page.extract_text()
                        content += f"\n--- Page {page_num + 1} ---\n{page_text}"

                        # Limit total content size
                        if len(content) > 10000:  # 10KB limit
                            content = (
                                content[:10000]
                                + "\n... [Content truncated due to size]"
                            )
                            break

                return content.strip()

        except Exception as e:
            logger.error(f"[File Reader] Error reading PDF file {file_path}: {e}")
            return f"PDF file uploaded but error reading content: {str(e)}"

    async def _read_word_content(self, file_path: str) -> str:
        """Read content from Word documents."""
        try:
            # Try to import python-docx for Word document reading
            try:
                from docx import Document
            except ImportError:
                logger.warning(
                    "[File Reader] python-docx not available, cannot read Word content"
                )
                return "Word document uploaded but python-docx library not available for content extraction"

            doc = Document(file_path)
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"

                # Limit content size
                if len(content) > 10000:  # 10KB limit
                    content = content[:10000] + "\n... [Content truncated due to size]"
                    break

            return content.strip()
        except Exception as e:
            logger.error(f"[File Reader] Error reading Word file {file_path}: {e}")
            return f"Word document uploaded but error reading content: {str(e)}"

    def _analyze_missing_categories(
        self, messages: list, sidebar_data
    ) -> Dict[str, Any]:
        """
        Analyze what required categories are missing from the conversation.
        Returns information about what still needs to be asked.
        """
        # Combine all user text for analysis
        user_messages = [msg for msg in messages if isinstance(msg, HumanMessage)]
        all_user_text = " ".join([msg.content.lower() for msg in user_messages])

        # Also check sidebar data for existing information
        existing_categories = set()

        # Check goals
        if sidebar_data.goals and sidebar_data.goals.exists and sidebar_data.goals.list:
            existing_categories.add("fitness_goals")

        # Check summary items for other categories
        summary_items = sidebar_data.summary_items or []
        for item in summary_items:
            category_lower = item.category.lower()
            if "experience" in category_lower or "level" in category_lower:
                existing_categories.add("experience_level")
            elif (
                "time" in category_lower
                or "availability" in category_lower
                or "days" in category_lower
            ):
                existing_categories.add("time_commitment")
            elif (
                "equipment" in category_lower
                or "gym" in category_lower
                or "access" in category_lower
            ):
                existing_categories.add("equipment_access")
            elif (
                "priorities" in category_lower
                or "focus" in category_lower
                or "season" in category_lower
            ):
                existing_categories.add("priorities_seasonality")

        # Check conversation text for missing categories
        text_based_categories = set()
        for category_key, category_info in REQUIRED_ONBOARDING_CATEGORIES.items():
            for keyword in category_info["keywords"]:
                if keyword in all_user_text:
                    text_based_categories.add(category_key)
                    break

        # Combine both sources
        found_categories = existing_categories.union(text_based_categories)
        missing_categories = (
            set(REQUIRED_ONBOARDING_CATEGORIES.keys()) - found_categories
        )

        logger.info(f"[Missing Analysis] Found categories: {found_categories}")
        logger.info(f"[Missing Analysis] Missing categories: {missing_categories}")

        # Select the most important missing category to ask about
        priority_order = [
            "fitness_goals",
            "experience_level",
            "time_commitment",
            "equipment_access",
            "priorities_seasonality",
        ]
        next_to_ask = None

        for category in priority_order:
            if category in missing_categories:
                next_to_ask = category
                break

        return {
            "found_categories": found_categories,
            "missing_categories": missing_categories,
            "next_to_ask": next_to_ask,
            "completion_percentage": len(found_categories)
            / len(REQUIRED_ONBOARDING_CATEGORIES)
            * 100,
        }

    def _filter_not_specified_items(self, summary_items: list) -> list:
        """
        Filter out summary items that contain 'Not specified' or similar non-informative content.
        """
        filtered_items = []
        not_specified_phrases = [
            "not specified",
            "not mentioned",
            "no information",
            "not provided",
            "not available",
            "not given",
            "not shared",
            "unknown",
            "to be determined",
            "tbd",
            "n/a",
        ]

        for item in summary_items:
            # Skip items with empty or very short details
            if not item.details or len(item.details.strip()) < 3:
                logger.info(f"[Filter] Removing empty item: {item.category}")
                continue

            details_lower = item.details.lower().strip()

            # Skip items that are just placeholder text
            if any(phrase in details_lower for phrase in not_specified_phrases):
                logger.info(
                    f"[Filter] Removing 'not specified' item: {item.category} - {item.details}"
                )
                continue

            # Skip items that are just category labels without content
            if details_lower == item.category.lower():
                logger.info(
                    f"[Filter] Removing duplicate category item: {item.category}"
                )
                continue

            # Keep items with meaningful content
            filtered_items.append(item)

        logger.info(
            f"[Filter] Kept {len(filtered_items)} out of {len(summary_items)} summary items"
        )
        return filtered_items

    def _create_smart_follow_up_prompt(
        self, missing_analysis: Dict[str, Any], base_prompt: str
    ) -> str:
        """
        Create an enhanced prompt that guides the AI to ask for missing information.
        """
        if not missing_analysis["next_to_ask"]:
            return base_prompt

        category_key = missing_analysis["next_to_ask"]
        category_info = REQUIRED_ONBOARDING_CATEGORIES[category_key]

        completion_guidance = f"""

**MISSING INFORMATION GUIDANCE:**
Based on the conversation analysis, the user has provided {missing_analysis['completion_percentage']:.0f}% of the required information.

**NEXT PRIORITY QUESTION:** 
The user hasn't yet provided information about: {category_info['name']}

**SUGGESTED FOLLOW-UP:**
{category_info['ask_prompt']}

**IMPORTANT:** Instead of just acknowledging what they've shared, actively ask about the missing category above. Be natural and conversational, but make sure to gather this specific information to move toward plan completion.

**REMAINING CATEGORIES NEEDED:**
{', '.join([REQUIRED_ONBOARDING_CATEGORIES[cat]['name'] for cat in missing_analysis['missing_categories']])}
"""

        return base_prompt + completion_guidance

    async def _check_completion_status(
        self,
        messages: List,
        goals: List[str],
        summary_items: List,
        selected_sports: List[str],
    ) -> bool:
        """
        Check if we have enough information to generate a plan summary.
        Uses the comprehensive missing categories analysis to be critically aware of gaps.
        """
        try:
            # Use the existing missing categories analysis for critical evaluation
            from athlea_langgraph.states.onboarding_state import (
                SidebarStateData,
                UserGoals,
            )

            # Create a temporary sidebar data object for analysis
            temp_sidebar_data = SidebarStateData(
                goals=UserGoals(list=goals, exists=len(goals) > 0),
                summary_items=summary_items,
                selected_sports=selected_sports,
            )

            # Analyze what's missing using the robust existing function
            missing_analysis = self._analyze_missing_categories(
                messages, temp_sidebar_data
            )

            found_categories = missing_analysis["found_categories"]
            missing_categories = missing_analysis["missing_categories"]
            completion_percentage = missing_analysis["completion_percentage"]

            logger.info(
                f"[Critical Completion Check] Found categories: {found_categories}"
            )
            logger.info(
                f"[Critical Completion Check] Missing categories: {missing_categories}"
            )
            logger.info(
                f"[Critical Completion Check] Completion percentage: {completion_percentage}%"
            )

            # CRITICAL EVALUATION: Require high completion percentage and essential categories
            has_essential_categories = all(
                category in found_categories
                for category in [
                    "fitness_goals",  # Must have goals
                    "experience_level",  # Must know their level
                    "time_commitment",  # Must know availability
                ]
            )

            # Equipment access is important but can be inferred from context if needed
            has_equipment_context = "equipment_access" in found_categories

            # Check if we have substantial conversation indicating comprehensive information sharing
            total_user_text_length = sum(
                len(msg.content)
                for msg in messages
                if hasattr(msg, "content")
                and hasattr(msg, "type")
                and msg.type == "human"
            )
            has_substantial_conversation = (
                total_user_text_length > 150
            )  # More than just basic responses

            # STRICTER COMPLETION CRITERIA:
            # 1. Must have at least 80% completion
            # 2. Must have essential categories (goals, experience, time)
            # 3. Must have selected sports
            # 4. Must have meaningful conversation length
            meets_completion_threshold = (
                completion_percentage >= 80.0
                and has_essential_categories
                and len(selected_sports) > 0
                and has_substantial_conversation
            )

            logger.info(
                f"[Critical Completion Check] Essential categories present: {has_essential_categories}"
            )
            logger.info(
                f"[Critical Completion Check] Equipment context: {has_equipment_context}"
            )
            logger.info(
                f"[Critical Completion Check] Substantial conversation: {has_substantial_conversation} (length: {total_user_text_length})"
            )
            logger.info(
                f"[Critical Completion Check] DECISION: {meets_completion_threshold}"
            )

            if not meets_completion_threshold:
                next_to_ask = missing_analysis.get("next_to_ask")
                logger.info(
                    f"[Critical Completion Check] Still need to gather: {next_to_ask}"
                )
                logger.info(
                    f"[Critical Completion Check] Missing: {missing_categories}"
                )

            return meets_completion_threshold

        except Exception as e:
            logger.error(f"[Critical Completion Check] Error checking completion: {e}")
            # Conservative fallback - don't transition on error
            return False

    async def _create_transition_to_plan_message(
        self,
        goals: List[str],
        summary_items: List,
        selected_sports: List[str],
        conversation_history: List,
    ) -> str:
        """
        Create a dynamic, AI-generated transition message when we have enough information for plan generation.
        This replaces asking more questions with a natural, contextual transition to plan creation.
        """
        try:
            # Build context from gathered information
            goals_context = (
                f"Goals identified: {', '.join(goals)}"
                if goals
                else "General fitness goals"
            )
            sports_context = (
                f"Activities/Sports: {', '.join(selected_sports)}"
                if selected_sports
                else "Various activities"
            )

            # Build summary context from extracted information
            summary_context = []
            for item in summary_items:
                summary_context.append(f"- {item.category}: {item.details}")

            summary_text = (
                "\n".join(summary_context)
                if summary_context
                else "Basic information gathered"
            )

            # Get recent conversation for context (last user message specifically)
            recent_user_message = ""
            for msg in reversed(conversation_history):
                if hasattr(msg, "type") and msg.type == "human":
                    recent_user_message = msg.content[:300] + (
                        "..." if len(msg.content) > 300 else ""
                    )
                    break

            # Create dynamic transition prompt
            transition_prompt = f"""You are Athlea, a fitness coach who has been gathering information from a user during onboarding. 

CONTEXT - Information Successfully Gathered:
{goals_context}
{sports_context}

Summary of Key Information:
{summary_text}

User's Most Recent Message:
"{recent_user_message}"

TASK: Create a natural, conversational transition message that:
1. Acknowledges the specific information they just shared (reference actual details from their recent message)
2. Shows understanding of their overall goals and situation
3. Naturally transitions to creating a plan summary
4. Matches their conversational tone and energy level
5. Feels like a natural continuation of the conversation, not a template

Be specific about what they've told you - reference their actual goals, timeline, experience level, equipment access, etc. Make it feel personal and contextual.

Keep it conversational and encouraging. End by naturally transitioning to plan creation - something like mentioning you'll create a summary for them to review before the full plan.

Write 2-3 sentences maximum. Be natural and specific to their situation."""

            # Use LLM to generate dynamic transition
            from langchain_core.messages import SystemMessage

            transition_llm = self.llm.with_config(
                {"tags": ["transition_message"], "temperature": 0.7}
            )

            response = await transition_llm.ainvoke(
                [SystemMessage(content=transition_prompt)]
            )
            transition_message = response.content.strip()

            logger.info(
                f"[Transition Message] Generated dynamic transition: {len(transition_message)} chars"
            )
            return transition_message

        except Exception as e:
            logger.error(
                f"[Transition Message] Error generating dynamic transition: {e}"
            )
            # Fallback to simple transition
            return "Perfect! Based on everything you've shared, I have what I need to create your personalized training plan. Let me put together a summary for you to review."


# Create the node instance with development mode support
information_gatherer_node = InformationGathererNode()
