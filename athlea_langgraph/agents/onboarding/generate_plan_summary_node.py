"""
Generate Plan Summary Node

This node creates a high-level summary of gathered information and asks for user confirmation
before proceeding to full plan generation. Follows single responsibility principle.
"""

import logging
import asyncio
import os
from datetime import datetime
from typing import Any, Dict
from pymongo import MongoClient

from langchain_core.messages import AIMessage, SystemMessage

from athlea_langgraph.services.azure_openai_service import create_azure_chat_openai
from athlea_langgraph.states.onboarding_state import OnboardingState, SidebarStateData
from athlea_langgraph.utils.prompt_loader import get_prompt_loader

logger = logging.getLogger(__name__)


class GeneratePlanSummaryNode:
    """Node for generating a plan summary and requesting user confirmation"""

    def __init__(self):
        self.llm = create_azure_chat_openai(temperature=0.3)
        self.summary_prompt_template = None  # Will be loaded lazily

        # MongoDB persistence setup (copied from information_gatherer_node)
        self.mongodb_uri = os.getenv("MONGODB_URI")
        self.dev_mode = os.getenv("ONBOARDING_DEV_MODE", "false").lower() == "true"
        self._mongo_client = None

        # Fallback prompt template
        self.fallback_prompt_template = """You are an expert fitness coach creating a PLAN SUMMARY that outlines the structure and approach of the training plan you will create.

Based on the user's information, create a structured plan summary that describes the PLAN STRUCTURE (not just their information).

User Goals: {user_goals}
User Information Summary:
{summary_string}
Selected Sports/Activities: {selected_sports}

Create a plan summary that includes:

**PLAN NAME**: Give the plan a catchy, descriptive name based on their goals
**APPROACH**: Describe the training philosophy/methodology you'll use
**DURATION & PHASES**: Break down the plan into specific training phases (e.g., Base Building, Build, Peak)
**WEEKLY STRUCTURE**: Describe the typical weekly training pattern and session types
**KEY FOCUS AREAS**: What the plan will emphasize based on their goals and constraints
**PROGRESSION STRATEGY**: How intensity/volume will progress over time

Format as a clear, engaging plan preview that shows what the training plan will look like.

Example format:
"## [Descriptive Plan Name]

**Approach**: [Training philosophy/methodology]

**Duration**: [Total length] divided into [X] phases:
- Phase 1: [Name] ([Duration]) - [Focus and objectives]
- Phase 2: [Name] ([Duration]) - [Focus and objectives]

**Weekly Structure**: [Typical week breakdown with session types]

**Key Focus**: [Primary training emphases based on their goals]

**Progression**: [How the plan will advance over time]

This plan is designed to [brief rationale for why it fits their specific goals and constraints].

Are you ready for me to generate your detailed training plan?"

Be specific about the plan structure based on their actual goals, timeline, and constraints. Don't just repeat their information back to them."""

    async def _get_summary_prompt_template(self) -> str:
        """Load the summary prompt template lazily"""
        if self.summary_prompt_template is None:
            try:
                prompt_loader = await get_prompt_loader()
                prompt_config = await prompt_loader.load_prompt(
                    "onboarding/plan_summary_confirmation"
                )
                self.summary_prompt_template = prompt_config.get_rendered_prompt()
                logger.info(
                    "Successfully loaded plan summary confirmation template from file"
                )
            except Exception as e:
                logger.error(f"Failed to load plan summary confirmation template: {e}")
                self.summary_prompt_template = self.fallback_prompt_template

        return self.summary_prompt_template

    async def _get_mongo_client(self):
        """Get or create MongoDB client for persistence"""
        if not self.mongodb_uri:
            logger.warning("[MongoDB] No MONGODB_URI configured, skipping persistence")
            return None

        if not self._mongo_client:
            self._mongo_client = MongoClient(self.mongodb_uri)

        return self._mongo_client

    async def _save_onboarding_sidebar_data(
        self, user_id: str, sidebar_data: SidebarStateData, thread_id: str = None
    ):
        """Persist onboarding sidebar data to MongoDB user document for resume functionality"""
        try:
            # Skip persistence in development mode
            if self.dev_mode:
                logger.info(
                    f"[DEV MODE] Skipping save of onboarding data for user: {user_id}"
                )
                return

            if not user_id or not sidebar_data:
                logger.warning(
                    "[MongoDB] Missing user_id or sidebar_data for persistence"
                )
                return

            mongo_client = await self._get_mongo_client()
            if not mongo_client:
                return

            # Convert Pydantic model to dict for MongoDB storage
            sidebar_dict = (
                sidebar_data.model_dump()
                if hasattr(sidebar_data, "model_dump")
                else sidebar_data.model_dump()
            )

            # Add timestamp for resume logic
            sidebar_dict["last_updated"] = datetime.now().isoformat()

            # Add thread_id if provided for resume functionality
            if thread_id:
                sidebar_dict["thread_id"] = thread_id

            logger.info(
                f"[MongoDB] [generatePlanSummary] Saving onboarding sidebar data for user: {user_id}, thread_id: {thread_id}"
            )

            db = mongo_client["AthleaUserData"]
            users_collection = db["users"]

            # Update user document with onboarding sidebar data and thread_id
            update_data = {
                "onboarding_sidebar_data": sidebar_dict,
                "onboarding_last_updated": sidebar_dict["last_updated"],
            }

            # Also save thread_id at the top level for easy access
            if thread_id:
                update_data["onboarding_thread_id"] = thread_id

            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": update_data},
                    upsert=True,
                ),
            )

            if result.modified_count > 0 or result.upserted_id:
                logger.info(
                    f"[MongoDB] [generatePlanSummary] ✅ Successfully saved onboarding data for user: {user_id}"
                )
            else:
                logger.warning(
                    f"[MongoDB] [generatePlanSummary] No changes made when saving onboarding data for user: {user_id}"
                )

        except Exception as error:
            logger.error(
                f"[MongoDB] [generatePlanSummary] Error saving onboarding sidebar data for user {user_id}: {error}"
            )

    async def _save_onboarding_stage(
        self, user_id: str, onboarding_stage: str, thread_id: str = None
    ):
        """Save the main onboarding stage to MongoDB"""
        try:
            if self.dev_mode:
                logger.info(
                    f"[DEV MODE] Skipping save of onboarding stage for user: {user_id}"
                )
                return

            if not user_id or not onboarding_stage:
                logger.warning(
                    "[MongoDB] Missing user_id or onboarding_stage for persistence"
                )
                return

            mongo_client = await self._get_mongo_client()
            if not mongo_client:
                return

            logger.info(
                f"[MongoDB] [generatePlanSummary] Saving onboarding stage '{onboarding_stage}' for user: {user_id}"
            )

            db = mongo_client["AthleaUserData"]
            users_collection = db["users"]

            # Update user document with onboarding stage
            update_data = {
                "onboarding_stage": onboarding_stage,
                "onboarding_stage_updated": datetime.now().isoformat(),
            }

            # Also save thread_id if provided
            if thread_id:
                update_data["onboarding_thread_id"] = thread_id

            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: users_collection.update_one(
                    {"user_id": user_id},
                    {"$set": update_data},
                    upsert=True,
                ),
            )

            if result.modified_count > 0 or result.upserted_id:
                logger.info(
                    f"[MongoDB] [generatePlanSummary] ✅ Successfully saved onboarding stage '{onboarding_stage}' for user: {user_id}"
                )
            else:
                logger.warning(
                    f"[MongoDB] [generatePlanSummary] No changes made when saving onboarding stage for user: {user_id}"
                )

        except Exception as error:
            logger.error(
                f"[MongoDB] [generatePlanSummary] Error saving onboarding stage for user {user_id}: {error}"
            )

    async def __call__(self, state: OnboardingState) -> Dict[str, Any]:
        """Generate a plan summary and ask for user confirmation"""
        logger.info(
            "[Node: generatePlanSummary] Creating plan summary for confirmation"
        )

        # Check if plan summary was already sent and user is returning
        plan_summary_sent = state.get("plan_summary_sent", False)
        onboarding_stage = state.get("onboarding_stage", "")

        if plan_summary_sent or onboarding_stage == "plan_summary_ready":
            logger.info(
                "[Node: generatePlanSummary] 🎯 Plan summary already sent - skipping regeneration, proceeding to confirmation"
            )
            # Don't regenerate the summary, just return current state to proceed to confirmation router
            return {
                "plan_summary_sent": True,
                "onboarding_stage": "plan_summary_ready",
            }

        # Handle both dict and SidebarStateData cases
        sidebar_raw = state.get("sidebar_data", {})
        logger.info(f"[DEBUG] Raw sidebar_data type: {type(sidebar_raw)}")
        logger.info(f"[DEBUG] Raw sidebar_data: {sidebar_raw}")

        if isinstance(sidebar_raw, SidebarStateData):
            current_sidebar_data = sidebar_raw
            logger.info("[DEBUG] Using SidebarStateData directly")
        else:
            current_sidebar_data = SidebarStateData(**sidebar_raw)
            logger.info("[DEBUG] Created SidebarStateData from dict")

        # Format summary items for the prompt
        summary_string = ""
        if current_sidebar_data.summary_items:
            summary_items_formatted = [
                f"{item.category}: {item.details}"
                for item in current_sidebar_data.summary_items
            ]
            summary_string = "\n - ".join(summary_items_formatted)
            logger.info(
                f"[DEBUG] Formatted {len(current_sidebar_data.summary_items)} summary items"
            )
        else:
            summary_string = "No detailed summary available."
            logger.info("[DEBUG] No summary items available")

        # Format user goals
        user_goals = ""
        if current_sidebar_data.goals and current_sidebar_data.goals.list:
            user_goals = ", ".join(current_sidebar_data.goals.list)
            logger.info(
                f"[DEBUG] Formatted {len(current_sidebar_data.goals.list)} goals: {user_goals}"
            )
        else:
            user_goals = "Not specified"
            logger.info("[DEBUG] No goals available")

        # Format selected sports
        selected_sports = ""
        if current_sidebar_data.selected_sports:
            selected_sports = ", ".join(current_sidebar_data.selected_sports)
            logger.info(
                f"[DEBUG] Formatted {len(current_sidebar_data.selected_sports)} sports: {selected_sports}"
            )
        else:
            selected_sports = "Not specified"
            logger.info("[DEBUG] No selected sports available")

        # Get the summary prompt template
        summary_template = await self._get_summary_prompt_template()
        logger.info(
            f"[DEBUG] Using prompt template: {len(summary_template)} characters"
        )

        # Prepare the system prompt
        system_prompt = summary_template.format(
            user_goals=user_goals,
            summary_string=summary_string,
            selected_sports=selected_sports,
        )

        logger.info(f"[DEBUG] Final system prompt: {len(system_prompt)} characters")
        logger.info(f"[DEBUG] System prompt preview: {system_prompt[:200]}...")

        logger.info("[Node: generatePlanSummary] Streaming plan summary to user...")

        try:
            # Stream the plan summary with confirmation request
            streaming_llm = self.llm.with_config(
                {"tags": ["plan_summary", "confirmation_request", "final_response"]}
            )

            logger.info("[DEBUG] Starting LLM streaming...")
            summary_content = ""
            chunk_count = 0

            async for chunk in streaming_llm.astream(
                [SystemMessage(content=system_prompt)]
            ):
                if hasattr(chunk, "content") and chunk.content:
                    chunk_count += 1
                    summary_content += chunk.content
                    if chunk_count <= 5:  # Log first 5 chunks for debugging
                        logger.info(f"[DEBUG] Chunk {chunk_count}: '{chunk.content}'")

            logger.info(f"[DEBUG] Streaming completed - received {chunk_count} chunks")
            logger.info(
                f"[DEBUG] Total summary content length: {len(summary_content)} characters"
            )
            logger.info(f"[DEBUG] Summary content preview: {summary_content[:300]}...")
            logger.info(f"[DEBUG] Summary content ending: ...{summary_content[-100:]}")

            if not summary_content:
                logger.error("[ERROR] No content received from LLM streaming!")
                summary_content = "I apologize, but I encountered an issue generating your plan summary. Let me try again."

            logger.info("[Node: generatePlanSummary] Plan summary streaming complete")

            # Create the AI message with the summary
            summary_message = AIMessage(content=summary_content)
            logger.info(
                f"[DEBUG] Created AIMessage with content length: {len(summary_message.content)}"
            )
            logger.info(f"[DEBUG] AIMessage type: {type(summary_message)}")
            logger.info(
                f"[DEBUG] AIMessage content preview: {summary_message.content[:200]}..."
            )

            # Update sidebar to reflect plan summary ready state
            updated_sidebar_data = SidebarStateData(
                current_stage="plan_summary_ready",  # New stage indicating summary is ready
                goals=current_sidebar_data.goals,
                summary_items=current_sidebar_data.summary_items,
                generated_plan=current_sidebar_data.generated_plan,
                sport_suggestions=current_sidebar_data.sport_suggestions,
                selected_sport=current_sidebar_data.selected_sport,
                selected_sports=current_sidebar_data.selected_sports,
            )

            logger.info("[DEBUG] Updated sidebar data to plan_summary_ready stage")

            # 🔧 FIX: Save the updated sidebar data and onboarding stage to MongoDB for persistence
            user_id = state.get("user_id")
            if user_id and updated_sidebar_data:
                try:
                    thread_id = state.get("thread_id")
                    logger.info(
                        f"[MongoDB] [generatePlanSummary] 💾 Saving plan_summary_ready stage for user: {user_id}"
                    )

                    # Save both sidebar data and main onboarding stage
                    await self._save_onboarding_sidebar_data(
                        user_id, updated_sidebar_data, thread_id
                    )
                    await self._save_onboarding_stage(
                        user_id, "plan_summary_ready", thread_id
                    )

                    logger.info(
                        f"[MongoDB] [generatePlanSummary] ✅ Successfully persisted plan_summary_ready stage and sidebar data"
                    )
                except Exception as e:
                    logger.error(
                        f"[MongoDB] [generatePlanSummary] ❌ Error saving plan summary stage: {e}"
                    )

            return_data = {
                "messages": [summary_message],
                "sidebar_data": updated_sidebar_data.model_dump(),
                "onboarding_stage": "plan_summary_ready",
                "plan_summary_sent": True,  # Flag to indicate summary was sent
            }

            logger.info(
                f"[DEBUG] Returning data with {len(return_data['messages'])} messages"
            )
            logger.info(
                f"[DEBUG] First message in return: {return_data['messages'][0].content[:100]}..."
            )
            logger.info(f"[DEBUG] Return data keys: {list(return_data.keys())}")
            logger.info(
                f"[DEBUG] plan_summary_sent flag: {return_data['plan_summary_sent']}"
            )

            return return_data

        except Exception as error:
            logger.error(
                f"[Node: generatePlanSummary] Error generating summary: {error}",
                exc_info=True,
            )

            error_sidebar_data = SidebarStateData(
                current_stage="error",
                goals=current_sidebar_data.goals,
                summary_items=current_sidebar_data.summary_items,
                generated_plan=current_sidebar_data.generated_plan,
                sport_suggestions=current_sidebar_data.sport_suggestions,
                selected_sport=current_sidebar_data.selected_sport,
                selected_sports=current_sidebar_data.selected_sports,
            )

            error_message = AIMessage(
                content="I apologize, but I encountered an error while preparing your plan summary. Please try again."
            )

            logger.info(f"[DEBUG] Returning error message: {error_message.content}")

            return {
                "messages": [error_message],
                "sidebar_data": error_sidebar_data.model_dump(),
                "onboarding_stage": "error",
            }


# Create the node instance
generate_plan_summary_node = GeneratePlanSummaryNode()
