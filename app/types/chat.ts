import { BaseMessage } from "@langchain/core/messages";
import { SidebarStateData } from "../api/onboarding/state"; // Adjust path if needed
import { ParsedToolSteps } from "@/utils/toolStepUtils"; // Corrected import

export type MessageDomain =
  | "strength"
  | "cardio"
  | "running"
  | "cycling"
  | "nutrition"
  | "recovery"
  | "mental"
  | "general"
  | "finalizer"
  | "map"
  | "head"
  | "planner"
  | "reasoning"
  | "Athlea";

export interface Message {
  id: string;
  type: "human" | "ai" | "system" | "tool";
  content: string;
  name?: string;
  isLoading?: boolean;
  metadata?: Record<string, any>;
  toolResult?: ToolCall;
  lastUpdated?: number;
  domain?: MessageDomain;
  keywords?: string[];
  highlightedContent?: string;
  sportSuggestions?: { label: string; value: string }[];
  inlineToolTrace?: ParsedToolSteps; // Added field for inline tool trace
}

export interface ToolCall {
  id: string;
  domain: MessageDomain;
  toolName: string;
  timestamp: number;
  content?: any;
  expanded?: boolean;
}

export interface CodeExecution {
  id: string;
  code: string;
  result: string;
  timestamp: number;
}

export const isPlannerOrReasoningDomain = (domain?: MessageDomain): boolean => {
  return domain === "planner" || domain === "reasoning";
};

export interface StreamEventData {
  type:
    | "agent_start"
    | "token"
    | "need_input"
    | "needs_input"
    | "sidebar_update"
    | "complete"
    | "done"
    | "error"
    | "history_loaded"
    | "tool_result"
    | "heartbeat";
  agent?: string;
  content?: string;
  toolName?: string;
  prompt?: string;
  field?: string;
  messages?: any[];
  message?: string;
  sidebarData?: SidebarStateData;
}
