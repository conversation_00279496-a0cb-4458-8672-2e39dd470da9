const VALID_DOMAINS = [
  "general",
  "cycling",
  "running",
  "strength",
  "nutrition",
  "recovery",
];

export function formatSystemMessage(updateInfo: Record<string, any>): string {
  const formattedUpdates = Object.entries(updateInfo)
    .map(([domain, updates]) => {
      const formattedUpdates = Object.entries(updates as Record<string, any>)
        .map(([key, value]) => `${key}: ${value}`)
        .join(", ");
      return `${domain}: {${formattedUpdates}}`;
    })
    .join(", ");

  console.log(`formatSystemMessage: ${formattedUpdates}`);

  return `Training profile updated: ${formattedUpdates}`;
}

export function extractTrainingProfileUpdate(
  updateArgs: any,
  currentProfile: any,
): any {
  console.log(
    `extract_training_profile_update called with arguments:`,
    JSON.stringify(updateArgs),
  );
  console.log(`Current profile:`, JSON.stringify(currentProfile));

  let updatedProfile: any =
    currentProfile && typeof currentProfile === "object"
      ? JSON.parse(JSON.stringify(currentProfile))
      : { training_profile: {} };

  const { domain, ...updateInfo } = updateArgs;

  if (!domain || !VALID_DOMAINS.includes(domain)) {
    console.warn("Invalid or missing domain. Using 'general'.");
    updateArgs.domain = "general";
  }

  if (!updatedProfile.training_profile[domain]) {
    updatedProfile.training_profile[domain] = {};
  }

  // Flatten nested structures and remove invalid domains
  const flattenedUpdateInfo: any = {};
  Object.entries(updateInfo).forEach(([key, value]) => {
    if (value !== null && typeof value === "object" && !Array.isArray(value)) {
      Object.entries(value as Record<string, unknown>).forEach(
        ([nestedKey, nestedValue]) => {
          if (!VALID_DOMAINS.includes(nestedKey)) {
            flattenedUpdateInfo[nestedKey] = nestedValue;
          }
        },
      );
    } else if (!VALID_DOMAINS.includes(key)) {
      flattenedUpdateInfo[key] = value;
    }
  });

  // Update the profile with flattened and cleaned data
  Object.assign(updatedProfile.training_profile[domain], flattenedUpdateInfo);

  // Clean up invalid domains and ensure only valid domains exist
  Object.keys(updatedProfile.training_profile).forEach((domainKey) => {
    if (!VALID_DOMAINS.includes(domainKey)) {
      delete updatedProfile.training_profile[domainKey];
      console.warn(`Removed invalid domain: ${domainKey}`);
    }
  });

  console.log(`Cleaned updated profile:`, JSON.stringify(updatedProfile));
  return updatedProfile;
}

export function mergeTrainingProfileUpdate(
  updateArgs: any,
  currentProfile: any,
): any {
  console.log(
    `mergeTrainingProfileUpdate called with arguments:`,
    JSON.stringify(updateArgs),
  );
  console.log(`Current profile:`, JSON.stringify(currentProfile));

  const { updateInfo } = updateArgs;

  if (!currentProfile.training_profile) {
    currentProfile.training_profile = {};
  }

  // Iterate through each domain in the updateInfo
  Object.entries(updateInfo).forEach(([domain, domainUpdates]) => {
    if (VALID_DOMAINS.includes(domain)) {
      if (!currentProfile.training_profile[domain]) {
        currentProfile.training_profile[domain] = {};
      }
      // Merge the updates for this domain
      Object.assign(currentProfile.training_profile[domain], domainUpdates);
    } else {
      console.warn(`Ignoring updates for invalid domain: ${domain}`);
    }
  });

  console.log(`Updated profile:`, JSON.stringify(currentProfile));
  return currentProfile;
}

export function removeFromTrainingProfile(
  currentProfile: any,
  domain: string,
  key: string,
): any {
  console.log(
    `removeFromTrainingProfile called for domain: ${domain}, key: ${key}`,
  );
  console.log(`Current profile:`, JSON.stringify(currentProfile));

  if (
    !currentProfile.training_profile ||
    !currentProfile.training_profile[domain]
  ) {
    console.warn(`Domain ${domain} not found in profile`);
    return currentProfile;
  }

  if (currentProfile.training_profile[domain].hasOwnProperty(key)) {
    delete currentProfile.training_profile[domain][key];
    console.log(`Removed ${key} from ${domain}`);
  } else {
    console.warn(`Key ${key} not found in ${domain}`);
  }

  console.log(`Updated profile:`, JSON.stringify(currentProfile));
  return currentProfile;
}
