import { NextRequest, NextResponse } from "next/server";
import { AzureOpenAI } from "openai";

// Azure OpenAI Configuration
const AZURE_OPENAI_KEY = process.env.AZURE_OPENAI_KEY;
const AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
const AZURE_DEPLOYMENT_NAME = process.env.AZURE_DEPLOYMENT_NAME;

// Initialize Azure OpenAI client
const azureClient = new AzureOpenAI({
  apiKey: AZURE_OPENAI_KEY,
  endpoint: AZURE_OPENAI_ENDPOINT,
  apiVersion: "2024-08-01-preview",
  deployment: AZURE_DEPLOYMENT_NAME,
});

export async function POST(req: NextRequest) {
  try {
    const { prompt } = await req.json();
    console.log("Received prompt", prompt);

    const completion = await azureClient.chat.completions.create({
      model: AZURE_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content:
            'You are a helpful assistant specializing in generating training plan names. Generate a short, catchy name for a training plan based on the given context. Respond with a single JSON object containing the suggestion in the format {"suggestion": "Your Plan Name Here"}. Do not use markdown or code blocks in your response.',
        },
        {
          role: "user",
          content: `Generate a training plan name based on this context: ${prompt}`,
        },
      ],
      max_tokens: 100,
      n: 1,
      temperature: 0.7,
    });

    const suggestionText = completion.choices[0].message.content;
    console.log("Raw suggestion:", suggestionText);

    let suggestion = null;

    if (suggestionText) {
      try {
        const parsedSuggestion = JSON.parse(suggestionText);
        console.log("Parsed suggestion:", parsedSuggestion);

        if (parsedSuggestion.suggestion) {
          suggestion = {
            suggestion_text: parsedSuggestion.suggestion,
            id: 1,
            text: parsedSuggestion.suggestion,
          };
        } else {
          throw new Error("Suggestion not found in parsed JSON");
        }
      } catch (e) {
        console.error("Failed to parse or validate suggestion:", e);
        console.error("Raw suggestion text:", suggestionText);
        return NextResponse.json(
          { error: "Failed to generate valid suggestion" },
          { status: 500 },
        );
      }
    }

    return NextResponse.json({ suggestions: suggestion ? [suggestion] : [] });
  } catch (error) {
    console.error("Error fetching suggestion from Azure OpenAI:", error);
    return NextResponse.json(
      { error: "Error fetching suggestion from Azure OpenAI" },
      { status: 500 },
    );
  }
}
