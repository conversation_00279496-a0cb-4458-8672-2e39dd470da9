import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";
import { createNotification } from "@/utils/createNotification";
import { Notification } from "../route";
import { renderTitle } from "@/utils/renderTitle";

interface Task {
  id: string;
  text: string;
  date: string;
  time: string
}

interface DateData {
  tasks: Task | Task[];
  notes?: string[];
}

interface NewTasks {
  [date: string]: DateData;
}

interface CalendarTask {
  user_id: string;
  tasks: {
    [date: string]: {
      tasks: Task[];
      notes: string[];
    };
  };
}

const sessionTypeMapping: { [key: string]: string } = {
  run: "running",
  running: "running",
  bike: "cycling",
  cycling: "cycling",
  strength_coach: "strength",
  strength: "strength",
  nutrition: "nutrition",
  recovery: "recovery",
  phase: "phase",
};

const getSessionsForLiter = (sessionType: string | undefined) => {
  return sessionTypeMapping[sessionType?.toLowerCase() ?? ""] || "general";
};

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  console.log("GET request received!");
  try {
    const userId = params.id;
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const collection = db.collection<CalendarTask>("calendarTasks");
    const calendarData = await collection.findOne({ user_id: userId });
    console.log("Calendar data:", calendarData);

    return new NextResponse(
      JSON.stringify({ tasks: calendarData?.tasks || {} }),
      { status: 200 },
    );
  } catch (error) {
    console.error("Error fetching calendar tasks:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch calendar tasks" }),
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  console.log("POST request received!");
  try {
    const userId = params.id;
    console.log("UserId:", userId);

    const body = await req.json();
    console.log("Request body:", JSON.stringify(body, null, 2));

    const { tasks: newTasks, notes: newNotes, tempTaskText } = body;

    console.log("Received userId:", userId);
    console.log("Received tasks:", JSON.stringify(newTasks, null, 2));
    console.log("Received notes:", JSON.stringify(newNotes, null, 2));
    console.log("Received tempTaskText:", tempTaskText);

    if (!newTasks && !newNotes) {
      console.log("Missing tasks and notes");
      return new NextResponse(
        JSON.stringify({ error: "Missing tasks and notes" }),
        { status: 400 },
      );
    }

    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection<CalendarTask>("calendarTasks");

    // Fetch existing tasks for the user
    const existingCalendarData = await collection.findOne({ user_id: userId });
    console.log(
      "Existing calendar data:",
      JSON.stringify(existingCalendarData, null, 2),
    );
    const existingTasks = existingCalendarData?.tasks || {};
    console.log("Existing tasks:", JSON.stringify(existingTasks, null, 2));

    // Merge existingTasks with newTasks and newNotes
    const mergedTasks = { ...existingTasks };

    // Function to add a single task
    const addSingleTask = (date: string, task: any) => {
      console.log(
        `Adding task for date ${date}:`,
        JSON.stringify(task, null, 2),
      );
      if (!mergedTasks[date]) {
        mergedTasks[date] = { tasks: [], notes: [] };
      }
      // Keep the task text as a string without parsing
      mergedTasks[date].tasks.push({
        id: task.id,
        text: task.text, // Keep this as a string
        date: task.date,
        time: task.time
      });
    };

    if (newTasks) {
      console.log("Processing new tasks");
      if (Array.isArray(newTasks)) {
        console.log("newTasks is an array");
        newTasks.forEach((task: any) => {
          addSingleTask(task.date, task);
        });
      } else if (typeof newTasks === "object") {
        console.log("newTasks is an object");
        Object.entries(newTasks as NewTasks).forEach(([date, dateData]) => {
          console.log(`Processing date: ${date}`);
          if (Array.isArray(dateData.tasks)) {
            dateData.tasks.forEach((task: Task) => addSingleTask(date, task));
          } else if (dateData.tasks) {
            addSingleTask(date, dateData.tasks);
          }
        });
      }
    }

    console.log("Merged tasks:", JSON.stringify(mergedTasks, null, 2));

    // Handle new notes
    if (newNotes) {
      console.log("Processing new notes");
      Object.entries(newNotes as { [date: string]: string[] }).forEach(
        ([date, notes]) => {
          console.log(`Adding notes for date ${date}:`, notes);
          if (!mergedTasks[date]) {
            mergedTasks[date] = { tasks: [], notes: [] };
          }
          // Append new notes to existing ones
          mergedTasks[date].notes = [
            ...(mergedTasks[date].notes || []),
            ...(Array.isArray(notes) ? notes : [notes]),
          ];
        },
      );
    }

    console.log(
      "Merged tasks with notes:",
      JSON.stringify(mergedTasks, null, 2),
    );

    const result = await collection.updateOne(
      { user_id: userId },
      { $set: { tasks: mergedTasks } },
      { upsert: true },
    );

    console.log("Database update result:", result);

    if (tempTaskText) {
      console.log("Processing tempTaskText");
      try {
        const parsedTask = JSON.parse(tempTaskText);
        console.log("Parsed tempTaskText:", parsedTask);
        if (parsedTask) {
          const sessionType = getSessionsForLiter(
            parsedTask.session_type?.toLowerCase(),
          );
          console.log("Session type:", sessionType);
          const contentTitle = `${renderTitle(parsedTask, sessionType)} task has been added`;
          console.log("Content title:", contentTitle);
          const contentDescription = "";

          const notificationCollection =
            db.collection<Notification>("notifications");

          await createNotification(
            notificationCollection,
            userId,
            contentDescription,
            contentTitle,
          );
          console.log("Notification created");
        }
      } catch (parseError) {
        console.error("Error parsing tempTaskText:", parseError);
      }
    }

    console.log("Sending success response");
    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error saving calendar tasks:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to save calendar tasks" }),
      { status: 500 },
    );
  }
}

export async function DELETE(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const userId = params.id;
    const { searchParams } = new URL(req.url);
    const taskId = searchParams.get("taskId");
    const date = searchParams.get("date");
    const t = searchParams.get("t");
    const noteIndex = searchParams.get("noteIndex");

    if (!userId || !date) {
      return new NextResponse(
        JSON.stringify({ error: "Missing userId or date" }),
        { status: 400 },
      );
    }

    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection<CalendarTask>("calendarTasks");

    let result;

    if (taskId) {
      if (t === 'edit') {
        // edit task
            result = await collection.updateOne(
          { user_id: userId },
          { $set:{modify:true} },
        );
        console.log('---result---',result);
            return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
      }else{
        // Delete task
        console.log(
          `Attempting to delete task: ${taskId} for user: ${userId} on date: ${date}`,
        );
        result = await collection.updateOne(
          { user_id: userId },
          { $pull: { [`tasks.${date}.tasks`]: { id: taskId } } },
        );

      }
    } else if (noteIndex !== null) {
      // Delete note
      console.log(
        `Attempting to delete note at index: ${noteIndex} for user: ${userId} on date: ${date}`,
      );
      result = await collection.updateOne(
        { user_id: userId },
        { $unset: { [`tasks.${date}.notes.${noteIndex}`]: 1 } },
      );

      // Remove null elements from the notes array
      await collection.updateOne(
        { user_id: userId },
        { $pull: { [`tasks.${date}.notes`]: null } },
      );
    } else {
      return new NextResponse(
        JSON.stringify({ error: "Missing taskId or noteIndex" }),
        { status: 400 },
      );
    }

    if (result.modifiedCount === 0) {
      console.log(`No modifications made for user: ${userId} on date: ${date}`);
      return new NextResponse(
        JSON.stringify({ error: "Item not found or no changes made" }),
        {
          status: 404,
        },
      );
    }

    console.log(
      `Successfully deleted item for user: ${userId} on date: ${date}`,
    );
    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error deleting item:", error);

    let errorMessage = "Failed to delete item";
    let errorDetails = "Unknown error occurred";

    if (error instanceof Error) {
      errorDetails = error.message;
    } else if (typeof error === "string") {
      errorDetails = error;
    }

    return new NextResponse(
      JSON.stringify({
        error: errorMessage,
        details: errorDetails,
      }),
      { status: 500 },
    );
  }
}
