import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

// Helper function to get date objects for a range
function getDateRange(
  daysBack: number,
  daysForward: number,
): { startDate: Date; endDate: Date } {
  const today = new Date();

  const startDate = new Date(today);
  startDate.setDate(startDate.getDate() - daysBack);
  startDate.setHours(0, 0, 0, 0); // Start of the day

  const endDate = new Date(today);
  endDate.setDate(endDate.getDate() + daysForward);
  endDate.setHours(23, 59, 59, 999); // End of the day

  return { startDate, endDate };
}

// Helper function to format a date as YYYY-MM-DD
function formatDateForKey(date: Date): string {
  return date.toISOString().split("T")[0];
}

export async function GET(req: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(req.url);
    const userId = url.searchParams.get("userId");
    const daysBack = parseInt(url.searchParams.get("daysBack") || "7", 10);
    const daysForward = parseInt(
      url.searchParams.get("daysForward") || "7",
      10,
    );
    const domain = url.searchParams.get("domain"); // Optional domain filter

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 },
      );
    }

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("calendar");

    // Fetch the calendar data for the user
    const calendarData = await collection.findOne({ user_id: userId });

    if (!calendarData || !calendarData.tasks) {
      return NextResponse.json({ tasks: {} }, { status: 200 });
    }

    // Get date range
    const { startDate, endDate } = getDateRange(daysBack, daysForward);
    console.log(
      `[CalendarRange] Fetching tasks from ${startDate.toISOString()} to ${endDate.toISOString()}`,
    );

    // Filter tasks within the date range
    const filteredTasks: Record<string, any> = {};

    // Function to check if a task matches the domain filter
    const matchesDomain = (task: any): boolean => {
      if (!domain) return true; // No domain filter, return all tasks

      // Check type directly if it exists and matches
      if (task.type && task.type.toLowerCase() === domain.toLowerCase()) {
        return true;
      }

      // Check if the task text contains the domain name
      if (task.text && task.text.toLowerCase().includes(domain.toLowerCase())) {
        return true;
      }

      return false;
    };

    // Loop through each date in the calendar data
    for (const dateStr in calendarData.tasks) {
      const taskDate = new Date(dateStr);

      // Check if the date is within our range
      if (taskDate >= startDate && taskDate <= endDate) {
        const dateData = calendarData.tasks[dateStr];

        // If domain is specified, filter tasks by domain
        if (domain && dateData.tasks) {
          const filteredDateTasks = dateData.tasks.filter(matchesDomain);

          // Only include this date if there are matching tasks
          if (filteredDateTasks.length > 0) {
            filteredTasks[dateStr] = {
              ...dateData,
              tasks: filteredDateTasks,
            };
          }
        } else {
          // Include all tasks for this date
          filteredTasks[dateStr] = dateData;
        }
      }
    }

    // Return filtered tasks
    return NextResponse.json(
      {
        tasks: filteredTasks,
        range: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          daysBack,
          daysForward,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("[CalendarRange] Error fetching calendar range:", error);
    return NextResponse.json(
      { error: "Failed to fetch calendar range" },
      { status: 500 },
    );
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Allow-Origin": "*",
    },
  });
}
