import { NextRequest } from "next/server";
import { ChatOpenAI } from "@langchain/openai";

export const maxDuration = 300; // 5 minute timeout for API calls

export async function POST(req: NextRequest) {
  console.log("API: Simple chat endpoint called");
  try {
    // Extract query from request
    const body = await req.json();
    console.log(`API: Request body: ${JSON.stringify(body, null, 2)}`);

    // Get query from either messages or prompt field
    let query = "";
    if (body.messages && body.messages.length > 0) {
      // Get the last user message if available
      const lastUserMessage = [...body.messages]
        .reverse()
        .find((msg) => msg.role === "user");
      if (lastUserMessage) {
        query = lastUserMessage.content;
        console.log(`API: Extracted query from messages: "${query}"`);
      }
    }

    // If no user message, use the prompt field
    if (!query && body.prompt) {
      query = body.prompt;
      console.log(`API: Using prompt field as query: "${query}"`);
    }

    console.log(`API: Final query value: "${query}"`);
    const profile = body?.profile;
    console.log(`API: User profile: ${profile ? "present" : "undefined"}`);

    // Always provide a default welcome message for empty queries
    if (!query || !query.trim()) {
      console.log("API: Empty query detected, sending default welcome message");
      const welcomeMessage =
        "Welcome to your fitness chat! How can I help you today? You can ask me about strength training, cardio, nutrition, recovery, or mental training.";

      return new Response(
        JSON.stringify({
          response: welcomeMessage,
        }),
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
    }

    // Set up streaming response
    const { writable, readable } = new TransformStream();
    const writer = writable.getWriter();
    const encoder = new TextEncoder();

    console.log("API: Initializing chat model with streaming");
    const llm = new ChatOpenAI({
      modelName: "gpt-4o-mini",
      temperature: 0.7,
      streaming: true,
      callbacks: [
        {
          handleLLMNewToken(token) {
            console.log(`API: Streaming token: "${token}"`);
            writer.write(encoder.encode(token));
          },
        },
      ],
    });

    // Create a detailed system prompt for the fitness coach
    const systemPrompt = `
      You are a Fitness Coach at Athlea, a premium fitness platform.
      
      Your role is to:
      1. Provide helpful responses about fitness, health, nutrition, or wellness
      2. Give evidence-based advice
      3. Be friendly, motivating, and professional
      
      ${profile ? `User Profile: ${JSON.stringify(profile)}` : ""}
    `;

    console.log("API: Invoking chat model with streaming");

    // Need to create a background process for streaming so we can return the response
    const streamProcess = async () => {
      try {
        await llm.invoke([
          { role: "system", content: systemPrompt },
          { role: "user", content: query },
        ]);
        await writer.close();
      } catch (error) {
        console.error("API: Error in streaming process:", error);
        await writer.close();
      }
    };

    // Start the streaming process
    streamProcess();

    // Return the stream
    console.log("API: Returning streaming response");
    return new Response(readable, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    console.error("API: Error in chat endpoint:", error);

    // Return error response
    return new Response(
      JSON.stringify({
        error: "An error occurred with the chat. Please try again.",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
  }
}
