import { StateGraph, START, END, MemorySaver } from "@langchain/langgraph";
import { PlanGenerationState, planGenerationChannels } from "./state";
import { generatePlanNode } from "./nodes"; // Import the node

// --- Define Node Name ---
const GENERATE_PLAN = "generatePlan";

// --- Define the Checkpointer ---
// Using MemorySaver for simplicity. Could use MongoDbCheckpointer if persistence is needed.
const memorySaver = new MemorySaver();

/**
 * Creates the plan generation graph.
 */
export function createPlanGenerationGraph() {
  const graph = new StateGraph<PlanGenerationState>({
    channels: planGenerationChannels,
  });

  // --- Add the Single Node ---
  graph.addNode(GENERATE_PLAN, generatePlanNode as any);

  // --- Define Edges (Simple Linear Flow) ---
  graph.addEdge(START, GENERATE_PLAN as any);
  graph.addEdge(GENERATE_PLAN as any, END); // End after the node runs

  // --- Compile the Graph ---
  // No interrupts needed in this simple version
  return graph.compile({
    checkpointer: memorySaver,
    // interruptAfter: [], // No interrupt needed
  });
}
