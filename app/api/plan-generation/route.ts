import { NextRequest, NextResponse } from "next/server";
import { RunnableConfig } from "@langchain/core/runnables";
import { createPlanGenerationGraph } from "./graph";
import { PlanGenerationState } from "./state";
import { v4 as uuidv4 } from "uuid";

// --- Global Compiled Graph ---
let compiledGraph = createPlanGenerationGraph();

// Helper to format SSE data
function formatSSEData(type: string, data: any): string {
  return `data: ${JSON.stringify({ type, ...data })}\n\n`;
}

/**
 * POST handler: Initiates plan generation with provided user details.
 */
export async function POST(req: NextRequest) {
  console.log("API: Received POST request to plan-generation API");
  const encoder = new TextEncoder();
  let requestBody;

  try {
    requestBody = await req.json();
  } catch (error) {
    console.error("API PlanGen: Error parsing JSON body:", error);
    return NextResponse.json({ error: "Invalid JSON body" }, { status: 400 });
  }

  // Extract required fields from body
  const {
    userId,
    threadId: providedThreadId, // Allow providing a threadId or generate one
    goal,
    experienceLevel,
    timeCommitment,
    equipment,
  } = requestBody;

  // Validate required input fields
  if (!goal || !experienceLevel || !timeCommitment || !equipment) {
    return NextResponse.json(
      {
        error:
          "Missing required fields: goal, experienceLevel, timeCommitment, equipment",
      },
      { status: 400 },
    );
  }

  const threadId = providedThreadId || `plangen-${uuidv4()}`;
  console.log(
    `API PlanGen: Processing request with Thread=${threadId}, UserId=${userId || "None"}`,
  );

  const stream = new ReadableStream({
    async start(controller) {
      let closed = false;

      try {
        // Initial state includes the input data
        const initialState: Partial<PlanGenerationState> = {
          userId: userId,
          goal: goal,
          experienceLevel: experienceLevel,
          timeCommitment: timeCommitment,
          equipment: equipment,
          messages: [], // Start with empty messages
          generatedPlan: null,
          currentStage: "generating",
        };

        const config: RunnableConfig = {
          configurable: {
            thread_id: threadId,
          },
        };

        console.log(
          `API PlanGen: Calling streamEvents for thread ${threadId}. Initial State Provided.`,
        );

        // Invoke the graph with the initial state
        const eventStream = compiledGraph.streamEvents(initialState, {
          ...config,
          version: "v2", // Use v2 for streamEvents
        });

        // Process stream for tokens and completion
        for await (const event of eventStream) {
          if (closed) break;
          const eventType = event.event;
          const eventName = event.name;
          const eventData = event.data;

          if (
            eventType === "on_chat_model_stream" ||
            eventType === "on_llm_stream"
          ) {
            const chunk = eventData?.chunk;
            let content: string | undefined;
            if (chunk && typeof chunk.content === "string") {
              content = chunk.content;
            } else if (typeof chunk === "string") {
              content = chunk;
            }
            if (content) {
              // Send token with a generic agent name for now
              controller.enqueue(
                encoder.encode(
                  formatSSEData("token", { agent: "plan_generator", content }),
                ),
              );
            }
          } else if (
            eventType === "on_chain_end" &&
            eventName === "__graph__"
          ) {
            console.log("API PlanGen: Graph finished normally.");
            // Graph finished, send complete signal
            controller.enqueue(encoder.encode(formatSSEData("complete", {})));
            closed = true; // Close after sending complete
            break; // Exit loop
          }
        }

        // Fallback in case loop finishes without graph end signal (shouldn't happen)
        if (!closed) {
          console.warn("API PlanGen: Stream finished unexpectedly.");
          controller.enqueue(encoder.encode(formatSSEData("complete", {})));
        }
      } catch (error: any) {
        console.error("API PlanGen: Error during graph execution:", error);
        if (!closed) {
          controller.enqueue(
            encoder.encode(
              formatSSEData("error", {
                message:
                  error.message || "Unknown error during plan generation",
              }),
            ),
          );
        }
      } finally {
        if (!closed) {
          controller.close();
          closed = true;
          console.log("API PlanGen: Stream closed.");
        }
      }
    },
    cancel: (reason) => {
      console.log(`API PlanGen: Stream cancelled. Reason: ${reason}`);
    },
  });

  return new NextResponse(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache, no-transform",
      Connection: "keep-alive",
      "X-Accel-Buffering": "no",
    },
  });
}
