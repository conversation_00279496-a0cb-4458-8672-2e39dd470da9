import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

interface CoachMessageRequest {
  coachId: string;
  userData: any;
}

export async function POST(req: NextRequest) {
  let coachId: string | undefined;
  let userData: any;

  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("coachMessages");

    const payload = (await req.json()) as CoachMessageRequest;
    coachId = payload.coachId;
    userData = payload.userData;

    // Check if required data is present
    if (!coachId) {
      return NextResponse.json(
        { error: "Coach ID is required" },
        { status: 400 },
      );
    }

    // Get Azure OpenAI credentials from environment variables
    const endpoint = process.env.AZURE_OPENAI_ENDPOINT!;
    const apiKey = process.env.AZURE_OPENAI_API_KEY!;
    const deploymentName = process.env.AZURE_DEPLOYMENT_NAME!;

    // Validate environment variables
    if (!endpoint || !apiKey || !deploymentName) {
      console.error("Missing Azure OpenAI environment variables");
      console.error("Endpoint:", endpoint ? "Set" : "Missing");
      console.error("API Key:", apiKey ? "Set" : "Missing");
      console.error("Deployment Name:", deploymentName ? "Set" : "Missing");

      // Generate a fallback message instead of failing
      const fallbackMessage = generateFallbackMessage(
        coachId,
        userData?.given_name || userData?.name,
      );
      return NextResponse.json({ message: fallbackMessage });
    }

    // Get focus area based on coach ID
    const getFocusArea = (id: string): string => {
      switch (id) {
        case "mental":
          return "enhancing performance through mental techniques";
        case "recovery":
          return "recovery techniques and injury prevention";
        case "strength":
          return "strength and conditioning";
        case "nutrition":
          return "dietary planning and nutrition optimization";
        default:
          return "your goals";
      }
    };

    // Format user data for the prompt
    const userInfo = {
      name: userData?.given_name || userData?.name || "",
      age: userData?.age || "",
      sport: userData?.sport || userData?.preferred_activity || "",
      fitnessLevel: userData?.fitness_level || "",
      goals: userData?.goals || [],
    };

    // Create prompt for the AI
    const prompt = `
      You are a ${coachId} coach specializing in ${getFocusArea(coachId)}.
      Write a brief, personalized welcome message for a user with these details (use only what seems appropriate):
      Name: ${userInfo.name}
      Age: ${userInfo.age}
      Sport/Activity: ${userInfo.sport}
      Fitness Level: ${userInfo.fitnessLevel}
      Goals: ${JSON.stringify(userInfo.goals)}
      
      The welcome message should:
      1. Be 1-2 sentences only
      2. Start with "Welcome" but DON'T include any placeholders like [Name] or [User's Name]
      3. IMPORTANT: Do NOT include any placeholders like [Sport/Activity] in your message - reference the actual sport if available
      4. Mention your focus area (${getFocusArea(coachId)})
      5. Feel natural and conversational - avoid rigid templates
      6. If the user has provided goals or sport preferences, incorporate them naturally
      7. Be encouraging and professional
      
      Return only the message text with no additional formatting or explanation.
    `;

    // Full URL for the deployment
    const apiUrl = `${endpoint}/openai/deployments/${deploymentName}/chat/completions?api-version=2023-05-15`;

    // Request body for Azure OpenAI
    const requestBody = {
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
      max_tokens: 100,
    };

    const isExistDoc = await collection.findOne({
      userId: userData.user_id,
      coachId,
    });
    if (isExistDoc) {
      console.log("---isExistDoc---", isExistDoc);
      const date1 = new Date(isExistDoc.date).getTime();
      const date2 = new Date().getTime(); // current date and time

      // Get the difference in milliseconds
      const diffInMs = date2 - date1;
      const hours24 = 24 * 60 * 60 * 1000;
      if (diffInMs > hours24) {
        console.log("More than 24 hours have passed.");
        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "api-key": apiKey,
          },
          body: JSON.stringify(requestBody),
        });

        // Check for error response
        if (!response.ok) {
          const errorData = await response.json();
          console.error("Azure OpenAI API error:", errorData);

          // Use fallback message instead of returning error
          const fallbackMessage = generateFallbackMessage(
            coachId,
            userInfo.name,
          );
          return NextResponse.json({ message: fallbackMessage });
        }

        // Process the successful response
        const responseData = await response.json();
        let message =
          responseData.choices?.[0]?.message?.content ||
          generateFallbackMessage(coachId);

        // Trim any extra whitespace and ensure the message doesn't have quotes
        message = message.trim().replace(/^["']|["']$/g, "");
        await collection.findOneAndUpdate(
          { userId: userData.user_id, coachId },
          {
            $set: {
              message,
              date: new Date(),
            },
          },
          { upsert: true },
        );
        return NextResponse.json({ message: isExistDoc.message });
      } else {
        console.log("-----------==========---------", isExistDoc);

        return NextResponse.json({ message: isExistDoc.message });
      }
    } else {
      // Make request to Azure OpenAI API
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": apiKey,
        },
        body: JSON.stringify(requestBody),
      });

      // Check for error response
      if (!response.ok) {
        const errorData = await response.json();
        console.error("Azure OpenAI API error:", errorData);

        // Use fallback message instead of returning error
        const fallbackMessage = generateFallbackMessage(coachId, userInfo.name);
        return NextResponse.json({ message: fallbackMessage });
      }

      // Process the successful response
      const responseData = await response.json();
      let message =
        responseData.choices?.[0]?.message?.content ||
        generateFallbackMessage(coachId);

      // Trim any extra whitespace and ensure the message doesn't have quotes
      message = message.trim().replace(/^["']|["']$/g, "");

      const coachDoc = await collection.insertOne({
        userId: userData.user_id,
        coachId,
        message,
        date: new Date(),
      });
      console.log("---coach doc---", coachDoc);

      return NextResponse.json({ message });
    }
  } catch (error) {
    console.error("Error generating coach message:", error);

    // Use fallback message instead of returning error
    if (coachId) {
      const userName = userData?.given_name || userData?.name;
      const fallbackMessage = generateFallbackMessage(coachId, userName);
      return NextResponse.json({ message: fallbackMessage });
    }

    return NextResponse.json(
      { error: "Failed to generate coach message" },
      { status: 500 },
    );
  }
}

function generateFallbackMessage(coachId: string, name?: string): string {
  switch (coachId) {
    case "mental":
      return "Welcome! I'm here to help you unlock your full potential by enhancing your performance through proven mental techniques—you're on a great path to reaching your goals.";
    case "recovery":
      return "Welcome! I'm here to support your recovery and help prevent injuries as you pursue your goals, so let's work together to keep you strong and injury-free.";
    case "strength":
      return "Welcome! I'm excited to help you improve your strength and conditioning to reach your goals—you're on a great path, and together we'll get you there.";
    case "nutrition":
      return "Welcome! I'm here to support your dietary planning and nutrition optimization as you pursue your goals, and I look forward to helping you achieve your best at your fitness level.";
    default:
      return "Welcome! I'm looking forward to helping you achieve your fitness goals.";
  }
}
