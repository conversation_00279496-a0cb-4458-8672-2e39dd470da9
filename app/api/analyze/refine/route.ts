import { NextRequest, NextResponse } from "next/server";
import { AzureOpenAI } from "openai";
import {
  TREND_ANALYSIS_MESSAGE,
  SINGLE_INSTANCE_MESSAGE,
  toolParameters,
} from "../route";

// Define more specific interfaces
interface AnalysisQuestion {
  question: string;
  context?: string;
  options?: string[];
}

interface AnalysisInsight {
  title: string;
  description: string;
  significance: "low" | "medium" | "high";
  recommendations?: string[];
}

interface Analysis {
  analysisType: "trend" | "single";
  questions?: AnalysisQuestion[];
  insights?: AnalysisInsight[];
  metadata?: {
    dataPoints: number;
    timeRange?: string;
    confidenceScore?: number;
  };
}

interface RefinedAnalysis extends Analysis {
  questions?: AnalysisQuestion[];
}

const azureClient = new AzureOpenAI({
  apiKey: process.env.AZURE_OPENAI_KEY,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  apiVersion: "2024-08-01-preview",
  deployment: process.env.AZURE_DEPLOYMENT_NAME,
});

async function processQuestionResponse(
  question: AnalysisQuestion,
  answer: string,
  data: any[],
  previousAnalysis: Analysis,
): Promise<RefinedAnalysis> {
  const response = await azureClient.chat.completions.create({
    model: process.env.AZURE_DEPLOYMENT_NAME as string,
    messages: [
      {
        role: "system",
        content:
          previousAnalysis.analysisType === "trend"
            ? TREND_ANALYSIS_MESSAGE
            : SINGLE_INSTANCE_MESSAGE,
      },
      {
        role: "user",
        content: `Question: "${question.question}"\nAnswer: "${answer}"\n\nPrevious Analysis: ${JSON.stringify(previousAnalysis)}\n\nData: ${JSON.stringify(data)}`,
      },
    ],
    tools: [toolParameters],
    tool_choice: {
      type: "function",
      function: { name: "analyze_fitness_data" },
    },
  });

  const analysisToolCall = response.choices[0].message.tool_calls?.[0];

  if (
    !analysisToolCall ||
    analysisToolCall.function.name !== "analyze_fitness_data"
  ) {
    throw new Error("Invalid analysis response");
  }

  const refinedAnalysis = JSON.parse(
    analysisToolCall.function.arguments,
  ) as RefinedAnalysis;

  // Return merged analysis
  return {
    ...previousAnalysis,
    ...refinedAnalysis,
  };
}

export async function POST(request: NextRequest) {
  try {
    const {
      question,
      answer,
      data,
      previousAnalysis,
    }: {
      question: AnalysisQuestion;
      answer: string;
      data: any[];
      previousAnalysis: Analysis;
    } = await request.json();

    const refinedAnalysis = await processQuestionResponse(
      question,
      answer,
      data,
      previousAnalysis,
    );

    // Type-safe check for questions
    const hasQuestions =
      Array.isArray(refinedAnalysis.questions) &&
      refinedAnalysis.questions.length > 0;

    if (hasQuestions) {
      return NextResponse.json({
        success: true,
        needsQuestion: true,
        analysis: {
          ...previousAnalysis,
          questions: refinedAnalysis.questions,
        },
      });
    }

    // Otherwise return the refined analysis
    return NextResponse.json({
      success: true,
      refinedInsights: refinedAnalysis.insights || [],
    });
  } catch (error) {
    console.error("Error refining analysis:", error);
    return NextResponse.json(
      { error: "Failed to refine analysis" },
      { status: 500 },
    );
  }
}
