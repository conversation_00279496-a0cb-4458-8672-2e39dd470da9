// app/api/analyze/determine/route.ts
import { NextRequest, NextResponse } from "next/server";
import { determineStats } from "@/utils/statsAnalyzer";

export async function POST(request: NextRequest) {
  try {
    const { userId, fileName } = await request.json();

    if (!userId || !fileName) {
      return NextResponse.json(
        { error: "Missing userId or fileName" },
        { status: 400 },
      );
    }

    const result = await determineStats(userId, fileName);
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to analyze content",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
