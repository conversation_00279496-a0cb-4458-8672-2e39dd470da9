import { NextRequest, NextResponse } from "next/server";
import { parse } from "csv-parse";
import { Readable } from "stream";
import * as XLSX from "xlsx";
import { AzureOpenAI } from "openai";
import {
  Analysis,
  AnalysisQuestion,
  BaseMetricGroup,
  MetricGroup,
  MetricGroups,
} from "@/types/stats";
import {
  chunkArray,
  parseDate,
  inferMetricGroups,
  getDataSummary,
  mergeAnalyses,
} from "@/utils/analysisHelpers";

// Azure OpenAI Configuration
const AZURE_OPENAI_KEY = process.env.AZURE_OPENAI_KEY;
const AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
const AZURE_DEPLOYMENT_NAME = process.env.AZURE_DEPLOYMENT_NAME;
const AZURE_VISION_DEPLOYMENT_NAME = process.env.AZURE_VISION_DEPLOYMENT_NAME;

// Constants
const MAX_FILE_SIZE = 30 * 1024 * 1024; // 30MB in bytes
const MAX_IMAGE_SIZE = 4 * 1024 * 1024; // 4MB for images
const MAX_ROWS = 10000;
const CHUNK_SIZE = 1000;

const ALLOWED_FILE_EXTENSIONS = [
  ".jpg",
  ".jpeg",
  ".png",
  ".gif",
  ".webp",
  ".csv",
  ".xlsx",
  ".xls",
  ".txt",
  ".json",
] as const;

const ALLOWED_MIME_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/gif",
  "image/webp",
  "text/csv",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "text/plain",
  "text/txt",
  "application/txt",
] as const;

// Initialize Azure OpenAI client
const azureClient = new AzureOpenAI({
  apiKey: AZURE_OPENAI_KEY,
  endpoint: AZURE_OPENAI_ENDPOINT,
  apiVersion: "2024-08-01-preview",
  deployment: AZURE_DEPLOYMENT_NAME,
});

// File type validation
function validateFileType(file: File): boolean {
  const extension = "." + file.name.split(".").pop()?.toLowerCase();
  const mimeType = file.type.toLowerCase();

  const isAllowedExtension = ALLOWED_FILE_EXTENSIONS.some(
    (ext) => extension.toLowerCase() === ext.toLowerCase(),
  );

  const isAllowedMimeType = ALLOWED_MIME_TYPES.includes(mimeType as any);
  const isJpegVariant =
    (extension === ".jpg" || extension === ".jpeg") &&
    mimeType.startsWith("image/");

  console.log("🔍 File validation:", {
    fileName: file.name,
    extension,
    mimeType,
    isAllowedExtension,
    isAllowedMimeType,
    isJpegVariant,
  });

  return isAllowedExtension || isAllowedMimeType || isJpegVariant;
}

// System message for analysis
const TYPE_DETERMINATION_MESSAGE = `You are an AI expert at analyzing fitness and health data. Your task is to determine if the provided data represents:

1. TREND DATA: Data collected over multiple days or training sessions, showing:
   - Measurements across different dates/sessions
   - Long-term progression over time (days, weeks, months)
   - Historical comparisons between different days/sessions
   - Changes in metrics across multiple training days

2. SINGLE INSTANCE: A snapshot or single training session, such as:
   - One workout session with multiple segments
   - A single training plan or workout blueprint
   - Sequential measurements within one session
   - A point-in-time health assessment
   - Same-day measurements or intervals

Key Distinction:
- If the data spans multiple days/sessions = "trend"
- If the data is from a single day/session = "single"

Analyze the provided data and respond with ONLY one of these two words: "trend" or "single".

Important: Just respond with one word - either "trend" or "single". No explanation needed.`;

const TREND_ANALYSIS_MESSAGE_BASE = `You are a data analysis expert specializing in data analysis. Your primary task is to analyze the provided dataset and provide insights. Ask questions ONLY if there is critical missing information that prevents you from correctly analyzing specific patterns or anomalies.

Analyze the data in THREE PARTS:

PART 1 - Date Range Analysis:
1. Identify the start and end dates of the data
2. Consider the time period covered in your analysis
3. Note any gaps or irregularities in the date sequence

PART 2 - Data Organization:
1. Examine all numerical metrics in the dataset
2. Group related metrics together based on their names and relationships
3. For each metric, determine:
   - An appropriate unit of measurement based on the metric name and values
   - The metric type (continuous, discrete, ordinal, or categorical)
   - A descriptive name for the group it belongs to
   - A description of what the metric measures
   - The category it belongs to (Cardiovascular, Strength, Endurance, etc.)

PART 3 - Analysis:
1. Analyze the data to identify:
   - Key trends and their confidence levels
   - Notable improvements or declines with quantified changes
   - Correlations between metrics
   - Unusual patterns or anomalies
   - Actionable recommendations based on the data

Your response must follow this structure:
{
  "dateRange": {
    "startDate": "YYYY-MM-DD",
    "endDate": "YYYY-MM-DD",
    "duration": "X days/weeks/months"
  },
  "metricGroups": {
    "Group Name": {
      "metrics": [
        {
          "name": "MetricName",
          "unit": "appropriate_unit",
          "type": "continuous|discrete|ordinal|categorical",
          "description": "What this metric measures"
        }
      ],
      "description": "Description of this group of metrics",
      "category": "Cardiovascular|Strength|Endurance|Recovery|Sleep|Nutrition|Body Composition|Performance|Wellness|Activity|Other"
    }
  },
  "trends": [
    {
      "metric": "metric_name",
      "period": "time_period",
      "change": numeric_value,
      "direction": "increasing|decreasing|stable",
      "confidence": "high|medium|low"
    }
  ],
  "insights": {
    "performance": [
      "Performance-related insights"
    ],
    "patterns": [
      "Observed patterns when applicable"
    ],
    "recommendations": [
      "Actionable recommendations based on the data"
    ]
  }
}

IMPORTANT GUIDELINES:
1. Make reasonable assumptions whenever possible
2. For each metric group:
   - Ensure all metrics have appropriate units from the allowed list (bpm, steps, watts, kg, reps, etc.)
   - Correctly identify the metric type for proper visualization
   - Assign the most specific category possible
3. For trends:
   - Only include when sufficient data points exist
   - Quantify changes where possible
   - Include confidence level based on data quality and quantity
4. For insights:
   - Performance insights are required
   - Include patterns only when clear patterns exist
   - Always provide actionable recommendations
5. Base analysis on common patterns in fitness/health data
6. Prioritize clear, actionable insights over complex analysis

Remember:
- Performance insights and recommendations are required
- Trends are optional and should only be included with sufficient data
- Each metric must have a type and appropriate unit
- Categories should be as specific as possible`;

const SINGLE_INSTANCE_MESSAGE_BASE = `Analyze this fitness/health data and provide detailed numerical analysis. Your analysis should include specific numbers from the data.

PART 1 - Basic Metrics Analysis:
1. Identify and list all numerical values shown (heart rates, power outputs, durations)
2. Calculate key statistics for each metric type (ranges, averages)
3. Note intensity distributions (time spent in each zone)

Example Format:
- Heart Rate Ranges: "Heart rate zones range from X-Y bpm in recovery to A-B bpm in VO2max"
- Power Output: "Power outputs vary from X-Y watts in recovery to A-B watts in threshold"
- Duration Analysis: "Total workout includes X minutes of high intensity, Y minutes of recovery"

PART 2 - Detailed Recommendations with Numbers:
Your recommendations should reference specific numbers from the data, for example:
- "Maintain recovery heart rate between 74-88 bpm to ensure proper recuperation"
- "Target power zones of 175-189W during VO2max intervals"
- "Consider extending threshold intervals from current 4 minutes to 5-6 minutes"

Structure the response following this exact format:
{
  "dateRange": {
    "startDate": "YYYY-MM-DD",
    "endDate": "YYYY-MM-DD",
    "duration": "duration_period"
  },
  "metricGroups": {
    "Group Name": {
      "metrics": [
        {
          "name": "metric_name",
          "unit": "unit_of_measurement",
          "type": "data_type",
          "description": "detailed_description_with_numbers"
        }
      ],
      "description": "description_with_numerical_analysis",
      "category": "category_name"
    }
  },
  "insights": {
    "performance": [
      "performance_insight_with_specific_numbers"
    ],
    "patterns": [
      "pattern_observation_with_numerical_data"
    ],
    "recommendations": [
      "specific_recommendation_with_target_numbers"
    ]
  }
}

REQUIRED NUMERICAL ANALYSIS:
1. For each training zone:
   - Exact heart rate ranges
   - Exact power outputs
   - Specific durations
2. For performance insights:
   - Intensity distribution percentages
   - Zone-specific metrics
   - Time-in-zone calculations
3. For recommendations:
   - Target heart rate ranges
   - Suggested power outputs
   - Specific duration modifications

Remember:
- Include SPECIFIC NUMBERS in all analysis points
- Reference EXACT VALUES from the data
- Provide QUANTIFIED recommendations
- Use PRECISE RANGES in all insights`;

// Analysis tool parameters
export const toolParameters = {
  type: "function",
  function: {
    name: "analyze_fitness_data",
    description:
      "Analyze any type of fitness data and provide structured insights",
    parameters: {
      type: "object",
      properties: {
        questions: {
          type: "array",
          description: "Questions needed for clarification",
          items: {
            type: "object",
            properties: {
              id: { type: "string" },
              question: { type: "string" },
              context: { type: "string" },
              metrics: {
                type: "array",
                items: { type: "string" },
              },
              importance: {
                type: "string",
                enum: ["high", "medium", "low"],
              },
            },
            required: ["id", "question", "context", "metrics", "importance"],
          },
        },
        dateRange: {
          type: "object",
          description: "Time period covered by the data",
          properties: {
            startDate: { type: "string", format: "date" },
            endDate: { type: "string", format: "date" },
            duration: { type: "string" },
          },
          required: ["startDate", "endDate", "duration"],
        },
        metricGroups: {
          type: "object",
          description: "Dynamic grouping of related metrics",
          patternProperties: {
            "^.*$": {
              type: "object",
              properties: {
                metrics: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      unit: { type: "string" },
                      type: {
                        type: "string",
                        enum: [
                          "continuous",
                          "discrete",
                          "ordinal",
                          "categorical",
                        ],
                      },
                      description: { type: "string" },
                    },
                    required: ["name", "unit", "description"],
                  },
                },
                description: { type: "string" },
                category: {
                  type: "string",
                  enum: [
                    "Cardiovascular",
                    "Strength",
                    "Endurance",
                    "Recovery",
                    "Sleep",
                    "Nutrition",
                    "Body Composition",
                    "Performance",
                    "Wellness",
                    "Activity",
                    "Other",
                  ],
                },
              },
              required: ["metrics", "description"],
            },
          },
        },
        insights: {
          type: "object",
          properties: {
            performance: {
              type: "array",
              items: { type: "string" },
              description: "Performance-related insights",
            },
            patterns: {
              type: "array",
              items: { type: "string" },
            },
            recommendations: {
              type: "array",
              items: { type: "string" },
              description: "Actionable recommendations based on the data",
            },
          },
          required: ["performance", "recommendations"],
        },
      },
      required: ["metricGroups", "insights"],
    },
  },
} as const;

// Add this to the beginning of your TREND_ANALYSIS_MESSAGE
const questionGuidelines = `If you encounter critical uncertainties that prevent accurate analysis, return ONLY a questions object:

{
  "questions": [{
    "id": "unique_id",
    "question": "Your specific question about the data",
    "context": "Why this information is needed for analysis",
    "metrics": ["affected_metric_names"],
    "importance": "high|medium|low"
  }]
}

Common scenarios requiring questions:
1. Ambiguous date formats or irregular date patterns
2. Unclear metric units or scales
3. Potentially erroneous or outlier values
4. Missing context for specialized metrics

Only ask questions if they are truly necessary for accurate analysis. If no questions are needed, proceed with the standard analysis format below.

`;

// Update your system messages by prepending the question guidelines
export const TREND_ANALYSIS_MESSAGE =
  questionGuidelines + TREND_ANALYSIS_MESSAGE_BASE;
export const SINGLE_INSTANCE_MESSAGE =
  questionGuidelines + SINGLE_INSTANCE_MESSAGE_BASE;

// Image analysis function
async function analyzeImageWithGPT4(imageBuffer: Buffer): Promise<any> {
  try {
    console.log(
      `Analyzing image with Azure GPT-4, buffer size: ${imageBuffer.length} bytes`,
    );

    const base64Image = imageBuffer.toString("base64");

    // First, determine if this is trend data or single instance
    console.log("🔄 Determining analysis type");
    const typeResponse = await azureClient.chat.completions.create({
      model: AZURE_VISION_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content: TYPE_DETERMINATION_MESSAGE,
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this image and determine if it shows trend data or single instance data.",
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${base64Image}`,
                detail: "high",
              },
            },
          ],
        },
      ],
    });

    const analysisType = typeResponse.choices[0].message.content
      ?.toLowerCase()
      .trim() as "trend" | "single";
    console.log(`📊 Determined analysis type: ${analysisType}`);

    // Now perform the full analysis with the appropriate message
    const response = await azureClient.chat.completions.create({
      model: AZURE_VISION_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content:
            analysisType === "trend"
              ? TREND_ANALYSIS_MESSAGE
              : SINGLE_INSTANCE_MESSAGE,
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this fitness/health data according to the specified format.",
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${base64Image}`,
                detail: "high",
              },
            },
          ],
        },
      ],
      tools: [toolParameters],
      tool_choice: {
        type: "function",
        function: { name: "analyze_fitness_data" },
      },
      max_tokens: 1000,
    });

    const analysisToolCall = response.choices[0].message.tool_calls?.[0];
    if (
      !analysisToolCall ||
      analysisToolCall.function.name !== "analyze_fitness_data"
    ) {
      throw new Error("Invalid analysis response");
    }

    const analysis = JSON.parse(analysisToolCall.function.arguments);

    // Add the analysis type to the results
    analysis.analysisType = analysisType;

    // Ensure appropriate fields exist based on analysis type
    if (analysisType === "single") {
      analysis.trends = []; // No trends for single instance
    } else {
      analysis.trends = analysis.trends || [];
    }

    analysis.insights = analysis.insights || {
      performance: [],
      patterns: [],
      recommendations: [],
    };

    console.log("Analysis structure:", {
      analysisType,
      groupsCount: Object.keys(analysis.metricGroups || {}).length,
      trendsCount: analysis.trends?.length || 0,
      insightsCount: Object.keys(analysis.insights || {}).length,
    });

    return analysis;
  } catch (error) {
    console.error("Error analyzing image with Azure GPT-4:", error);
    throw error;
  }
}

async function determineAnalysisType(
  content: Buffer | string,
  fileType: string,
): Promise<"trend" | "single"> {
  try {
    const response = await azureClient.chat.completions.create({
      model: fileType.startsWith("image/")
        ? (AZURE_VISION_DEPLOYMENT_NAME as string)
        : (AZURE_DEPLOYMENT_NAME as string),
      messages: [
        {
          role: "system",
          content: TYPE_DETERMINATION_MESSAGE,
        },
        {
          role: "user",
          content: fileType.startsWith("image/")
            ? [
                {
                  type: "text",
                  text: "Analyze this image and determine if it shows trend data or single instance data.",
                },
                {
                  type: "image_url",
                  image_url: {
                    url: `data:${fileType};base64,${
                      Buffer.isBuffer(content)
                        ? content.toString("base64")
                        : Buffer.from(content).toString("base64")
                    }`,
                    detail: "high",
                  },
                },
              ]
            : `Analyze this data and determine if it shows trend data or single instance data: ${content}`,
        },
      ],
    });

    const analysisType = response.choices[0].message.content
      ?.toLowerCase()
      .trim() as "trend" | "single";

    console.log(`🔍 Analysis type determined: ${analysisType}`);
    return analysisType;
  } catch (error) {
    console.error("Error determining analysis type:", error);
    return "single"; // Default to single if determination fails
  }
}

// Helper function to handle image analysis
async function handleImageAnalysis(file: File) {
  if (file.size > MAX_IMAGE_SIZE) {
    return NextResponse.json(
      { error: "Image size exceeds 4MB limit" },
      { status: 400 },
    );
  }

  try {
    const buffer = await file.arrayBuffer();
    const imageBuffer = Buffer.from(buffer);
    console.log("🔄 Starting image analysis");

    // First, determine if this is trend data or single instance
    console.log("🔍 Determining analysis type");
    const typeResponse = await azureClient.chat.completions.create({
      model: AZURE_VISION_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content: TYPE_DETERMINATION_MESSAGE,
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this image and determine if it shows trend data or single instance data.",
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBuffer.toString("base64")}`,
                detail: "high",
              },
            },
          ],
        },
      ],
    });

    const analysisType = typeResponse.choices[0].message.content
      ?.toLowerCase()
      .trim() as "trend" | "single";
    console.log(`📊 Determined analysis type: ${analysisType}`);

    // Now perform the analysis with appropriate message
    const response = await azureClient.chat.completions.create({
      model: AZURE_VISION_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content:
            analysisType === "trend"
              ? TREND_ANALYSIS_MESSAGE
              : SINGLE_INSTANCE_MESSAGE,
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Analyze this fitness/health data according to the specified format.",
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${imageBuffer.toString("base64")}`,
                detail: "high",
              },
            },
          ],
        },
      ],
      tools: [toolParameters],
      tool_choice: {
        type: "function",
        function: { name: "analyze_fitness_data" },
      },
      max_tokens: 1000,
    });

    const analysisToolCall = response.choices[0].message.tool_calls?.[0];
    if (
      !analysisToolCall ||
      analysisToolCall.function.name !== "analyze_fitness_data"
    ) {
      throw new Error("Invalid analysis response");
    }

    const analysis = JSON.parse(analysisToolCall.function.arguments);
    const currentDate = new Date().toISOString().split("T")[0];

    // Check for questions first
    if (analysis.questions?.length > 0) {
      return NextResponse.json({
        success: true,
        needsQuestion: true,
        analysis: {
          ...analysis,
          analysisType,
        },
        metadata: {
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          rowCount: 1,
          columnCount: 1,
          processedChunks: 1,
        },
        rawData: [
          {
            Date: currentDate,
            Workout_Analysis: analysis,
          },
        ],
        dateRange: {
          startDate: new Date(currentDate),
          endDate: new Date(currentDate),
          formattedRange: currentDate,
        },
      });
    }

    // If no questions, proceed with normal analysis
    analysis.analysisType = analysisType;
    analysis.trends = analysisType === "trend" ? analysis.trends || [] : [];
    analysis.insights = analysis.insights || {
      performance: [],
      patterns: [],
      recommendations: [],
    };

    const finalResponse = {
      success: true,
      analysis: {
        ...analysis,
        metricGroups: analysis.metricGroups || {
          "Image Analysis": {
            metrics: [
              {
                name: "Workout_Analysis",
                unit: "text",
                type: "categorical",
                description: "Analysis of workout content from image",
              },
            ],
            description: "Analysis of image content",
            category: "Performance",
          },
        },
      },
      metadata: {
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        rowCount: 1,
        columnCount: 1,
        processedChunks: 1,
        image_url: `data:image/jpeg;base64,${imageBuffer.toString("base64")}`,
      },
      rawData: [
        {
          Date: currentDate,
          Workout_Analysis: analysis,
        },
      ],
      dateRange: {
        startDate: new Date(currentDate),
        endDate: new Date(currentDate),
        formattedRange: currentDate,
      },
    };

    console.log("📊 Image analysis results:", finalResponse);
    return NextResponse.json(finalResponse);
  } catch (error) {
    console.error("❌ Error processing image:", error);
    return NextResponse.json(
      {
        error: "Failed to analyze image",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

async function handleDataAnalysis(file: File) {
  if (file.size > MAX_FILE_SIZE) {
    return NextResponse.json(
      { error: `File size exceeds ${MAX_FILE_SIZE / (1024 * 1024)}MB limit` },
      { status: 400 },
    );
  }

  try {
    // Parse file data
    const buffer = await file.arrayBuffer();
    let data: any[] = [];

    if (file.type === "text/csv") {
      console.log("📑 Parsing CSV file");
      const stream = Readable.from(Buffer.from(buffer));
      const parser = parse({
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });

      for await (const record of stream.pipe(parser)) {
        data.push(record);
        if (data.length > MAX_ROWS) {
          return NextResponse.json(
            { error: `File exceeds ${MAX_ROWS} rows limit` },
            { status: 400 },
          );
        }
      }
    } else if (
      file.type.includes("spreadsheetml") ||
      file.type.includes("excel")
    ) {
      console.log("📊 Parsing Excel file");
      const workbook = XLSX.read(new Uint8Array(buffer), { type: "array" });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      data = XLSX.utils.sheet_to_json(worksheet);

      if (data.length > MAX_ROWS) {
        return NextResponse.json(
          { error: `File exceeds ${MAX_ROWS} rows limit` },
          { status: 400 },
        );
      }
    } else if (
      file.type === "text/plain" ||
      file.type === "text/txt" ||
      file.type === "application/txt" ||
      file.name.toLowerCase().endsWith(".txt")
    ) {
      console.log("📝 Parsing text file");
      const textContent = Buffer.from(buffer).toString("utf-8");
      try {
        // First try to parse as JSON
        try {
          const jsonData = JSON.parse(textContent);
          data = Array.isArray(jsonData) ? jsonData : [jsonData];
          console.log("✅ JSON parsing successful");
        } catch (jsonError) {
          // If JSON parsing fails, try CSV parsing
          console.log("ℹ️ JSON parse failed, attempting CSV parse");
          const stream = Readable.from(textContent);
          const parser = parse({
            columns: true,
            skip_empty_lines: true,
            trim: true,
            relaxColumnCount: true,
          });

          for await (const record of stream.pipe(parser)) {
            data.push(record);
            if (data.length > MAX_ROWS) {
              return NextResponse.json(
                { error: `File exceeds ${MAX_ROWS} rows limit` },
                { status: 400 },
              );
            }
          }
          console.log("✅ CSV parsing successful");
        }
      } catch (error) {
        console.error("❌ Text file parsing failed:", error);
        return NextResponse.json(
          {
            error: "Unable to parse text file",
            details: "File must contain valid JSON or CSV data",
            fileName: file.name,
            fileType: file.type,
          },
          { status: 400 },
        );
      }
    }

    // Validate parsed data
    if (!data || !Array.isArray(data)) {
      return NextResponse.json(
        {
          error: "Invalid data format",
          details: "Data must be an array of records",
        },
        { status: 400 },
      );
    }

    if (data.length === 0) {
      return NextResponse.json(
        {
          error: "No data found in the upload",
          metadata: {
            fileName: file.name,
            rowCount: 0,
            columnCount: 0,
            numericalSummaries: {},
          },
        },
        { status: 400 },
      );
    }

    // Determine analysis type first
    const analysisType = await determineAnalysisType(
      JSON.stringify(data),
      file.type,
    );
    console.log(`📊 Determined analysis type: ${analysisType}`);

    // Generate data summary
    console.log("📊 Generating data summary");
    const summary = getDataSummary(data);
    console.log("📊 Data summary:", summary);

    // Process first chunk to check for questions
    const chunks = chunkArray(data, CHUNK_SIZE);
    console.log(`🔄 Processing first chunk for potential questions`);

    const initialAnalysis = await azureClient.chat.completions.create({
      model: AZURE_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content:
            analysisType === "trend"
              ? TREND_ANALYSIS_MESSAGE
              : SINGLE_INSTANCE_MESSAGE,
        },
        {
          role: "user",
          content: `Analyze this fitness data with the following context:
          Total Rows: ${summary.rowCount}
          Total Columns: ${summary.columnCount}
          Numerical Summaries: ${JSON.stringify(summary.numericalSummaries)}
          Analysis Type: ${analysisType}
          
          Data Sample: ${JSON.stringify(chunks[0])}`,
        },
      ],
      tools: [toolParameters],
      tool_choice: {
        type: "function",
        function: { name: "analyze_fitness_data" },
      },
    });

    const initialToolCall = initialAnalysis.choices[0].message.tool_calls?.[0];
    if (
      !initialToolCall ||
      initialToolCall.function.name !== "analyze_fitness_data"
    ) {
      throw new Error("Invalid analysis response");
    }

    const initialResult = JSON.parse(initialToolCall.function.arguments);

    // Check for questions before proceeding with full analysis
    if (initialResult.questions?.length > 0) {
      const dates = data.map((row) => parseDate(row.Date));
      const timestamps = dates.map((date) => date.getTime());
      const startDate = new Date(Math.min(...timestamps));
      const endDate = new Date(Math.max(...timestamps));

      return NextResponse.json({
        success: true,
        needsQuestion: true,
        analysis: {
          ...initialResult,
          analysisType,
        },
        metadata: {
          fileName: file.name,
          ...summary,
          processedChunks: chunks.length,
          analysisType,
        },
        rawData: data,
        dateRange: {
          startDate,
          endDate,
          formattedRange: `${startDate.toISOString().split("T")[0]} to ${endDate.toISOString().split("T")[0]}`,
        },
      });
    }

    // If no questions, proceed with full analysis
    console.log(`🔄 Processing all ${chunks.length} chunks`);
    let combinedAnalysis = null;

    // Analyze each chunk with appropriate message
    for (let i = 0; i < chunks.length; i++) {
      console.log(`📝 Analyzing chunk ${i + 1}/${chunks.length}`);
      const analysisCompletion = await azureClient.chat.completions.create({
        model: AZURE_DEPLOYMENT_NAME as string,
        messages: [
          {
            role: "system",
            content:
              analysisType === "trend"
                ? TREND_ANALYSIS_MESSAGE
                : SINGLE_INSTANCE_MESSAGE,
          },
          {
            role: "user",
            content: `Analyze this chunk of fitness data with the following context:
            Total Rows: ${summary.rowCount}
            Total Columns: ${summary.columnCount}
            Numerical Summaries: ${JSON.stringify(summary.numericalSummaries)}
            Analysis Type: ${analysisType}
            
            Chunk Data: ${JSON.stringify(chunks[i])}`,
          },
        ],
        tools: [toolParameters],
        tool_choice: {
          type: "function",
          function: { name: "analyze_fitness_data" },
        },
      });

      const analysisToolCall =
        analysisCompletion.choices[0].message.tool_calls?.[0];

      if (analysisToolCall?.function.name === "analyze_fitness_data") {
        try {
          const chunkAnalysis = JSON.parse(analysisToolCall.function.arguments);

          // Add inferred metric groups if missing
          if (!chunkAnalysis.metricGroups) {
            console.log("Adding inferred metric groups");
            chunkAnalysis.metricGroups = inferMetricGroups(chunks[i]);
          }

          // Add analysis type to the chunk analysis
          chunkAnalysis.analysisType = analysisType;

          // Ensure required fields exist
          chunkAnalysis.trends =
            analysisType === "trend" ? chunkAnalysis.trends || [] : [];
          chunkAnalysis.insights = chunkAnalysis.insights || {
            performance: [],
            patterns: [],
            recommendations: [],
          };

          // Merge with previous analysis if exists
          combinedAnalysis = combinedAnalysis
            ? mergeAnalyses(combinedAnalysis, chunkAnalysis)
            : chunkAnalysis;

          console.log("Analysis structure:", {
            analysisType,
            groupsCount: Object.keys(chunkAnalysis.metricGroups || {}).length,
            trendsCount: chunkAnalysis.trends?.length || 0,
            insightsCount: Object.keys(chunkAnalysis.insights || {}).length,
          });
          console.log(`✅ Chunk ${i + 1} analysis complete`);
        } catch (parseError) {
          console.error("❌ Error parsing analysis arguments:", parseError);
          throw new Error("Failed to parse analysis response");
        }
      }
    }

    if (!combinedAnalysis) {
      return NextResponse.json(
        { error: "Failed to analyze data" },
        { status: 500 },
      );
    }

    // Extract date range
    const dates = data.map((row) => parseDate(row.Date));
    const timestamps = dates.map((date) => date.getTime());
    const startDate = new Date(Math.min(...timestamps));
    const endDate = new Date(Math.max(...timestamps));

    console.log("✅ Analysis complete:", {
      analysisType,
      trendsCount: combinedAnalysis.trends.length,
      insightsCount: Object.keys(combinedAnalysis.insights).length,
      groupsCount: Object.keys(combinedAnalysis.metricGroups).length,
      dateRange: {
        start: startDate.toISOString().split("T")[0],
        end: endDate.toISOString().split("T")[0],
      },
    });

    // Return final response
    return NextResponse.json({
      success: true,
      analysis: {
        ...combinedAnalysis,
        analysisType,
      },
      dateRange: {
        startDate,
        endDate,
        formattedRange: `${startDate.toISOString().split("T")[0]} to ${endDate.toISOString().split("T")[0]}`,
      },
      metadata: {
        fileName: file.name,
        ...summary,
        processedChunks: chunks.length,
        analysisType,
      },
      rawData: data,
    });
  } catch (parseError) {
    console.error("❌ Error parsing file:", parseError);
    return NextResponse.json(
      {
        error: "Failed to parse file",
        details:
          parseError instanceof Error
            ? parseError.message
            : "Unknown parsing error",
      },
      { status: 400 },
    );
  }
}

// Main POST route handler
export async function POST(request: NextRequest) {
  try {
    console.log("📥 Received upload request");
    const formData = await request.formData();
    const file = formData.get("file") as File | null;

    // Initial validation
    if (!file) {
      console.error("❌ No file provided");
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    console.log("📁 File details:", {
      name: file.name,
      type: file.type,
      size: file.size,
    });

    // File type validation
    if (!validateFileType(file)) {
      return NextResponse.json(
        {
          error: "File type not allowed",
          details: `Supported types: ${ALLOWED_MIME_TYPES.join(", ")}`,
          providedType: file.type || "no mime type",
          fileName: file.name,
        },
        { status: 400 },
      );
    }

    // Handle based on file type
    if (file.type.startsWith("image/")) {
      return await handleImageAnalysis(file);
    } else {
      return await handleDataAnalysis(file);
    }
  } catch (error) {
    console.error("❌ Error processing request:", error);
    return NextResponse.json(
      {
        error: "Analysis failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
