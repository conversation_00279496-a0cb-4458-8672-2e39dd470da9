import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

export async function GET(
  req: NextRequest,
  props: { params: Promise<{ id: string }> },
) {
  const params = await props.params;
  const { searchParams } = new URL(req.url);
  const getOnboardingData = searchParams.get("onboarding_data") === "true";

  console.log("GET request received for user!");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    const user_id = params.id;
    console.log("userID:", user_id);

    if (!user_id) {
      console.log("Missing user id");
      return NextResponse.json({ error: "Missing user id" }, { status: 400 });
    }

    const userCollection = db.collection("users");
    const trainingPlanCollection = db.collection("trainingPlans");

    // Fetch the user's full data, including the training_profile
    const userInfo = await userCollection.findOne({ user_id });

    if (!userInfo) {
      return NextResponse.json({ userData: {}, isNewUser: true });
    }

    // If specifically requesting onboarding data, return just that
    if (getOnboardingData) {
      const onboardingSidebarData = userInfo.onboarding_sidebar_data || null;
      const onboardingThreadId = userInfo.onboarding_thread_id || null;

      console.log("GET /api/users - Onboarding data request details:", {
        user_id,
        found_user: !!userInfo,
        raw_onboarding_sidebar_data: userInfo.onboarding_sidebar_data,
        raw_onboarding_thread_id: userInfo.onboarding_thread_id,
        has_data: !!onboardingSidebarData,
        thread_id: onboardingThreadId,
      });

      console.log(
        "Fetched onboarding sidebar data for user:",
        user_id,
        onboardingSidebarData ? "Data found" : "No data",
        "Thread ID:",
        onboardingThreadId,
      );
      return NextResponse.json({
        onboarding_sidebar_data: onboardingSidebarData,
        onboarding_thread_id: onboardingThreadId,
        has_data: !!onboardingSidebarData,
      });
    }

    // Check for onboarding training plan
    const onboardingPlan = await trainingPlanCollection.findOne({
      user_id,
      isOnboarding: true,
    });

    // Determine if the user is new based on the training_profile and onboarding plan
    const isNewUser =
      (!userInfo.training_profile ||
        Object.keys(userInfo.training_profile).length === 0) &&
      !onboardingPlan;

    return NextResponse.json({ ...userInfo, isNewUser });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch user data" },
      { status: 500 },
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const userCollection = db.collection("users");
    const trainingPlanCollection = db.collection("trainingPlans");

    const userInfo = await req.json();
    const user_id = userInfo?.user_id;

    if (!user_id || !userInfo) {
      console.log("Missing user_id or userInfo");
      return new NextResponse(
        JSON.stringify({ error: "Missing user_id or userInfo" }),
        { status: 400 },
      );
    }

    // Check if the user already exists
    const existingUser = await userCollection.findOne({ user_id });
    let isNewUser = false;

    if (existingUser) {
      // Update the existing user data
      await userCollection.updateOne(
        { user_id },
        {
          $set: {
            email: userInfo?.email,
            family_name: userInfo?.family_name,
            given_name: userInfo?.given_name,
            picture: userInfo?.picture,
            training_profile: existingUser.training_profile || {},
            currentOnboardingStep: existingUser.currentOnboardingStep || 1,
          },
        },
      );
      isNewUser =
        !existingUser.training_profile ||
        Object.keys(existingUser.training_profile).length === 0;
    } else {
      // Create a new user entry with empty training_profile
      const dateAdded = new Date().toISOString();
      await userCollection.insertOne({
        email: userInfo?.email,
        family_name: userInfo?.family_name,
        given_name: userInfo?.given_name,
        user_id: userInfo?.user_id,
        picture: userInfo?.picture,
        training_profile: {},
        dateAdded,
        currentOnboardingStep: 1,
      });
      isNewUser = true;
    }

    // Fetch the updated or newly inserted user data
    const updatedUser = await userCollection.findOne({ user_id });

    if (!updatedUser) {
      console.error("Failed to fetch updated user data");
      return new NextResponse(
        JSON.stringify({ error: "Failed to fetch updated user data" }),
        { status: 500 },
      );
    }

    // Check for onboarding training plan
    const onboardingPlan = await trainingPlanCollection.findOne({
      user_id,
      isOnboarding: true,
    });

    // Determine if the user is new based on the training_profile and onboarding plan
    isNewUser =
      (!updatedUser.training_profile ||
        Object.keys(updatedUser.training_profile).length === 0) &&
      !onboardingPlan;

    return new NextResponse(
      JSON.stringify({
        ...updatedUser,
        isNewUser,
        currentOnboardingStep: updatedUser.currentOnboardingStep || 1,
      }),
      {
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error saving user data:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to save user data" }),
      { status: 500 },
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { user_id, action, dev, thread_id } = body;

    console.log("PATCH /api/users called with:", {
      user_id,
      action,
      dev,
      thread_id,
    });

    // Only allow development actions when dev flag is true
    if (action === "clear_onboarding") {
      if (!dev && process.env.NODE_ENV === "production") {
        return new NextResponse(
          JSON.stringify({
            error: "Development actions not allowed in production",
          }),
          { status: 403 },
        );
      }

      if (!user_id) {
        return new NextResponse(JSON.stringify({ error: "Missing user_id" }), {
          status: 400,
        });
      }

      const client = await clientPromise;
      const db = client.db("AthleaUserData");
      const userCollection = db.collection("users");

      // Clear onboarding data for the user
      const result = await userCollection.updateOne(
        { user_id },
        {
          $unset: {
            onboarding_sidebar_data: 1,
            onboarding_last_updated: 1,
            onboarding_thread_id: 1,
          },
        },
      );

      console.log(
        "Cleared onboarding data for user:",
        user_id,
        "Result:",
        result,
      );

      return NextResponse.json({
        success: true,
        message: `Cleared onboarding data for user ${user_id}`,
        modified: result.modifiedCount > 0,
      });
    }

    // New action: start_onboarding
    if (action === "start_onboarding") {
      if (!user_id || !thread_id) {
        return new NextResponse(
          JSON.stringify({ error: "Missing user_id or thread_id" }),
          { status: 400 },
        );
      }

      const client = await clientPromise;
      const db = client.db("AthleaUserData");
      const userCollection = db.collection("users");

      // Create minimal onboarding sidebar data to mark as started
      const onboardingSidebarData = {
        currentStage: "started",
        goals: { exists: false, list: [] },
        summaryItems: [],
        selectedSports: [],
        sportSuggestions: [],
        generatedPlan: null,
        last_updated: new Date().toISOString(),
        thread_id: thread_id,
      };

      // Update user document with initial onboarding data
      const result = await userCollection.updateOne(
        { user_id },
        {
          $set: {
            onboarding_sidebar_data: onboardingSidebarData,
            onboarding_thread_id: thread_id,
            onboarding_last_updated: onboardingSidebarData.last_updated,
          },
        },
        { upsert: true },
      );

      console.log(
        "Marked onboarding as started for user:",
        user_id,
        "Thread:",
        thread_id,
        "Result:",
        result,
      );

      return NextResponse.json({
        success: true,
        message: `Marked onboarding as started for user ${user_id}`,
        thread_id: thread_id,
        modified: result.modifiedCount > 0 || result.upsertedId,
      });
    }

    return new NextResponse(JSON.stringify({ error: "Invalid action" }), {
      status: 400,
    });
  } catch (error) {
    console.error("Error in PATCH /api/users:", error);
    return new NextResponse(
      JSON.stringify({ error: "Internal server error" }),
      { status: 500 },
    );
  }
}
