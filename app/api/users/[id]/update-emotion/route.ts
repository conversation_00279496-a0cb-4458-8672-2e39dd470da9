// File: app/api/users/[id]/emotional-profile/route.ts

import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
    const params = await props.params;
    console.log("Fetching emotional profile");
    try {
        const client = await clientPromise;
        const db = client.db("AthleaUserData");

        const user_id = params.id;
        console.log("userID:", user_id);

        if (!user_id) {
            console.log("Missing user id");
            return NextResponse.json({ error: "Missing user id" }, { status: 400 });
        }

        const collection = db.collection("users");
        // Fetch only the emotional_profile
        const userInfo = await collection.findOne(
            { user_id },
            { projection: { emotional_profile: 1, _id: 0 } },
        );

        if (!userInfo) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        console.log("Fetched emotional profile:", userInfo.emotional_profile);
        if (!userInfo.emotional_profile || userInfo.emotional_profile === undefined) {
            return NextResponse.json(
                { error: "Failed to fetch emotional profile" },
                { status: 500 },
            );
        }
        return NextResponse.json(userInfo.emotional_profile);
    } catch (error) {
        console.error("Error fetching emotional profile:", error);
        return NextResponse.json(
            { error: "Failed to fetch emotional profile" },
            { status: 500 },
        );
    }
}

export async function POST(req: NextRequest, props: { params: Promise<{ id: string }> }) {
    const params = await props.params;
    const user_id = params.id;
    const { emotions } = await req.json();

    try {
        const client = await clientPromise;
        const db = client.db("AthleaUserData");
        const collection = db.collection("users");

        const result = await collection.updateOne(
            { user_id }, { $set: { emotional_profile: emotions } }
        );

        if (result.modifiedCount === 1) {
            return NextResponse.json({
                success: true,
                message: "Profile added successfully!",
            });
        } else {
            return NextResponse.json(
                { error: "Failed to add Profile" },
                { status: 500 },
            );
        }
    } catch (error) {
        console.error("Error adding Profile:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 },
        );
    }
}