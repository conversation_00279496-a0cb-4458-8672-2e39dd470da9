// @ts-nocheck
import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";
import { ObjectId } from "mongodb"; // If currentPlan is stored as an ObjectId

// Define an interface for your User document (customize as needed)
interface UserDocument {
  _id: ObjectId;
  user_id: string;
  currentPlan?: PlanDetails | ObjectId | null; // Could be populated object, ObjectId, or null
  plans?: PlanDetails[];
  weeklyPlans?: Record<string, any>; // Adjust type as per your weeklyPlans structure
  // ... other user fields
}

// Define an interface for your PlanDetails (customize as needed)
// This should match the structure you expect for a plan
interface PlanDetails {
  planId: string;
  name: string;
  // ... other plan properties
}

interface Params {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(req: NextRequest, props: Params) {
  const params = await props.params;
  console.log("GET request received for user plan data!");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const usersCollection = db.collection<UserDocument>("users"); // Specify document type

    const userId = params.id;

    if (!userId) {
      console.log("Missing user id for plan-data");
      return NextResponse.json({ error: "Missing user id" }, { status: 400 });
    }

    console.log(`Fetching plan data for user_id: ${userId}`);

    // Fetch the user document
    // Adjust the query and population as per your schema.
    // Example: If currentPlan is an ObjectId referencing a 'plans' collection:
    // const user = await usersCollection.findOne({ user_id: userId });
    // let populatedCurrentPlan = null;
    // if (user && user.currentPlan && user.currentPlan instanceof ObjectId) {
    //   const plansCollection = db.collection<PlanDetails>("plans"); // Assuming a 'plans' collection
    //   populatedCurrentPlan = await plansCollection.findOne({ _id: user.currentPlan });
    // } else if (user && user.currentPlan) {
    //   populatedCurrentPlan = user.currentPlan; // Assumes currentPlan is already embedded
    // }

    // Simplified fetch for now - REPLACE WITH YOUR ACTUAL LOGIC
    const user = await usersCollection.findOne({ user_id: userId });

    if (!user) {
      console.log(`User not found for plan-data: ${userId}`);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // *** IMPORTANT: Replace with your actual logic to fetch/populate these fields ***
    const currentPlanData = user.currentPlan || null; // This might need population
    const plansData = user.plans || []; // This might need population if storing ObjectIds
    const weeklyPlansData = user.weeklyPlans || {};

    console.log("Returning plan data:", {
      currentPlan: currentPlanData,
      plans: plansData,
      weeklyPlans: weeklyPlansData,
    });

    return NextResponse.json({
      currentPlan: currentPlanData,
      plans: plansData,
      weeklyPlans: weeklyPlansData,
    });
  } catch (error) {
    console.error("Error fetching user plan data:", error);
    return NextResponse.json(
      { error: "Failed to fetch user plan data" },
      { status: 500 },
    );
  }
}
