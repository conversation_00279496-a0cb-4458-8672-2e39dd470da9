import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  console.log("GET request received for user!");
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    // Await the params before using
    const params = await Promise.resolve(await context.params);
    const user_id = params.id;

    // Check if this is a request for onboarding data specifically
    const { searchParams } = new URL(req.url);
    const getOnboardingData = searchParams.get("onboarding_data") === "true";

    if (!user_id) {
      console.log("Missing user id");
      return NextResponse.json({ error: "Missing user id" }, { status: 400 });
    }

    const collection = db.collection("users");
    // Fetch the user's full data, including the training_profile
    const userInfo = await collection.findOne({ user_id });

    if (!userInfo) {
      return NextResponse.json({ userData: {}, isNewUser: true });
    }

    // If specifically requesting onboarding data, return just that
    if (getOnboardingData) {
      const onboardingSidebarData = userInfo.onboarding_sidebar_data || null;
      const onboardingThreadId = userInfo.onboarding_thread_id || null;
      const onboardingLastUpdated = userInfo.onboarding_last_updated || null;

      console.log("GET /api/users/[id] - Onboarding data request details:", {
        user_id,
        found_user: !!userInfo,
        has_onboarding_sidebar_data: !!onboardingSidebarData,
        has_onboarding_thread_id: !!onboardingThreadId,
        has_onboarding_last_updated: !!onboardingLastUpdated,
        onboarding_sidebar_data_keys: onboardingSidebarData
          ? Object.keys(onboardingSidebarData)
          : null,
        thread_id: onboardingThreadId,
      });

      // Log the actual data structure for debugging
      if (onboardingSidebarData) {
        console.log("Onboarding sidebar data details:", {
          currentStage: onboardingSidebarData.current_stage,
          goals: onboardingSidebarData.goals,
          selectedSports: onboardingSidebarData.selected_sports,
          summaryItems: onboardingSidebarData.summary_items?.length || 0,
          uploadedDocuments:
            onboardingSidebarData.uploaded_documents?.length || 0,
          keyInsights: onboardingSidebarData.key_insights
            ? Object.keys(onboardingSidebarData.key_insights).length
            : 0,
        });
      }

      console.log(
        "Fetched onboarding sidebar data for user:",
        user_id,
        onboardingSidebarData ? "Data found" : "No data",
        "Thread ID:",
        onboardingThreadId,
      );

      // Convert snake_case properties to camelCase for frontend
      let convertedSidebarData = null;
      if (onboardingSidebarData) {
        convertedSidebarData = {
          currentStage: onboardingSidebarData.current_stage,
          goals: onboardingSidebarData.goals,
          summaryItems: onboardingSidebarData.summary_items || [],
          generatedPlan: onboardingSidebarData.generated_plan,
          sportSuggestions: onboardingSidebarData.sport_suggestions,
          selectedSport: onboardingSidebarData.selected_sport,
          selectedSports: onboardingSidebarData.selected_sports || [],
          weeklyPlan: onboardingSidebarData.weekly_plan,
          uploadedDocuments: onboardingSidebarData.uploaded_documents || [],
          keyInsights: onboardingSidebarData.key_insights || {},
          lastUpdated: onboardingSidebarData.last_updated,
        };

        console.log("Property name conversion:", {
          before_summary_items:
            onboardingSidebarData.summary_items?.length || 0,
          after_summaryItems: convertedSidebarData.summaryItems?.length || 0,
          before_selected_sports:
            onboardingSidebarData.selected_sports?.length || 0,
          after_selectedSports:
            convertedSidebarData.selectedSports?.length || 0,
          before_uploaded_documents:
            onboardingSidebarData.uploaded_documents?.length || 0,
          after_uploadedDocuments:
            convertedSidebarData.uploadedDocuments?.length || 0,
        });
      }

      return NextResponse.json({
        onboarding_sidebar_data: convertedSidebarData,
        onboarding_thread_id: onboardingThreadId,
        onboarding_last_updated: onboardingLastUpdated,
        has_data: !!convertedSidebarData,
      });
    }

    // Determine if the user is new based on the training_profile
    const isNewUser =
      !userInfo.training_profile ||
      Object.keys(userInfo.training_profile).length === 0;

    return NextResponse.json({ ...userInfo, isNewUser });
  } catch (error) {
    console.error("Error fetching user data:", error);
    return NextResponse.json(
      { error: "Failed to fetch user data" },
      { status: 500 },
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const user_id = req.nextUrl.pathname.split("/").pop();
    const { personal_info, connection } = await req.json();
    let result;
    if (!user_id) {
      return new NextResponse(JSON.stringify({ error: "Missing user_id" }), {
        status: 400,
      });
    }
    if (connection) {
      result = await collection.updateOne(
        { user_id },
        { $addToSet: { connections: connection } },
      );
    } else {
      if (!personal_info) {
        return new NextResponse(
          JSON.stringify({ error: "Missing personal_info" }),
          { status: 400 },
        );
      }
      result = await collection.updateOne(
        { user_id },
        { $set: { personal_info } },
      );
    }

    if (result.matchedCount === 0) {
      return new NextResponse(JSON.stringify({ error: "User not found" }), {
        status: 404,
      });
    }

    return new NextResponse(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    console.error("Error updating personal info:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to update personal info" }),
      { status: 500 },
    );
  }
}

export async function PATCH(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const user_id = req.nextUrl.pathname.split("/").pop();
    const updates = await req.json();

    if (!user_id || Object.keys(updates).length === 0) {
      return new NextResponse(
        JSON.stringify({ error: "Missing user_id or update data" }),
        { status: 400 },
      );
    }

    const result = await collection.updateOne({ user_id }, { $set: updates });

    if (result.matchedCount === 0) {
      return new NextResponse(JSON.stringify({ error: "User not found" }), {
        status: 404,
      });
    }

    // Fetch the updated user data
    const updatedUser = await collection.findOne({ user_id });

    return new NextResponse(JSON.stringify(updatedUser), { status: 200 });
  } catch (error) {
    console.error("Error updating user data:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to update user data" }),
      { status: 500 },
    );
  }
}
