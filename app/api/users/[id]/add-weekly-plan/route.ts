import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

function getDaysBetweenDates(startDate: Date, endDate: Date): number {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  const diffDays = Math.floor(
    (endDate.getTime() - startDate.getTime()) / oneDay,
  );
  return diffDays;
}

export async function POST(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const user_id = params.id;
  const { coach, weeklyPlan, startDate } = await req.json();

  console.log(`Received request for user_id: ${user_id}`);
  console.log(`Received coach: ${coach}`);
  console.log(`Received startDate: ${startDate}`);

  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    console.log("Connected to MongoDB");

    const user = await collection.findOne({ user_id });
    console.log(`Found user: ${user ? "Yes" : "No"}`);

    if (!user || !user.currentPlan || !user.currentPlan.dateAdded) {
      console.log("User or current plan not found");
      return NextResponse.json(
        { error: "User or current plan not found" },
        { status: 404 },
      );
    }

    const planStartDate = new Date(user.currentPlan.dateAdded);
    planStartDate.setHours(0, 0, 0, 0);

    const weekStart = startDate ? new Date(startDate) : new Date();
    weekStart.setHours(0, 0, 0, 0);

    console.log(`Plan start date: ${planStartDate}`);
    console.log(`Week start date: ${weekStart}`);

    const daysPassed = getDaysBetweenDates(planStartDate, weekStart);
    const actualWeekNumber = Math.floor(daysPassed / 7) + 1;

    console.log(`Days passed: ${daysPassed}`);
    console.log(`Calculated actual week number: ${actualWeekNumber}`);

    const updateObject = {
      [`weeklyPlans.week${actualWeekNumber}.startDate`]:
        weekStart.toISOString(),
      [`weeklyPlans.week${actualWeekNumber}.plans.${coach}`]: weeklyPlan,
    };

    console.log(`Update object: ${JSON.stringify(updateObject)}`);

    const result = await collection.updateOne(
      { user_id },
      { $set: updateObject },
    );

    console.log(`Update result: ${JSON.stringify(result)}`);

    if (result.modifiedCount === 1) {
      const updatedUser = await collection.findOne({ user_id });
      console.log("Weekly plan added successfully");
      return NextResponse.json({
        success: true,
        message: `Weekly plan for ${coach} in Week ${actualWeekNumber} added successfully!`,
        weeklyPlans: updatedUser?.weeklyPlans || {},
      });
    }

    console.log("Failed to add weekly plan");
    return NextResponse.json(
      { error: "Failed to add weekly plan" },
      { status: 500 },
    );
  } catch (error) {
    console.error("Error adding weekly plan:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
