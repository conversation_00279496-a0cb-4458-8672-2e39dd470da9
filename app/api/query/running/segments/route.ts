import { NextRequest, NextResponse } from "next/server";
import Airtable from "airtable";

// Define the expected structure for a Running Segment based on the Airtable schema
interface RunningSegment {
  id: string; // Airtable Record ID
  SegmentID?: string;
  "Session ID"?: string;
  "Running Sessions"?: string[]; // Links to session records
  "SessionID (from Running Sessions)"?: string[];
  SegmentOrder?: string;
  Duration?: string;
  Intensity?: string;
  Cadence?: string;
  "Segment Type"?: string;
  RepeatCount?: string;
  "Segment Description"?: string;
  LastModified?: string;

  // Allow for any other fields
  [key: string]: any;
}

export async function GET(req: NextRequest) {
  try {
    // Ensure proper environment variables are configured
    const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
    // Try both base ID variables
    const AIRTABLE_BASE_ID =
      process.env.AIRTABLE_RUNNING_BASE_ID || process.env.AIRTABLE_BASE_ID;
    const AIRTABLE_TABLE_NAME =
      process.env.AIRTABLE_RUNNING_SEGMENTS_TABLE_NAME || "Running Segments";

    if (!AIRTABLE_API_KEY || !AIRTABLE_BASE_ID) {
      console.error("Missing Airtable environment variables");
      return NextResponse.json(
        {
          error: "Server configuration error",
          details: "Airtable API key or base ID is missing",
        },
        { status: 500 },
      );
    }

    // Initialize Airtable client
    const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(
      AIRTABLE_BASE_ID,
    );
    const table = base(AIRTABLE_TABLE_NAME);

    // Get search parameters from the request
    const searchParams = req.nextUrl.searchParams;
    const segmentId = searchParams.get("segmentId");
    const sessionId = searchParams.get("sessionId");
    const segmentOrder = searchParams.get("segmentOrder");
    const duration = searchParams.get("duration");
    const intensity = searchParams.get("intensity");
    const cadence = searchParams.get("cadence");
    const segmentType = searchParams.get("segmentType");
    const repeatCount = searchParams.get("repeatCount");
    const description = searchParams.get("description");

    // Limit search results (default: 10, max: 100)
    const limit = Math.min(
      parseInt(searchParams.get("limit") || "10", 10),
      100,
    );

    // Build Airtable filter formula based on search parameters
    let filterFormulas: string[] = [];

    // Add filters for each search parameter
    if (segmentId) {
      filterFormulas.push(`{SegmentID} = "${segmentId}"`);
    }

    if (sessionId) {
      filterFormulas.push(`{Session ID} = "${sessionId}"`);
    }

    if (segmentOrder) {
      filterFormulas.push(`{SegmentOrder} = "${segmentOrder}"`);
    }

    if (duration) {
      filterFormulas.push(`{Duration} = "${duration}"`);
    }

    if (intensity) {
      filterFormulas.push(`{Intensity} = "${intensity}"`);
    }

    if (cadence) {
      filterFormulas.push(`{Cadence} = "${cadence}"`);
    }

    if (segmentType) {
      filterFormulas.push(`{Segment Type} = "${segmentType}"`);
    }

    if (repeatCount) {
      filterFormulas.push(`{RepeatCount} = "${repeatCount}"`);
    }

    if (description) {
      filterFormulas.push(
        `FIND("${description.toLowerCase()}", LOWER({Segment Description})) > 0`,
      );
    }

    // Combine filters with AND logic if any filters are specified
    const filterByFormula = filterFormulas.length
      ? `AND(${filterFormulas.join(", ")})`
      : "";

    // Log query information for debugging
    console.log(
      `Querying Airtable with formula: ${filterByFormula || "(none)"}`,
    );
    console.log(`Table name: ${AIRTABLE_TABLE_NAME}`);

    // Execute the search
    // Initialize an empty array to store the records
    const records: RunningSegment[] = [];

    // Make the query to Airtable
    await table
      .select({
        maxRecords: limit,
        filterByFormula: filterByFormula || "",
      })
      .eachPage(
        function page(pageRecords, fetchNextPage) {
          // Map Airtable records to the RunningSegment interface
          const mappedRecords = pageRecords.map((record) => {
            return {
              id: record.id,
              ...record.fields,
            } as RunningSegment;
          });

          // Add these records to our array
          records.push(...mappedRecords);

          // Get the next page of records
          fetchNextPage();
        },
        function done(err) {
          if (err) {
            console.error("Error fetching data from Airtable:", err);
            throw err;
          }
        },
      );

    // Return the search results
    return NextResponse.json(
      {
        success: true,
        count: records.length,
        data: records,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
