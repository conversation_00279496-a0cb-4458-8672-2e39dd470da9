import { NextRequest, NextResponse } from "next/server";
import Airtable from "airtable";

// Define the expected structure for a Running Session based on the Airtable schema
interface RunningSession {
  id: string; // Airtable Record ID
  "Session ID"?: string;
  "Running Plans"?: string[]; // Links to plan records
  "Run Category"?: string;
  "Session Name"?: string;
  "Session Description"?: string;
  Duration?: string;
  Distance?: number;
  "Skill Category"?: string;
  "Running Segments"?: string[]; // Links to segment records
  IntensityMeasure?: string;
  "Stress Level Measure"?: string;
  IntensityLevel?: string;
  IntensityLevelIF?: number;
  StressLevel?: number;
  "Stress Level TSS"?: number;
  CoachingPoints?: string;
  CommonErrors?: string;
  "Zone Distribution"?: string;

  // Allow for any other fields
  [key: string]: any;
}

export async function GET(req: NextRequest) {
  try {
    // Ensure proper environment variables are configured
    const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
    // Try both base ID variables
    const AIRTABLE_BASE_ID =
      process.env.AIRTABLE_RUNNING_BASE_ID || process.env.AIRTABLE_BASE_ID;
    const AIRTABLE_TABLE_NAME =
      process.env.AIRTABLE_RUNNING_SESSIONS_TABLE_NAME || "Running Sessions";

    if (!AIRTABLE_API_KEY || !AIRTABLE_BASE_ID) {
      console.error("Missing Airtable environment variables");
      return NextResponse.json(
        {
          error: "Server configuration error",
          details: "Airtable API key or base ID is missing",
        },
        { status: 500 },
      );
    }

    // Initialize Airtable client
    const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(
      AIRTABLE_BASE_ID,
    );
    const table = base(AIRTABLE_TABLE_NAME);

    // Get search parameters from the request
    const searchParams = req.nextUrl.searchParams;
    const sessionId = searchParams.get("sessionId");
    const runCategory = searchParams.get("runCategory");
    const sessionName = searchParams.get("sessionName");
    const duration = searchParams.get("duration");
    const distance = searchParams.get("distance");
    const skillCategory = searchParams.get("skillCategory");
    const intensityLevel = searchParams.get("intensityLevel");
    const stressLevel = searchParams.get("stressLevel");
    const intensityMeasure = searchParams.get("intensityMeasure");

    // Limit search results (default: 10, max: 100)
    const limit = Math.min(
      parseInt(searchParams.get("limit") || "10", 10),
      100,
    );

    // Build Airtable filter formula based on search parameters
    let filterFormulas: string[] = [];

    // Add filters for each search parameter
    if (sessionId) {
      filterFormulas.push(`{Session ID} = "${sessionId}"`);
    }

    if (runCategory) {
      filterFormulas.push(
        `FIND("${runCategory.toLowerCase()}", LOWER({Run Category})) > 0`,
      );
    }

    if (sessionName) {
      filterFormulas.push(
        `FIND("${sessionName.toLowerCase()}", LOWER({Session Name})) > 0`,
      );
    }

    if (duration) {
      filterFormulas.push(`{Duration} = "${duration}"`);
    }

    if (distance) {
      filterFormulas.push(`{Distance} = ${distance}`);
    }

    if (skillCategory) {
      filterFormulas.push(
        `FIND("${skillCategory.toLowerCase()}", LOWER({Skill Category})) > 0`,
      );
    }

    if (intensityLevel) {
      filterFormulas.push(`{IntensityLevel} = "${intensityLevel}"`);
    }

    if (stressLevel) {
      filterFormulas.push(`{StressLevel} = ${stressLevel}`);
    }

    if (intensityMeasure) {
      filterFormulas.push(`{IntensityMeasure} = "${intensityMeasure}"`);
    }

    // Combine filters with AND logic if any filters are specified
    const filterByFormula = filterFormulas.length
      ? `AND(${filterFormulas.join(", ")})`
      : "";

    // Log query information for debugging
    console.log(
      `Querying Airtable with formula: ${filterByFormula || "(none)"}`,
    );
    console.log(`Table name: ${AIRTABLE_TABLE_NAME}`);

    // Execute the search
    // Initialize an empty array to store the records
    const records: RunningSession[] = [];

    // Make the query to Airtable
    await table
      .select({
        maxRecords: limit,
        filterByFormula: filterByFormula || "",
      })
      .eachPage(
        function page(pageRecords, fetchNextPage) {
          // Map Airtable records to the RunningSession interface
          const mappedRecords = pageRecords.map((record) => {
            return {
              id: record.id,
              ...record.fields,
            } as RunningSession;
          });

          // Add these records to our array
          records.push(...mappedRecords);

          // Get the next page of records
          fetchNextPage();
        },
        function done(err) {
          if (err) {
            console.error("Error fetching data from Airtable:", err);
            throw err;
          }
        },
      );

    // Return the search results
    return NextResponse.json(
      {
        success: true,
        count: records.length,
        data: records,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
