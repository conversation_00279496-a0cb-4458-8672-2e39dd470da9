import { NextRequest, NextResponse } from "next/server";
import Airtable from "airtable";

// Define the expected structure for a Strength Session based on the Airtable schema
interface StrengthSession {
  id: string; // Airtable Record ID
  SessionID?: string;
  SessionName?: string;
  SessionDescription?: string;
  Duration?: string; // Duration in minutes
  Location?: string; // E.g., "Gym", "Home", "Outdoor"
  IntensityLevel?: string;
  StressLevel?: number;
  EquipmentRequired?: string[];
  ExerciseType?: string; // E.g., "Strength", "Mobility", "Core"
  TargetMuscleGroups?: string[];
  LastModified?: string;

  // Allow for any other fields
  [key: string]: any;
}

export async function GET(req: NextRequest) {
  try {
    // Ensure proper environment variables are configured
    const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
    // Try both base ID variables
    const AIRTABLE_BASE_ID =
      process.env.AIRTABLE_STRENGTH_BASE_ID || process.env.AIRTABLE_BASE_ID;
    const AIRTABLE_TABLE_NAME =
      process.env.AIRTABLE_STRENGTH_SESSIONS_TABLE_NAME || "Strength Sessions";

    if (!AIRTABLE_API_KEY || !AIRTABLE_BASE_ID) {
      console.error("Missing Airtable environment variables");
      return NextResponse.json(
        {
          error: "Server configuration error",
          details: "Airtable API key or base ID is missing",
        },
        { status: 500 },
      );
    }

    // Initialize Airtable client
    const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(
      AIRTABLE_BASE_ID,
    );
    const table = base(AIRTABLE_TABLE_NAME);

    // Get search parameters from the request
    const searchParams = req.nextUrl.searchParams;
    const sessionId = searchParams.get("sessionId");
    const sessionName = searchParams.get("sessionName");
    const duration = searchParams.get("duration");
    const location = searchParams.get("location");
    const intensityLevel = searchParams.get("intensityLevel");
    const equipmentRequired = searchParams.get("equipmentRequired");
    const exerciseType = searchParams.get("exerciseType");
    const targetMuscleGroups = searchParams.get("targetMuscleGroups");

    // Limit search results (default: 10, max: 100)
    const limit = Math.min(
      parseInt(searchParams.get("limit") || "10", 10),
      100,
    );

    // Build Airtable filter formula based on search parameters
    let filterFormulas: string[] = [];

    // Add filters for each search parameter
    if (sessionId) {
      filterFormulas.push(`{SessionID} = "${sessionId}"`);
    }

    if (sessionName) {
      filterFormulas.push(
        `FIND("${sessionName.toLowerCase()}", LOWER({SessionName})) > 0`,
      );
    }

    if (duration) {
      filterFormulas.push(`{Duration} = "${duration}"`);
    }

    if (location) {
      filterFormulas.push(`{Location} = "${location}"`);
    }

    if (intensityLevel) {
      filterFormulas.push(`{IntensityLevel} = "${intensityLevel}"`);
    }

    if (equipmentRequired) {
      filterFormulas.push(
        `FIND("${equipmentRequired}", ARRAYJOIN({EquipmentRequired}, ",")) > 0`,
      );
    }

    if (exerciseType) {
      filterFormulas.push(`{ExerciseType} = "${exerciseType}"`);
    }

    if (targetMuscleGroups) {
      filterFormulas.push(
        `FIND("${targetMuscleGroups}", ARRAYJOIN({TargetMuscleGroups}, ",")) > 0`,
      );
    }

    // Combine filters with AND logic if any filters are specified
    const filterByFormula = filterFormulas.length
      ? `AND(${filterFormulas.join(", ")})`
      : "";

    // Log query information for debugging
    console.log(
      `Querying Airtable with formula: ${filterByFormula || "(none)"}`,
    );
    console.log(`Table name: ${AIRTABLE_TABLE_NAME}`);

    // Execute the search
    // Initialize an empty array to store the records
    const records: StrengthSession[] = [];

    // Make the query to Airtable
    await table
      .select({
        maxRecords: limit,
        filterByFormula: filterByFormula || "",
      })
      .eachPage(
        function page(pageRecords, fetchNextPage) {
          // Map Airtable records to the StrengthSession interface
          const mappedRecords = pageRecords.map((record) => {
            return {
              id: record.id,
              ...record.fields,
            } as StrengthSession;
          });

          // Add these records to our array
          records.push(...mappedRecords);

          // Get the next page of records
          fetchNextPage();
        },
        function done(err) {
          if (err) {
            console.error("Error fetching data from Airtable:", err);
            throw err;
          }
        },
      );

    // Return the search results
    return NextResponse.json(
      {
        success: true,
        count: records.length,
        data: records,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
