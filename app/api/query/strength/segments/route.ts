import { NextRequest, NextResponse } from "next/server";
import Airtable from "airtable";

// Define the expected structure for a Strength Segment based on the Airtable schema
interface StrengthSegment {
  id: string; // Airtable Record ID
  SegmentID?: string;
  SessionID?: string; // Reference to the session this segment belongs to
  "Strength Sessions"?: string[]; // Links to session records
  "SessionName (from Strength Sessions)"?: string[]; // Lookup field from linked sessions
  SegmentOrder?: string;
  Duration?: string;
  IntensityLevel?: string;
  RestDuration?: string;
  RepeatCount?: string;
  SetCount?: string;
  RepsPerSet?: string;
  SegmentType?: string; // E.g., "Warm-up", "Main Set", "Cool Down"
  ExerciseName?: string;
  SegmentDescription?: string;
  LastModified?: string;

  // Allow for any other fields
  [key: string]: any;
}

export async function GET(req: NextRequest) {
  try {
    // Ensure proper environment variables are configured
    const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
    // Try both base ID variables
    const AIRTABLE_BASE_ID =
      process.env.AIRTABLE_STRENGTH_BASE_ID || process.env.AIRTABLE_BASE_ID;
    const AIRTABLE_TABLE_NAME =
      process.env.AIRTABLE_STRENGTH_SEGMENTS_TABLE_NAME || "Strength Segments";

    if (!AIRTABLE_API_KEY || !AIRTABLE_BASE_ID) {
      console.error("Missing Airtable environment variables");
      return NextResponse.json(
        {
          error: "Server configuration error",
          details: "Airtable API key or base ID is missing",
        },
        { status: 500 },
      );
    }

    // Initialize Airtable client
    const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(
      AIRTABLE_BASE_ID,
    );
    const table = base(AIRTABLE_TABLE_NAME);

    // Get search parameters from the request
    const searchParams = req.nextUrl.searchParams;
    const segmentId = searchParams.get("segmentId");
    const sessionId = searchParams.get("sessionId");
    const segmentType = searchParams.get("segmentType");
    const exerciseName = searchParams.get("exerciseName");
    const duration = searchParams.get("duration");
    const intensityLevel = searchParams.get("intensityLevel");
    const repeatCount = searchParams.get("repeatCount");
    const setCount = searchParams.get("setCount");
    const repsPerSet = searchParams.get("repsPerSet");

    // Limit search results (default: 10, max: 100)
    const limit = Math.min(
      parseInt(searchParams.get("limit") || "10", 10),
      100,
    );

    // Build Airtable filter formula based on search parameters
    let filterFormulas: string[] = [];

    // Add filters for each search parameter
    if (segmentId) {
      filterFormulas.push(`{SegmentID} = "${segmentId}"`);
    }

    if (sessionId) {
      filterFormulas.push(`{SessionID} = "${sessionId}"`);
    }

    if (segmentType) {
      filterFormulas.push(`{SegmentType} = "${segmentType}"`);
    }

    if (exerciseName) {
      filterFormulas.push(
        `FIND("${exerciseName.toLowerCase()}", LOWER({ExerciseName})) > 0`,
      );
    }

    if (duration) {
      filterFormulas.push(`{Duration} = "${duration}"`);
    }

    if (intensityLevel) {
      filterFormulas.push(`{IntensityLevel} = "${intensityLevel}"`);
    }

    if (repeatCount) {
      filterFormulas.push(`{RepeatCount} = "${repeatCount}"`);
    }

    if (setCount) {
      filterFormulas.push(`{SetCount} = "${setCount}"`);
    }

    if (repsPerSet) {
      filterFormulas.push(`{RepsPerSet} = "${repsPerSet}"`);
    }

    // Combine filters with AND logic if any filters are specified
    const filterByFormula = filterFormulas.length
      ? `AND(${filterFormulas.join(", ")})`
      : "";

    // Log query information for debugging
    console.log(
      `Querying Airtable with formula: ${filterByFormula || "(none)"}`,
    );
    console.log(`Table name: ${AIRTABLE_TABLE_NAME}`);

    // Execute the search
    // Initialize an empty array to store the records
    const records: StrengthSegment[] = [];

    // Make the query to Airtable
    await table
      .select({
        maxRecords: limit,
        filterByFormula: filterByFormula || "",
      })
      .eachPage(
        function page(pageRecords, fetchNextPage) {
          // Map Airtable records to the StrengthSegment interface
          const mappedRecords = pageRecords.map((record) => {
            return {
              id: record.id,
              ...record.fields,
            } as StrengthSegment;
          });

          // Add these records to our array
          records.push(...mappedRecords);

          // Get the next page of records
          fetchNextPage();
        },
        function done(err) {
          if (err) {
            console.error("Error fetching data from Airtable:", err);
            throw err;
          }
        },
      );

    // Return the search results
    return NextResponse.json(
      {
        success: true,
        count: records.length,
        data: records,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
