import { NextRequest, NextResponse } from "next/server";
import Airtable from "airtable";

// Define the expected structure for a Cycling Segment based on the actual Airtable schema
interface CyclingSegment {
  id: string; // Airtable Record ID
  SegmentID?: string;
  SessionID?: string; // Reference to the session this segment belongs to
  "Cycling Sessions"?: string[]; // Links to session records
  "SessionName (from Cycling Sessions)"?: string[]; // Lookup field from linked sessions
  SegmentOrder?: string;
  Duration?: string;
  IntensityWatts?: string;
  IntensityFTP?: string;
  Cadence?: string;
  SegmentType?: string;
  RepeatCount?: string;
  SegmentDescription?: string;
  LastModified?: string;

  // Allow for any other fields
  [key: string]: any;
}

// Initialize Airtable Client
const airtableApiKey = process.env.AIRTABLE_API_KEY;
// Try AIRTABLE_CYCLING_BASE_ID first, fall back to AIRTABLE_BASE_ID if not available
const airtableBaseId =
  process.env.AIRTABLE_CYCLING_BASE_ID || process.env.AIRTABLE_BASE_ID;
const cyclingSegmentsTableName =
  process.env.AIRTABLE_CYCLING_SEGMENTS_TABLE_NAME || "Cycling Segments";

console.log("Environment Check for Segments API:");
console.log("- API Key exists:", !!airtableApiKey);
console.log("- Base ID exists:", !!airtableBaseId);
console.log("- Using table name:", cyclingSegmentsTableName);

if (!airtableApiKey || !airtableBaseId) {
  console.error(
    "Airtable API Key or Base ID is not configured in environment variables.",
  );
}

function getAirtableBase() {
  if (!airtableApiKey || !airtableBaseId) {
    return null;
  }
  return new Airtable({ apiKey: airtableApiKey }).base(airtableBaseId);
}

export async function GET(req: NextRequest) {
  const base = getAirtableBase();
  if (!base) {
    return NextResponse.json(
      { message: "Airtable configuration missing. Please check server logs." },
      { status: 500 },
    );
  }

  const searchParams = req.nextUrl.searchParams;

  // Extract search parameters from the request URL
  const segmentId = searchParams.get("segmentId");
  const sessionId = searchParams.get("sessionId");
  const segmentType = searchParams.get("segmentType");
  const duration = searchParams.get("duration");
  const minDuration = searchParams.get("minDuration");
  const maxDuration = searchParams.get("maxDuration");
  const cadence = searchParams.get("cadence");
  const intensityWatts = searchParams.get("intensityWatts");
  const intensityFTP = searchParams.get("intensityFTP");
  const segmentOrder = searchParams.get("segmentOrder");
  const repeatCount = searchParams.get("repeatCount");

  const filters: string[] = [];

  // Build Airtable filter formula based on provided parameters

  if (segmentId) {
    filters.push(`{SegmentID} = "${segmentId}"`);
  }

  if (sessionId) {
    filters.push(`{SessionID} = "${sessionId}"`);
  }

  if (segmentType) {
    filters.push(`{SegmentType} = "${segmentType}"`);
  }

  // Handle Duration as a SingleSelect field
  if (duration) {
    filters.push(`{Duration} = "${duration}"`);
  } else {
    // For min/max duration, we need to convert the string values to numbers for comparison
    if (minDuration) {
      filters.push(`VALUE({Duration}) >= ${minDuration}`);
    }
    if (maxDuration) {
      filters.push(`VALUE({Duration}) <= ${maxDuration}`);
    }
  }

  if (cadence) {
    filters.push(`{Cadence} = "${cadence}"`);
  }

  if (intensityWatts) {
    filters.push(`{IntensityWatts} = "${intensityWatts}"`);
  }

  if (intensityFTP) {
    filters.push(`{IntensityFTP} = "${intensityFTP}"`);
  }

  if (segmentOrder) {
    filters.push(`{SegmentOrder} = "${segmentOrder}"`);
  }

  if (repeatCount) {
    filters.push(`{RepeatCount} = "${repeatCount}"`);
  }

  let filterByFormula = "";
  if (filters.length > 0) {
    filterByFormula = `AND(${filters.join(", ")})`;
  }

  console.log(`Querying Airtable with formula: ${filterByFormula}`);
  console.log(`Using table: ${cyclingSegmentsTableName}`);

  try {
    const records = await base(cyclingSegmentsTableName)
      .select({
        filterByFormula: filterByFormula,
      })
      .all();

    console.log(`Found ${records.length} segments from Airtable.`);

    // Log the first record fields to help debugging
    if (records.length > 0) {
      console.log("First segment field names:", Object.keys(records[0].fields));
    }

    const segments: CyclingSegment[] = records.map((record) => {
      // Create a segment object that includes all fields
      const segment: CyclingSegment = {
        id: record.id,
      };

      // Copy all fields directly from the record
      Object.keys(record.fields).forEach((fieldName) => {
        segment[fieldName] = record.fields[fieldName];
      });

      return segment;
    });

    // Return the consistent success response structure
    return NextResponse.json(
      {
        success: true,
        count: segments.length,
        data: segments,
      },
      { status: 200 },
    );
  } catch (error: any) {
    console.error("Airtable API Error:", error);

    if (error.message.includes("table not found")) {
      return NextResponse.json(
        {
          success: false,
          message: `Table "${cyclingSegmentsTableName}" not found in the Airtable base. Please check your table name.`,
          error: error.message,
        },
        { status: 500 },
      );
    }

    if (error.message.includes("Invalid API key")) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid Airtable API key. Please check your credentials.",
          error: error.message,
        },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch cycling segments from Airtable.",
        error: error.message,
      },
      { status: 500 },
    );
  }
}
