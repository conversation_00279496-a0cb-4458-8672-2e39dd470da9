import { NextRequest, NextResponse } from "next/server";
import Airtable from "airtable";

// Define the expected structure for a S&C Session based on the Airtable schema
interface SCSession {
  id: string; // Airtable Record ID
  SessionID?: string;
  "Program ID"?: string;
  "Name (from Program Description)"?: string[];
  SessionName?: string;
  SessionDescription?: string;
  Duration?: number;
  SkillCategory?: string;
  Segments?: string[]; // Links to segment records
  TrainingCategory?: string;
  IntensityMeasure?: string;
  StressLevelMeasure?: string;
  IntensityLevel?: number;
  StressLevel?: number;
  CoachingPoints?: string;
  CommonErrors?: string;
  ExerciseDistribution?: string;

  // Allow for any other fields
  [key: string]: any;
}

export async function GET(req: NextRequest) {
  try {
    // Ensure proper environment variables are configured
    const AIRTABLE_API_KEY = process.env.AIRTABLE_API_KEY;
    // Try both base ID variables
    const AIRTABLE_BASE_ID =
      process.env.AIRTABLE_SC_BASE_ID || process.env.AIRTABLE_BASE_ID;
    const AIRTABLE_TABLE_NAME =
      process.env.AIRTABLE_SC_SESSIONS_TABLE_NAME || "S&C Sessions";

    if (!AIRTABLE_API_KEY || !AIRTABLE_BASE_ID) {
      console.error("Missing Airtable environment variables");
      return NextResponse.json(
        {
          error: "Server configuration error",
          details: "Airtable API key or base ID is missing",
        },
        { status: 500 },
      );
    }

    // Initialize Airtable client
    const base = new Airtable({ apiKey: AIRTABLE_API_KEY }).base(
      AIRTABLE_BASE_ID,
    );
    const table = base(AIRTABLE_TABLE_NAME);

    // Get search parameters from the request
    const searchParams = req.nextUrl.searchParams;
    const sessionId = searchParams.get("sessionId");
    const programId = searchParams.get("programId");
    const sessionName = searchParams.get("sessionName");
    const duration = searchParams.get("duration");
    const skillCategory = searchParams.get("skillCategory");
    const trainingCategory = searchParams.get("trainingCategory");
    const intensityLevel = searchParams.get("intensityLevel");
    const stressLevel = searchParams.get("stressLevel");

    // Limit search results (default: 10, max: 100)
    const limit = Math.min(
      parseInt(searchParams.get("limit") || "10", 10),
      100,
    );

    // Build Airtable filter formula based on search parameters
    let filterFormulas: string[] = [];

    // Add filters for each search parameter
    if (sessionId) {
      filterFormulas.push(`{SessionID} = "${sessionId}"`);
    }

    if (programId) {
      filterFormulas.push(`{Program ID} = "${programId}"`);
    }

    if (sessionName) {
      filterFormulas.push(
        `FIND("${sessionName.toLowerCase()}", LOWER({SessionName})) > 0`,
      );
    }

    if (duration) {
      filterFormulas.push(`{Duration} = ${duration}`);
    }

    if (skillCategory) {
      filterFormulas.push(
        `FIND("${skillCategory.toLowerCase()}", LOWER({SkillCategory})) > 0`,
      );
    }

    if (trainingCategory) {
      filterFormulas.push(
        `FIND("${trainingCategory.toLowerCase()}", LOWER({TrainingCategory})) > 0`,
      );
    }

    if (intensityLevel) {
      filterFormulas.push(`{IntensityLevel} = ${intensityLevel}`);
    }

    if (stressLevel) {
      filterFormulas.push(`{StressLevel} = ${stressLevel}`);
    }

    // Combine filters with AND logic if any filters are specified
    const filterByFormula = filterFormulas.length
      ? `AND(${filterFormulas.join(", ")})`
      : "";

    // Log query information for debugging
    console.log(
      `Querying Airtable with formula: ${filterByFormula || "(none)"}`,
    );
    console.log(`Table name: ${AIRTABLE_TABLE_NAME}`);

    // Execute the search
    // Initialize an empty array to store the records
    const records: SCSession[] = [];

    // Make the query to Airtable
    await table
      .select({
        maxRecords: limit,
        filterByFormula: filterByFormula || "",
      })
      .eachPage(
        function page(pageRecords, fetchNextPage) {
          // Map Airtable records to the SCSession interface
          const mappedRecords = pageRecords.map((record) => {
            return {
              id: record.id,
              ...record.fields,
            } as SCSession;
          });

          // Add these records to our array
          records.push(...mappedRecords);

          // Get the next page of records
          fetchNextPage();
        },
        function done(err) {
          if (err) {
            console.error("Error fetching data from Airtable:", err);
            throw err;
          }
        },
      );

    // Return the search results
    return NextResponse.json(
      {
        success: true,
        count: records.length,
        data: records,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
