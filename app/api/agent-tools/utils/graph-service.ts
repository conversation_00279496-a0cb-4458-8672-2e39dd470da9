/**
 * GraphService: A utility for creating various types of charts and graphs
 *
 * This service provides functionality to generate base64-encoded images of charts
 * including line charts, bar charts, scatter plots, pie charts, and histograms.
 *
 * In browser environments, it uses Chart.js to render charts to canvas elements.
 * On the server, it would typically use a server-side charting library.
 */

type ChartType = "line" | "bar" | "scatter" | "pie" | "histogram";

interface GraphData {
  // Can be a simple array of values, a dictionary of series, or a complex dataset
  [key: string]: any;
}

interface GraphOptions {
  title: string;
  xLabel?: string;
  yLabel?: string;
  xData?: any[];
  figsize?: [number, number];
  colors?: string[];
  labels?: string[];
  grid?: boolean;
  legend?: boolean;
}

/**
 * Creates and returns a base64-encoded image string representing a chart
 */
export async function createGraph(
  data: GraphData | any[],
  chartType: ChartType,
  options: GraphOptions,
): Promise<string> {
  // In a server environment, we'd use a server-side charting library
  // For now, we'll return a placeholder that can be enhanced later

  // This implementation provides a simulated response
  // In a production environment, you would integrate with a proper
  // server-side charting library like node-canvas + Chart.js

  const chartDetails = {
    type: chartType,
    data: data,
    options: options,
    timestamp: new Date().toISOString(),
  };

  // For now, return a mock base64-encoded image
  return mockChartImage(chartDetails);
}

/**
 * Creates a line chart representation
 */
export async function createLineChart(
  data: GraphData | number[],
  options: GraphOptions,
): Promise<string> {
  return createGraph(data, "line", options);
}

/**
 * Creates a bar chart representation
 */
export async function createBarChart(
  data: GraphData | number[],
  options: GraphOptions,
): Promise<string> {
  return createGraph(data, "bar", options);
}

/**
 * Creates a scatter plot representation
 */
export async function createScatterPlot(
  data: GraphData | number[],
  options: GraphOptions,
): Promise<string> {
  return createGraph(data, "scatter", options);
}

/**
 * Creates a pie chart representation
 */
export async function createPieChart(
  data: GraphData | number[],
  options: GraphOptions,
): Promise<string> {
  return createGraph(data, "pie", options);
}

/**
 * Creates a histogram representation
 */
export async function createHistogram(
  data: GraphData | number[],
  options: GraphOptions,
): Promise<string> {
  return createGraph(data, "histogram", options);
}

/**
 * Mock function to return a placeholder base64 image string
 * In a real implementation, this would be replaced with actual chart generation
 */
function mockChartImage(chartDetails: any): string {
  // This is a tiny transparent 1x1 pixel GIF in base64
  // In a real implementation, this would be a chart image
  return "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
}

/**
 * Validates the input data format for charting
 */
export function validateGraphData(data: any, chartType: ChartType): boolean {
  if (!data) return false;

  if (Array.isArray(data)) {
    return data.length > 0;
  }

  if (typeof data === "object") {
    if (chartType === "pie") {
      return (
        Object.keys(data).length > 0 &&
        Object.values(data).every((v) => typeof v === "number")
      );
    }

    // For other chart types with multiple series
    return (
      Object.keys(data).length > 0 &&
      Object.values(data).every((v) => Array.isArray(v) && v.length > 0)
    );
  }

  return false;
}

/**
 * Gets appropriate colors for charts based on the series count
 */
export function getChartColors(count: number): string[] {
  const colorSets = [
    // Basic color set
    [
      "#4e79a7",
      "#f28e2c",
      "#e15759",
      "#76b7b2",
      "#59a14f",
      "#edc949",
      "#af7aa1",
      "#ff9da7",
      "#9c755f",
      "#bab0ab",
    ],

    // Tableau 10 colors
    [
      "#4c78a8",
      "#f58518",
      "#e45756",
      "#72b7b2",
      "#54a24b",
      "#eeca3b",
      "#b279a2",
      "#ff9da6",
      "#9d755d",
      "#bab0ac",
    ],
  ];

  // Pick a color set and ensure we have enough colors by cycling if needed
  const colors = colorSets[0];
  const result = [];

  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }

  return result;
}
