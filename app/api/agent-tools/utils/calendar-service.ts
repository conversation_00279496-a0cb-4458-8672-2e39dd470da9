/**
 * Provider-agnostic calendar service interface that can be implemented by various calendar providers
 */

export interface CalendarEvent {
  id?: string;
  summary: string;
  description?: string;
  start: Date | string;
  end: Date | string;
  location?: string;
  attendees?: Array<{ email: string; name?: string }>;
  status?: string;
  htmlLink?: string;
  organizer?: { email: string; name?: string };
}

export interface AvailableTimeSlot {
  start: string;
  end: string;
  length: string;
  tag: string;
}

export interface AvailableDaySlots {
  day: string;
  dayISO: string;
  slots: AvailableTimeSlot[];
}

/**
 * Generic credentials interface that will be extended by specific providers
 */
export interface CalendarCredentials {
  provider: string;
  [key: string]: any;
}

/**
 * Calendar Service Interface that all providers must implement
 */
export interface ICalendarService {
  /**
   * Get the provider name
   */
  getProviderName(): string;

  /**
   * List events from the calendar within a specified time range
   */
  listEvents(
    timeMin: Date,
    timeMax: Date,
    maxResults?: number,
  ): Promise<CalendarEvent[]>;

  /**
   * Create a new calendar event
   */
  createEvent(
    summary: string,
    description: string,
    start: Date,
    end: Date,
    location?: string,
    attendees?: Array<{ email: string; name?: string }>,
  ): Promise<CalendarEvent>;

  /**
   * Update an existing calendar event
   */
  updateEvent(
    eventId: string,
    summary?: string,
    description?: string,
    start?: Date,
    end?: Date,
    location?: string,
    attendees?: Array<{ email: string; name?: string }>,
  ): Promise<CalendarEvent>;

  /**
   * Delete a calendar event
   */
  deleteEvent(eventId: string): Promise<void>;

  /**
   * Find available time slots for workouts within a date range
   */
  findAvailableTimeSlots(
    startDate: Date,
    endDate: Date,
    minDuration?: number,
    excludedTimeRanges?: Array<{ startHour: number; endHour: number }>,
  ): Promise<AvailableDaySlots[]>;
}

/**
 * Calendar Service Factory - creates and returns the appropriate calendar service based on provider
 */
export class CalendarServiceFactory {
  private static services: Record<string, ICalendarService> = {};

  /**
   * Register a new calendar service provider
   */
  static registerService(provider: string, service: ICalendarService): void {
    this.services[provider] = service;
  }

  /**
   * Get a calendar service by provider name
   */
  static getService(provider: string): ICalendarService {
    const service = this.services[provider];
    if (!service) {
      throw new Error(
        `Calendar provider "${provider}" not found or not registered`,
      );
    }
    return service;
  }

  /**
   * Get the default calendar service based on environment configuration
   */
  static getDefaultService(): ICalendarService {
    // Determine default provider based on environment variable or configuration
    const defaultProvider = process.env.DEFAULT_CALENDAR_PROVIDER || "mock";

    // Try to get the configured service, fall back to mock if not available
    try {
      return this.getService(defaultProvider);
    } catch (error) {
      console.warn(
        `Default calendar provider "${defaultProvider}" not available, using mock provider`,
      );
      return this.getService("mock");
    }
  }
}

/**
 * Mock Calendar Service implementation for testing and fallback
 */
export class MockCalendarService implements ICalendarService {
  getProviderName(): string {
    return "mock";
  }

  async listEvents(
    timeMin: Date,
    timeMax: Date,
    maxResults = 100,
  ): Promise<CalendarEvent[]> {
    // Generate mock events
    const events: CalendarEvent[] = [];

    // Generate daily events for a work schedule over the date range
    let currentDate = new Date(timeMin);
    while (currentDate <= timeMax) {
      const dayOfWeek = currentDate.getDay();

      // Skip weekends (0 = Sunday, 6 = Saturday)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        // Morning standup
        events.push({
          id: `mock-${Date.now()}-${Math.random()}`,
          summary: "Team Standup",
          start: new Date(new Date(currentDate).setHours(9, 30, 0, 0)),
          end: new Date(new Date(currentDate).setHours(10, 0, 0, 0)),
          location: "Virtual",
        });

        // Lunch break
        events.push({
          id: `mock-${Date.now()}-${Math.random()}`,
          summary: "Lunch Break",
          start: new Date(new Date(currentDate).setHours(12, 0, 0, 0)),
          end: new Date(new Date(currentDate).setHours(13, 0, 0, 0)),
        });

        // Afternoon meeting
        if (dayOfWeek === 2 || dayOfWeek === 4) {
          // Tuesday or Thursday
          events.push({
            id: `mock-${Date.now()}-${Math.random()}`,
            summary: "Project Review",
            start: new Date(new Date(currentDate).setHours(14, 0, 0, 0)),
            end: new Date(new Date(currentDate).setHours(15, 30, 0, 0)),
            location: "Conference Room",
          });
        }
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return events.slice(0, maxResults);
  }

  async createEvent(
    summary: string,
    description: string,
    start: Date,
    end: Date,
    location?: string,
    attendees?: Array<{ email: string; name?: string }>,
  ): Promise<CalendarEvent> {
    // Create a mock event
    return {
      id: `mock-${Date.now()}-${Math.random()}`,
      summary,
      description,
      start,
      end,
      location,
      attendees,
      status: "confirmed",
      htmlLink: "https://example.com/mock-calendar-event",
    };
  }

  async updateEvent(
    eventId: string,
    summary?: string,
    description?: string,
    start?: Date,
    end?: Date,
    location?: string,
    attendees?: Array<{ email: string; name?: string }>,
  ): Promise<CalendarEvent> {
    // Return a mock updated event
    return {
      id: eventId,
      summary: summary || "Mock Event",
      description: description || "",
      start: start || new Date(),
      end: end || new Date(Date.now() + 3600000),
      location,
      attendees,
      status: "confirmed",
      htmlLink: "https://example.com/mock-calendar-event",
    };
  }

  async deleteEvent(eventId: string): Promise<void> {
    // Simulate deleting an event
    console.log(`Mock deleted event with ID: ${eventId}`);
  }

  async findAvailableTimeSlots(
    startDate: Date,
    endDate: Date,
    minDuration = 30,
    excludedTimeRanges: Array<{ startHour: number; endHour: number }> = [],
  ): Promise<AvailableDaySlots[]> {
    const events = await this.listEvents(startDate, endDate);

    // Group events by day
    const eventsByDay = events.reduce(
      (grouped, event) => {
        const startDate =
          event.start instanceof Date
            ? event.start
            : new Date(event.start as string);
        const dateStr = startDate.toISOString().split("T")[0];
        if (!grouped[dateStr]) {
          grouped[dateStr] = [];
        }
        grouped[dateStr].push({
          start: startDate,
          end:
            event.end instanceof Date
              ? event.end
              : new Date(event.end as string),
          title: event.summary,
        });
        return grouped;
      },
      {} as Record<string, Array<{ start: Date; end: Date; title: string }>>,
    );

    // Find available slots for each day
    const availableSlots: AvailableDaySlots[] = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split("T")[0];
      const dayEvents = eventsByDay[dateStr] || [];

      // Sort events by start time
      dayEvents.sort((a, b) => a.start.getTime() - b.start.getTime());

      // Early morning slot (5:30 AM - 7:00 AM)
      const earlyMorningStart = new Date(
        new Date(currentDate).setHours(5, 30, 0, 0),
      );
      const earlyMorningEnd = new Date(
        new Date(currentDate).setHours(7, 0, 0, 0),
      );

      // Lunch slot if no lunch event (12:00 PM - 1:00 PM)
      const lunchStart = new Date(new Date(currentDate).setHours(12, 0, 0, 0));
      const lunchEnd = new Date(new Date(currentDate).setHours(13, 0, 0, 0));

      // Evening slot (6:00 PM - 8:00 PM)
      const eveningStart = new Date(
        new Date(currentDate).setHours(18, 0, 0, 0),
      );
      const eveningEnd = new Date(new Date(currentDate).setHours(20, 0, 0, 0));

      const slots: AvailableTimeSlot[] = [];

      // Check if early morning slot is available
      const conflictingMorningEvent = dayEvents.find(
        (event) =>
          event.start < earlyMorningEnd && event.end > earlyMorningStart,
      );

      if (!conflictingMorningEvent) {
        slots.push({
          start: this.formatTime(earlyMorningStart),
          end: this.formatTime(earlyMorningEnd),
          length: "1.5 hours",
          tag: "Early Morning",
        });
      }

      // Check if lunch slot is available
      const hasLunchEvent = dayEvents.find(
        (event) =>
          event.start < lunchEnd &&
          event.end > lunchStart &&
          event.title.toLowerCase().includes("lunch"),
      );

      if (!hasLunchEvent) {
        slots.push({
          start: this.formatTime(lunchStart),
          end: this.formatTime(lunchEnd),
          length: "1 hour",
          tag: "Lunch Break",
        });
      }

      // Check if evening slot is available
      const conflictingEveningEvent = dayEvents.find(
        (event) => event.start < eveningEnd && event.end > eveningStart,
      );

      if (!conflictingEveningEvent) {
        slots.push({
          start: this.formatTime(eveningStart),
          end: this.formatTime(eveningEnd),
          length: "2 hours",
          tag: "Evening",
        });
      }

      if (slots.length > 0) {
        availableSlots.push({
          day: currentDate.toLocaleDateString("en-US", {
            weekday: "long",
            month: "short",
            day: "numeric",
          }),
          dayISO: dateStr,
          slots,
        });
      }

      // Move to next day
      currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
    }

    return availableSlots;
  }

  private formatTime(date: Date): string {
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  }
}

// Register the mock service as a fallback
CalendarServiceFactory.registerService("mock", new MockCalendarService());

/**
 * Parse natural language date string to Date objects
 * @param dateString Natural language date (e.g., "today", "tomorrow", "next Monday")
 * @param referenceDate Optional reference date (defaults to now)
 * @returns Object with startDate and endDate
 */
export function parseNaturalDate(
  dateString: string,
  referenceDate: Date = new Date(),
): { startDate: Date; endDate: Date } {
  const today = new Date(referenceDate);
  today.setHours(0, 0, 0, 0);

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Initialize with default results (today only)
  const result = {
    startDate: new Date(today),
    endDate: new Date(today),
  };
  result.endDate.setHours(23, 59, 59, 999);

  if (!dateString) {
    return result;
  }

  const lowerDateString = dateString.toLowerCase().trim();

  if (lowerDateString === "today") {
    result.startDate = today;
    result.endDate = new Date(today);
    result.endDate.setHours(23, 59, 59, 999);
    return result;
  }

  if (lowerDateString === "tomorrow") {
    result.startDate = tomorrow;
    result.endDate = new Date(tomorrow);
    result.endDate.setHours(23, 59, 59, 999);
    return result;
  }

  if (lowerDateString === "yesterday") {
    result.startDate = yesterday;
    result.endDate = new Date(yesterday);
    result.endDate.setHours(23, 59, 59, 999);
    return result;
  }

  // Handle "this week"
  if (lowerDateString === "this week") {
    const startOfWeek = new Date(today);
    const currentDay = startOfWeek.getDay();
    startOfWeek.setDate(
      startOfWeek.getDate() - currentDay + (currentDay === 0 ? -6 : 1),
    ); // Adjust to Monday
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(endOfWeek.getDate() + 6); // Sunday
    endOfWeek.setHours(23, 59, 59, 999);

    result.startDate = startOfWeek;
    result.endDate = endOfWeek;
    return result;
  }

  // Handle "next week"
  if (lowerDateString === "next week") {
    const startOfNextWeek = new Date(today);
    const currentDay = startOfNextWeek.getDay();
    startOfNextWeek.setDate(
      startOfNextWeek.getDate() - currentDay + (currentDay === 0 ? -6 : 1) + 7,
    ); // Next Monday
    startOfNextWeek.setHours(0, 0, 0, 0);

    const endOfNextWeek = new Date(startOfNextWeek);
    endOfNextWeek.setDate(endOfNextWeek.getDate() + 6); // Next Sunday
    endOfNextWeek.setHours(23, 59, 59, 999);

    result.startDate = startOfNextWeek;
    result.endDate = endOfNextWeek;
    return result;
  }

  // Check for "next X" pattern (days)
  const nextDayMatch = lowerDateString.match(
    /next (monday|tuesday|wednesday|thursday|friday|saturday|sunday)/i,
  );
  if (nextDayMatch && nextDayMatch[1]) {
    const dayNames = [
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ];
    const targetDay = dayNames.indexOf(nextDayMatch[1].toLowerCase());

    if (targetDay >= 0) {
      const nextDate = new Date(today);
      const currentDay = nextDate.getDay();
      let daysToAdd = targetDay - currentDay;
      if (daysToAdd <= 0) daysToAdd += 7;
      nextDate.setDate(today.getDate() + daysToAdd);

      result.startDate = nextDate;
      result.endDate = new Date(nextDate);
      result.endDate.setHours(23, 59, 59, 999);
      return result;
    }
  }

  // Check for "this X" pattern (days)
  const thisDayMatch = lowerDateString.match(
    /this (monday|tuesday|wednesday|thursday|friday|saturday|sunday)/i,
  );
  if (thisDayMatch && thisDayMatch[1]) {
    const dayNames = [
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ];
    const targetDay = dayNames.indexOf(thisDayMatch[1].toLowerCase());

    if (targetDay >= 0) {
      const thisDate = new Date(today);
      const currentDay = thisDate.getDay();
      let daysToAdd = targetDay - currentDay;
      if (daysToAdd < 0) daysToAdd += 7;
      thisDate.setDate(today.getDate() + daysToAdd);

      result.startDate = thisDate;
      result.endDate = new Date(thisDate);
      result.endDate.setHours(23, 59, 59, 999);
      return result;
    }
  }

  // Check for "next few days" or "next 3 days"
  if (
    lowerDateString.includes("next few days") ||
    lowerDateString.includes("next 3 days")
  ) {
    result.startDate = today;
    result.endDate = new Date(today);
    result.endDate.setDate(today.getDate() + 3);
    result.endDate.setHours(23, 59, 59, 999);
    return result;
  }

  // Check for "next 7 days" or "next week"
  if (
    lowerDateString.includes("next 7 days") ||
    lowerDateString.includes("next week")
  ) {
    result.startDate = today;
    result.endDate = new Date(today);
    result.endDate.setDate(today.getDate() + 7);
    result.endDate.setHours(23, 59, 59, 999);
    return result;
  }

  // If we couldn't parse it specifically but it has a number of days
  const daysMatch = lowerDateString.match(/next (\d+) days?/);
  if (daysMatch && daysMatch[1]) {
    const days = parseInt(daysMatch[1], 10);
    if (!isNaN(days)) {
      result.startDate = today;
      result.endDate = new Date(today);
      result.endDate.setDate(today.getDate() + days);
      result.endDate.setHours(23, 59, 59, 999);
      return result;
    }
  }

  // If we can't parse it, default to today only
  result.startDate = today;
  result.endDate = new Date(today);
  result.endDate.setHours(23, 59, 59, 999);
  return result;
}
