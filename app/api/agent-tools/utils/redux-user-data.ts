import axios from "axios";
import { routeLogger } from "./logger";

// Types adapted from userSlice
export interface TrainingProfile {
  [domain: string]: {
    [key: string]: any;
  };
}

export interface PlanPhase {
  name: string;
  duration: number;
  weeks: number;
  disciplines: string[];
  description?: string;
  goal?: string;
  [domain: string]: any;
}

export interface TrainingPlan {
  plan_id: string;
  plan_name: string;
  plan_type: string;
  plan_description?: string;
  dateAdded: string;
  current_phase: number;
  phases: PlanPhase[];
  adjustment_history?: Array<{
    date: string;
    adjustments: Array<{
      phase_index: number;
      discipline: string;
      parameters: Record<string, string>;
      impact: string;
      rationale: string;
    }>;
  }>;
  [key: string]: any;
}

/**
 * Fetch user data from the API (which gets data from Redux)
 * @param userId The user ID to fetch data for
 */
export async function fetchUserDataFromAPI(userId: string): Promise<{
  trainingProfile: TrainingProfile | null;
  currentPlan: TrainingPlan | null;
}> {
  try {
    routeLogger.info(`Fetching user data from API for userId: ${userId}`);

    // Make API call to fetch user data from the existing endpoint
    const baseUrl = process.env.NEXT_PUBLIC_API_URL;

    if (!baseUrl) {
      routeLogger.error(
        "Error: NEXT_PUBLIC_API_URL environment variable is not set.",
      );
      throw new Error(
        "API base URL is not configured in environment variables.",
      );
    }

    const response = await axios.get(`${baseUrl}/api/users/${userId}`);

    if (!response.data) {
      routeLogger.warn(`No user data found for userId: ${userId}`);
      return { trainingProfile: null, currentPlan: null };
    }

    const userData = response.data;

    // Log the raw data returned from API for debugging
    routeLogger.info(
      `User data API returned data structure: ${Object.keys(userData).join(", ")}`,
    );

    // Transform response to match expected format
    const trainingProfile = userData.training_profile || null;
    const currentPlan = userData.currentPlan || null;

    routeLogger.info(
      `Successfully fetched user data from API for userId: ${userId}`,
    );
    if (trainingProfile) {
      routeLogger.info(
        `Training profile domains: ${Object.keys(trainingProfile).join(", ")}`,
      );
    }
    if (currentPlan) {
      routeLogger.info(
        `Current plan: ${currentPlan.name || currentPlan.plan_name || "Unknown"}`,
      );
    }

    return {
      trainingProfile,
      currentPlan,
    };
  } catch (error) {
    routeLogger.error(`Error fetching user data from API: ${error}`);
    return { trainingProfile: null, currentPlan: null };
  }
}

/**
 * Map the user profile data from Redux format to our internal format
 * @param profile The training profile from Redux
 */
export function mapTrainingProfile(profile: any): TrainingProfile {
  try {
    if (!profile) return {};

    // Deep copy to avoid mutation
    const result: TrainingProfile = {};

    // Map each domain
    for (const [domain, data] of Object.entries(profile)) {
      if (typeof data === "object" && data !== null) {
        result[domain.toLowerCase()] = { ...data };
      }
    }

    return result;
  } catch (error) {
    routeLogger.error(`Error mapping training profile: ${error}`);
    return {};
  }
}

/**
 * Map the plan data from Redux format to our internal format
 * @param plan The training plan from Redux
 */
export function mapTrainingPlan(plan: any): TrainingPlan | null {
  try {
    if (!plan) return null;

    // Map the plan phases
    const phases =
      plan.phases?.map((phase: any) => ({
        name: phase.phaseName || "",
        duration: parseInt(phase.duration) || 0,
        weeks: parseInt(phase.weeks) || 0,
        disciplines: Object.keys(phase.disciplineSpecifics || {}),
        description: phase.description || "",
        goal: phase.focus || "",
        // Add domain-specific data
        ...Object.fromEntries(
          Object.entries(phase.disciplineSpecifics || {}).map(
            ([domain, data]: [string, any]) => {
              return [domain.toLowerCase(), data?.specificDetails || {}];
            },
          ),
        ),
      })) || [];

    return {
      plan_id: plan.id || plan.planId || `plan-${Date.now()}`,
      plan_name: plan.name || plan.planName || "Training Plan",
      plan_type: plan.planType || "general",
      plan_description: plan.description || "",
      dateAdded: plan.dateAdded || new Date().toISOString(),
      current_phase: plan.currentPhase || 0,
      phases,
    };
  } catch (error) {
    routeLogger.error(`Error mapping training plan: ${error}`);
    return null;
  }
}

/**
 * Format training profile data for prompt consumption
 */
export function formatTrainingProfileForPrompt(
  profile: TrainingProfile | null,
): string {
  try {
    if (!profile || Object.keys(profile).length === 0) {
      return "No training profile data available.";
    }

    let profileText = "User Training Profile:\n\n";

    // Process each domain in the profile
    for (const [domain, data] of Object.entries(profile)) {
      if (!data || typeof data !== "object") {
        continue;
      }

      profileText += `${domain.toUpperCase()} PROFILE:\n`;

      // Special handling for nutrition data that might be stored character by character
      if (
        domain === "nutrition" &&
        Object.keys(data).every((key) => !isNaN(Number(key)))
      ) {
        // This is likely character-by-character storage - reconstruct the string
        const nutritionText = Object.keys(data)
          .sort((a, b) => Number(a) - Number(b))
          .map((key) => data[key])
          .join("");

        profileText += `- Nutrition Information: ${nutritionText}\n`;
        continue;
      }

      // Format each key-value pair in this domain
      for (const [key, value] of Object.entries(data)) {
        // Skip phaseDomainCounts and Profile fields
        if (
          key === "phaseDomainCounts" ||
          key === "weeklyDomainCounts" ||
          key === "Profile"
        ) {
          continue;
        }

        // Format the key for better readability
        const formattedKey = key
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

        // Format the value based on its type with robust error handling
        let formattedValue = "";
        try {
          if (Array.isArray(value)) {
            // Safe handling of array join
            formattedValue = value
              .filter((item) => item !== undefined && item !== null)
              .map((item) => String(item))
              .join(", ");
          } else if (typeof value === "object" && value !== null) {
            try {
              formattedValue = JSON.stringify(value);
            } catch (jsonError) {
              formattedValue = "[Complex Object]";
            }
          } else {
            formattedValue = String(value);
          }
        } catch (formatError) {
          formattedValue = "[Error: Could not format value]";
        }

        profileText += `- ${formattedKey}: ${formattedValue}\n`;
      }

      profileText += "\n";
    }

    return profileText;
  } catch (error) {
    return "Error formatting training profile data.";
  }
}

/**
 * Formats current plan for prompt context
 */
export function formatCurrentPlanForPrompt(
  plan: TrainingPlan | null,
  currentDomain?: string,
): string {
  try {
    if (!plan) {
      return "No current training plan.";
    }

    let planText = `Current Training Plan: ${plan.plan_name || "Unknown"} (${plan.plan_type || "Unknown"})\n\n`;

    // Add plan description if available
    if (plan.plan_description) {
      planText += `Plan Description: ${plan.plan_description}\n\n`;
    }

    // Add current phase information
    const currentPhaseIndex = plan.current_phase || 0;
    const phases = plan.phases || [];

    if (!Array.isArray(phases)) {
      return planText + "Error: Plan phases data invalid.";
    }

    const currentPhase = phases[currentPhaseIndex];

    if (currentPhase) {
      planText += `CURRENT PHASE (${currentPhaseIndex + 1}/${phases.length}): ${currentPhase.name || "Unknown"}\n`;
      planText += `- Duration: ${currentPhase.duration || "Unknown"} weeks\n`;
      planText += `- Goal: ${currentPhase.goal || "Not specified"}\n`;

      // If a domain is specified, focus on that domain's details
      if (currentDomain && currentPhase[currentDomain]) {
        planText += `\n${currentDomain.toUpperCase()} DETAILS:\n`;
        const domainData = currentPhase[currentDomain];

        if (typeof domainData === "object" && domainData !== null) {
          for (const [key, value] of Object.entries(domainData)) {
            try {
              // Skip phaseDomainCounts and weeklyDomainCounts fields
              if (key === "phaseDomainCounts" || key === "weeklyDomainCounts") {
                continue;
              }

              const formattedKey = key
                .replace(/_/g, " ")
                .replace(/\b\w/g, (l) => l.toUpperCase());

              // Safe formatting of value
              let formattedValue = "";
              if (Array.isArray(value)) {
                formattedValue = value
                  .filter((item) => item !== undefined && item !== null)
                  .map((item) => String(item))
                  .join(", ");
              } else if (typeof value === "object" && value !== null) {
                try {
                  formattedValue = JSON.stringify(value);
                } catch (jsonError) {
                  formattedValue = "[Complex Object]";
                }
              } else {
                formattedValue = String(value);
              }

              planText += `- ${formattedKey}: ${formattedValue}\n`;
            } catch (keyError) {
              planText += `- Error formatting field\n`;
            }
          }
        } else {
          planText += `- ${domainData}\n`;
        }
      }
      // Otherwise, list all disciplines
      else {
        try {
          const disciplines = currentPhase.disciplines || [];
          if (Array.isArray(disciplines)) {
            const safeList = disciplines
              .filter((d) => d !== undefined && d !== null)
              .map((d) => String(d))
              .join(", ");
            planText += `- Disciplines: ${safeList || "None specified"}\n`;
          } else {
            planText += `- Disciplines: Unknown\n`;
          }
        } catch (disciplinesError) {
          planText += `- Disciplines: Error formatting\n`;
        }
      }
    }

    // Add recent adjustments if available
    if (
      plan.adjustment_history &&
      Array.isArray(plan.adjustment_history) &&
      plan.adjustment_history.length > 0
    ) {
      try {
        const recentAdjustments = plan.adjustment_history.slice(-1)[0];
        if (
          recentAdjustments &&
          recentAdjustments.adjustments &&
          Array.isArray(recentAdjustments.adjustments)
        ) {
          planText += "\nRECENT ADJUSTMENTS:\n";

          recentAdjustments.adjustments.forEach((adjustment, index) => {
            if (!currentDomain || adjustment.discipline === currentDomain) {
              planText += `- ${adjustment.discipline || "Unknown"}: ${adjustment.rationale || "No rationale provided"}\n`;
            }
          });
        }
      } catch (adjustmentsError) {
        planText += "\nRECENT ADJUSTMENTS: Error formatting adjustments data\n";
      }
    }

    return planText;
  } catch (error) {
    return "Error formatting current training plan data.";
  }
}

/**
 * Extract key training insights from profile and plan
 */
export function extractTrainingInsights(
  profile: TrainingProfile | null,
  plan: TrainingPlan | null,
  domain?: string,
): string {
  try {
    if (!profile) {
      profile = {};
    }

    // Build a comprehensive text insight rather than a structural object
    let insights = "";

    // Start with basic profile assessment
    const hasProfile = Object.keys(profile).length > 0;
    const hasPlan = !!plan;

    insights += "## User Profile Summary\n";

    // Extract profile information
    if (hasProfile) {
      insights += "Based on the provided user data, ";

      // Extract training level
      let trainingLevel = "beginner";
      if (profile.general?.fitnessLevel) {
        trainingLevel = profile.general.fitnessLevel;
      } else if (profile.strength?.currentLevel) {
        trainingLevel = profile.strength.currentLevel;
      }

      // Extract primary goals
      let primaryGoals: string[] = [];
      if (profile.general?.goals) {
        if (Array.isArray(profile.general.goals)) {
          primaryGoals = profile.general.goals.map((g) => String(g));
        } else if (profile.general.goals) {
          primaryGoals = [String(profile.general.goals)];
        }
      } else if (profile.general?.fitnessGoals) {
        primaryGoals = [String(profile.general.fitnessGoals)];
      }

      // Extract limitations
      let limitations: string[] = [];
      if (profile.general?.limitations) {
        if (Array.isArray(profile.general.limitations)) {
          limitations = profile.general.limitations.map((l) => String(l));
        } else if (profile.general.limitations) {
          limitations = [String(profile.general.limitations)];
        }
      }

      // Add training level
      insights += `the user appears to be at a ${trainingLevel} training level. `;

      // Add goals if available
      if (primaryGoals.length > 0) {
        insights += `Their primary fitness goals include: ${primaryGoals.join(", ")}. `;
      }

      // Add training routine if available
      if (profile.general?.trainingRoutine) {
        insights += `Their current training routine is: ${profile.general.trainingRoutine}. `;
      }

      // Add limitations if available
      if (limitations.length > 0) {
        insights += `They have noted the following limitations: ${limitations.join(", ")}. `;
      }

      // Add dietary info if available
      if (profile.general?.diet) {
        insights += `Dietary information: ${profile.general.diet}. `;
      } else if (profile.nutrition && typeof profile.nutrition === "object") {
        if (Object.keys(profile.nutrition).every((k) => !isNaN(Number(k)))) {
          // Handle character-by-character nutrition data
          const nutritionText = Object.keys(profile!.nutrition)
            .sort((a, b) => Number(a) - Number(b))
            .map((key) => profile!.nutrition[key])
            .join("");
          insights += `Nutrition information: ${nutritionText}. `;
        } else {
          insights +=
            "Some nutrition information is available in their profile. ";
        }
      }
    } else {
      insights +=
        "Limited profile information is available. Assuming a beginner-to-intermediate fitness level. ";
    }

    // Plan information
    insights += "\n\n## Training Plan Context\n";

    if (hasPlan) {
      // Get plan details
      const planName = plan.plan_name || "";
      const planType = plan.plan_type || "";
      const planDesc = plan.plan_description || "";
      const currentPhase =
        plan.current_phase !== undefined ? Number(plan.current_phase) : 0;

      insights += `The user is currently following the "${planName}" training plan (${planType}). `;
      if (planDesc) {
        insights += `This plan focuses on: ${planDesc}. `;
      }

      // Phase information
      if (
        plan.phases &&
        plan.phases.length > 0 &&
        currentPhase < plan.phases.length
      ) {
        const phase = plan.phases[currentPhase];
        if (phase) {
          insights += `They are currently in phase ${currentPhase + 1} (${phase.name || "Unnamed Phase"}). `;

          if (phase.goal) {
            insights += `The focus of this phase is: ${phase.goal}. `;
          }

          if (phase.disciplines && Array.isArray(phase.disciplines)) {
            insights += `This phase incorporates: ${phase.disciplines.join(", ")}. `;
          }

          // Domain-specific details if a domain was specified
          if (domain && phase[domain]) {
            insights += `Specific ${domain} parameters for this phase: ${JSON.stringify(phase[domain])}. `;
          }
        }
      }
    } else {
      insights += "No specific training plan information is available. ";
    }

    // Final recommendations based on all data
    insights += "\n\n## Workout Recommendations\n";

    if (hasProfile || hasPlan) {
      insights +=
        "Based on this user's profile and training plan, workouts should: ";

      // Level-specific recommendations
      const trainingLevel =
        profile.general?.fitnessLevel ||
        profile.strength?.currentLevel ||
        "beginner";

      if (trainingLevel.includes("advanc")) {
        insights +=
          "Be appropriately challenging for an advanced athlete with technical focus. ";
        insights +=
          "Include complex movement patterns and higher intensity techniques. ";
        insights +=
          "Incorporate periodization appropriate to their current training phase. ";
      } else if (trainingLevel.includes("intermed")) {
        insights +=
          "Build upon established fitness foundations with progressive overload. ";
        insights +=
          "Include moderate complexity exercises with focus on technique refinement. ";
      } else {
        insights +=
          "Focus on foundational movement patterns and proper technique. ";
        insights += "Gradually introduce progressive overload principles. ";
      }

      // Training routine integration
      if (
        profile.general?.trainingRoutine &&
        profile.general.trainingRoutine.toLowerCase().includes("cycling")
      ) {
        insights +=
          "Consider their cycling training when programming strength work to avoid overtraining. ";
      }
    }

    return insights;
  } catch (error) {
    return "Unable to extract detailed training insights. Generic workout recommendations will be provided.";
  }
}
