/**
 * WebSearchService: A utility service for web search functionality
 *
 * This service provides a simplified interface for web search operations.
 * It's implemented as a singleton to ensure consistent configuration across the application.
 */

import axios from "axios";

export interface SearchResult {
  title: string;
  link: string;
  snippet: string;
}

export class WebSearchService {
  private static instance: WebSearchService;
  private searchEngineId: string;
  private apiKey: string;
  private baseUrl: string = "https://www.googleapis.com/customsearch/v1";

  // Use Google Custom Search API keys from environment or provide fallbacks for development
  private constructor() {
    this.apiKey = process.env.GOOGLE_SEARCH_API_KEY || "";
    this.searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID || "";
  }

  /**
   * Get the singleton instance of WebSearchService
   */
  public static getInstance(): WebSearchService {
    if (!WebSearchService.instance) {
      WebSearchService.instance = new WebSearchService();
    }
    return WebSearchService.instance;
  }

  /**
   * Perform a web search
   */
  async search(query: string): Promise<SearchResult[]> {
    // Check if credentials are configured
    if (!this.apiKey || !this.searchEngineId) {
      console.warn("Web search API key or search engine ID not configured");
      return this.mockSearchResults(query);
    }

    try {
      const response = await axios.get(this.baseUrl, {
        params: {
          key: this.apiKey,
          cx: this.searchEngineId,
          q: query,
          num: 5,
        },
      });

      if (response.data && response.data.items) {
        return response.data.items.map((item: any) => ({
          title: item.title,
          link: item.link,
          snippet: item.snippet,
        }));
      }

      return [];
    } catch (error) {
      console.error("Error performing web search:", error);
      // Fall back to mock results if the API call fails
      return this.mockSearchResults(query);
    }
  }

  /**
   * Generate mock search results when API is not available
   * This is useful for development or when the API is not configured
   */
  private mockSearchResults(query: string): SearchResult[] {
    return [
      {
        title: `Information about ${query}`,
        link: `https://example.com/info/${encodeURIComponent(query)}`,
        snippet: `Comprehensive information about ${query} including details, facts, and relevant context.`,
      },
      {
        title: `${query} - Wikipedia`,
        link: `https://en.wikipedia.org/wiki/${encodeURIComponent(query)}`,
        snippet: `${query} is a location or point of interest. Learn about its history, features, and significance.`,
      },
    ];
  }
}
