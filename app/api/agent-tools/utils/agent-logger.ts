/**
 * Agent Activity Logger
 *
 * Utility for logging agent activities and events in a way that can be
 * displayed in the UI during streaming responses.
 */

// Shared event emitter for logs
let logListeners: ((log: AgentActivityLog) => void)[] = [];

// Log types for different agent activities
export enum AgentActivityType {
  AGENT_ACTIVE = "AGENT_ACTIVE",
  SPECIALIZED_AGENT_CALL = "SPECIALIZED_AGENT_CALL",
  USER_DATA_FETCH = "USER_DATA_FETCH",
  TOOL_CALL = "TOOL_CALL",
  STREAM_START = "STREAM_START",
  COLLECTION_AGENT_CALL = "COLLECTION_AGENT_CALL",
}

// Structure for agent activity logs
export interface AgentActivityLog {
  type: AgentActivityType;
  timestamp: string;
  agentName?: string;
  message: string;
  details?: Record<string, any>;
}

/**
 * Log an agent activity and emit it to all listeners
 */
export function logAgentActivity(
  type: AgentActivityType,
  message: string,
  agentName?: string,
  details?: Record<string, any>,
): void {
  const timestamp = new Date().toISOString();
  const log: AgentActivityLog = {
    type,
    timestamp,
    agentName,
    message,
    details,
  };

  // Console log for server-side visibility
  console.log(
    `[AGENT:ACTIVITY] [${type}] ${agentName ? `[${agentName}] ` : ""}${message}`,
  );

  // Emit to all listeners (used for streaming)
  logListeners.forEach((listener) => listener(log));
}

/**
 * Register a listener for agent activity logs
 */
export function addAgentLogListener(
  listener: (log: AgentActivityLog) => void,
): void {
  logListeners.push(listener);
}

/**
 * Remove a log listener
 */
export function removeAgentLogListener(
  listener: (log: AgentActivityLog) => void,
): void {
  logListeners = logListeners.filter((l) => l !== listener);
}

/**
 * Clear all log listeners
 */
export function clearAgentLogListeners(): void {
  logListeners = [];
}

// Convenience logging functions
export function logAgentActive(agentName: string, reason: string): void {
  logAgentActivity(
    AgentActivityType.AGENT_ACTIVE,
    `Agent ${agentName} is now active: ${reason}`,
    agentName,
  );
}

export function logSpecializedAgentCall(
  callerAgent: string,
  targetAgent: string,
  query: string,
): void {
  logAgentActivity(
    AgentActivityType.SPECIALIZED_AGENT_CALL,
    `${callerAgent} is calling ${targetAgent}`,
    callerAgent,
    { targetAgent, query },
  );
}

export function logCollectionAgentCall(
  callerAgent: string,
  targetAgent: string,
  query: string,
): void {
  logAgentActivity(
    AgentActivityType.COLLECTION_AGENT_CALL,
    `${callerAgent} is calling collection agent ${targetAgent}`,
    callerAgent,
    { targetAgent, query },
  );
}

export function logUserDataFetch(userId: string, status: string): void {
  logAgentActivity(
    AgentActivityType.USER_DATA_FETCH,
    `Fetching user data for ${userId}: ${status}`,
    undefined,
    { userId, status },
  );
}

export function logToolCall(
  agentName: string,
  toolName: string,
  params?: Record<string, any>,
): void {
  logAgentActivity(
    AgentActivityType.TOOL_CALL,
    `${agentName} is using tool: ${toolName}`,
    agentName,
    { toolName, params },
  );
}

export function logStreamStart(agentName: string): void {
  logAgentActivity(
    AgentActivityType.STREAM_START,
    `Starting stream from ${agentName}`,
    agentName,
  );
}
