import { openai } from "./openai";
import { groq } from "./groq";

// Model provider configuration from environment variables
const MODEL_PROVIDER = process.env.MODEL_PROVIDER || "openai"; // Default to OpenAI
const GROQ_MODEL_ID = process.env.GROQ_MODEL_ID || "llama-3.1-8b-instant";
const OPENAI_MODEL_ID = process.env.OPENAI_MODEL_ID || "gpt-4o";

/**
 * Get the appropriate model provider based on configuration
 * This allows for easy switching between providers without changing code throughout the application
 */
export const getModelProvider = (modelName?: string) => {
  switch (MODEL_PROVIDER.toLowerCase()) {
    case "groq":
      // If a specific model is requested, use it, otherwise use the default Groq model
      return groq(modelName || GROQ_MODEL_ID);
    case "openai":
      // If a specific model is requested, use it, otherwise default to GPT-4
      return openai(modelName || OPENAI_MODEL_ID);
    default:
      console.warn(
        `Unknown model provider ${MODEL_PROVIDER}, defaulting to Groq`,
      );
      return groq(GROQ_MODEL_ID);
  }
};

// For backward compatibility, provide a default model
export const defaultModel = getModelProvider();
