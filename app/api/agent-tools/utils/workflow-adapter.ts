import { Step, Workflow as CoreWorkflow } from "@mastra/core/workflows";
import { z } from "zod";

// Define the types that match your custom type declaration
export interface WorkflowRunContext {
  run: {
    runId: string;
    trigger: {
      data: any;
    };
    results: Record<string, StepResult>;
    steps: Record<string, StepResult>;
  };
  toolkit?: any;
}

export interface StepResult<T = any> {
  status: "success" | "error" | "skipped";
  output?: T;
  error?: Error;
}

export interface WorkflowStep<TOutput = any> {
  id: string;
  dependencies?: string[];
  outputSchema?: z.ZodType<TOutput>;
  execute: (context: WorkflowRunContext) => Promise<TOutput>;
}

export interface WorkflowDefinition {
  id: string;
  triggerSchema: z.ZodType<any>;
}

export interface WorkflowRunApi {
  start: (options: { triggerData: any }) => Promise<any>;
}

export interface Workflow {
  id: string;
  createRun: () => { start: WorkflowRunApi["start"] };
  triggerSchema?: z.ZodType<any>;
  run?: (params: { input: any }) => Promise<any>;
  trigger?: (input: any) => Promise<any>;
  commit: () => Workflow;
}

// Add WorkflowStepOutputs for plan-adjustment-workflow.ts
export type WorkflowStepOutputs<T> = {
  [K in keyof T]: T[K] extends z.ZodType<infer U> ? U : never;
};

// Types for the adapter methods
export interface WorkflowAdapterStep {
  after: (stepId: string | string[]) => WorkflowAdapterAfter;
  step: (stepOptions: WorkflowStep) => WorkflowAdapterStep;
  commit: () => Workflow;
  createRun?: () => { start: WorkflowRunApi["start"] };
  triggerSchema?: z.ZodType<any>;
  run?: (params: { input: any }) => Promise<any>;
  trigger?: (input: any) => Promise<any>;
}

export interface WorkflowAdapterAfter {
  step: (stepOptions: WorkflowStep) => WorkflowAdapterStep;
}

// Create a compatibility function that mimics the old defineWorkflow API
export function defineWorkflow(
  options: WorkflowDefinition,
): WorkflowAdapterStep {
  const workflow = new CoreWorkflow({
    name: options.id,
    triggerSchema: options.triggerSchema,
  });

  let lastStepId: string | null = null;

  // Create an adapter object with chainable methods
  const adapter: WorkflowAdapterStep = {
    step: function (stepOptions: WorkflowStep) {
      // Create a Step instance with adapted context
      const coreStep = new Step({
        id: stepOptions.id,
        outputSchema: stepOptions.outputSchema,
        execute: async ({ context }: any) => {
          // Adapt the context to match the old WorkflowRunContext format
          const adaptedContext: WorkflowRunContext = {
            run: {
              runId: context.runId || "",
              trigger: {
                data: context.triggerData || {},
              },
              results: {},
              steps: {},
            },
            toolkit: context.toolkit,
          };

          // Map the steps data
          if (context.steps) {
            Object.keys(context.steps).forEach((stepId) => {
              const step = context.steps[stepId];
              adaptedContext.run.steps[stepId] = {
                status: step.status || "success",
                output: step.output || {},
                error: step.error,
              };
              adaptedContext.run.results[stepId] =
                adaptedContext.run.steps[stepId];
            });
          }

          // Call the original execute function with the adapted context
          return await stepOptions.execute(adaptedContext);
        },
      });

      // Add the step to the workflow
      workflow.step(coreStep);
      lastStepId = stepOptions.id;

      return adapter;
    },
    after: function (stepId: string | string[]) {
      return {
        step: function (nextStepOptions: WorkflowStep) {
          const nextStep = new Step({
            id: nextStepOptions.id,
            outputSchema: nextStepOptions.outputSchema,
            execute: async ({ context }: any) => {
              // Adapt context similar to above
              const adaptedContext: WorkflowRunContext = {
                run: {
                  runId: context.runId || "",
                  trigger: {
                    data: context.triggerData || {},
                  },
                  results: {},
                  steps: {},
                },
                toolkit: context.toolkit,
              };

              if (context.steps) {
                Object.keys(context.steps).forEach((stepId) => {
                  const step = context.steps[stepId];
                  adaptedContext.run.steps[stepId] = {
                    status: step.status || "success",
                    output: step.output || {},
                    error: step.error,
                  };
                  adaptedContext.run.results[stepId] =
                    adaptedContext.run.steps[stepId];
                });
              }

              return await nextStepOptions.execute(adaptedContext);
            },
          });

          // Handle both string and array of strings for stepId
          if (Array.isArray(stepId)) {
            // For now, we'll use the first ID as a simple approach
            // A more complex implementation would need to handle dependencies differently
            if (stepId.length > 0) {
              workflow.after(stepId[0] as any).step(nextStep);
            } else {
              workflow.step(nextStep);
            }
          } else if (typeof stepId === "string") {
            workflow.after(stepId as any).step(nextStep);
          } else {
            // Fallback to last added step
            workflow.step(nextStep);
          }

          lastStepId = nextStepOptions.id;
          return adapter;
        },
      };
    },
    commit: function () {
      workflow.commit();
      const result: Workflow = {
        id: options.id,
        createRun: () => workflow.createRun(),
        triggerSchema: options.triggerSchema,
        commit: function () {
          return result;
        },
      };

      // Add these methods to the adapter as well for backward compatibility
      adapter.createRun = result.createRun;
      adapter.triggerSchema = options.triggerSchema;

      // Add run and trigger methods
      result.run = (params: { input: any }) => {
        const { start } = result.createRun();
        return start({ triggerData: params.input });
      };

      result.trigger = (input: any) => {
        const { start } = result.createRun();
        return start({ triggerData: input });
      };

      // Add these to the adapter too
      adapter.run = result.run;
      adapter.trigger = result.trigger;

      return result;
    },
  };

  return adapter;
}
