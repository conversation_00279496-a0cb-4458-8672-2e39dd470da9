import { groq as aiSdkGroq, createGroq } from "@ai-sdk/groq";

// Groq API key from environment variable
const GROQ_API_KEY = process.env.GROQ_API_KEY;

// Validate the API key
if (!GROQ_API_KEY) {
  console.warn(
    "GROQ_API_KEY environment variable not set. Groq models may not function correctly.",
  );
}

// Create a customized Groq provider
const customGroq = createGroq({
  apiKey: GROQ_API_KEY,
});

// Default model from environment variable
const DEFAULT_MODEL = process.env.GROQ_MODEL_ID || "llama-3.1-8b-instant";

// A wrapper around the Groq function from @ai-sdk/groq
export const groq = (model = DEFAULT_MODEL) => {
  console.log(`Initializing Groq model: ${model}`);
  return customGroq(model);
};

// For compatibility, export the default model
export const defaultGroqModel = groq();
