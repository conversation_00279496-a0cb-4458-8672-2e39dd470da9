/**
 * GoogleMapsService: A utility service for interacting with Google Maps APIs
 *
 * This service provides functionality for getting elevation data from Google Maps API.
 */

import axios from "axios";

interface ElevationPoint {
  distance: number;
  elevation: number;
}

export class GoogleMapsService {
  private apiKey: string;
  private baseUrl: string =
    "https://maps.googleapis.com/maps/api/elevation/json";

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Get elevation for a specific location
   */
  async getElevation(lat: number, lon: number): Promise<number | null> {
    try {
      const response = await axios.get(this.baseUrl, {
        params: {
          locations: `${lat},${lon}`,
          key: this.apiKey,
        },
      });

      if (
        response.data &&
        response.data.results &&
        response.data.results.length > 0 &&
        response.data.results[0].elevation !== undefined
      ) {
        return response.data.results[0].elevation;
      }

      console.error("No elevation results found in response");
      return null;
    } catch (error) {
      console.error("Error getting elevation:", error);
      throw new Error(
        `Failed to get elevation: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Get elevation profile along a path
   * @param points Array of latitude/longitude points
   * @returns Array of distance and elevation points
   */
  async getElevationProfile(
    points: Array<{ lat: number; lon: number }>,
  ): Promise<Array<ElevationPoint>> {
    try {
      if (!points || points.length < 2) {
        throw new Error(
          "At least 2 points are required for an elevation profile",
        );
      }

      // Format the locations string for path request
      const path = points.map((p) => `${p.lat},${p.lon}`).join("|");
      // Get more samples than points to ensure good resolution
      const samples = Math.max(points.length * 2, 10);

      const response = await axios.get(this.baseUrl, {
        params: {
          path,
          samples,
          key: this.apiKey,
        },
      });

      if (
        !response.data ||
        !response.data.results ||
        response.data.results.length === 0
      ) {
        console.error("No elevation results found in response");
        return [];
      }

      // Calculate distances and create elevation profile
      const totalDistance = this.calculateTotalDistance(points);
      const segmentLength = totalDistance / (response.data.results.length - 1);

      return response.data.results.map((result: any, index: number) => ({
        distance: index * segmentLength,
        elevation: result.elevation,
      }));
    } catch (error) {
      console.error("Error getting elevation profile:", error);
      throw new Error(
        `Failed to get elevation profile: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Calculate the total distance of a path in meters
   * @param points Array of latitude/longitude points
   * @returns Total distance in meters
   */
  private calculateTotalDistance(
    points: Array<{ lat: number; lon: number }>,
  ): number {
    let totalDistance = 0;

    for (let i = 0; i < points.length - 1; i++) {
      const point1 = points[i];
      const point2 = points[i + 1];
      totalDistance += this.calculateHaversineDistance(
        point1.lat,
        point1.lon,
        point2.lat,
        point2.lon,
      );
    }

    return totalDistance;
  }

  /**
   * Calculate the Haversine distance between two points
   * @returns Distance in meters
   */
  private calculateHaversineDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371000; // Earth radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in meters

    return distance;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
