import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Import the actual tool implementations directly
import {
  browserTool as importedBrowserTool,
  googleSearch,
} from "../tools/browser";
import { fsTool } from "../tools/fs";
import { readPDF } from "../tools/pdf";
import { listEvents } from "../tools/calendar";

/**
 * Create a complete set of tools that can be added to any agent
 */
export const createAgentTools = () => {
  return {
    browserTool: importedBrowserTool,
    fileReadTool: fsTool,
    pdfReadTool: readPDF,
    googleSearch,
    googleCalendarViewTool: listEvents,
    googleCalendarCreateTool: listEvents,
  };
};

// Re-export tools for direct usage
export {
  importedBrowserTool as browserTool,
  googleSearch,
  fsTool,
  readPDF,
  listEvents,
};
