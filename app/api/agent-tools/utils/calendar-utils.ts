import axios from "axios";
import clientPromise from "@/app/lib/mongodb";
import { routeLogger } from "./logger";

// URL check for server-side
const isServer = typeof window === "undefined";

/**
 * Interface for calendar task data
 */
export interface CalendarTask {
  id: string;
  text: string;
  type?: string;
  date: string;
  time?: string;
  isCompleted?: boolean;
  description?: string;
  [key: string]: any; // Other custom fields
}

/**
 * Interface for calendar data by date
 */
export interface CalendarDateData {
  tasks: CalendarTask[];
  [key: string]: any; // Other date-specific data
}

/**
 * Interface for calendar data range
 */
export interface CalendarRangeData {
  tasks: Record<string, CalendarDateData>;
  range: {
    startDate: string;
    endDate: string;
    daysBack: number;
    daysForward: number;
  };
}

/**
 * Fetch calendar data directly from MongoDB
 */
async function fetchCalendarDataFromDB(
  userId: string,
  daysBack: number = 7,
  daysForward: number = 7,
  domain?: string,
): Promise<CalendarRangeData | null> {
  try {
    console.log(
      `[fetchCalendarDataFromDB] Fetching calendar data for userId: ${userId}`,
    );

    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("calendar");

    // Fetch the calendar data for the user
    const calendarData = await collection.findOne({ user_id: userId });

    if (!calendarData || !calendarData.tasks) {
      console.log(
        `[fetchCalendarDataFromDB] No calendar data found for userId: ${userId}`,
      );
      return null;
    }

    // Get date range
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - daysBack);
    startDate.setHours(0, 0, 0, 0); // Start of the day

    const endDate = new Date(today);
    endDate.setDate(today.getDate() + daysForward);
    endDate.setHours(23, 59, 59, 999); // End of the day

    console.log(
      `[fetchCalendarDataFromDB] Fetching tasks from ${startDate.toISOString()} to ${endDate.toISOString()}`,
    );

    // Filter tasks within the date range
    const filteredTasks: Record<string, any> = {};

    // Function to check if a task matches the domain filter
    const matchesDomain = (task: any): boolean => {
      if (!domain) return true; // No domain filter, return all tasks

      // Check type directly if it exists and matches
      if (task.type && task.type.toLowerCase() === domain.toLowerCase()) {
        return true;
      }

      // Check if the task text contains the domain name
      if (task.text && task.text.toLowerCase().includes(domain.toLowerCase())) {
        return true;
      }

      return false;
    };

    // Format date for key
    const formatDateForKey = (date: Date): string => {
      return date.toISOString().split("T")[0];
    };

    // Loop through each date in the calendar data
    for (const dateStr in calendarData.tasks) {
      const taskDate = new Date(dateStr);

      // Check if the date is within our range
      if (taskDate >= startDate && taskDate <= endDate) {
        const dateData = calendarData.tasks[dateStr];

        // If domain is specified, filter tasks by domain
        if (domain && dateData.tasks) {
          const filteredDateTasks = dateData.tasks.filter(matchesDomain);

          // Only include this date if there are matching tasks
          if (filteredDateTasks.length > 0) {
            filteredTasks[dateStr] = {
              ...dateData,
              tasks: filteredDateTasks,
            };
          }
        } else {
          // Include all tasks for this date
          filteredTasks[dateStr] = dateData;
        }
      }
    }

    return {
      tasks: filteredTasks,
      range: {
        startDate: formatDateForKey(startDate),
        endDate: formatDateForKey(endDate),
        daysBack,
        daysForward,
      },
    };
  } catch (error) {
    console.error(
      "[fetchCalendarDataFromDB] Error fetching calendar data:",
      error,
    );
    return null;
  }
}

/**
 * Fetch calendar data for a specified range
 */
export async function fetchCalendarRange(
  userId: string,
  daysBack: number = 7,
  daysForward: number = 7,
  domain?: string,
): Promise<CalendarRangeData> {
  try {
    // In server environment, try to fetch from MongoDB first
    if (isServer) {
      console.log(
        `[fetchCalendarRange] Server-side execution for userId: ${userId}`,
      );
      const dbData = await fetchCalendarDataFromDB(
        userId,
        daysBack,
        daysForward,
        domain,
      );

      if (dbData) {
        console.log(
          `[fetchCalendarRange] Successfully fetched calendar data from DB for userId: ${userId}`,
        );
        return dbData;
      }

      console.log(
        `[fetchCalendarRange] Falling back to mock data for userId: ${userId}`,
      );
      return getMockCalendarData(userId, daysBack, daysForward);
    }

    let url = `/api/calendar/range?userId=${userId}&daysBack=${daysBack}&daysForward=${daysForward}`;
    if (domain) {
      url += `&domain=${domain}`;
    }

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("[fetchCalendarRange] Error fetching calendar range:", error);
    return getMockCalendarData(userId, daysBack, daysForward);
  }
}

/**
 * Categorizes calendar tasks into past, present, and future
 *
 * @param calendarData The calendar data to categorize
 * @returns Object with past, present, and future tasks
 */
export function categorizeCalendarTasks(calendarData: CalendarRangeData) {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Start of today

  const todayStr = today.toISOString().split("T")[0];

  const pastTasks: CalendarTask[] = [];
  const todayTasks: CalendarTask[] = [];
  const futureTasks: CalendarTask[] = [];

  // Process each date in the calendar data
  Object.entries(calendarData.tasks).forEach(([dateStr, dateData]) => {
    const date = new Date(dateStr);
    date.setHours(0, 0, 0, 0); // Start of the day

    // Add task date for easier processing later
    const tasksWithDate = dateData.tasks.map((task) => ({
      ...task,
      date: dateStr,
    }));

    if (dateStr === todayStr) {
      // Today's tasks
      todayTasks.push(...tasksWithDate);
    } else if (date < today) {
      // Past tasks
      pastTasks.push(...tasksWithDate);
    } else {
      // Future tasks
      futureTasks.push(...tasksWithDate);
    }
  });

  // Sort tasks by date
  pastTasks.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
  ); // Most recent first
  futureTasks.sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  ); // Earliest first

  return {
    pastTasks,
    todayTasks,
    futureTasks,
  };
}

/**
 * Formats calendar tasks for prompt context
 *
 * @param calendarData The calendar data to format
 * @param maxPastTasks Maximum number of past tasks to include
 * @param maxFutureTasks Maximum number of future tasks to include
 * @returns A formatted string for use in prompts
 */
export function formatCalendarContextForPrompt(
  calendarData: CalendarRangeData,
  maxPastTasks: number = 3,
  maxFutureTasks: number = 3,
): string {
  try {
    if (!calendarData || !calendarData.tasks) {
      routeLogger.warn(
        "Invalid or empty calendar data passed to formatCalendarContextForPrompt",
      );
      return "No calendar data available.";
    }

    const categorized = categorizeCalendarTasks(calendarData);

    // Handle potential undefined values safely
    const pastTasks = categorized.pastTasks || [];
    const todayTasks = categorized.todayTasks || [];
    const futureTasks = categorized.futureTasks || [];

    let contextText = "User's fitness calendar:\n\n";

    // Add past tasks
    if (pastTasks.length > 0) {
      contextText += "Recent past sessions:\n";

      try {
        pastTasks.slice(0, maxPastTasks).forEach((task) => {
          if (!task) return;

          const date = task.date || "Unknown date";
          const text = task.text || "Untitled task";
          const completedStatus = task.isCompleted ? " (Completed)" : "";

          contextText += `- ${date}: ${text}${completedStatus}\n`;
        });
      } catch (pastError) {
        routeLogger.error(`Error formatting past tasks: ${pastError}`);
        contextText += "- Error retrieving past sessions\n";
      }

      contextText += "\n";
    }

    // Add today's tasks
    if (todayTasks.length > 0) {
      contextText += "Today's sessions:\n";

      try {
        todayTasks.forEach((task) => {
          if (!task) return;

          const text = task.text || "Untitled task";
          const time = task.time ? ` at ${task.time}` : "";
          const completedStatus = task.isCompleted ? " (Completed)" : "";

          contextText += `- ${text}${time}${completedStatus}\n`;
        });
      } catch (todayError) {
        routeLogger.error(`Error formatting today's tasks: ${todayError}`);
        contextText += "- Error retrieving today's sessions\n";
      }

      contextText += "\n";
    }

    // Add future tasks
    if (futureTasks.length > 0) {
      contextText += "Upcoming sessions:\n";

      try {
        futureTasks.slice(0, maxFutureTasks).forEach((task) => {
          if (!task) return;

          const date = task.date || "Unknown date";
          const text = task.text || "Untitled task";

          contextText += `- ${date}: ${text}\n`;
        });
      } catch (futureError) {
        routeLogger.error(`Error formatting future tasks: ${futureError}`);
        contextText += "- Error retrieving upcoming sessions\n";
      }
    }

    return contextText;
  } catch (error) {
    routeLogger.error(`Error in formatCalendarContextForPrompt: ${error}`);
    return "Error formatting calendar data.";
  }
}

/**
 * Detects relevant task patterns in calendar data
 *
 * @param calendarData The calendar data to analyze
 * @returns Insights about the user's scheduling patterns
 */
export function detectCalendarPatterns(
  calendarData: CalendarRangeData,
): Record<string, any> {
  const { pastTasks, todayTasks, futureTasks } =
    categorizeCalendarTasks(calendarData);
  const allTasks = [...pastTasks, ...todayTasks, ...futureTasks];

  // Count tasks by type
  const tasksByType: Record<string, number> = {};
  allTasks.forEach((task) => {
    const type = task.type || "unknown";
    tasksByType[type] = (tasksByType[type] || 0) + 1;
  });

  // Calculate completion rate
  const completedTasks = pastTasks.filter((task) => task.isCompleted).length;
  const completionRate =
    pastTasks.length > 0 ? completedTasks / pastTasks.length : 0;

  // Detect day preferences
  const dayPreferences: Record<string, number> = {};
  allTasks.forEach((task) => {
    const date = new Date(task.date);
    const day = date.toLocaleDateString("en-US", { weekday: "long" });
    dayPreferences[day] = (dayPreferences[day] || 0) + 1;
  });

  return {
    taskCount: allTasks.length,
    tasksByType,
    completionRate,
    dayPreferences,
    recentTaskCount: pastTasks.length,
    upcomingTaskCount: futureTasks.length,
    todayTaskCount: todayTasks.length,
    hasActiveRoutine: allTasks.length > 3, // Simple heuristic
  };
}

/**
 * Utility functions for calendar operations
 */

/**
 * Parse natural language date expressions into date objects
 * @param text Natural language date expression (e.g., "next week", "tomorrow")
 * @returns Object with startDate and endDate properties
 */
export function parseNaturalDate(text: string) {
  const now = new Date();
  let startDate = new Date(now);
  let endDate = new Date(now);

  // Convert text to lowercase for easier matching
  const lowerText = text.toLowerCase().trim();

  // Match specific date patterns
  if (lowerText === "today") {
    // Do nothing, dates are already set to today
  } else if (lowerText === "tomorrow") {
    startDate.setDate(startDate.getDate() + 1);
    endDate.setDate(endDate.getDate() + 1);
  } else if (lowerText === "yesterday") {
    startDate.setDate(startDate.getDate() - 1);
    endDate.setDate(endDate.getDate() - 1);
  } else if (lowerText.includes("next week")) {
    // Next week = next Monday through Sunday
    const dayOfWeek = startDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysUntilMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek;
    startDate.setDate(startDate.getDate() + daysUntilMonday);
    endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 6);
  } else if (lowerText.includes("this week")) {
    // This week = most recent Monday through upcoming Sunday
    const dayOfWeek = startDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    startDate.setDate(
      startDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1),
    );
    endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 6);
  } else if (lowerText.includes("next month")) {
    startDate.setMonth(startDate.getMonth() + 1);
    startDate.setDate(1);
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
  } else if (lowerText.includes("this month")) {
    startDate.setDate(1);
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
  } else if (lowerText.match(/next (\d+) days?/)) {
    // Match "next X days"
    const matches = lowerText.match(/next (\d+) days?/);
    const days = matches ? parseInt(matches[1]) : 1;
    endDate.setDate(endDate.getDate() + days);
  }

  return { startDate, endDate };
}

/**
 * Generate mock calendar data for testing
 */
function getMockCalendarData(
  userId: string,
  daysBack: number,
  daysForward: number,
): CalendarRangeData {
  const today = new Date();
  const startDate = new Date(today);
  startDate.setDate(today.getDate() - daysBack);

  const endDate = new Date(today);
  endDate.setDate(today.getDate() + daysForward);

  const tasks: Record<string, CalendarDateData> = {};

  // Fill in some sample workout days
  const formatDate = (date: Date): string => {
    return date.toISOString().split("T")[0];
  };

  // Past workouts (completed)
  for (let i = daysBack; i > 0; i -= 2) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    const dateStr = formatDate(date);

    tasks[dateStr] = {
      tasks: [
        {
          id: `task-${date.getTime()}-1`,
          text: i % 6 === 0 ? "Rest day" : getRandomWorkout(i),
          type: i % 6 === 0 ? "recovery" : "workout",
          date: dateStr,
          time: "08:00",
          isCompleted: true,
          description:
            i % 6 === 0 ? "Scheduled rest and recovery" : "Completed workout",
        },
      ],
    };
  }

  // Today's workout
  const todayStr = formatDate(today);
  tasks[todayStr] = {
    tasks: [
      {
        id: `task-${today.getTime()}-1`,
        text: "Today's scheduled workout",
        type: "workout",
        date: todayStr,
        time: "17:30",
        isCompleted: false,
        description: "Pending workout for today",
      },
    ],
  };

  // Future workouts
  for (let i = 1; i <= daysForward; i += 2) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const dateStr = formatDate(date);

    tasks[dateStr] = {
      tasks: [
        {
          id: `task-${date.getTime()}-1`,
          text: i % 6 === 0 ? "Rest day" : getRandomWorkout(i),
          type: i % 6 === 0 ? "recovery" : "workout",
          date: dateStr,
          time: i % 4 === 0 ? "07:30" : "18:00",
          isCompleted: false,
          description:
            i % 6 === 0 ? "Scheduled rest and recovery" : "Planned workout",
        },
      ],
    };
  }

  return {
    tasks,
    range: {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      daysBack,
      daysForward,
    },
  };
}

/**
 * Helper to generate random workout descriptions
 */
function getRandomWorkout(seed: number): string {
  const workouts = [
    "Full body strength training",
    "Upper body focus day",
    "Lower body and core",
    "HIIT cardio session",
    "Chest and triceps",
    "Back and biceps",
    "Legs and shoulders",
    "Running: 5k tempo",
    "Swimming: interval training",
    "Cycling: endurance ride",
  ];

  return workouts[seed % workouts.length];
}
