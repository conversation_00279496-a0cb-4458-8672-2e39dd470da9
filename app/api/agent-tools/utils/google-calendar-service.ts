import { google, calendar_v3 } from "googleapis";
import { JWT } from "google-auth-library";
import {
  ICalendarService,
  CalendarEvent,
  AvailableDaySlots,
  CalendarCredentials,
  CalendarServiceFactory,
} from "./calendar-service";

/**
 * Google Calendar specific credentials
 */
export interface GoogleCalendarCredentials extends CalendarCredentials {
  provider: "google";
  clientEmail: string;
  privateKey: string;
  calendarId: string;
}

/**
 * Google Calendar service implementation of the calendar service interface
 */
export class GoogleCalendarService implements ICalendarService {
  private calendar: calendar_v3.Calendar;
  private calendarId: string;

  constructor(credentials: GoogleCalendarCredentials) {
    const auth = new JWT({
      email: credentials.clientEmail,
      key: credentials.privateKey.replace(/\\n/g, "\n"),
      scopes: [
        "https://www.googleapis.com/auth/calendar",
        "https://www.googleapis.com/auth/calendar.events",
      ],
    });

    this.calendar = google.calendar({ version: "v3", auth });
    this.calendarId = credentials.calendarId;
  }

  getProviderName(): string {
    return "google";
  }

  /**
   * List events from the calendar within a specified time range
   */
  async listEvents(
    timeMin: Date,
    timeMax: Date,
    maxResults = 100,
  ): Promise<CalendarEvent[]> {
    try {
      const response = await this.calendar.events.list({
        calendarId: this.calendarId,
        timeMin: timeMin.toISOString(),
        timeMax: timeMax.toISOString(),
        maxResults,
        singleEvents: true,
        orderBy: "startTime",
      });

      // Convert Google Calendar events to our generic format
      return (response.data.items || []).map((event) => {
        // Transform attendees
        const transformedAttendees = event.attendees
          ?.map((att) =>
            att.email
              ? { email: att.email, name: att.displayName ?? undefined }
              : null,
          )
          .filter((att): att is NonNullable<typeof att> => att !== null);

        // Transform organizer
        const transformedOrganizer = event.organizer?.email
          ? {
              email: event.organizer.email,
              name: event.organizer.displayName ?? undefined,
            }
          : undefined;

        return {
          id: event.id ?? `temp-id-${Math.random()}`,
          summary: event.summary ?? "Untitled Event",
          description: event.description ?? undefined,
          start:
            event.start?.dateTime ??
            event.start?.date ??
            new Date().toISOString(),
          end:
            event.end?.dateTime ?? event.end?.date ?? new Date().toISOString(),
          location: event.location ?? undefined,
          attendees: transformedAttendees,
          status: event.status ?? undefined,
          htmlLink: event.htmlLink ?? undefined,
          organizer: transformedOrganizer,
        };
      });
    } catch (error) {
      console.error("Error listing Google Calendar events:", error);
      throw new Error(`Failed to list calendar events: ${error}`);
    }
  }

  /**
   * Create a new calendar event
   */
  async createEvent(
    summary: string,
    description: string,
    start: Date,
    end: Date,
    location?: string,
    attendees?: Array<{ email: string; name?: string }>,
  ): Promise<CalendarEvent> {
    try {
      const eventData: calendar_v3.Schema$Event = {
        summary,
        description,
        start: {
          dateTime: start.toISOString(),
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
        end: {
          dateTime: end.toISOString(),
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
      };

      if (location) {
        eventData.location = location;
      }

      if (attendees && attendees.length > 0) {
        eventData.attendees = attendees;
      }

      const response = await this.calendar.events.insert({
        calendarId: this.calendarId,
        requestBody: eventData,
      });

      // Convert Google Calendar event to our generic format
      // Transform attendees
      const transformedAttendees = response.data.attendees
        ?.map((att) =>
          att.email
            ? { email: att.email, name: att.displayName ?? undefined }
            : null,
        )
        .filter((att): att is NonNullable<typeof att> => att !== null);

      // Transform organizer
      const transformedOrganizer = response.data.organizer?.email
        ? {
            email: response.data.organizer.email,
            name: response.data.organizer.displayName ?? undefined,
          }
        : undefined;

      return {
        id: response.data.id ?? `temp-id-${Math.random()}`,
        summary: response.data.summary ?? "Untitled Event",
        description: response.data.description ?? undefined,
        start:
          response.data.start?.dateTime ??
          response.data.start?.date ??
          new Date().toISOString(),
        end:
          response.data.end?.dateTime ??
          response.data.end?.date ??
          new Date().toISOString(),
        location: response.data.location ?? undefined,
        attendees: transformedAttendees,
        status: response.data.status ?? undefined,
        htmlLink: response.data.htmlLink ?? undefined,
        organizer: transformedOrganizer,
      };
    } catch (error) {
      console.error("Error creating Google Calendar event:", error);
      throw new Error(`Failed to create calendar event: ${error}`);
    }
  }

  /**
   * Update an existing calendar event
   */
  async updateEvent(
    eventId: string,
    summary?: string,
    description?: string,
    start?: Date,
    end?: Date,
    location?: string,
    attendees?: Array<{ email: string; name?: string }>,
  ): Promise<CalendarEvent> {
    try {
      // First, get the existing event
      const existingEvent = await this.calendar.events.get({
        calendarId: this.calendarId,
        eventId,
      });

      // Create the update payload
      const eventData: calendar_v3.Schema$Event = { ...existingEvent.data };

      if (summary) eventData.summary = summary;
      if (description) eventData.description = description;

      if (start) {
        eventData.start = {
          dateTime: start.toISOString(),
          timeZone:
            eventData.start?.timeZone ||
            Intl.DateTimeFormat().resolvedOptions().timeZone,
        };
      }

      if (end) {
        eventData.end = {
          dateTime: end.toISOString(),
          timeZone:
            eventData.end?.timeZone ||
            Intl.DateTimeFormat().resolvedOptions().timeZone,
        };
      }

      if (location) eventData.location = location;
      if (attendees) eventData.attendees = attendees;

      const response = await this.calendar.events.update({
        calendarId: this.calendarId,
        eventId,
        requestBody: eventData,
      });

      // Convert Google Calendar event to our generic format
      // Transform attendees
      const transformedAttendees = response.data.attendees
        ?.map((att) =>
          att.email
            ? { email: att.email, name: att.displayName ?? undefined }
            : null,
        )
        .filter((att): att is NonNullable<typeof att> => att !== null);

      // Transform organizer
      const transformedOrganizer = response.data.organizer?.email
        ? {
            email: response.data.organizer.email,
            name: response.data.organizer.displayName ?? undefined,
          }
        : undefined;

      return {
        id: response.data.id ?? `temp-id-${Math.random()}`,
        summary: response.data.summary ?? "Untitled Event",
        description: response.data.description ?? undefined,
        start:
          response.data.start?.dateTime ??
          response.data.start?.date ??
          new Date().toISOString(),
        end:
          response.data.end?.dateTime ??
          response.data.end?.date ??
          new Date().toISOString(),
        location: response.data.location ?? undefined,
        attendees: transformedAttendees,
        status: response.data.status ?? undefined,
        htmlLink: response.data.htmlLink ?? undefined,
        organizer: transformedOrganizer,
      };
    } catch (error) {
      console.error("Error updating Google Calendar event:", error);
      throw new Error(`Failed to update calendar event: ${error}`);
    }
  }

  /**
   * Delete a calendar event
   */
  async deleteEvent(eventId: string): Promise<void> {
    try {
      await this.calendar.events.delete({
        calendarId: this.calendarId,
        eventId,
      });
    } catch (error) {
      console.error("Error deleting Google Calendar event:", error);
      throw new Error(`Failed to delete calendar event: ${error}`);
    }
  }

  /**
   * Find available time slots for workouts
   * This identifies blocks of free time in the calendar suitable for workouts
   */
  async findAvailableTimeSlots(
    startDate: Date,
    endDate: Date,
    minDuration = 30, // minimum duration in minutes
    excludedTimeRanges: { startHour: number; endHour: number }[] = [], // time ranges to exclude (e.g., work hours)
  ): Promise<AvailableDaySlots[]> {
    try {
      // Get all events in the date range
      const events = await this.listEvents(startDate, endDate);

      // Group events by day
      const eventsByDay = events.reduce(
        (grouped, event) => {
          const startTime =
            event.start instanceof Date
              ? event.start
              : new Date(event.start as string);
          const dateStr = startTime.toISOString().split("T")[0];
          if (!grouped[dateStr]) {
            grouped[dateStr] = [];
          }

          grouped[dateStr].push({
            start: startTime,
            end:
              event.end instanceof Date
                ? event.end
                : new Date(event.end as string),
            title: event.summary,
          });

          return grouped;
        },
        {} as Record<string, Array<{ start: Date; end: Date; title: string }>>,
      );

      // Find available slots for each day
      const availableSlots: AvailableDaySlots[] = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split("T")[0];
        const dayEvents = eventsByDay[dateStr] || [];

        // Convert events to time ranges
        const busyRanges = dayEvents.map((event) => {
          return { start: event.start, end: event.end };
        });

        // Add excluded time ranges for today
        const today = new Date(currentDate);
        excludedTimeRanges.forEach(({ startHour, endHour }) => {
          const rangeStart = new Date(today);
          rangeStart.setHours(startHour, 0, 0, 0);

          const rangeEnd = new Date(today);
          rangeEnd.setHours(endHour, 0, 0, 0);

          busyRanges.push({ start: rangeStart, end: rangeEnd });
        });

        // Sort by start time
        busyRanges.sort((a, b) => a.start.getTime() - b.start.getTime());

        // Define the day's boundaries (typically 5am to 10pm)
        const dayStart = new Date(today);
        dayStart.setHours(5, 0, 0, 0);

        const dayEnd = new Date(today);
        dayEnd.setHours(22, 0, 0, 0);

        // Find free slots
        const freeSlots: Array<{ start: Date; end: Date }> = [];
        let timePointer = new Date(dayStart);

        for (const busy of busyRanges) {
          // If there's time before this busy period, it's potentially free
          if (busy.start.getTime() > timePointer.getTime()) {
            freeSlots.push({
              start: new Date(timePointer),
              end: new Date(busy.start),
            });
          }

          // Move pointer to the end of this busy period
          if (busy.end.getTime() > timePointer.getTime()) {
            timePointer = new Date(busy.end);
          }
        }

        // Check if there's time after the last busy period until day end
        if (timePointer.getTime() < dayEnd.getTime()) {
          freeSlots.push({
            start: new Date(timePointer),
            end: new Date(dayEnd),
          });
        }

        // Filter slots by minimum duration
        const minDurationMs = minDuration * 60 * 1000;
        const validSlots = freeSlots.filter(
          (slot) => slot.end.getTime() - slot.start.getTime() >= minDurationMs,
        );

        // Format slots for output
        const formattedSlots = validSlots.map((slot) => {
          const durationMs = slot.end.getTime() - slot.start.getTime();
          const durationMinutes = Math.floor(durationMs / (60 * 1000));
          const hours = Math.floor(durationMinutes / 60);
          const minutes = durationMinutes % 60;
          let durationText = "";

          if (hours > 0) {
            durationText = `${hours} hour${hours > 1 ? "s" : ""}`;
            if (minutes > 0) durationText += ` ${minutes} min`;
          } else {
            durationText = `${minutes} min`;
          }

          // Determine the time of day tag
          let tag = "Free Time";
          const hour = slot.start.getHours();
          if (hour >= 5 && hour < 9) tag = "Early Morning";
          else if (hour >= 9 && hour < 12) tag = "Morning";
          else if (hour >= 12 && hour < 13) tag = "Lunch Break";
          else if (hour >= 13 && hour < 17) tag = "Afternoon";
          else if (hour >= 17 && hour < 20) tag = "Evening";
          else if (hour >= 20) tag = "Night";

          return {
            start: this.formatTime(slot.start),
            end: this.formatTime(slot.end),
            length: durationText,
            tag,
          };
        });

        if (formattedSlots.length > 0) {
          availableSlots.push({
            day: currentDate.toLocaleDateString("en-US", {
              weekday: "long",
              month: "short",
              day: "numeric",
            }),
            dayISO: dateStr,
            slots: formattedSlots,
          });
        }

        // Move to next day
        currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
      }

      return availableSlots;
    } catch (error) {
      console.error(
        "Error finding available time slots in Google Calendar:",
        error,
      );
      throw new Error(`Failed to find available time slots: ${error}`);
    }
  }

  /**
   * Helper function to format time in 12-hour format
   */
  private formatTime(date: Date): string {
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  }
}

// Initialize and register Google Calendar service if credentials are available
const googleCalendarCredentials: GoogleCalendarCredentials = {
  provider: "google",
  clientEmail: process.env.GOOGLE_CALENDAR_CLIENT_EMAIL || "",
  privateKey: process.env.GOOGLE_CALENDAR_PRIVATE_KEY || "",
  calendarId: process.env.GOOGLE_CALENDAR_CALENDAR_ID || "",
};

// Only register if credentials are provided
if (
  googleCalendarCredentials.clientEmail &&
  googleCalendarCredentials.privateKey &&
  googleCalendarCredentials.calendarId
) {
  try {
    const googleCalendarService = new GoogleCalendarService(
      googleCalendarCredentials,
    );
    CalendarServiceFactory.registerService("google", googleCalendarService);
    console.log("Google Calendar service registered");

    // Set as default if specified
    if (process.env.DEFAULT_CALENDAR_PROVIDER === "google") {
      console.log("Google Calendar set as default provider");
    }
  } catch (error) {
    console.error("Failed to initialize Google Calendar service:", error);
  }
}
