import { z } from "zod";
import * as graphService from "./graph-service";

/**
 * GraphTool: A tool for creating various types of charts and graphs
 *
 * This tool provides an interface for agents to create visualizations
 * from data. It supports multiple chart types and configuration options.
 */

// Schema for graph data validation
export const graphDataSchema = z.union([
  z.record(z.any()), // Object with key-value pairs
  z.array(z.number()), // Array of numbers
  z.array(z.record(z.any())), // Array of objects for complex datasets
]);

// Schema for chart type
export const chartTypeSchema = z.enum([
  "line",
  "bar",
  "scatter",
  "pie",
  "histogram",
]);

// Schema for graph options
export const graphOptionsSchema = z.object({
  title: z.string(),
  xLabel: z.string().optional(),
  yLabel: z.string().optional(),
  xData: z.array(z.any()).optional(),
  figsize: z.tuple([z.number(), z.number()]).optional(),
  colors: z.array(z.string()).optional(),
  labels: z.array(z.string()).optional(),
  grid: z.boolean().optional(),
  legend: z.boolean().optional(),
});

// Schema for graph tool input
export const graphToolInputSchema = z.object({
  data: graphDataSchema,
  chartType: chartTypeSchema,
  title: z.string(),
  x_label: z.string().optional(),
  y_label: z.string().optional(),
  x_data: z.array(z.any()).optional(),
  figsize: z.tuple([z.number(), z.number()]).optional(),
  colors: z.array(z.string()).optional(),
  labels: z.array(z.string()).optional(),
  grid: z.boolean().optional(),
  legend: z.boolean().optional(),
});

// Schema for graph tool output
export const graphToolOutputSchema = z.object({
  image: z.string(),
  type: z.string(),
  success: z.boolean(),
  error: z.string().optional(),
});

/**
 * Creates a graph based on the provided data and parameters
 */
export async function createGraph(
  params: z.infer<typeof graphToolInputSchema>,
): Promise<z.infer<typeof graphToolOutputSchema>> {
  try {
    // Validate input data format
    if (!graphService.validateGraphData(params.data, params.chartType as any)) {
      return {
        image: "",
        type: params.chartType,
        success: false,
        error: "Invalid data format for selected chart type",
      };
    }

    // Convert snake_case to camelCase for options
    const options = {
      title: params.title,
      xLabel: params.x_label,
      yLabel: params.y_label,
      xData: params.x_data,
      figsize: params.figsize,
      colors:
        params.colors ||
        graphService.getChartColors(
          Array.isArray(params.data) ? 1 : Object.keys(params.data).length,
        ),
      labels: params.labels,
      grid: params.grid ?? true,
      legend: params.legend ?? true,
    };

    // Call the appropriate graph service function based on chart type
    let imageData: string;
    switch (params.chartType) {
      case "line":
        imageData = await graphService.createLineChart(params.data, options);
        break;
      case "bar":
        imageData = await graphService.createBarChart(params.data, options);
        break;
      case "scatter":
        imageData = await graphService.createScatterPlot(params.data, options);
        break;
      case "pie":
        imageData = await graphService.createPieChart(params.data, options);
        break;
      case "histogram":
        imageData = await graphService.createHistogram(params.data, options);
        break;
      default:
        return {
          image: "",
          type: params.chartType,
          success: false,
          error: `Unsupported chart type: ${params.chartType}`,
        };
    }

    return {
      image: imageData,
      type: params.chartType,
      success: true,
    };
  } catch (error) {
    console.error("Error creating graph:", error);
    return {
      image: "",
      type: params.chartType,
      success: false,
      error:
        error instanceof Error ? error.message : "Unknown error creating graph",
    };
  }
}

/**
 * Default options for graph creation
 */
export const defaultGraphOptions = {
  figsize: [10, 6] as [number, number],
  grid: true,
  legend: true,
};

/**
 * Provides a description of the graph tool for agent documentation
 */
export function getGraphToolDescription(): string {
  return `Creates and displays a graph based on the provided data and parameters.
Supports various chart types including line, bar, scatter, pie, and histogram.
Returns a base64 encoded image that can be displayed in the UI.`;
}
