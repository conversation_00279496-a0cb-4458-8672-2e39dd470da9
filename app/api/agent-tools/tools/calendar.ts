import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import {
  CalendarServiceFactory,
  parseNaturalDate,
} from "../utils/calendar-service";
import { toolLogger } from "../utils/logger";

export const listEvents = createTool({
  id: "listEvents",
  name: "List Calendar Events",
  description: "Lists events from the calendar within a specified date range",
  inputSchema: z.object({
    timeRange: z
      .string()
      .describe(
        'Time range to fetch events for (e.g., "this week", "next 3 days")',
      ),
  }),
  execute: async ({ context }) => {
    toolLogger.info(
      `Calendar tool called with time range: ${context.timeRange}`,
    );
    const startTime = Date.now();

    try {
      toolLogger.debug("Getting calendar service");
      // Try to get the default service
      let calendarService;
      try {
        calendarService = CalendarServiceFactory.getDefaultService();
      } catch (e) {
        // If default service failed, explicitly get the mock service
        try {
          calendarService = CalendarServiceFactory.getService("mock");
        } catch (e2) {
          toolLogger.warn("No calendar service configured");
          return {
            events: [],
            message:
              "No calendar service configured. Please set up calendar integration.",
          };
        }
      }

      // Parse the natural language time range
      toolLogger.debug(`Parsing time range: ${context.timeRange}`);
      const dateRange = parseNaturalDate(context.timeRange);

      if (!dateRange || !dateRange.startDate || !dateRange.endDate) {
        toolLogger.warn(`Invalid time range: ${context.timeRange}`);
        return {
          events: [],
          message: `Could not parse time range: "${context.timeRange}". Please use a clearer description.`,
        };
      }

      const { startDate, endDate } = dateRange;
      toolLogger.debug(
        `Fetching events from ${startDate.toISOString()} to ${endDate.toISOString()}`,
      );

      // Get events from the calendar service
      const events = await calendarService.listEvents(startDate, endDate);

      // Format the response
      const formattedEvents = events.map((event) => ({
        title: event.summary,
        start:
          event.start instanceof Date ? event.start.toISOString() : event.start,
        end: event.end instanceof Date ? event.end.toISOString() : event.end,
        location: event.location || "No location",
      }));

      const duration = Date.now() - startTime;
      toolLogger.info(
        `Calendar tool completed in ${duration}ms, found ${events.length} events`,
      );

      return {
        events: formattedEvents,
        message: `Found ${events.length} events in the specified time range.`,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      if (error instanceof Error) {
        toolLogger.error(
          `Calendar tool error after ${duration}ms: ${error.message}`,
          { error },
        );
        return {
          events: [],
          message: `Error accessing calendar: ${error.message}`,
        };
      }

      toolLogger.error(`Unknown calendar tool error after ${duration}ms`);
      return {
        events: [],
        message: "An unknown error occurred while accessing the calendar",
      };
    }
  },
});
