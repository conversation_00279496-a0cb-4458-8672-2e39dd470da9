import { createTool } from "@mastra/core/tools";
import fs from "fs";
import path from "path";
import { z } from "zod";
import { toolLogger } from "../utils/logger";

export const fsTool = createTool({
  id: "fileSystem",
  name: "File System Tool",
  description: "Read files from the file system",
  inputSchema: z.object({
    path: z.string().describe("Path to the file to read"),
    encoding: z
      .enum(["utf8", "base64", "ascii"])
      .optional()
      .default("utf8")
      .describe("Encoding for reading the file"),
  }),
  outputSchema: z.object({
    content: z.string(),
    message: z.string(),
  }),
  execute: async ({ context: { path: filePath, encoding = "utf8" } }) => {
    toolLogger.info(`File system tool called with path: ${filePath}`);
    const startTime = Date.now();

    try {
      // Normalize and secure the path
      const normalizedPath = path.normalize(filePath);
      toolLogger.debug(`Normalized path: ${normalizedPath}`);

      // Ensure the file exists
      if (!fs.existsSync(normalizedPath)) {
        toolLogger.warn(`File not found: ${normalizedPath}`);
        return {
          content: "",
          message: `File not found: ${normalizedPath}`,
        };
      }

      // Check if it's a file
      const stats = fs.statSync(normalizedPath);
      if (!stats.isFile()) {
        toolLogger.warn(`Not a file: ${normalizedPath}`);
        return {
          content: "",
          message: `Not a file: ${normalizedPath}`,
        };
      }

      // Read the file
      toolLogger.debug(`Reading file with encoding: ${encoding}`);
      const content = fs.readFileSync(normalizedPath, {
        encoding: encoding as BufferEncoding,
      });

      const duration = Date.now() - startTime;
      toolLogger.info(
        `File system tool completed in ${duration}ms, read ${content.length} bytes`,
      );

      return {
        content,
        message: `Successfully read file: ${normalizedPath} (${content.length} bytes)`,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      if (error instanceof Error) {
        toolLogger.error(
          `File system tool error after ${duration}ms: ${error.message}`,
          {
            error,
            path: filePath,
          },
        );
        return {
          content: "",
          message: `Error reading file: ${error.message}`,
        };
      }

      toolLogger.error(`Unknown file system tool error after ${duration}ms`, {
        path: filePath,
      });
      return {
        content: "",
        message: "An unknown error occurred while reading the file",
      };
    }
  },
});
