import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import axios from "axios";

// Environment variables - these will be accessed at runtime
const NUTRITIONAI_SUBSCRIPTION_KEY = process.env.NUTRITIONAI_SUBSCRIPTION_KEY;

// Logging function for consistency
function log(level: string, message: string) {
  console.log(`[NUTRITION:TOOL] [${level}] ${message}`);
}

// Helper to mask sensitive info
function maskApiKey(key: string | undefined): string {
  if (!key) return "undefined";
  if (key.length <= 8) return "****";
  return key.substring(0, 4) + "****" + key.substring(key.length - 4);
}

// Log environment variables on startup (masked for security)
log("INFO", `Environment variables check:`);
log(
  "INFO",
  `NUTRITIONAI_SUBSCRIPTION_KEY: ${maskApiKey(NUTRITIONAI_SUBSCRIPTION_KEY)}`,
);

/**
 * Tool for searching nutrition information using Passio NutritionAI API
 */
export const nutritionAiSearchTool = createTool({
  id: "nutritionAiSearch",
  name: "Nutrition AI Search",
  description:
    "Search for nutrition information using the Passio NutritionAI API",
  inputSchema: z.object({
    query: z
      .string()
      .describe("The food item or recipe to search for nutrition information"),
  }),
  outputSchema: z.object({
    result: z
      .any()
      .describe("Nutrition information from Passio NutritionAI API"),
  }),
  execute: async ({ context }) => {
    const { query } = context;
    log("INFO", `Searching for nutrition info with query: ${query}`);

    // Directly use the subscription key if it's not set in env
    const apiKey =
      NUTRITIONAI_SUBSCRIPTION_KEY ||
      "catE927LqY60a0f36DBEr31NHoORkNeRRvHmngtk";

    log("DEBUG", `Using subscription key: ${maskApiKey(apiKey)}`);

    try {
      // Step 1: Get the Bearer token following documentation
      const tokenUrl = `https://api.passiolife.com/v2/token-cache/napi/oauth/token/${apiKey}`;
      log("DEBUG", `Requesting auth token from: ${tokenUrl}`);

      const tokenResponse = await axios.post(tokenUrl);
      log("DEBUG", `Token response status: ${tokenResponse.status}`);

      if (!tokenResponse.data || !tokenResponse.data.access_token) {
        log(
          "ERROR",
          `No access token in response: ${JSON.stringify(tokenResponse.data)}`,
        );
        throw new Error("No access token received from Passio API");
      }

      const token = tokenResponse.data.access_token;
      const customerId = tokenResponse.data.customer_id; // Extract customer_id from response
      log("DEBUG", `Received token: ${maskApiKey(token)}`);
      log("DEBUG", `Received customer_id: ${customerId}`);

      // Step 2: Use the token to make the actual API request with the correct endpoint
      // The correct endpoint is the semantic search endpoint based on the documentation
      const apiUrl =
        "https://api.passiolife.com/v2/products/napi/food/search/semantic";
      log("DEBUG", `Making semantic search request to: ${apiUrl}`);

      const response = await axios.get(apiUrl, {
        params: { term: query }, // Using 'term' parameter as required by the API
        headers: {
          Authorization: `Bearer ${token}`,
          "Passio-ID": customerId, // Add the required Passio-ID header
          "Content-Type": "application/json",
        },
      });

      log("INFO", "Nutrition AI search successful");
      log("DEBUG", `Response status: ${response.status}`);
      log(
        "DEBUG",
        `Response data (preview): ${JSON.stringify(response.data).substring(0, 200)}...`,
      );

      return { result: response.data };
    } catch (error: any) {
      log("ERROR", `Error in nutritionAiSearch: ${error}`);

      // More detailed error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        log("ERROR", `Response status: ${error.response.status}`);
        log(
          "ERROR",
          `Response headers: ${JSON.stringify(error.response.headers)}`,
        );
        log("ERROR", `Response data: ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        // The request was made but no response was received
        log("ERROR", `No response received: ${String(error.request)}`);
      } else {
        // Something happened in setting up the request
        log("ERROR", `Request setup error: ${error.message}`);
      }

      // Fallback to return a graceful error message
      return {
        result: {
          error: true,
          message: `Failed to search nutrition info: ${error.message || "Unknown error"}`,
          details: error.response?.data || {},
        },
      };
    }
  },
});
