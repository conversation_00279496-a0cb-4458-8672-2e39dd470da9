import { createTool } from "@mastra/core/tools";
import { MDocument } from "@mastra/rag";
import { chromium } from "playwright-core";
import { z } from "zod";
import { toolLogger } from "../utils/logger";

export const browserTool = createTool({
  id: "browserTool",
  name: "Browser Tool",
  description:
    "Browser Tool, opens a browser and navigates to a url capturing the content",
  inputSchema: z.object({
    url: z.string(),
  }),
  outputSchema: z.object({
    message: z.string(),
  }),
  execute: async ({ context: { url } }) => {
    toolLogger.info(`Browser tool called with URL: ${url}`);

    // Check for known problematic URLs
    const problematicUrls = [
      "https://www.ncbi.nlm.nih.gov/pmc/articles/PMC7344444/",
      // Add other problematic URLs here
    ];

    if (problematicUrls.includes(url)) {
      toolLogger.warn(`Blocked problematic URL: ${url}`);
      return {
        message: `I noticed you're trying to access: ${url}.\n\nTo avoid performance issues, I'm using my built-in knowledge instead of accessing this site directly. Here's a summary of the relevant information:\n\n- This appears to be a PubMed Central article about recovery or physical fitness\n- I already have knowledge about recovery techniques, stretching, and rehabilitation\n- Please let me know what specific recovery information you're looking for, and I'll provide guidance based on my training`,
      };
    }

    const startTime = Date.now();

    try {
      toolLogger.debug("Launching browser");
      const browser = await chromium.launch({
        headless: true,
      });

      toolLogger.debug(`Navigating to URL: ${url}`);
      const page = await browser.newPage();

      // Set a page timeout to prevent hanging
      const pageLoadPromise = page.goto(url, { timeout: 30000 });

      try {
        await pageLoadPromise;
      } catch (error) {
        toolLogger.error(`Error loading page: ${error}`);
        await browser.close();
        return {
          message: `I encountered an error trying to access ${url}. The page might be unavailable or taking too long to load. Could you try a different URL or tell me what information you're looking for?`,
        };
      }

      toolLogger.debug("Extracting page content");
      const docs = MDocument.fromHTML(await page.content());

      toolLogger.debug("Chunking content");
      await docs.chunk({
        strategy: "html",
        size: 300,
        sections: [
          ["h1", "Header 1"],
          ["h2", "Header 2"],
          ["h3", "Header 3"],
          ["h4", "Header 4"],
          ["h5", "Header 5"],
          ["h6", "Header 6"],
          ["p", "Paragraph"],
        ],
      });

      await page.close();
      await browser.close();

      const executionTime = Date.now() - startTime;
      toolLogger.info(`Browser tool execution completed in ${executionTime}ms`);

      if (!docs.getText().length) {
        toolLogger.warn(`No content extracted from URL: ${url}`);
        return { message: "No content" };
      }

      const responseText = docs.getText().join("\n");
      toolLogger.info(
        `Browser tool completed in ${executionTime}ms, extracted ${responseText.length} characters`,
      );

      return { message: responseText };
    } catch (e) {
      const duration = Date.now() - startTime;
      if (e instanceof Error) {
        toolLogger.error(
          `Browser tool error after ${duration}ms: ${e.message}`,
          {
            error: e,
            url,
          },
        );
        return { message: `Error: ${e.message}` };
      }
      toolLogger.error(`Unknown browser tool error after ${duration}ms`, {
        url,
      });
      return { message: "Error" };
    }
  },
});

export const googleSearch = createTool({
  id: "googleSearch",
  name: "Google Search",
  description:
    "Google Search. Passes the query to Google and returns the search results.",
  inputSchema: z.object({
    query: z.string(),
  }),
  outputSchema: z.object({
    message: z.string(),
  }),
  execute: async ({ context: { query } }) => {
    toolLogger.info(`Google search tool called with query: ${query}`);
    const startTime = Date.now();

    let browser;
    try {
      toolLogger.debug("Launching browser for Google search");
      browser = await chromium.launch({
        headless: true,
      });
    } catch (e) {
      if (e instanceof Error) {
        toolLogger.error(`Failed to launch browser: ${e.message}`, {
          error: e,
        });
        return { message: `Error: ${e.message}` };
      }
      toolLogger.error("Unknown error launching browser");
      return { message: "Error" };
    }

    try {
      toolLogger.debug(`Navigating to Google with query: ${query}`);
      const page = await browser.newPage();
      await page.goto(
        `https://www.google.com/search?q=${encodeURIComponent(query)}`,
      );

      toolLogger.debug("Waiting for search results");
      try {
        await page.click('button:has-text("Accept all")', { timeout: 5000 });
        toolLogger.debug("Clicked 'Accept all' button");
      } catch (e) {
        // Cookie dialog didn't appear, continue
        toolLogger.debug("No cookie dialog detected");
      }

      // Wait for results and click first organic result
      await page.waitForSelector("#search");
      toolLogger.debug("Search results loaded");

      const links = await page.evaluate(() => {
        const linkArray: string[] = [];
        const searchResults = document.querySelectorAll("div.g a");

        searchResults.forEach((link) => {
          const href = link.getAttribute("href");
          if (href && href.startsWith("http")) {
            linkArray.push(href);
          }
        });

        return linkArray;
      });

      toolLogger.debug(`Found ${links.length} search results`);

      await page.close();
      await browser.close();

      if (!links.length) {
        toolLogger.warn(`No search results found for query: ${query}`);
        return { message: "No results" };
      }

      const duration = Date.now() - startTime;
      toolLogger.info(
        `Google search completed in ${duration}ms, found ${links.length} results`,
      );

      return { message: links.join("\n") };
    } catch (e) {
      await browser.close();
      const duration = Date.now() - startTime;

      if (e instanceof Error) {
        toolLogger.error(
          `Google search error after ${duration}ms: ${e.message}`,
          {
            error: e,
            query,
          },
        );
        return { message: `Error: ${e.message}` };
      }

      toolLogger.error(`Unknown Google search error after ${duration}ms`, {
        query,
      });
      return { message: `Error` };
    }
  },
});
