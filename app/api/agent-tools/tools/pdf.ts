import { createTool } from "@mastra/core/tools";
import fs from "fs";
import path from "path";
import { z } from "zod";
// @ts-ignore - PDF parse doesn't have TypeScript types
import pdfParse from "pdf-parse/lib/pdf-parse.js";
import { toolLogger } from "../utils/logger";

export const readPDF = createTool({
  id: "readPDF",
  name: "PDF Reader Tool",
  description: "Read and extract text from PDF files",
  inputSchema: z.object({
    path: z.string().describe("Path to the PDF file to read"),
    pages: z.string().optional().describe('Page range to read (e.g., "1-5")'),
  }),
  outputSchema: z.object({
    content: z.string(),
    title: z.string(),
    pages: z.number(),
    message: z.string(),
  }),
  execute: async ({ context: { path: filePath, pages } }) => {
    toolLogger.info(
      `PDF tool called with path: ${filePath}, pages: ${pages || "all"}`,
    );
    const startTime = Date.now();

    try {
      // Normalize and secure the path
      const normalizedPath = path.normalize(filePath);
      toolLogger.debug(`Normalized path: ${normalizedPath}`);

      // Check if file exists
      if (!fs.existsSync(normalizedPath)) {
        toolLogger.warn(`PDF file not found: ${normalizedPath}`);
        return {
          content: "",
          title: "",
          pages: 0,
          message: `PDF file not found: ${normalizedPath}`,
        };
      }

      // Check if file is a PDF
      if (path.extname(normalizedPath).toLowerCase() !== ".pdf") {
        toolLogger.warn(`Not a PDF file: ${normalizedPath}`);
        return {
          content: "",
          title: "",
          pages: 0,
          message: `Not a PDF file: ${normalizedPath}`,
        };
      }

      // Read the PDF file
      toolLogger.debug("Reading PDF file");
      const dataBuffer = fs.readFileSync(normalizedPath);

      // Parse PDF options
      const options: any = {};
      if (pages) {
        const pageRangeMatch = pages.match(/(\d+)-(\d+)/);
        if (pageRangeMatch) {
          const [, startPage, endPage] = pageRangeMatch;
          options.max = parseInt(endPage);
          options.min = parseInt(startPage);
          toolLogger.debug(
            `Parsing PDF with page range: ${options.min}-${options.max}`,
          );
        } else if (pages.match(/^\d+$/)) {
          // Single page
          options.max = parseInt(pages);
          options.min = parseInt(pages);
          toolLogger.debug(`Parsing PDF with single page: ${options.min}`);
        }
      } else {
        toolLogger.debug("Parsing entire PDF");
      }

      // Parse the PDF
      const data = await pdfParse(dataBuffer, options);

      const duration = Date.now() - startTime;
      toolLogger.info(
        `PDF tool completed in ${duration}ms, extracted ${data.text.length} characters from ${data.numpages} pages`,
      );

      return {
        content: data.text,
        title: data.info?.Title || path.basename(normalizedPath, ".pdf"),
        pages: data.numpages,
        message: `Successfully read PDF with ${data.numpages} pages (${data.text.length} characters)`,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      if (error instanceof Error) {
        toolLogger.error(
          `PDF tool error after ${duration}ms: ${error.message}`,
          {
            error,
            path: filePath,
            pages,
          },
        );
        return {
          content: "",
          title: "",
          pages: 0,
          message: `Error reading PDF: ${error.message}`,
        };
      }

      toolLogger.error(`Unknown PDF tool error after ${duration}ms`, {
        path: filePath,
        pages,
      });
      return {
        content: "",
        title: "",
        pages: 0,
        message: "An unknown error occurred while reading the PDF",
      };
    }
  },
});
