import { z } from "zod";
import { createTool } from "@mastra/core/tools";
import { toolLogger } from "../utils/logger";
import axios from "axios";
import * as cheerio from "cheerio";
import TurndownService from "turndown";

// Input schema for search
const searchInputSchema = z.object({
  query: z.string().describe("The search query to perform"),
});

// Output schema for search
const searchOutputSchema = z.object({
  message: z.string().describe("The search results"),
});

// Input schema for webpage visit
const visitWebpageInputSchema = z.object({
  url: z.string().describe("The URL of the webpage to visit"),
});

// Output schema for webpage visit
const visitWebpageOutputSchema = z.object({
  message: z.string().describe("The content of the webpage in markdown format"),
});

// Input schema for credibility evaluation
const credibilityInputSchema = z.object({
  url: z.string().describe("The URL of the source to evaluate"),
  content: z.string().describe("The content from the source to evaluate"),
  metadata: z
    .record(z.any())
    .optional()
    .describe("Additional metadata about the source"),
});

// Output schema for credibility evaluation
const credibilityOutputSchema = z.object({
  score: z.number(),
  level: z.string(),
  reasons: z.array(z.string()),
  url: z.string(),
});

interface SearchResult {
  title: string;
  href: string;
  body: string;
}

/**
 * Web search tool using SERPAPI
 */
export const duckDuckGoSearch = createTool({
  id: "duckDuckGoSearch",
  name: "Web Search",
  description: "Searches the web for the given query using SERPAPI",
  inputSchema: searchInputSchema,
  outputSchema: searchOutputSchema,
  execute: async ({ context }: { context: { query: string } }) => {
    const startTime = Date.now();
    const { query } = context;
    toolLogger.info(`Search tool called with query: ${query}`);

    try {
      // Using SERPAPI for web search
      const apiKey =
        process.env.SERPAPI_API_KEY ||
        "253c9220d1dc945816a7ae4ce4b04a32e8db215358c651084cc796bc431654f0";

      if (!apiKey) {
        toolLogger.error("No SERPAPI API key found");
        return {
          message:
            "Error: No API key available for search. Please configure SERPAPI_API_KEY.",
        };
      }

      const response = await axios.get("https://serpapi.com/search", {
        params: {
          q: query,
          api_key: apiKey,
          engine: "google",
          num: 10,
        },
      });

      if (response.data && response.data.organic_results) {
        const results = response.data.organic_results.map((result: any) => ({
          title: result.title,
          href: result.link,
          body: result.snippet || "",
        }));

        // Format results
        const formattedResults = results
          .map(
            (result: SearchResult) =>
              `[${result.title}](${result.href})\n${result.body}`,
          )
          .join("\n\n");

        const executionTime = Date.now() - startTime;
        toolLogger.info(`Search completed in ${executionTime}ms`);

        return { message: "## Search Results\n\n" + formattedResults };
      } else {
        toolLogger.warn("No organic results found in SERPAPI response");
        return {
          message: "No search results found. Please try a different query.",
        };
      }
    } catch (error) {
      toolLogger.error(
        `Search exception: ${error instanceof Error ? error.message : String(error)}`,
      );
      return {
        message: `An error occurred during search: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

/**
 * Visit webpage tool - fetches and converts a webpage to markdown
 */
export const visitWebpage = createTool({
  id: "visitWebpage",
  name: "Visit Webpage",
  description:
    "Visits a webpage at the given URL and returns its content in markdown format",
  inputSchema: visitWebpageInputSchema,
  outputSchema: visitWebpageOutputSchema,
  execute: async ({ context }: { context: { url: string } }) => {
    const startTime = Date.now();
    const { url } = context;
    toolLogger.info(`Visit webpage tool called with URL: ${url}`);

    try {
      // Use axios to fetch the webpage content
      const response = await axios.get(url, {
        timeout: 20000, // 20 seconds timeout
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        },
      });

      // Use cheerio to parse the HTML content
      const $ = cheerio.load(response.data);
      const title = $("title").text() || "No title found";

      // Remove script and style tags
      $("script, style").remove();

      // Get the body content
      const htmlContent = $("body").html() || "";

      // Convert HTML to Markdown using turndown
      const turndownService = new TurndownService({
        headingStyle: "atx",
        codeBlockStyle: "fenced",
      });

      // Convert HTML to Markdown
      let markdownContent = turndownService.turndown(htmlContent).trim();

      // Remove multiple line breaks
      markdownContent = markdownContent.replace(/\n{3,}/g, "\n\n");

      // Truncate if necessary
      const maxLength = 10000;
      if (markdownContent.length > maxLength) {
        markdownContent =
          markdownContent.substring(0, maxLength) +
          "\n\n[Content truncated due to length...]";
      }

      const executionTime = Date.now() - startTime;
      toolLogger.info(
        `Visit webpage completed in ${executionTime}ms, content length: ${markdownContent.length} characters`,
      );

      return { message: `# ${title}\n\n${markdownContent}` };
    } catch (error) {
      toolLogger.error(
        `Visit webpage exception: ${error instanceof Error ? error.message : String(error)}`,
      );
      return {
        message: `An error occurred while visiting the webpage: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  },
});

/**
 * Evaluate source credibility tool - assesses the credibility of a source
 */
export const evaluateSourceCredibility = createTool({
  id: "evaluateSourceCredibility",
  name: "Evaluate Source Credibility",
  description:
    "Evaluates the credibility of a source based on multiple factors",
  inputSchema: credibilityInputSchema,
  outputSchema: credibilityOutputSchema,
  execute: async ({
    context,
  }: {
    context: { url: string; content: string; metadata?: Record<string, any> };
  }) => {
    const startTime = Date.now();
    const { url, content, metadata = {} } = context;
    toolLogger.info(`Credibility evaluation tool called for URL: ${url}`);

    try {
      // Parse the URL
      const urlObj = new URL(url);
      const domain = urlObj.hostname.toLowerCase();

      // Initialize score and reasons
      let score = 0;
      const reasons: string[] = [];

      // Domain analysis (25 points)
      let domainScore = 0;

      // Check for academic/government domains
      const academicDomains = [
        ".edu",
        ".gov",
        ".org",
        "scholar.google",
        "arxiv.org",
        "researchgate.net",
      ];
      if (academicDomains.some((edu) => domain.includes(edu))) {
        domainScore += 25;
        reasons.push("Source is from a recognized academic/government domain");
      }
      // Check for reputable news sources
      else if (
        [
          "reuters.com",
          "apnews.com",
          "bloomberg.com",
          "bbc.com",
          "nytimes.com",
        ].some((news) => domain.includes(news))
      ) {
        domainScore += 20;
        reasons.push("Source is from a reputable news organization");
      }
      // Check for suspicious patterns
      else if (/free|fake|conspiracy|gossip/.test(domain)) {
        domainScore += 5;
        reasons.push("Domain contains potentially suspicious keywords");
      }
      // Standard domain
      else {
        domainScore += 15;
        reasons.push("Standard domain with no specific credibility markers");
      }

      score += domainScore;

      // Content analysis (25 points)
      let contentScore = 0;

      // Check for citations and references
      if (/\[\d+\]|et al\.|cited in|according to/.test(content)) {
        contentScore += 10;
        reasons.push("Content contains citations and references");
      }

      // Check for balanced language
      if (
        !/absolutely|definitely|always|never|proven|guaranteed/i.test(content)
      ) {
        contentScore += 5;
        reasons.push("Content uses balanced, non-absolute language");
      }

      // Check for data and statistics
      if (
        /\d+%|\d+\.\d+|statistical|study shows|research indicates/.test(content)
      ) {
        contentScore += 5;
        reasons.push("Content includes specific data and statistics");
      }

      // Check for expert quotes
      if (
        /Dr\.|Professor|researcher|expert|scientist|study author/.test(content)
      ) {
        contentScore += 5;
        reasons.push("Content includes expert opinions or quotes");
      }

      score += contentScore;

      // Citation analysis (25 points)
      let citationScore = 0;
      const citationMatches = content.match(/\[\d+\]|\(\d{4}\)/g);
      const citationCount = citationMatches ? citationMatches.length : 0;

      if (citationCount > 10) {
        citationScore += 25;
        reasons.push(`Extensive citations found (${citationCount} references)`);
      } else if (citationCount > 5) {
        citationScore += 15;
        reasons.push(`Multiple citations found (${citationCount} references)`);
      } else if (citationCount > 0) {
        citationScore += 10;
        reasons.push(`Some citations found (${citationCount} references)`);
      } else {
        reasons.push("No formal citations found");
      }

      score += citationScore;

      // Metadata analysis (25 points)
      let metadataScore = 0;

      if (metadata.author) {
        metadataScore += 5;
        reasons.push(`Author identified: ${metadata.author}`);
      }

      if (metadata.date) {
        metadataScore += 5;
        reasons.push(`Publication date available: ${metadata.date}`);
      }

      if (metadata.institution) {
        metadataScore += 10;
        reasons.push(`Institutional affiliation: ${metadata.institution}`);
      }

      if (metadata.peer_reviewed) {
        metadataScore += 5;
        reasons.push("Source is peer-reviewed");
      }

      score += metadataScore;

      // Get credibility level
      let level = "Very Low Credibility";
      if (score >= 90) {
        level = "Very High Credibility";
      } else if (score >= 70) {
        level = "High Credibility";
      } else if (score >= 50) {
        level = "Moderate Credibility";
      } else if (score >= 30) {
        level = "Low Credibility";
      }

      const executionTime = Date.now() - startTime;
      toolLogger.info(
        `Credibility evaluation completed in ${executionTime}ms, score: ${score}`,
      );

      return {
        score,
        level,
        reasons,
        url,
      };
    } catch (error) {
      toolLogger.error(
        `Credibility evaluation exception: ${error instanceof Error ? error.message : String(error)}`,
      );
      return {
        score: 0,
        level: "Error",
        reasons: [
          `Error evaluating source: ${error instanceof Error ? error.message : String(error)}`,
        ],
        url,
      };
    }
  },
});
