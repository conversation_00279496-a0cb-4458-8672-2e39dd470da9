import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";
import <PERSON><PERSON><PERSON><PERSON> from "superjson"; // Import SuperJSON for potential parsing
import { Checkpoint } from "@langchain/langgraph"; // Checkpoint from langgraph
import { HumanMessage, BaseMessage } from "@langchain/core/messages"; // Messages from core

// Common interface for sidebar items
interface SidebarItem {
  id: string; // threadId
  name: string; // Generated name
  type: "chat"; // Only chats now
  lastUpdated: string; // Changed from Date to string for serialization
  // isOnboarding: boolean; // TODO: Determine if onboarding chats need special handling
}

// Helper function to generate a chat name (placeholder)
function generateChatName(threadId: string): string {
  // Basic name for now
  return `Chat (${threadId.substring(0, 6)}...)`;
  // TODO: Potentially enhance name generation later if needed
}

export async function GET(req: NextRequest) {
  const user_id = req.nextUrl.searchParams.get("user_id");
  let limitParam = req.nextUrl.searchParams.get("limit");
  let skipParam = req.nextUrl.searchParams.get("skip");

  const limit = limitParam ? parseInt(limitParam) : undefined;
  const skip = skipParam ? parseInt(skipParam) : 0; // Default skip to 0

  if (!user_id) {
    return new NextResponse(JSON.stringify({ error: "user_id is required" }), {
      status: 400,
    });
  }

  try {
    const client = await clientPromise;

    // Fetch LangGraph Chats
    const langGraphDb = client.db("athlea");
    const checkpointsCollection = langGraphDb.collection(
      "langGraphCheckpoints",
    );

    const chatAggregationPipeline = [
      { $match: { userId: user_id } }, // Filter by user
      { $sort: { timestamp: -1 } }, // Sort by timestamp descending within each thread
      {
        $group: {
          _id: "$threadId", // Group by threadId
          lastUpdated: { $first: "$timestamp" }, // Get the latest timestamp
        },
      },
      {
        $project: {
          _id: 0, // Remove the default _id
          id: "$_id", // Rename _id to id (threadId)
          type: "chat" as const, // Use literal type
          lastUpdated: { $toDate: "$lastUpdated" }, // Convert ISO string to Date
        },
      },
      // Sort aggregated results by lastUpdated descending
      { $sort: { lastUpdated: -1 } },
    ];

    const langGraphChats = await checkpointsCollection
      .aggregate(chatAggregationPipeline)
      .toArray();

    // ADDED: Fetch Training Plans from AthleaUserData
    const trainingPlansDb = client.db("AthleaUserData");
    const trainingPlansCollection = trainingPlansDb.collection("trainingPlans");

    const trainingPlans = await trainingPlansCollection
      .find({ user_id: user_id })
      .sort({ lastUpdated: -1 })
      .toArray();

    // Map Lang Graph chats to sidebar items
    const mappedChats: SidebarItem[] = langGraphChats.map((chat) => ({
      id: chat.id,
      name: generateChatName(chat.id), // Generate name
      type: "chat",
      lastUpdated: chat.lastUpdated.toISOString(),
      // TODO: Add logic for isOnboarding if needed based on threadId or metadata
    }));

    // ADDED: Map training plans to sidebar items
    const mappedTrainingPlans: SidebarItem[] = trainingPlans.map((plan) => ({
      id: plan.jobId,
      name: plan.planTitle || `Chat (${plan.jobId.substring(0, 6)}...)`,
      type: "chat",
      lastUpdated: plan.lastUpdated || plan.dateAdded,
    }));

    // ADDED: Combine both collections
    const combinedItems = [...mappedChats, ...mappedTrainingPlans].sort(
      (a, b) =>
        new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime(),
    );

    // Apply pagination to the sorted results
    const paginatedItems = limit
      ? combinedItems.slice(skip, skip + limit)
      : combinedItems.slice(skip);

    return new NextResponse(JSON.stringify(paginatedItems), { status: 200 });
  } catch (error) {
    console.error("Error fetching sidebar chat items:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch sidebar chat items" }),
      { status: 500 },
    );
  }
}

// Keep POST as is for now, assuming it might be used for onboarding plan creation
export async function POST(req: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("trainingPlans");

    const newTrainingPlan = await req.json(); // This is likely an onboarding plan structure
    console.log(
      "Creating new onboarding/training plan with data:",
      newTrainingPlan,
    );

    // Ensure coaches is an array, even if a single coach is provided
    if (newTrainingPlan.coachName && !newTrainingPlan.coaches) {
      newTrainingPlan.coaches = [newTrainingPlan.coachName];
    } else if (!Array.isArray(newTrainingPlan.coaches)) {
      newTrainingPlan.coaches = newTrainingPlan.coaches
        ? [newTrainingPlan.coaches]
        : [];
    }

    // Set isOnboarding flag if it's an onboarding plan
    if (
      newTrainingPlan.serviceName === "onboarding" ||
      newTrainingPlan.isOnboarding === true
    ) {
      newTrainingPlan.isOnboarding = true;
    } else {
      // Ensure non-onboarding plans created via POST don't have isOnboarding: true
      newTrainingPlan.isOnboarding = false;
    }

    // Ensure user_id is set from the request body
    if (!newTrainingPlan.user_id) {
      return new NextResponse(
        JSON.stringify({ error: "user_id is required" }),
        { status: 400 },
      );
    }

    // Ensure jobId is present
    if (!newTrainingPlan.jobId || newTrainingPlan.jobId === "undefined") {
      // Generate one if missing or invalid, although frontend usually provides it
      const { v4: uuidv4 } = require("uuid");
      newTrainingPlan.jobId = uuidv4();
      console.warn(
        `Generated jobId server-side for onboarding plan: ${newTrainingPlan.jobId}`,
      );
    }

    // Ensure essential fields are present
    newTrainingPlan.dateAdded =
      newTrainingPlan.dateAdded || new Date().toISOString();
    newTrainingPlan.lastUpdated =
      newTrainingPlan.lastUpdated || new Date().toISOString();
    newTrainingPlan.planTitle = newTrainingPlan.planTitle || "Onboarding Plan"; // Default title

    const result = await collection.insertOne(newTrainingPlan);
    console.log("Onboarding/Training plan created with result:", result);

    // Find the created plan and explicitly check for jobId
    const createdPlan = await collection.findOne({ _id: result.insertedId });

    if (
      !createdPlan ||
      !createdPlan.jobId ||
      createdPlan.jobId === "undefined"
    ) {
      console.error("Created plan has missing or invalid jobId:", createdPlan);
      return new NextResponse(
        JSON.stringify({
          error: "Failed to create training plan - invalid jobId",
          trainingPlan: null,
        }),
        { status: 500 },
      );
    }

    console.log("Successfully created plan with jobId:", createdPlan.jobId);

    return new NextResponse(
      JSON.stringify({
        message: "Onboarding/Training plan created",
        trainingPlan: createdPlan, // Return the created document
        jobId: createdPlan.jobId, // Explicitly include the jobId for clarity
      }),
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating onboarding/training plan:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to create onboarding/training plan" }),
      { status: 500 },
    );
  }
}
