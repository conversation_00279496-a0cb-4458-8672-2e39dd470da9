/**
 * Python LangGraph Onboarding API Proxy Route
 *
 * This route proxies requests to the Python FastAPI backend that runs
 * the LangGraph onboarding system. It maintains the same interface as
 * the TypeScript onboarding route but forwards to the Python implementation.
 */

import { NextRequest } from "next/server";

// Python backend configuration
const PYTHON_BACKEND_URL =
  process.env.PYTHON_LANGGRAPH_URL || "http://localhost:8000";

export async function GET(req: NextRequest) {
  const startTime = Date.now();
  console.log(
    "🚀 ONBOARDING PROXY: [GET] Received request to Python onboarding proxy",
  );

  // Get parameters from URL
  const searchParams = req.nextUrl.searchParams;
  const messageContent = searchParams.get("message");
  const threadId = searchParams.get("threadId");
  const userId = searchParams.get("userId");
  const initialMessage = searchParams.get("initialMessage");
  const resumeFromInterrupt = searchParams.get("resume") === "true";

  console.log("📊 ONBOARDING PROXY: [GET] Request parameters:", {
    messageContent:
      messageContent?.substring(0, 100) +
      (messageContent && messageContent.length > 100 ? "..." : ""),
    threadId,
    userId,
    initialMessage,
    timestamp: new Date().toISOString(),
  });

  // Validate required parameters
  if (!threadId || !userId) {
    const errorMsg = `Missing required parameters: ${!threadId ? "threadId " : ""}${!userId ? "userId" : ""}`;
    console.error("❌ ONBOARDING PROXY: [GET] Validation failed:", errorMsg);
    return new Response(JSON.stringify({ error: errorMsg }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  // Use message or initialMessage, or default to start_onboarding
  const message = messageContent || initialMessage || "start_onboarding";

  console.log(
    `ONBOARDING API: Proxying to Python backend - Thread=${threadId}, UserId=${userId}, Message=${message}`,
  );

  try {
    // Use the new GET endpoint that loads full conversation history
    const pythonParams = new URLSearchParams({
      message: message,
      userId: userId,
      threadId: threadId,
    });

    if (resumeFromInterrupt) {
      pythonParams.set("resume", "true");
    }

    const pythonUrl = `${PYTHON_BACKEND_URL}/api/onboarding?${pythonParams.toString()}`;

    console.log(
      "🔗 ONBOARDING PROXY: [GET] Forwarding to Python backend GET:",
      {
        url: pythonUrl,
        method: "GET",
        backend_host: PYTHON_BACKEND_URL,
        params: Object.fromEntries(pythonParams),
      },
    );

    // Create SSE stream that proxies the Python backend response
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let eventCount = 0;
        let bytesStreamed = 0;

        console.log("🌊 ONBOARDING PROXY: [GET] Starting SSE stream proxy");

        try {
          const fetchStartTime = Date.now();
          const response = await fetch(pythonUrl, {
            method: "GET",
            headers: {
              Accept: "text/event-stream",
            },
          });

          const fetchDuration = Date.now() - fetchStartTime;

          console.log("📡 ONBOARDING PROXY: [GET] Python backend response:", {
            status: response.status,
            statusText: response.statusText,
            fetch_duration_ms: fetchDuration,
            has_body: !!response.body,
            content_type: response.headers.get("content-type"),
          });

          if (!response.ok) {
            throw new Error(
              `Python backend responded with status: ${response.status}`,
            );
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error("No response body from Python backend");
          }

          // Stream the response with proper SSE handling
          const decoder = new TextDecoder();

          console.log(
            "⚡ ONBOARDING PROXY: [GET] Starting stream processing...",
          );

          try {
            let lastHeartbeat = Date.now();
            const heartbeatTimeout = 35000; // 35 seconds

            while (true) {
              const readStartTime = Date.now();
              const { done, value } = await reader.read();
              const readDuration = Date.now() - readStartTime;

              if (done) {
                console.log(
                  "✅ ONBOARDING PROXY: [GET] Python backend stream completed:",
                  {
                    total_events: eventCount,
                    total_bytes: bytesStreamed,
                    total_duration_ms: Date.now() - startTime,
                  },
                );
                break;
              }

              eventCount++;
              bytesStreamed += value.length;

              // Immediately forward chunks to avoid buffering issues
              const chunk = decoder.decode(value, { stream: true });

              if (chunk) {
                // Check for heartbeat events
                if (chunk.includes("event: heartbeat")) {
                  lastHeartbeat = Date.now();
                  console.log(
                    "💓 ONBOARDING PROXY: [GET] Received heartbeat from backend",
                  );
                }

                // Check for error events that should stop retries
                if (chunk.includes("event: error")) {
                  console.log(
                    "❌ ONBOARDING PROXY: [GET] Error event detected in stream",
                  );
                  // Extract error message if possible
                  const errorMatch = chunk.match(/data: ({.*?})\n/);
                  if (errorMatch) {
                    try {
                      const errorData = JSON.parse(errorMatch[1]);
                      console.error(
                        "❌ ONBOARDING PROXY: [GET] Error details:",
                        errorData,
                      );
                    } catch (e) {
                      console.error(
                        "❌ ONBOARDING PROXY: [GET] Could not parse error data",
                      );
                    }
                  }
                }

                // Process and transform sidebar updates from Python backend
                let processedChunk = chunk;
                if (chunk.includes('"type": "sidebar_update"')) {
                  try {
                    const dataMatches = chunk.matchAll(/data: ({.*?})\n/g);
                    for (const match of dataMatches) {
                      const data = JSON.parse(match[1]);
                      if (data.type === "sidebar_update" && data.sidebarData) {
                        // Transform Python sidebar_data to TypeScript format
                        const transformedSidebarData = {
                          currentStage:
                            data.sidebarData.current_stage || "initial",
                          goals: {
                            exists: data.sidebarData.goals?.exists || false,
                            list: data.sidebarData.goals?.list || [],
                          },
                          summaryItems: data.sidebarData.summary_items || [],
                          generatedPlan:
                            data.sidebarData.generated_plan || null,
                          sportSuggestions:
                            data.sidebarData.sport_suggestions || null,
                          selectedSport:
                            data.sidebarData.selected_sport || null,
                          selectedSports:
                            data.sidebarData.selected_sports || [],
                          uploadedDocuments:
                            data.sidebarData.uploaded_documents || [],
                          keyInsights: data.sidebarData.key_insights || {},
                        };

                        console.log(
                          "🔄 ONBOARDING PROXY: [GET] Transformed sidebar data (GET method):",
                          {
                            original_goals: data.sidebarData.goals,
                            transformed_goals: transformedSidebarData.goals,
                            summary_items_count:
                              transformedSidebarData.summaryItems?.length || 0,
                            selected_sports:
                              transformedSidebarData.selectedSports,
                          },
                        );

                        // Replace the Python format with TypeScript format
                        const transformedData = {
                          ...data,
                          sidebarData: transformedSidebarData,
                        };
                        processedChunk = processedChunk.replace(
                          match[0],
                          `data: ${JSON.stringify(transformedData)}\n`,
                        );
                      }
                    }
                  } catch (e) {
                    console.error(
                      "❌ ONBOARDING PROXY: [GET] Error transforming sidebar data:",
                      e,
                    );
                  }
                }

                console.log("📨 ONBOARDING PROXY: [GET] Forwarding chunk:", {
                  event_number: eventCount,
                  chunk_size: processedChunk.length,
                  read_duration_ms: readDuration,
                  chunk_preview:
                    processedChunk.substring(0, 200) +
                    (processedChunk.length > 200 ? "..." : ""),
                  has_sidebar_update: processedChunk.includes(
                    '"type": "sidebar_update"',
                  ),
                });

                controller.enqueue(encoder.encode(processedChunk));
              }

              // Check for heartbeat timeout
              if (Date.now() - lastHeartbeat > heartbeatTimeout) {
                console.error(
                  "❌ ONBOARDING PROXY: [GET] Heartbeat timeout - backend may be unresponsive",
                );
                const timeoutEvent = `event: error\ndata: ${JSON.stringify({
                  type: "error",
                  message: "Backend timeout - no heartbeat received",
                })}\n\n`;
                controller.enqueue(encoder.encode(timeoutEvent));
                break;
              }

              // Log every 10th event to avoid spam
              if (eventCount % 10 === 0) {
                console.log("📈 ONBOARDING PROXY: [GET] Stream progress:", {
                  events_processed: eventCount,
                  bytes_streamed: bytesStreamed,
                  elapsed_ms: Date.now() - startTime,
                });
              }
            }
          } catch (streamError) {
            console.error(
              "❌ ONBOARDING PROXY: [GET] Error reading Python backend stream:",
              {
                error: streamError,
                events_before_error: eventCount,
                bytes_before_error: bytesStreamed,
              },
            );

            // Send error event with retry prevention flag
            const errorEvent = `event: error\ndata: ${JSON.stringify({
              type: "error",
              message: "Stream interrupted",
              preventRetry: true,
            })}\n\n`;
            controller.enqueue(encoder.encode(errorEvent));
          }
        } catch (error) {
          console.error(
            "❌ ONBOARDING PROXY: [GET] Error proxying to Python backend:",
            {
              error: error instanceof Error ? error.message : String(error),
              backend_url: PYTHON_BACKEND_URL,
              elapsed_ms: Date.now() - startTime,
            },
          );

          // Send error event to frontend
          const errorEvent = `event: error\ndata: ${JSON.stringify({
            type: "error",
            message: "Failed to connect to Python onboarding backend",
            error: error instanceof Error ? error.message : String(error),
          })}\n\n`;

          controller.enqueue(encoder.encode(errorEvent));
        } finally {
          console.log(
            "🏁 ONBOARDING PROXY: [GET] Stream closed, final stats:",
            {
              total_events: eventCount,
              total_bytes: bytesStreamed,
              total_duration_ms: Date.now() - startTime,
            },
          );
          controller.close();
        }
      },

      cancel() {
        console.log(
          "🚫 ONBOARDING PROXY: [GET] Client cancelled Python backend stream",
        );
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("ONBOARDING API: Error in Python onboarding proxy:", error);

    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  console.log(
    "🚀 ONBOARDING PROXY: [POST] Received request to Python onboarding proxy",
  );

  try {
    const body = await req.json();
    const { message, threadId, userId, userProfile, resumeFromInterrupt } =
      body;

    console.log("📊 ONBOARDING PROXY: [POST] Request body parsed:", {
      message:
        message?.substring(0, 100) +
        (message && message.length > 100 ? "..." : ""),
      threadId,
      userId,
      has_userProfile: !!userProfile,
      timestamp: new Date().toISOString(),
    });

    // Validate required parameters
    if (!threadId || !userId || !message) {
      console.error(
        "❌ ONBOARDING PROXY: [POST] Validation failed: Missing required parameters",
      );
      return new Response(
        JSON.stringify({
          error: "Missing required parameters: message, threadId, userId",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // Prepare request body for Python backend
    const pythonRequestBody = {
      message,
      user_id: userId,
      thread_id: threadId,
      user_profile: userProfile,
      conversation_history: [],
      resume_from_interrupt: resumeFromInterrupt || false,
    };

    const pythonUrl = `${PYTHON_BACKEND_URL}/api/onboarding`;

    console.log("🔗 ONBOARDING PROXY: [POST] Forwarding to Python backend:", {
      url: pythonUrl,
      method: "POST",
      backend_host: PYTHON_BACKEND_URL,
      body_size: JSON.stringify(pythonRequestBody).length,
    });

    // Create SSE stream that proxies the Python backend response
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder();
        let eventCount = 0;
        let bytesStreamed = 0;

        console.log("🌊 ONBOARDING PROXY: [POST] Starting SSE stream proxy");

        try {
          const fetchStartTime = Date.now();
          const response = await fetch(pythonUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "text/event-stream",
            },
            body: JSON.stringify(pythonRequestBody),
          });

          const fetchDuration = Date.now() - fetchStartTime;

          console.log("📡 ONBOARDING PROXY: [POST] Python backend response:", {
            status: response.status,
            statusText: response.statusText,
            fetch_duration_ms: fetchDuration,
            has_body: !!response.body,
            content_type: response.headers.get("content-type"),
          });

          if (!response.ok) {
            throw new Error(
              `Python backend responded with status: ${response.status}`,
            );
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error("No response body from Python backend");
          }

          // Stream the response
          const decoder = new TextDecoder();

          console.log(
            "⚡ ONBOARDING PROXY: [POST] Starting stream processing...",
          );

          try {
            let lastHeartbeat = Date.now();
            const heartbeatTimeout = 35000; // 35 seconds

            while (true) {
              const readStartTime = Date.now();
              const { done, value } = await reader.read();
              const readDuration = Date.now() - readStartTime;

              if (done) {
                console.log(
                  "✅ ONBOARDING PROXY: [POST] Python backend stream completed:",
                  {
                    total_events: eventCount,
                    total_bytes: bytesStreamed,
                    total_duration_ms: Date.now() - startTime,
                  },
                );
                break;
              }

              eventCount++;
              bytesStreamed += value.length;

              // Immediately forward chunks to avoid buffering issues
              const chunk = decoder.decode(value, { stream: true });

              if (chunk) {
                // Check for heartbeat events
                if (chunk.includes("event: heartbeat")) {
                  lastHeartbeat = Date.now();
                  console.log(
                    "💓 ONBOARDING PROXY: [POST] Received heartbeat from backend",
                  );
                }

                // Check for error events that should stop retries
                if (chunk.includes("event: error")) {
                  console.log(
                    "❌ ONBOARDING PROXY: [POST] Error event detected in stream",
                  );
                  // Extract error message if possible
                  const errorMatch = chunk.match(/data: ({.*?})\n/);
                  if (errorMatch) {
                    try {
                      const errorData = JSON.parse(errorMatch[1]);
                      console.error(
                        "❌ ONBOARDING PROXY: [POST] Error details:",
                        errorData,
                      );
                    } catch (e) {
                      console.error(
                        "❌ ONBOARDING PROXY: [POST] Could not parse error data",
                      );
                    }
                  }
                }

                // Process and transform sidebar updates from Python backend
                let processedChunk = chunk;
                if (chunk.includes('"type": "sidebar_update"')) {
                  try {
                    const dataMatches = chunk.matchAll(/data: ({.*?})\n/g);
                    for (const match of dataMatches) {
                      const data = JSON.parse(match[1]);
                      if (data.type === "sidebar_update" && data.sidebarData) {
                        // Transform Python sidebar_data to TypeScript format
                        const transformedSidebarData = {
                          currentStage:
                            data.sidebarData.current_stage || "initial",
                          goals: {
                            exists: data.sidebarData.goals?.exists || false,
                            list: data.sidebarData.goals?.list || [],
                          },
                          summaryItems: data.sidebarData.summary_items || [],
                          generatedPlan:
                            data.sidebarData.generated_plan || null,
                          sportSuggestions:
                            data.sidebarData.sport_suggestions || null,
                          selectedSport:
                            data.sidebarData.selected_sport || null,
                          selectedSports:
                            data.sidebarData.selected_sports || [],
                          uploadedDocuments:
                            data.sidebarData.uploaded_documents || [],
                          keyInsights: data.sidebarData.key_insights || {},
                        };

                        console.log(
                          "🔄 ONBOARDING PROXY: [POST] Transformed sidebar data (POST method):",
                          {
                            original_goals: data.sidebarData.goals,
                            transformed_goals: transformedSidebarData.goals,
                            summary_items_count:
                              transformedSidebarData.summaryItems?.length || 0,
                            selected_sports:
                              transformedSidebarData.selectedSports,
                          },
                        );

                        // Replace the Python format with TypeScript format
                        const transformedData = {
                          ...data,
                          sidebarData: transformedSidebarData,
                        };
                        processedChunk = processedChunk.replace(
                          match[0],
                          `data: ${JSON.stringify(transformedData)}\n`,
                        );
                      }
                    }
                  } catch (e) {
                    console.error(
                      "❌ ONBOARDING PROXY: [POST] Error transforming sidebar data:",
                      e,
                    );
                  }
                }

                console.log("📨 ONBOARDING PROXY: [POST] Forwarding chunk:", {
                  event_number: eventCount,
                  chunk_size: processedChunk.length,
                  read_duration_ms: readDuration,
                  chunk_preview:
                    processedChunk.substring(0, 200) +
                    (processedChunk.length > 200 ? "..." : ""),
                  has_sidebar_update: processedChunk.includes(
                    '"type": "sidebar_update"',
                  ),
                });

                controller.enqueue(encoder.encode(processedChunk));
              }

              // Check for heartbeat timeout
              if (Date.now() - lastHeartbeat > heartbeatTimeout) {
                console.error(
                  "❌ ONBOARDING PROXY: [POST] Heartbeat timeout - backend may be unresponsive",
                );
                const timeoutEvent = `event: error\ndata: ${JSON.stringify({
                  type: "error",
                  message: "Backend timeout - no heartbeat received",
                })}\n\n`;
                controller.enqueue(encoder.encode(timeoutEvent));
                break;
              }

              // Log every 10th event to avoid spam
              if (eventCount % 10 === 0) {
                console.log("📈 ONBOARDING PROXY: [POST] Stream progress:", {
                  events_processed: eventCount,
                  bytes_streamed: bytesStreamed,
                  elapsed_ms: Date.now() - startTime,
                });
              }
            }
          } catch (streamError) {
            console.error(
              "❌ ONBOARDING PROXY: [POST] Error reading Python backend stream:",
              {
                error: streamError,
                events_before_error: eventCount,
                bytes_before_error: bytesStreamed,
              },
            );

            // Send error event with retry prevention flag
            const errorEvent = `event: error\ndata: ${JSON.stringify({
              type: "error",
              message: "Stream interrupted",
              preventRetry: true,
            })}\n\n`;
            controller.enqueue(encoder.encode(errorEvent));
          }
        } catch (error) {
          console.error(
            "❌ ONBOARDING PROXY: [POST] Error proxying to Python backend:",
            {
              error: error instanceof Error ? error.message : String(error),
              backend_url: PYTHON_BACKEND_URL,
              elapsed_ms: Date.now() - startTime,
            },
          );

          const errorEvent = `event: error\ndata: ${JSON.stringify({
            type: "error",
            message: "Failed to connect to Python onboarding backend",
            error: error instanceof Error ? error.message : String(error),
          })}\n\n`;

          controller.enqueue(encoder.encode(errorEvent));
        } finally {
          console.log(
            "🏁 ONBOARDING PROXY: [POST] Stream closed, final stats:",
            {
              total_events: eventCount,
              total_bytes: bytesStreamed,
              total_duration_ms: Date.now() - startTime,
            },
          );
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache, no-transform",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error(
      "ONBOARDING API POST: Error in Python onboarding proxy:",
      error,
    );

    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
