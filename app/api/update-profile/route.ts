import { NextRequest, NextResponse } from "next/server";
import { MongoClient } from "mongodb";
import { AzureOpenAI } from "openai";
import {
  extractTrainingProfileUpdate,
  mergeTrainingProfileUpdate,
  removeFromTrainingProfile,
} from "@/app/lib/db/trainingProfileUpdate";
import clientPromise from "@/app/lib/mongodb";
import { createNotification } from "@/utils/createNotification";
import { Notification } from "../calendar/route";

// Azure OpenAI Configuration
const AZURE_OPENAI_KEY = process.env.AZURE_OPENAI_KEY;
const AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
const AZURE_DEPLOYMENT_NAME = process.env.AZURE_DEPLOYMENT_NAME;

// Initialize Azure OpenAI client
const azureClient = new AzureOpenAI({
  apiKey: AZURE_OPENAI_KEY,
  endpoint: AZURE_OPENAI_ENDPOINT,
  apiVersion: "2024-08-01-preview",
  deployment: AZURE_DEPLOYMENT_NAME,
});

const DETERMINE_UPDATE_SYSTEM_MESSAGE = `You are a personal trainer and fitness expert. Your primary responsibility is to determine if the user's training profile needs to be updated based on new information provided.

IMPORTANT: Use the determine_profile_update function to indicate whether an update is needed. 

CRITICAL: ONLY update the profile when the user EXPLICITLY shares NEW FACTUAL INFORMATION about themselves. The profile should NEVER be updated with suggestions, recommendations, or training advice - it should ONLY contain facts about the user.

NEVER update the profile when:
- The user is asking a question (e.g., "What do you know about me?", "Can you help me?")
- The user is requesting information or advice
- The user is requesting a service or session
- The message is ambiguous or unclear
- The message doesn't contain explicit new facts about the user

The profile should only be updated when the user explicitly shares information like:
- "I am 5'10" and weigh 180 pounds"
- "I have a lower back injury"
- "I go to the gym 3 times per week"
- "I prefer cycling over running"

Always err strongly on the side of caution. If there's any doubt whether the message contains new factual information about the user, do NOT update the profile.

For service requests (e.g., "generate a session", "create a workout"), set isServiceRequest to true and shouldUpdate to false.

For questions or information requests (e.g., "What do you know about me?", "How should I train?"), set isServiceRequest to false and shouldUpdate to false.`;

const UPDATE_PROFILE_SYSTEM_MESSAGE = `You are a personal trainer and fitness expert. Your task is to extract ONLY factual information about the user from their message and update their training profile accordingly.

IMPORTANT: Use the update_training_profile function to provide concise updates to the user's profile. Always include an updateInfo object with relevant key-value pairs for each domain that needs updating.

CRITICAL: ONLY include FACTUAL INFORMATION about the user, NEVER include suggestions, recommendations, or advice. For example:
- CORRECT: "weight: 180 pounds" (factual information about the user)
- INCORRECT: "should increase hydration" (this is advice, not a fact about the user)
- CORRECT: "has lower back pain" (factual information about the user)
- INCORRECT: "needs to work on mobility" (this is a recommendation, not a fact)

VALUES MUST BE FACTUAL ATTRIBUTES OF THE USER, not prescriptive advice. Keep values extremely short and to the point.

You can update multiple domains at once, but only update the domains that are relevant to the new information provided. The ONLY valid domains are:
- general: for overall fitness, goals, preferences, or lifestyle information
- cycling: for cycling-specific information
- running: for running-specific information
- strength: for strength training-specific information
- nutrition: for diet and nutrition-specific information
- recovery: for recovery and rest-specific information

Do NOT use any domains other than these six. Choose the most specific domain(s) that apply to the information being updated. Only use 'general' if the information doesn't fit into any of the other specific domains.`;

const VALID_DOMAINS = [
  "general",
  "cycling",
  "running",
  "strength",
  "nutrition",
  "recovery",
];

const mongoClient = new MongoClient(process.env.MONGODB_URI as string);

function serializeUpdateInfo(
  obj: Record<string, any>,
  parentKey: string = "",
): string {
  let serialized: string[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const fullKey = parentKey ? `${parentKey}.${key}` : key;

    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
      serialized.push(serializeUpdateInfo(value, fullKey));
    } else {
      serialized.push(`${fullKey}: ${value}`);
    }
  }

  return serialized.join(", ");
}

export async function POST(req: NextRequest) {
  console.log("Entering POST function");
  try {
    const { message, userId, action, domain, key } = await req.json();
    console.log("Received request data:", {
      message,
      userId,
      action,
      domain,
      key,
    });

    // Special check for simple greetings - skip update process entirely
    if (message) {
      const simplifiedMessage = message.toLowerCase().trim();
      const simpleGreetings = [
        "hi",
        "hello",
        "hey",
        "what's up",
        "good morning",
        "good afternoon",
        "good evening",
        "yo",
      ];

      // Check for profile inquiry questions that should never trigger an update
      const profileInquiries = [
        "what do you know about me",
        "what do you know",
        "tell me about my profile",
        "tell me what you know about me",
        "show me my profile",
        "what information do you have about me",
        "what have i told you about myself",
        "can you show me my profile",
        "what's in my profile",
        "what is in my profile",
      ];

      if (
        simpleGreetings.some(
          (greeting) =>
            simplifiedMessage === greeting ||
            simplifiedMessage.startsWith(`${greeting} `),
        )
      ) {
        console.log(
          "Message is a simple greeting, skipping profile update check",
        );
        return NextResponse.json({
          updated: false,
          message: "No update needed - simple greeting detected",
        });
      }

      // Skip profile update if message is asking about profile information
      if (
        profileInquiries.some(
          (inquiry) =>
            simplifiedMessage === inquiry ||
            simplifiedMessage.includes(inquiry),
        )
      ) {
        console.log(
          "Message is asking about profile information, skipping profile update check",
        );
        return NextResponse.json({
          updated: false,
          message: "No update needed - profile inquiry detected",
        });
      }

      // Also skip if message is extremely short (likely not containing profile information)
      if (simplifiedMessage.length < 5) {
        console.log("Message is too short to contain profile updates");
        return NextResponse.json({
          updated: false,
          message: "No update needed - message too short",
        });
      }
    }

    // Fetch current profile from MongoDB
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");
    const user = await collection.findOne({ user_id: userId });
    console.log("Full user document:", JSON.stringify(user));

    if (!user) {
      console.log("User not found");
      return NextResponse.json({
        updated: false,
        error: "User not found",
      });
    }

    if (action === "remove") {
      if (!domain || !key) {
        return NextResponse.json({
          updated: false,
          error: "Domain and key are required for removal",
        });
      }

      const updatedProfile = removeFromTrainingProfile(user, domain, key);
      const result = await collection.updateOne(
        { user_id: userId },
        { $set: { training_profile: updatedProfile.training_profile } },
      );

      if (result.modifiedCount > 0) {
        console.log("Field removed successfully");
        return NextResponse.json({
          updated: true,
          message: `Removed ${key} from ${domain}`,
          updatedProfile: updatedProfile.training_profile,
        });
      } else {
        console.log("No changes made to the profile");
        return NextResponse.json({
          updated: false,
          message: "No changes made to the profile",
        });
      }
    }

    // Determine if update is needed using Azure OpenAI
    console.log(
      "Sending request to Azure OpenAI to determine if update is needed",
    );
    const determineUpdateCompletion = await azureClient.chat.completions.create(
      {
        model: AZURE_DEPLOYMENT_NAME as string,
        messages: [
          { role: "system", content: DETERMINE_UPDATE_SYSTEM_MESSAGE },
          { role: "user", content: message },
          {
            role: "system",
            content:
              "Current user profile: " + JSON.stringify(user.training_profile),
          },
        ],
        tools: [
          {
            type: "function",
            function: {
              name: "determine_profile_update",
              description:
                "Determine if the user's training profile needs to be updated based on new information.",
              parameters: {
                type: "object",
                properties: {
                  shouldUpdate: {
                    type: "boolean",
                    description:
                      "Whether the profile should be updated based on the message",
                  },
                  explanation: {
                    type: "string",
                    description:
                      "Explanation of why an update is needed or not",
                  },
                  isServiceRequest: {
                    type: "boolean",
                    description:
                      "Whether the message is a service or task request rather than profile information",
                  },
                },
                required: ["shouldUpdate", "explanation", "isServiceRequest"],
              },
            },
          },
        ],
        tool_choice: {
          type: "function",
          function: { name: "determine_profile_update" },
        },
      },
    );

    const determineUpdateToolCall =
      determineUpdateCompletion.choices[0].message.tool_calls?.[0];
    console.log(
      "Determine update tool call:",
      JSON.stringify(determineUpdateToolCall, null, 2),
    );

    if (
      determineUpdateToolCall &&
      determineUpdateToolCall.function.name === "determine_profile_update"
    ) {
      const { shouldUpdate, explanation, isServiceRequest } = JSON.parse(
        determineUpdateToolCall.function.arguments || "{}",
      );
      console.log("Should update:", shouldUpdate);
      console.log("Explanation:", explanation);
      console.log("Is service request:", isServiceRequest);

      if (isServiceRequest) {
        console.log("Message is a service request, not updating profile");
        return NextResponse.json({
          updated: false,
          message: "No update needed - service request",
        });
      }

      if (shouldUpdate) {
        // Perform the update using Azure OpenAI
        console.log(
          "Update needed. Sending request to Azure OpenAI to get update details",
        );
        const updateCompletion = await azureClient.chat.completions.create({
          model: AZURE_DEPLOYMENT_NAME as string,
          messages: [
            { role: "system", content: UPDATE_PROFILE_SYSTEM_MESSAGE },
            { role: "user", content: message },
            {
              role: "system",
              content:
                "Current user profile: " +
                JSON.stringify(user.training_profile),
            },
            {
              role: "assistant",
              content: `An update is needed: ${explanation}. Please provide specific updates to the user's profile, only including information that is not already present in the current profile.`,
            },
          ],
          tools: [
            {
              type: "function",
              function: {
                name: "update_training_profile",
                description:
                  "Update the user's training profile with new information across multiple domains.",
                parameters: {
                  type: "object",
                  properties: {
                    updateInfo: {
                      type: "object",
                      description:
                        "The information to update in the profile, organized by domain.",
                      properties: {
                        general: {
                          type: "object",
                          description: "Updates for the general domain",
                          additionalProperties: true,
                        },
                        cycling: {
                          type: "object",
                          description: "Updates for the cycling domain",
                          additionalProperties: true,
                        },
                        running: {
                          type: "object",
                          description: "Updates for the running domain",
                          additionalProperties: true,
                        },
                        strength: {
                          type: "object",
                          description: "Updates for the strength domain",
                          additionalProperties: true,
                        },
                        nutrition: {
                          type: "object",
                          description: "Updates for the nutrition domain",
                          additionalProperties: true,
                        },
                        recovery: {
                          type: "object",
                          description: "Updates for the recovery domain",
                          additionalProperties: true,
                        },
                      },
                      additionalProperties: false,
                    },
                  },
                  required: ["updateInfo"],
                },
              },
            },
          ],
          tool_choice: {
            type: "function",
            function: { name: "update_training_profile" },
          },
        });

        const updateToolCall =
          updateCompletion.choices[0].message.tool_calls?.[0];
        console.log(
          "Update tool call:",
          JSON.stringify(updateToolCall, null, 2),
        );

        if (
          updateToolCall &&
          updateToolCall.function.name === "update_training_profile"
        ) {
          const { updateInfo: rawUpdateInfo } = JSON.parse(
            updateToolCall.function.arguments || "{}",
          );
          console.log(
            "Raw Update info from LLM:",
            JSON.stringify(rawUpdateInfo, null, 2),
          );

          if (
            !rawUpdateInfo ||
            typeof rawUpdateInfo !== "object" ||
            Object.keys(rawUpdateInfo).length === 0
          ) {
            console.log(
              "No update information object provided by LLM or it's empty",
            );
            return NextResponse.json({
              updated: false,
              message: "No update details provided by the AI.",
            });
          }

          const meaningfulUpdateInfo: Record<string, Record<string, any>> = {};
          let hasMeaningfulUpdates = false;

          for (const domain of VALID_DOMAINS) {
            if (
              rawUpdateInfo[domain] &&
              typeof rawUpdateInfo[domain] === "object" &&
              Object.keys(rawUpdateInfo[domain]).length > 0
            ) {
              const domainUpdates = rawUpdateInfo[domain];
              const filteredDomainUpdates: Record<string, any> = {};
              let domainHasMeaningfulData = false;

              for (const [key, value] of Object.entries(domainUpdates)) {
                if (typeof value === "string") {
                  const lowerValue = value.toLowerCase().trim();
                  if (
                    !lowerValue.startsWith("no new") &&
                    !lowerValue.startsWith("no update") &&
                    !lowerValue.startsWith("no change") && // Covers "no changes"
                    lowerValue !== "nothing to update" &&
                    lowerValue !== "no information provided" &&
                    lowerValue !== "none" && // Common negative
                    !lowerValue.includes("not specified") &&
                    !lowerValue.includes("not mentioned")
                  ) {
                    filteredDomainUpdates[key] = value;
                    domainHasMeaningfulData = true;
                  }
                } else {
                  // If value is not a string (e.g., number, boolean, nested object), assume it's meaningful
                  filteredDomainUpdates[key] = value;
                  domainHasMeaningfulData = true;
                }
              }

              if (
                domainHasMeaningfulData &&
                Object.keys(filteredDomainUpdates).length > 0
              ) {
                meaningfulUpdateInfo[domain] = filteredDomainUpdates;
                hasMeaningfulUpdates = true;
              }
            }
          }

          console.log(
            "Meaningful Update Info after filtering:",
            JSON.stringify(meaningfulUpdateInfo, null, 2),
          );

          if (!hasMeaningfulUpdates) {
            console.log(
              "No meaningful factual information found to update the profile after filtering.",
            );
            return NextResponse.json({
              updated: false,
              message:
                "No new factual information found to update the profile.",
            });
          }

          console.log("Updating profile with meaningful data");
          const updatedProfile = mergeTrainingProfileUpdate(
            { updateInfo: meaningfulUpdateInfo }, // Use meaningfulUpdateInfo
            user,
          );
          console.log(
            "Updated profile after merge:",
            JSON.stringify(updatedProfile),
          );

          const result = await collection.updateOne(
            { user_id: userId },
            { $set: { training_profile: updatedProfile.training_profile } },
          );

          if (result.modifiedCount > 0) {
            // MODIFIED: Was if(true)
            console.log(
              "Sending successful update response as profile was modified",
            );
            const formattedUpdateInfo =
              serializeUpdateInfo(meaningfulUpdateInfo); // Use meaningfulUpdateInfo
            const systemMessage = `Training profile updated: ${formattedUpdateInfo}`;
            console.log(`System message: ${systemMessage}`);

            return NextResponse.json({
              updated: true,
              updatedProfile: updatedProfile.training_profile,
              cleanedProfile: meaningfulUpdateInfo, // Use meaningfulUpdateInfo
              systemMessage: systemMessage,
            });
          } else {
            console.log(
              "No changes made to the profile in the database, or data was identical.",
            );
            return NextResponse.json({
              updated: false,
              message:
                "Profile data was reviewed, but no net changes were made.",
            });
          }
        } else {
          console.log("Update tool call name mismatch or missing tool call.");
          return NextResponse.json({
            updated: false,
            error: "Failed to get update details from AI.",
          });
        }
      } else {
        console.log(
          "No update needed based on initial AI determination (shouldUpdate is false).",
        );
        return NextResponse.json({
          updated: false,
          message: "No update needed.", // Original message when shouldUpdate is false
        });
      }
    }

    console.log("No valid determine update function call received");
    return NextResponse.json({
      updated: false,
      error: "Failed to determine if update is needed",
    });
  } catch (error) {
    console.error("Error in Azure OpenAI route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
