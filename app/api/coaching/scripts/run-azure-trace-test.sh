#!/bin/bash

# Run Azure AI Tracing Test Script
# This script runs the test-azure-tracing.mjs script with the correct environment

# Ensure we're in the project root directory
cd "$(dirname "$0")/../../../.."

# Load the environment variables
if [ -f .env.local ]; then
  echo "Loading environment from .env.local"
  export $(grep -v '^#' .env.local | xargs)
else
  echo "Warning: .env.local file not found"
fi

# Verify Azure Monitor connection string is set
if [ -z "$AZURE_MONITOR_CONNECTION_STRING" ]; then
  echo "Error: AZURE_MONITOR_CONNECTION_STRING is not set in .env.local"
  exit 1
fi

# Verify Azure OpenAI credentials are set
if [ -z "$AZURE_OPENAI_API_KEY" ] || [ -z "$AZURE_OPENAI_ENDPOINT" ] || [ -z "$AZURE_DEPLOYMENT_NAME" ]; then
  echo "Error: One or more Azure OpenAI environment variables are missing."
  echo "Please ensure the following are set in .env.local:"
  echo "- AZURE_OPENAI_API_KEY"
  echo "- AZURE_OPENAI_ENDPOINT"
  echo "- AZURE_DEPLOYMENT_NAME"
  exit 1
fi

echo "----------------------------------------"
echo "🚀 Running Azure AI Tracing Test Script"
echo "----------------------------------------"

# Run the test script with Node.js
node --experimental-modules app/api/coaching/scripts/test-azure-tracing.mjs

echo "----------------------------------------"
echo "✅ Test execution complete"
echo "----------------------------------------" 