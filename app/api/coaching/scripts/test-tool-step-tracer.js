#!/usr/bin/env node

/**
 * Test script for the tool step visualization parsing
 *
 * This script creates mock data for the Azure Search Graph tool
 * and tests parsing of tool output into visualization steps.
 *
 * Run with: node test-tool-step-tracer.js
 * Or via the shell script: ./run-tool-step-test.sh
 */

// Since we can't directly import TypeScript modules in a Node.js script,
// we'll recreate the core parsing logic here for testing purposes.

/**
 * Simplified version of the parseToolOutputToSteps function for testing
 */
function parseAzureSearchGraphOutput(data) {
  if (!data || typeof data !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] Input data is not an object or is null",
    );
    return null;
  }
  if (!data.meta || typeof data.meta !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.meta is missing or not an object",
    );
    return null;
  }
  if (!data.results || !Array.isArray(data.results)) {
    console.warn(
      "[parseAzureSearchGraphOutput] data.results is missing or not an array",
    );
    return null;
  }
  if (typeof data.query !== "string") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.query is missing or not a string",
    );
    return null;
  }

  // Add checks for critical meta fields
  if (
    typeof data.meta.originalResultCount !== "number" ||
    typeof data.meta.filteredResultCount !== "number" ||
    typeof data.meta.relevanceFiltered !== "number" ||
    typeof data.meta.credibilityEvaluated !== "boolean"
  ) {
    console.warn(
      "[parseAzureSearchGraphOutput] One or more critical meta fields are missing or have incorrect types",
    );
    return null;
  }

  const { query, results, meta } = data;
  const steps = [];
  let currentStep = 1;

  const numOriginalResults = meta.originalResultCount;
  const numRelevanceRemoved = meta.relevanceFiltered;
  const calculatedResultsAfterRelevance =
    numOriginalResults - numRelevanceRemoved;
  const numFinalFilteredCount = meta.filteredResultCount;

  steps.push({
    number: currentStep++,
    title: "Initial Search",
    description: `Searched for: "${query}". Found ${numOriginalResults} docs.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: numOriginalResults,
        highlightedLabel: "found",
        regularColor: "bg-transparent",
      },
    },
  });

  steps.push({
    number: currentStep++,
    title: "Relevance Filter",
    description: `${calculatedResultsAfterRelevance} docs after relevance filter.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: calculatedResultsAfterRelevance,
        highlightedLabel: "relevant",
        nonHighlightedLabel: "filtered",
      },
    },
  });

  if (meta.credibilityEvaluated) {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Sources evaluated.",
      content: {
        type: "text",
        value: "Source credibility assessed.",
      },
    });
  } else {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Skipped.",
      content: {
        type: "text",
        value: "Credibility check skipped.",
      },
    });
  }

  steps.push({
    number: currentStep++,
    title: "Final Selection",
    description: `Presenting top ${numFinalFilteredCount} documents.`,
    content: {
      type: "items",
      props: {
        items: results.map((r) => ({
          title: r.title,
          content: r.content,
          source: r.source || r.url,
        })),
        maxItems: 2,
        moreLabel: `more result${numFinalFilteredCount > 2 ? "s" : ""}`,
      },
    },
  });

  return {
    toolName: "Azure Search",
    steps,
    summary: `${numFinalFilteredCount} of ${numOriginalResults} results shown`,
    triggerLabel: "Search details",
  };
}

function parseToolOutputToSteps(toolCall) {
  if (!toolCall || !toolCall.output) return null;

  switch (toolCall.name) {
    case "azure_search_graph":
      let parsedGraphOutput = toolCall.output;
      if (typeof toolCall.output === "string") {
        try {
          console.log(
            "[parseToolOutputToSteps] Attempting JSON.parse for string output",
          );
          parsedGraphOutput = JSON.parse(toolCall.output);
        } catch (error) {
          console.error(
            "[parseToolOutputToSteps] JSON.parse failed:",
            error.message,
          );
          return null;
        }
      }
      return parseAzureSearchGraphOutput(parsedGraphOutput);

    case "data_analysis_tool":
      console.log("Parsing data_analysis_tool output - to be implemented");
      return null;
    default:
      console.warn(
        `[parseToolOutputToSteps] No parser implemented for tool: ${toolCall.name}`,
      );
      return null;
  }
}

// Helper function to validate the structure of parsed tool steps
function validateParsedOutput(parsedOutput) {
  if (!parsedOutput) {
    return { valid: false, reason: "Output is null" };
  }

  // Validate required fields
  if (!parsedOutput.toolName) {
    return { valid: false, reason: "Missing toolName" };
  }

  if (
    !parsedOutput.steps ||
    !Array.isArray(parsedOutput.steps) ||
    parsedOutput.steps.length === 0
  ) {
    return { valid: false, reason: "Missing or empty steps array" };
  }

  // Validate steps structure
  for (let i = 0; i < parsedOutput.steps.length; i++) {
    const step = parsedOutput.steps[i];

    if (typeof step.number !== "number") {
      return { valid: false, reason: `Step ${i} missing number property` };
    }

    if (!step.title) {
      return { valid: false, reason: `Step ${i} missing title` };
    }

    if (!step.content) {
      return { valid: false, reason: `Step ${i} missing content` };
    }

    if (!step.content.type) {
      return { valid: false, reason: `Step ${i} content missing type` };
    }

    // Validate specific content types
    if (
      step.content.type === "dots" &&
      (!step.content.props || !step.content.props.total)
    ) {
      return {
        valid: false,
        reason: `Step ${i} dots content missing required props`,
      };
    }

    if (
      step.content.type === "items" &&
      (!step.content.props || !step.content.props.items)
    ) {
      return {
        valid: false,
        reason: `Step ${i} items content missing required props`,
      };
    }

    if (step.content.type === "text" && !step.content.value) {
      return { valid: false, reason: `Step ${i} text content missing value` };
    }
  }

  return { valid: true };
}

// Create a mock of the Azure Search Graph tool output
const mockAzureSearchGraphOutput = {
  name: "azure_search_graph",
  output: {
    query: "benefits of high intensity interval training",
    results: [
      {
        title: "HIIT Benefits for Endurance Athletes",
        content:
          "High-intensity interval training (HIIT) has been shown to improve VO2 max and endurance performance with less time commitment than traditional endurance training.",
        source: "Journal of Sports Science",
        qualityScore: 0.85,
        url: "https://example.com/hiit-benefits",
        relevanceScore: 0.92,
        credibilityScore: 0.88,
        relevanceReasoning:
          "This article directly addresses the benefits of HIIT for athletes.",
        credibilityReasoning:
          "Published in a peer-reviewed journal with references to scientific studies.",
      },
      {
        title: "HIIT for Weight Management",
        content:
          "HIIT workouts can burn more calories in less time compared to steady-state cardio, and may continue to burn calories hours after the workout through EPOC (excess post-exercise oxygen consumption).",
        source: "American College of Sports Medicine",
        qualityScore: 0.78,
        url: "https://example.com/hiit-weight-management",
        relevanceScore: 0.85,
        credibilityScore: 0.95,
        relevanceReasoning:
          "Directly discusses a key benefit of HIIT (weight management).",
        credibilityReasoning:
          "Content from a highly respected sports medicine organization.",
      },
      {
        title: "HIIT vs Traditional Cardio",
        content:
          "Research shows that HIIT produces similar or greater improvements in cardiovascular fitness while requiring less time than traditional moderate-intensity continuous training.",
        source: "Sports Medicine Review",
        qualityScore: 0.82,
        url: "https://example.com/hiit-vs-cardio",
        relevanceScore: 0.88,
        credibilityScore: 0.86,
        relevanceReasoning:
          "Compares HIIT benefits to traditional training methods.",
        credibilityReasoning:
          "Published in a reputable sports medicine publication.",
      },
    ],
    meta: {
      originalResultCount: 12,
      filteredResultCount: 3,
      relevanceFiltered: 9,
      credibilityEvaluated: true,
    },
  },
};

// Variant with string output instead of object (to test the parser's ability to handle string JSON)
const mockAzureSearchGraphStringOutput = {
  name: "azure_search_graph",
  output: JSON.stringify({
    query: "benefits of high intensity interval training",
    results: [
      {
        title: "HIIT Benefits for Endurance Athletes",
        content:
          "High-intensity interval training (HIIT) has been shown to improve VO2 max and endurance performance with less time commitment than traditional endurance training.",
        source: "Journal of Sports Science",
        qualityScore: 0.85,
        url: "https://example.com/hiit-benefits",
        relevanceScore: 0.92,
        credibilityScore: 0.88,
        relevanceReasoning:
          "This article directly addresses the benefits of HIIT for athletes.",
        credibilityReasoning:
          "Published in a peer-reviewed journal with references to scientific studies.",
      },
    ],
    meta: {
      originalResultCount: 12,
      filteredResultCount: 1,
      relevanceFiltered: 11,
      credibilityEvaluated: true,
    },
  }),
};

// Mock with errors to test error handling
const mockInvalidOutput = {
  name: "azure_search_graph",
  output: {
    query: "benefits of high intensity interval training",
    // Missing 'results' array
    meta: {
      originalResultCount: 12,
      // Missing some required fields
      credibilityEvaluated: true,
    },
  },
};

// Mock for a non-implemented tool
const mockUnimplementedTool = {
  name: "some_other_tool",
  output: { data: "something" },
};

// Run the tests with detailed validation
console.log("===== Testing Tool Step Visualization Parsing =====\n");

// Test with object output
console.log("Test 1: Valid Object Output");
const result1 = parseToolOutputToSteps(mockAzureSearchGraphOutput);
console.log("Parsing Result:", JSON.stringify(result1, null, 2));
const validation1 = validateParsedOutput(result1);
console.log(
  "Validation:",
  validation1.valid ? "PASSED" : `FAILED - ${validation1.reason}`,
);
console.log("\n");

// Test with string output
console.log("Test 2: Valid String Output");
const result2 = parseToolOutputToSteps(mockAzureSearchGraphStringOutput);
console.log("Parsing Result:", JSON.stringify(result2, null, 2));
const validation2 = validateParsedOutput(result2);
console.log(
  "Validation:",
  validation2.valid ? "PASSED" : `FAILED - ${validation2.reason}`,
);
console.log("\n");

// Test with invalid output
console.log("Test 3: Invalid Output (Missing Fields)");
const result3 = parseToolOutputToSteps(mockInvalidOutput);
console.log("Parsing Result:", result3); // Should be null
console.log(
  "Validation: ",
  result3 === null
    ? "PASSED - correctly returned null"
    : "FAILED - should return null",
);
console.log("\n");

// Test with unimplemented tool
console.log("Test 4: Unimplemented Tool");
const result4 = parseToolOutputToSteps(mockUnimplementedTool);
console.log("Parsing Result:", result4); // Should be null
console.log(
  "Validation: ",
  result4 === null
    ? "PASSED - correctly returned null"
    : "FAILED - should return null",
);
console.log("\n");

// Summary
console.log("===== Summary =====");
console.log(
  "Test 1 (Valid Object): ",
  validation1.valid ? "PASSED" : `FAILED - ${validation1.reason}`,
);
console.log(
  "Test 2 (Valid String): ",
  validation2.valid ? "PASSED" : `FAILED - ${validation2.reason}`,
);
console.log(
  "Test 3 (Invalid Data): ",
  result3 === null ? "PASSED" : "FAILED - should return null",
);
console.log(
  "Test 4 (Unimplemented Tool): ",
  result4 === null ? "PASSED" : "FAILED - should return null",
);

// Overall status
const overallStatus =
  validation1.valid &&
  validation2.valid &&
  result3 === null &&
  result4 === null;
console.log(
  "\nOverall Test Status:",
  overallStatus ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED",
);

// For CI/CD purposes, exit with appropriate code
process.exit(overallStatus ? 0 : 1);
