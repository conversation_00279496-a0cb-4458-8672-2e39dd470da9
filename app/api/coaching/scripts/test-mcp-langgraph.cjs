"use strict";

const { StateGraph, E<PERSON> } = require("@langchain/langgraph");
const { RunnableLambda } = require("@langchain/core/runnables");
// Assuming this test script is in app/api/coaching/tools/, mcp-tools.js is in the same directory.
const mcpToolsModule = require("../tools/mcp-tools.cjs");

async function main() {
  console.log("[TestScript] Starting MCP tools test...");

  try {
    console.log("[TestScript] Attempting to get MCP client directly...");
    const client = await mcpToolsModule.getClient();
    if (client) {
      console.log("[TestScript] Successfully got MCP client instance.");
      // Attempt to list tools using the client, as a further test of client functionality
      const toolDefinitions = await client.listTools();
      console.log(
        "[TestScript] MCP Client listTools() definitions:",
        toolDefinitions.tools.map((t) => t.name),
      );
    } else {
      console.error(
        "[TestScript] Failed to get MCP client instance (returned null/undefined).",
      );
      // Attempt cleanup even if client fetching failed, as server process might have started
      await mcpToolsModule.cleanup();
      process.exit(1);
    }
  } catch (e) {
    console.error("[TestScript] Error getting MCP client directly:", e);
    await mcpToolsModule.cleanup();
    process.exit(1); // Stop if client fails
  }

  let toolsFromModule;
  try {
    console.log(
      "[TestScript] Attempting to get MCP tools via mcpToolsModule.getTools()...",
    );
    toolsFromModule = await mcpToolsModule.getTools(); // This is the call that fails elsewhere
    if (toolsFromModule && Object.keys(toolsFromModule).length > 0) {
      console.log(
        "[TestScript] Successfully got tools from mcpToolsModule.getTools():",
        Object.keys(toolsFromModule),
      );
    } else {
      console.log(
        "[TestScript] mcpToolsModule.getTools() returned no tools or an empty object.",
      );
      // If getTools() doesn't error but returns empty, it's still a problem for the graph.
    }
  } catch (e) {
    console.error("[TestScript] Error calling mcpToolsModule.getTools():", e);
    // If this errors, the graph node will likely also error, but we proceed to see.
  }

  const workflow = new StateGraph({
    channels: {
      input: null,
      tool_names: null,
      error: null,
    },
  });

  workflow.addNode(
    "load_mcp_tools_node",
    new RunnableLambda({
      func: async (state) => {
        console.log(
          "[GraphNode] Attempting to load MCP tools inside graph node...",
        );
        try {
          const tools = await mcpToolsModule.getTools();
          if (!tools || Object.keys(tools).length === 0) {
            console.warn(
              "[GraphNode] No MCP tools found by graph node or tools object is empty.",
            );
            return { error: "No MCP tools found by graph node." };
          }
          console.log(
            "[GraphNode] Successfully loaded MCP tools:",
            Object.keys(tools),
          );
          return { tool_names: Object.keys(tools) };
        } catch (err) {
          console.error(
            "[GraphNode] Error loading MCP tools in graph node:",
            err,
          );
          return {
            error: err.message || "Failed to load MCP tools in graph node.",
          };
        }
      },
    }),
  );

  workflow.setEntryPoint("load_mcp_tools_node");
  workflow.addEdge("load_mcp_tools_node", END);

  const app = workflow.compile();
  console.log("[TestScript] Compiled test LangGraph app.");

  try {
    console.log("[TestScript] Invoking test LangGraph app...");
    const result = await app.invoke({ input: "test" });
    console.log("[TestScript] LangGraph app invocation result:", result);
  } catch (e) {
    console.error("[TestScript] Error invoking LangGraph app:", e);
  }

  console.log("[TestScript] Attempting to clean up MCP resources...");
  try {
    await mcpToolsModule.cleanup();
    console.log("[TestScript] MCP cleanup successful.");
  } catch (e) {
    console.error("[TestScript] Error during MCP cleanup:", e);
  }

  console.log("[TestScript] Test finished.");
  // Ensure the process exits, which should trigger the 'exit' handler in mcp-tools.js
  // and kill any spawned server process.
  process.exit(0);
}

main().catch(async (e) => {
  // Make catch async to await cleanup
  console.error("[TestScript] Unhandled error in main:", e);
  try {
    await mcpToolsModule.cleanup();
    console.log(
      "[TestScript] MCP cleanup attempted after unhandled error in main.",
    );
  } catch (cleanupError) {
    console.error(
      "[TestScript] Error during MCP cleanup after unhandled error in main:",
      cleanupError,
    );
  }
  process.exit(1);
});
