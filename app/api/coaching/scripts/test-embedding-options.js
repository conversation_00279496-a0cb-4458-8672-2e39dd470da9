import fetch from "node-fetch";
import dotenv from "dotenv";

// Try to load environment variables from .env file if it exists
try {
  dotenv.config();
} catch (e) {
  console.log(
    "No .env file found or error loading it. Will use provided or hard-coded values.",
  );
}

async function testEmbedding(endpoint, apiKey, modelName) {
  console.log("\n--------------------------------------------------");
  console.log(`Testing with:`);
  console.log(`Endpoint: ${endpoint}`);
  console.log(`API Key: ${apiKey ? "******" : "Not provided"}`);
  console.log(`Model Name: ${modelName}`);

  if (!endpoint || !apiKey) {
    console.log("Skipping test: Missing endpoint or API key");
    return false;
  }

  const requestBody = {
    input: "This is a test for embedding API",
    model: modelName,
  };

  try {
    console.log(`Making request to ${endpoint}`);
    console.log(`Request body: ${JSON.stringify(requestBody)}`);

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    const status = response.status;
    console.log(`Response status: ${status} (${response.statusText})`);

    const text = await response.text();
    console.log(
      `Response body: ${text.substring(0, 200)}${text.length > 200 ? "..." : ""}`,
    );

    return status === 200;
  } catch (error) {
    console.log(`Error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  // Get environment variables (original config)
  const apiKey = process.env.EMBEDDING_API_KEY;
  const endpoint = process.env.EMBEDDING_ENDPOINT;

  // AZURE_SEARCH_API_KEY might be a fallback in some setups
  const possibleApiKey =
    apiKey || process.env.AZURE_SEARCH_API_KEY || process.env.AZURE_OPENAI_KEY;

  console.log("==== Environment Variables ====");
  console.log(`EMBEDDING_API_KEY: ${apiKey ? "******" : "Not set"}`);
  console.log(`EMBEDDING_ENDPOINT: ${endpoint || "Not set"}`);
  console.log(
    `AZURE_EMBEDDING_DEPLOYMENT_ID: ${process.env.AZURE_EMBEDDING_DEPLOYMENT_ID || "Not set"}`,
  );
  console.log(
    `AZURE_SEARCH_API_KEY: ${process.env.AZURE_SEARCH_API_KEY ? "******" : "Not set"}`,
  );
  console.log(
    `AZURE_OPENAI_KEY: ${process.env.AZURE_OPENAI_KEY ? "******" : "Not set"}`,
  );

  // Base values to try
  const possibleModels = [
    "text-embedding-3-large", // Most common embedding model
    "text-embedding-ada-002", // Legacy model
    process.env.AZURE_EMBEDDING_DEPLOYMENT_ID, // From env if set
  ].filter((m) => m); // Remove nulls

  // Let's make test variations
  const testVariations = [];

  // If we have an endpoint, test it
  if (endpoint) {
    for (const model of possibleModels) {
      testVariations.push({
        endpoint: endpoint,
        apiKey: possibleApiKey,
        modelName: model,
      });
    }

    // Try without model parameter (some endpoints might not need it)
    testVariations.push({
      endpoint: endpoint,
      apiKey: possibleApiKey,
      modelName: undefined,
    });
  }

  // Try common Azure OpenAI endpoints if we have resource name
  // Extract resource name if we have an endpoint
  let resourceName = "";
  if (endpoint) {
    const match = endpoint.match(/https:\/\/([^.]+)/);
    if (match && match[1]) {
      resourceName = match[1];
    }
  }

  if (resourceName) {
    for (const deploymentId of possibleModels) {
      // Format 1: Base with deployments path
      testVariations.push({
        endpoint: `https://${resourceName}.openai.azure.com/openai/deployments/${deploymentId}/embeddings?api-version=2023-05-15`,
        apiKey: possibleApiKey,
        modelName: deploymentId,
      });

      // Format 2: Similar but with 2024 API version
      testVariations.push({
        endpoint: `https://${resourceName}.openai.azure.com/openai/deployments/${deploymentId}/embeddings?api-version=2024-02-15-preview`,
        apiKey: possibleApiKey,
        modelName: deploymentId,
      });
    }
  }

  // Test all variations
  let successFound = false;

  for (const test of testVariations) {
    const success = await testEmbedding(
      test.endpoint,
      test.apiKey,
      test.modelName,
    );

    if (success) {
      console.log("\n✅ SUCCESS! This configuration works:");
      console.log(`Endpoint: ${test.endpoint}`);
      console.log(
        `Model name: ${test.modelName || "Not provided in request body"}`,
      );
      console.log(
        "\nTo fix your application, update your environment variables:",
      );
      console.log(`EMBEDDING_ENDPOINT="${test.endpoint}"`);
      if (test.modelName) {
        console.log(`AZURE_EMBEDDING_DEPLOYMENT_ID="${test.modelName}"`);
      } else {
        console.log("No model name needed in request body for this endpoint");
      }

      successFound = true;
      break;
    }
  }

  if (!successFound) {
    console.log("\n❌ None of the tested configurations worked.");
    console.log("Please check your Azure OpenAI resource in the Azure portal:");
    console.log("1. Verify you have an active embedding model deployment");
    console.log("2. Check the exact deployment name/ID");
    console.log("3. Confirm the API key has access to that deployment");
    console.log("4. Check the correct API version for your resource");
  }
}

runTests();
