// This is a JavaScript version of the test script to avoid TypeScript compilation issues
import { StateGraph, END } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { ChatOpenAI } from "@langchain/openai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import {
  strengthSessionGenerationTool,
  runningSessionGenerationTool,
  cyclingSessionGenerationTool,
  recoverySessionGenerationTool,
} from "../tools.js"; // Adjusted path
import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: ".env.local" });

// Initialize the LLM
const llm = new ChatOpenAI({
  temperature: 0,
  modelName: process.env.OPENAI_MODEL_NAME || "gpt-4o",
  openAIApiKey: process.env.OPENAI_API_KEY,
});

// Create a standalone version of the tool nodes
const strengthToolNode = new ToolNode([strengthSessionGenerationTool], { llm });
const runningToolNode = new ToolNode([runningSessionGenerationTool], { llm });
const cyclingToolNode = new ToolNode([cyclingSessionGenerationTool], { llm });
const recoveryToolNode = new ToolNode([recoverySessionGenerationTool], { llm });

// Create a simple prompt template
const promptTemplate = ChatPromptTemplate.fromMessages([
  [
    "system",
    `You are a coach that helps generate training sessions for athletes.
Your job is to understand what kind of session the user wants and use the appropriate
session generation tool to create it. You have access to the following tools:

- generate_strength_session: For strength training sessions
- generate_running_session: For running sessions 
- generate_cycling_session: For cycling sessions
- generate_recovery_session: For recovery sessions

When the user asks for a workout, determine which type it is and use the appropriate
tool with relevant parameters. Always use today's date as the date parameter in ISO format (YYYY-MM-DD).`,
  ],
  ["human", "{input}"],
]);

// Create a simple graph
async function createSessionGenerationGraph() {
  const workflow = new StateGraph({
    channels: {
      prompt: {
        value: null,
      },
      response: {
        value: null,
      },
    },
  });

  // Add nodes
  workflow.addNode("prompter", {
    prompt: async (state) => {
      const currentPrompt = state.prompt;
      const formattedPrompt = await promptTemplate.invoke({
        input: currentPrompt,
      });
      return formattedPrompt;
    },
  });

  workflow.addNode("llm", {
    prompt: async (state) => {
      return await llm.invoke(state.prompt);
    },
  });

  workflow.addNode("parser", {
    prompt: async (state) => {
      const parser = new StringOutputParser();
      return await parser.invoke(state.prompt.content);
    },
  });

  workflow.addNode("strength", strengthToolNode);
  workflow.addNode("running", runningToolNode);
  workflow.addNode("cycling", cyclingToolNode);
  workflow.addNode("recovery", recoveryToolNode);

  // Add edges
  workflow.addEdge("prompter", "llm");
  workflow.addEdge("llm", "parser");

  // Connect the tool nodes based on content
  workflow.addConditionalEdges(
    "parser",
    // This function checks the content and routes to appropriate node
    async (state) => {
      const content = state.prompt.toLowerCase();

      if (
        content.includes("strength") ||
        content.includes("weight") ||
        content.includes("resistance")
      ) {
        return "strength";
      } else if (
        content.includes("run") ||
        content.includes("running") ||
        content.includes("jog")
      ) {
        return "running";
      } else if (
        content.includes("cycl") ||
        content.includes("bike") ||
        content.includes("biking")
      ) {
        return "cycling";
      } else if (
        content.includes("recovery") ||
        content.includes("rest") ||
        content.includes("stretch")
      ) {
        return "recovery";
      } else {
        // Default to strength if unclear
        return "strength";
      }
    },
  );

  // Connect all tool nodes to END
  workflow.addEdge("strength", END);
  workflow.addEdge("running", END);
  workflow.addEdge("cycling", END);
  workflow.addEdge("recovery", END);

  // Set the entry point
  workflow.setEntryPoint("prompter");

  // Compile the graph
  return workflow.compile();
}

// Function to run the test
async function runTest() {
  try {
    console.log("Creating session generation graph...");
    const graph = await createSessionGenerationGraph();

    // Test different session types
    const testQueries = [
      "I need a high intensity strength workout for today",
      "Can you create a 5K running session for me?",
      "I'd like a cycling workout that's about 45 minutes long",
      "I need a recovery session after my hard workout yesterday",
    ];

    // Get today's date in ISO format (YYYY-MM-DD)
    const today = new Date().toISOString().split("T")[0];

    for (const query of testQueries) {
      console.log("\n---------------------------------------------------");
      console.log(`TESTING: "${query}"`);
      console.log("---------------------------------------------------");

      // Run the graph
      const result = await graph.invoke({
        prompt: query,
        response: null,
      });

      // Process the result
      const response = result.response || result;
      console.log("Result:", JSON.stringify(response, null, 2));
    }
  } catch (error) {
    console.error("Error running test:", error);
  }
}

// Run the test
runTest().then(() => console.log("Test completed"));
