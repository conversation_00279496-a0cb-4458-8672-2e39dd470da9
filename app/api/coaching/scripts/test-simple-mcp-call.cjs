"use strict";

// Add environment variables from .env.local for Airtable (ensure MCP server has necessary env vars if needed)
process.env.NEXT_PUBLIC_AIRTABLE_API_KEY =
  "**********************************************************************************";
process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID = "appkOxteq7fiscoye";
// Add others if the specific tool needs them
process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID = "appg6TiMRFQVf0OXd";
process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID = "appbhSTOiO1EHDFrK";

const { DynamicTool } = require("@langchain/core/tools");
const { z } = require("zod");
const mcpAdapter = require("../tools/mcp-langchain-adapter.cjs");

// --- Define Zod Schema ONLY for the target tool ---
const cyclingSessionsZodSchema = z.object({
  skillCategory: z.string().optional().nullable(),
  duration: z.string().optional().nullable(),
  limit: z
    .number()
    .int()
    .positive()
    .optional()
    .nullable()
    .default(10)
    .describe("Maximum number of records to return (default 10, max 100)."),
});

// --- Main Test Function ---
async function testSingleTool() {
  const targetToolName = "mcp__athlea__search_cycling_sessions";
  console.log(`[SimpleTest] Starting test for tool: ${targetToolName}`);

  let originalTool;
  let correctedTool;

  try {
    // 1. Initialize Adapter and Load Tools
    console.log("[SimpleTest] Initializing MCP adapter...");
    await mcpAdapter.getClient(); // Ensure client is ready
    const allTools = await mcpAdapter.getTools();
    console.log(
      `[SimpleTest] Adapter loaded ${allTools.length} tools:`,
      allTools.map((t) => t.name).join(", "),
    );

    // 2. Find the Specific Tool
    originalTool = allTools.find((t) => t.name === targetToolName);
    if (!originalTool) {
      throw new Error(`Tool "${targetToolName}" not found in adapter tools.`);
    }
    console.log(`[SimpleTest] Found target tool: ${originalTool.name}`);
    console.log(
      `[SimpleTest] Original tool schema from adapter:`,
      JSON.stringify(originalTool.schema), // Log the schema the adapter provided
    );

    // 3. Create Corrected DynamicTool instance
    console.log("[SimpleTest] Creating corrected DynamicTool instance...");
    correctedTool = new DynamicTool({
      name: originalTool.name,
      description: originalTool.description,
      schema: cyclingSessionsZodSchema, // Use our manually defined Zod schema
      func: async (args) => {
        console.log(
          `---> [CorrectedTool ${originalTool.name}] Func ENTERED with validated args: `,
          JSON.stringify(args),
        );
        console.log(
          `---> [CorrectedTool ${originalTool.name}] Calling originalTool.invoke with args: `,
          JSON.stringify(args || {}),
        );
        try {
          // Call invoke on the *original* tool instance from the adapter
          const result = await originalTool.invoke(args || {});
          const resultString =
            typeof result === "string" ? result : JSON.stringify(result);
          console.log(
            `---> [CorrectedTool ${originalTool.name}] Original tool invocation successful. Result (first 500 chars):`,
            resultString.slice(0, 500) +
              (resultString.length > 500 ? "..." : ""),
          );
          return result; // Return the actual result
        } catch (invocationError) {
          console.error(
            `---> [CorrectedTool ${originalTool.name}] Error during originalTool.invoke:`,
            invocationError,
          );
          throw invocationError; // Re-throw the error
        }
      },
    });
    console.log(
      `[SimpleTest] Corrected tool "${correctedTool.name}" created successfully.`,
    );

    // 4. Define Sample Arguments
    const sampleArgs = {
      skillCategory: "Intermediate",
      duration: "60", // Ensure duration is a string if schema expects it
      limit: 3,
    };
    console.log(
      `[SimpleTest] Sample arguments for tool call:`,
      JSON.stringify(sampleArgs),
    );

    // 5. Directly Call the Corrected Tool's Function
    console.log(
      `[SimpleTest] Calling correctedTool.func with sample arguments...`,
    );
    const toolResult = await correctedTool.func(sampleArgs);
    console.log(
      `\n[SimpleTest] Direct tool call successful! Result:`,
      JSON.stringify(toolResult, null, 2),
    );
  } catch (error) {
    console.error("\n[SimpleTest] Error during execution:", error);
  } finally {
    // 6. Cleanup
    console.log("\n[SimpleTest] Attempting to clean up MCP resources...");
    try {
      await mcpAdapter.closeClient();
      console.log("[SimpleTest] MCP cleanup successful.");
    } catch (cleanupError) {
      console.error("[SimpleTest] Error during MCP cleanup:", cleanupError);
    }
    console.log("[SimpleTest] Test finished.");
  }
}

// --- Run the Test ---
testSingleTool();

// --- Process Exit Handlers (Optional but recommended) ---
process.on("uncaughtException", async (error) => {
  console.error("Uncaught exception:", error);
  try {
    // Ensure cleanup even on uncaught exceptions
    await mcpAdapter.closeClient();
  } catch (closeError) {
    console.error("Error closing MCP client after exception:", closeError);
  }
  process.exit(1);
});

process.on("SIGINT", async () => {
  console.log("[SimpleTest] SIGINT received - gracefully shutting down");
  try {
    await mcpAdapter.closeClient();
    console.log("[SimpleTest] MCP client closed successfully via SIGINT");
  } catch (error) {
    console.error("[SimpleTest] Error closing MCP client via SIGINT:", error);
  }
  process.exit(0);
});
