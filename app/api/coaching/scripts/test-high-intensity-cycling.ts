import { AIMessage, HumanMessage } from "@langchain/core/messages";
import { AzureChatOpenAI } from "@langchain/openai";
import { config } from "dotenv";
import {
  connectAirtableMcpTools,
  closeMcpClient,
} from "../lib/tools/airtable-mcp-integration";

// Load environment variables
config();

// Configure Azure OpenAI
function createAzureChatOpenAI(options = {}) {
  console.log(
    `[TestAzureOpenAI] Creating AzureChatOpenAI with options:`,
    options,
  );

  const AZURE_OPENAI_ENDPOINT =
    process.env.AZURE_OPENAI_ENDPOINT ||
    "https://ft-gpt40mini.openai.azure.com/";
  const AZURE_OPENAI_DEPLOYMENT =
    process.env.AZURE_DEPLOYMENT_NAME || "gpt-4.1-nano";
  const AZURE_OPENAI_API_VERSION = "2025-01-01-preview";
  const AZURE_OPENAI_API_KEY =
    process.env.AZURE_OPENAI_API_KEY || "********************************";

  const instanceNameMatch = AZURE_OPENAI_ENDPOINT.match(
    /https:\/\/([^.]+)\.openai\.azure\.com/,
  );
  const instanceName = instanceNameMatch?.[1] || "ft-gpt40mini";

  console.log(`[TestAzureOpenAI] Configuration:`);
  console.log(`  - API Version: ${AZURE_OPENAI_API_VERSION}`);
  console.log(`  - Deployment Name: ${AZURE_OPENAI_DEPLOYMENT}`);
  console.log(`  - Instance Name: ${instanceName}`);
  console.log(`  - Endpoint URL: ${AZURE_OPENAI_ENDPOINT}`);

  return new AzureChatOpenAI({
    azureOpenAIApiKey: AZURE_OPENAI_API_KEY,
    azureOpenAIApiVersion: AZURE_OPENAI_API_VERSION,
    modelName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiDeploymentName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiInstanceName: instanceName,
    temperature: 0.0,
    cache: true,
    maxRetries: 3,
    ...options,
  });
}

// Simplified Prompt for Airtable Agent
const promptString =
  "You are an AI assistant that can search Airtable for cycling training sessions.\n" +
  "You can use the search_records tool to find specific cycling sessions based on criteria.\n" +
  "For high intensity sessions, you should look for records where IntensityLevel='4' in the cycling sessions table.\n" +
  "For the Cycling base, use baseId: 'appkOxteq7fiscoye' and tableId: 'Cycling Sessions'.\n" +
  "After retrieving records, briefly summarize key details about each session (name, duration, intensity, category).\n";

async function main() {
  // Get query from command line arguments or use default
  const userQuery =
    process.argv[2] || "Find me high intensity cycling sessions";

  console.log("[TestScript] Starting High-Intensity Cycling Test...");

  let mcpAdapterClient = null;

  try {
    // Get Airtable API Key from environment
    const airtableApiKey =
      process.env.AIRTABLE_API_KEY ||
      "**********************************************************************************";

    // Use the connectAirtableMcpTools function from our integration module
    console.log("[TestScript] Connecting to Airtable MCP...");
    const { tools, client } = await connectAirtableMcpTools(
      airtableApiKey,
      process.env.NODE_PATH,
      process.env.NPX_PATH,
    );

    mcpAdapterClient = client;
    console.log(
      `[TestScript] Successfully connected to Airtable MCP server and got ${tools.length} tools`,
    );

    // Print the available tools
    console.log("[TestScript] Available Airtable MCP tools:");
    tools.forEach((tool) => {
      console.log(`  - ${tool.name}`);
    });

    // Setup LLM and Bind the tools
    console.log("[TestScript] Creating Azure LLM...");
    const llm = createAzureChatOpenAI({
      temperature: 0.0,
      maxTokens: 1500,
      timeout: 45000,
    });

    console.log("[TestScript] Binding tools to LLM...");
    const llmWithTools = llm.bindTools(tools);
    console.log("[TestScript] LLM with tools set up.");

    // Create messages for the query
    console.log(`[TestScript] User query: "${userQuery}"`);
    const messages = [
      new HumanMessage(promptString),
      new HumanMessage(userQuery),
    ];

    // Invoke LLM with tools
    console.log("[TestScript] Invoking LLM with tools...");
    const response = await llmWithTools.invoke(messages);

    console.log("\n=== LLM Response ===");
    console.log(response.content);

    if (response.tool_calls && response.tool_calls.length > 0) {
      console.log("\n=== Tool Calls ===");
      response.tool_calls.forEach((call, index) => {
        console.log(`Tool Call #${index + 1}:`);
        console.log(`  Name: ${call.name}`);
        console.log(`  Args: ${JSON.stringify(call.args, null, 2)}`);
      });
    }
  } catch (error) {
    console.error("[TestScript] Error during execution:", error);
  } finally {
    // Always clean up MCP client
    console.log("[TestScript] Attempting to clean up MCP client...");
    if (mcpAdapterClient) {
      await closeMcpClient(mcpAdapterClient);
    }
    console.log("[TestScript] Test finished.");
  }
}

// Handle process exit events
process.on("uncaughtException", async (error) => {
  console.error("Uncaught Exception:", error);
  process.exit(1);
});

process.on("SIGINT", async () => {
  console.log("SIGINT received - shutting down");
  process.exit(0);
});

// Run the script
main();
