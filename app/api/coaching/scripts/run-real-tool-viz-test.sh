#!/bin/bash

# run-real-tool-viz-test.sh
# Shell script to run the real data tool step visualization test

# Get the directory of this script
SCRIPT_DIR="$(dirname "$0")"

# Default query if none provided
DEFAULT_QUERY="benefits of high intensity interval training"

# Use the provided query or default
QUERY="${1:-$DEFAULT_QUERY}"

echo "Running Tool Step Visualization Test with Real Data..."
echo "Query: \"$QUERY\""
echo "---------------------------------------------------"

# Run the test script with the query - use .cjs extension for CommonJS
node "$SCRIPT_DIR/test-real-tool-visualization.cjs" "$QUERY"

# Exit with the status of the node script
exit $? 