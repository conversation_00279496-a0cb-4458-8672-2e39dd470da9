// This is a standalone script to test session generation without dependencies
import dotenv from "dotenv";
import { z } from "zod";
import { tool } from "@langchain/core/tools";
import { AzureChatOpenAI } from "@langchain/openai";

// Load environment variables
dotenv.config({ path: ".env.local" });

// Simplified session generation tool (copied from tools.ts)
const testStrengthSessionGenerationTool = tool(
  async ({
    date,
    intensity = "high",
    duration = 60,
    skillCategory = "General Strength",
    trainingCategory = "Full Body",
  }) => {
    console.log(`StrengthSessionTool: Generating strength session for ${date}`);
    const now = new Date().toISOString();
    const sessionId = `S&C-${date.replace(/-/g, "")}-${Math.floor(
      Math.random() * 1000,
    )
      .toString()
      .padStart(3, "0")}`;

    let intensityLevel = 8;
    let stressLevel = 8;

    if (intensity.toLowerCase() === "low") {
      intensityLevel = 3;
      stressLevel = 3;
    } else if (intensity.toLowerCase() === "moderate") {
      intensityLevel = 5;
      stressLevel = 5;
    }

    // Determine exercises based on training category
    let exercises = [];
    if (trainingCategory.toLowerCase().includes("full body")) {
      exercises = [
        {
          name: "Barbell Squats",
          sets: 3,
          reps: "8-10",
          intensity: "75% 1RM or RPE 8",
        },
        {
          name: "Bench Press",
          sets: 3,
          reps: "8-10",
          intensity: "70% 1RM or RPE 7-8",
        },
        {
          name: "Deadlifts",
          sets: 1,
          reps: "5",
          intensity: "80% 1RM or RPE 8-9",
        },
      ];
    } else if (trainingCategory.toLowerCase().includes("upper")) {
      exercises = [
        {
          name: "Bench Press",
          sets: 4,
          reps: "6-8",
          intensity: "80% 1RM or RPE 8",
        },
        { name: "Pull-ups", sets: 3, reps: "8-10", intensity: "Bodyweight" },
        {
          name: "Overhead Press",
          sets: 3,
          reps: "8-10",
          intensity: "70% 1RM or RPE 7",
        },
      ];
    } else if (trainingCategory.toLowerCase().includes("lower")) {
      exercises = [
        {
          name: "Back Squats",
          sets: 4,
          reps: "6-8",
          intensity: "80% 1RM or RPE 8",
        },
        {
          name: "Romanian Deadlifts",
          sets: 3,
          reps: "8-10",
          intensity: "75% 1RM or RPE 7-8",
        },
        {
          name: "Leg Press",
          sets: 3,
          reps: "10-12",
          intensity: "70% 1RM or RPE 7",
        },
      ];
    }

    try {
      const templateSession = {
        SessionID: sessionId,
        SessionName: `${trainingCategory} Strength Session - ${intensity} Intensity`,
        SessionDescription: `A ${duration}-minute ${skillCategory.toLowerCase()} session focusing on ${trainingCategory.toLowerCase()} development at ${intensity.toLowerCase()} intensity.`,
        Duration: duration,
        SkillCategory: skillCategory,
        TrainingCategory: trainingCategory,
        IntensityLevel: intensityLevel,
        StressLevel: stressLevel,
        Segments: exercises.map((exercise, index) => ({
          SegmentID: `${sessionId}-SEG${index + 1}`,
          SessionID: sessionId,
          SegmentOrder: index + 1,
          Exercise: exercise.name,
          Sets: exercise.sets,
          Reps: exercise.reps,
          Intensity: exercise.intensity,
          RestPeriod: intensityLevel > 6 ? "90-120 seconds" : "60-90 seconds",
          SegmentType: index === 0 ? "Main" : "Accessory",
          RepeatCount: 1,
          CreateTime: now,
          LastModified: now,
        })),
      };

      console.log(
        "StrengthSessionTool: Generated template session successfully",
      );
      return templateSession;
    } catch (error) {
      console.error("StrengthSessionTool Error:", error.message);
      return `Error generating strength session: ${error.message}`;
    }
  },
  {
    name: "generate_strength_session",
    description:
      "Generates a template strength training session with exercises, sets, reps, and other parameters.",
    schema: z.object({
      date: z
        .string()
        .describe(
          "The date for the session (YYYY-MM-DD format), used for ID generation.",
        ),
      // Make fields nullable to avoid API warnings
      intensity: z
        .string()
        .optional()
        .nullable()
        .default("high")
        .describe("Intensity level of the session (low, moderate, high)."),
      duration: z
        .number()
        .optional()
        .nullable()
        .default(60)
        .describe("Duration of the session in minutes."),
      skillCategory: z
        .string()
        .optional()
        .nullable()
        .default("General Strength")
        .describe("Skill focus of the session."),
      trainingCategory: z
        .string()
        .optional()
        .nullable()
        .default("Full Body")
        .describe("Training focus (e.g., Full Body, Upper Body, Lower Body)."),
    }),
  },
);

// Function to parse a user query and extract parameters for session generation
function parseUserQuery(query) {
  const today = new Date().toISOString().split("T")[0];

  // Default parameters
  const params = {
    date: today,
    intensity: "moderate",
    duration: 60,
    skillCategory: "General Strength",
    trainingCategory: "Full Body",
  };

  // Extract intensity
  if (query.match(/\blow\b/i)) {
    params.intensity = "low";
  } else if (query.match(/\bhigh\b/i)) {
    params.intensity = "high";
  } else if (query.match(/\bmoderate\b/i)) {
    params.intensity = "moderate";
  }

  // Extract body part focus
  if (query.match(/\bupper\s*body\b/i)) {
    params.trainingCategory = "Upper Body";
  } else if (query.match(/\blower\s*body\b/i)) {
    params.trainingCategory = "Lower Body";
  } else if (query.match(/\bfull\s*body\b/i)) {
    params.trainingCategory = "Full Body";
  }

  // Extract duration
  const durationMatch = query.match(/(\d+)\s*min(ute)?s?/i);
  if (durationMatch) {
    params.duration = parseInt(durationMatch[1], 10);
  }

  // Extract skill category
  if (query.match(/\bhypertrophy\b/i)) {
    params.skillCategory = "Hypertrophy";
  } else if (query.match(/\bendurance\b/i)) {
    params.skillCategory = "Endurance";
  } else if (query.match(/\bpower\b/i)) {
    params.skillCategory = "Power";
  } else if (query.match(/\bstrength\b/i)) {
    params.skillCategory = "Strength";
  }

  return params;
}

// Function to handle user query and generate session
async function handleUserQuery(query) {
  console.log(`\n==== PROCESSING USER QUERY ====`);
  console.log(`User Query: "${query}"`);

  // Parse the query to extract parameters
  const params = parseUserQuery(query);
  console.log("\nExtracted Parameters:");
  console.log(JSON.stringify(params, null, 2));

  // Generate the session
  console.log("\nGenerating Session...");
  const session = await testStrengthSessionGenerationTool.invoke(params);

  console.log("\n==== GENERATED SESSION ====");
  console.log(`Session ID: ${session.SessionID}`);
  console.log(`Name: ${session.SessionName}`);
  console.log(`Description: ${session.SessionDescription}`);
  console.log("\nExercises:");
  session.Segments.forEach((segment) => {
    console.log(
      `  - ${segment.Exercise}: ${segment.Sets} sets of ${segment.Reps} at ${segment.Intensity}`,
    );
  });

  return session;
}

// Function to simulate the process of combining session generation with Airtable storage
async function simulateSessionGenerationAndStorage(userQuery) {
  console.log("\n==== SIMULATING FULL USER FLOW WITH AIRTABLE MCP ====");
  console.log(`User Request: "${userQuery}"`);

  // 1. Process the user query and generate a session
  console.log("\nSTEP 1: Processing user query and generating session");
  const session = await handleUserQuery(userQuery);

  // 2. Mock Airtable processing and storage
  console.log("\nSTEP 2: Transforming session for Airtable storage");
  const airtableRecord = {
    fields: {
      SessionID: session.SessionID,
      SessionName: session.SessionName,
      SessionDescription: session.SessionDescription,
      Duration: session.Duration,
      SkillCategory: session.SkillCategory,
      TrainingCategory: session.TrainingCategory,
      IntensityLevel: session.IntensityLevel,
      StressLevel: session.StressLevel,
      // Other fields would be mapped here
    },
  };

  // 3. Simulate storing to Airtable
  console.log("\nSTEP 3: Storing in Airtable (simulated)");
  console.log("Creating record in 'S&C Sessions' table in 'Strength' base");

  // 4. Simulate success response
  console.log("\nSTEP 4: Success! Record created in Airtable");
  console.log(`Record ID: rec${Math.random().toString(36).substring(2, 10)}`);

  // 5. Show how segments would be handled
  console.log("\nSTEP 5: Creating segment records in Airtable");
  session.Segments.forEach((segment, index) => {
    console.log(`Creating segment ${index + 1}: ${segment.Exercise}`);
  });

  console.log("\nComplete integration flow demonstrated successfully!");

  return session;
}

// Function to run the test
async function runTest() {
  try {
    console.log("Running user query-based session generation test...");

    // Test 1: Process different user queries
    console.log("\n==== TEST 1: PROCESSING USER QUERIES ====");

    const userQueries = [
      "I want a high intensity upper body workout for 45 minutes",
      "Give me a moderate full body strength session",
      "Create a low intensity leg workout for 30 minutes",
      "I need a hypertrophy upper body session",
    ];

    for (const query of userQueries) {
      await handleUserQuery(query);
    }

    // Test 2: Full integration flow with user query
    console.log("\n==== TEST 2: FULL INTEGRATION ====");
    const complexQuery =
      "I need a 50 minute upper body hypertrophy workout with moderate intensity for tomorrow";

    await simulateSessionGenerationAndStorage(complexQuery);

    // Try Azure OpenAI if credentials are valid
    try {
      // Test 3: Test using an LLM to enhance query parsing (if possible)
      console.log(
        "\n==== TEST 3: ADVANCED QUERY PARSING WITH LLM (OPTIONAL) ====",
      );

      // Create Azure OpenAI client
      const llm = new AzureChatOpenAI({
        temperature: 0,
        azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY,
        azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION,
        azureOpenAIEndpoint: process.env.AZURE_OPENAI_ENDPOINT,
        deploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
      });

      console.log("Azure OpenAI client initialized, attempting to connect...");
      // This part would be expanded if Azure OpenAI is working
      console.log(
        "(Skipping LLM-based parsing due to potential Azure OpenAI limitations)",
      );
    } catch (error) {
      console.log(
        "Note: Azure OpenAI integration skipped (not critical for testing)",
      );
    }
  } catch (error) {
    console.error("Error running test:", error);
  }
}

// Check if script is run with a specific query from command line
const userProvidedQuery = process.argv.slice(2).join(" ");

// Run the test with provided query or default tests
if (userProvidedQuery) {
  console.log(`Running test with provided query: "${userProvidedQuery}"`);
  handleUserQuery(userProvidedQuery).then(() =>
    console.log("\nTest completed for provided query"),
  );
} else {
  // Run all default tests
  runTest().then(() => console.log("\nAll tests completed"));
}
