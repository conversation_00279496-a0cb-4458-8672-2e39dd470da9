/**
 * Test Azure AI Tracing with LangGraph
 *
 * This script checks the environment and runs the test-azure-tracing.mjs script
 */

// Load environment from .env.local
import * as dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";
import { execFile } from "child_process";

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: resolve(__dirname, "../../../../.env.local") });

// Verify environment variables are set
const requiredVars = [
  "AZURE_MONITOR_CONNECTION_STRING",
  "AZURE_OPENAI_API_KEY",
  "AZURE_OPENAI_ENDPOINT",
  "AZURE_DEPLOYMENT_NAME",
];

const missingVars = requiredVars.filter((varName) => !process.env[varName]);

if (missingVars.length > 0) {
  console.error(
    `Error: The following required environment variables are missing:`,
  );
  missingVars.forEach((varName) => console.error(`- ${varName}`));
  console.error(
    "\nPlease check your .env.local file and ensure all required variables are set.",
  );
  process.exit(1);
}

console.log("----------------------------------------");
console.log("🚀 Running Azure AI Tracing Test Script");
console.log("----------------------------------------");

// Import and run the main test script
import("./test-azure-tracing.mjs")
  .then(() => {
    console.log("----------------------------------------");
    console.log("✅ Test execution complete");
    console.log("----------------------------------------");
  })
  .catch((error) => {
    console.error("----------------------------------------");
    console.error("❌ Error running the test:", error);
    console.error("----------------------------------------");
    process.exit(1);
  });
