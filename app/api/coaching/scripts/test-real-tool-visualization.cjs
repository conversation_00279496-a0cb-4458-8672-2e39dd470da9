#!/usr/bin/env node

/**
 * Test script for the tool step visualization using real Azure Search Graph tool
 *
 * This script runs a real query against the Azure Search Graph tool and tests
 * the parsing of the actual tool output into visualization steps.
 *
 * Run with: node test-real-tool-visualization.cjs "your search query"
 * Or via the shell script: ./run-real-tool-viz-test.sh "your search query"
 */

// Import required modules
const fs = require("fs");
const path = require("path");
const { promisify } = require("util");
const exec = promisify(require("child_process").exec);

// Get query from command line args
const args = process.argv.slice(2);
let query = "benefits of high intensity interval training"; // Default query
if (args.length > 0) {
  query = args[0];
  console.log(`Using provided query: "${query}"`);
} else {
  console.log(`Using default query: "${query}"`);
}

const preloadScriptPath = path.join(__dirname, "preload-env.cjs");
const searchScriptPath = path.join(__dirname, "test-azure-search-graph.mjs");

// Function to validate the structure of parsed tool steps
function validateParsedOutput(parsedOutput) {
  if (!parsedOutput) {
    return { valid: false, reason: "Output is null" };
  }

  // Validate required fields
  if (!parsedOutput.toolName) {
    return { valid: false, reason: "Missing toolName" };
  }

  if (
    !parsedOutput.steps ||
    !Array.isArray(parsedOutput.steps) ||
    parsedOutput.steps.length === 0
  ) {
    return { valid: false, reason: "Missing or empty steps array" };
  }

  // Validate steps structure
  for (let i = 0; i < parsedOutput.steps.length; i++) {
    const step = parsedOutput.steps[i];

    if (typeof step.number !== "number") {
      return { valid: false, reason: `Step ${i} missing number property` };
    }

    if (!step.title) {
      return { valid: false, reason: `Step ${i} missing title` };
    }

    if (!step.content) {
      return { valid: false, reason: `Step ${i} missing content` };
    }

    if (!step.content.type) {
      return { valid: false, reason: `Step ${i} content missing type` };
    }

    // Validate specific content types
    if (
      step.content.type === "dots" &&
      (!step.content.props || typeof step.content.props.total !== "number")
    ) {
      return {
        valid: false,
        reason: `Step ${i} dots content missing required props (total)`,
      };
    }

    if (
      step.content.type === "items" &&
      (!step.content.props || !Array.isArray(step.content.props.items))
    ) {
      return {
        valid: false,
        reason: `Step ${i} items content missing required props (items array)`,
      };
    }

    if (
      step.content.type === "text" &&
      typeof step.content.value !== "string"
    ) {
      return {
        valid: false,
        reason: `Step ${i} text content missing value (string)`,
      };
    }
  }

  return { valid: true };
}

// The simplified version of parseAzureSearchGraphOutput (compatible with the real data)
function parseAzureSearchGraphOutput(data) {
  if (!data || typeof data !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] Input data is not an object or is null",
    );
    return null;
  }

  // Check for expected structure
  if (!data.query || typeof data.query !== "string") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.query is missing or not a string",
    );
    return null;
  }

  if (!data.results || !Array.isArray(data.results)) {
    console.warn(
      "[parseAzureSearchGraphOutput] data.results is missing or not an array",
    );
    return null;
  }

  if (!data.meta || typeof data.meta !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.meta is missing or not an object",
    );
    return null;
  }

  // Add checks for critical meta fields (adjust based on actual structure)
  if (
    typeof data.meta.originalResultCount !== "number" ||
    typeof data.meta.filteredResultCount !== "number" ||
    typeof data.meta.relevanceFiltered !== "number" ||
    typeof data.meta.credibilityEvaluated !== "boolean"
  ) {
    console.warn(
      "[parseAzureSearchGraphOutput] One or more critical meta fields are missing or incorrect types",
    );
    console.warn("meta data structure:", JSON.stringify(data.meta, null, 2));
    return null;
  }

  const { query, results, meta } = data;
  const steps = [];
  let currentStep = 1;

  const numOriginalResults = meta.originalResultCount;
  const numRelevanceRemoved = meta.relevanceFiltered;
  const calculatedResultsAfterRelevance =
    numOriginalResults - numRelevanceRemoved;
  const numFinalFilteredCount = meta.filteredResultCount;

  steps.push({
    number: currentStep++,
    title: "Initial Search",
    description: `Searched for: "${query}". Found ${numOriginalResults} docs.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: numOriginalResults,
        highlightedLabel: "found",
        regularColor: "bg-transparent",
      },
    },
  });

  steps.push({
    number: currentStep++,
    title: "Relevance Filter",
    description: `${calculatedResultsAfterRelevance} docs after relevance filter.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: calculatedResultsAfterRelevance,
        highlightedLabel: "relevant",
        nonHighlightedLabel: "filtered",
      },
    },
  });

  if (meta.credibilityEvaluated) {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Sources evaluated.",
      content: {
        type: "text",
        value: "Source credibility assessed.",
      },
    });
  } else {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Skipped.",
      content: {
        type: "text",
        value: "Credibility check skipped.",
      },
    });
  }

  steps.push({
    number: currentStep++,
    title: "Final Selection",
    description: `Presenting top ${numFinalFilteredCount} documents.`,
    content: {
      type: "items",
      props: {
        items: results.map((r) => ({
          title: r.title,
          content: r.content,
          source: r.source || r.url,
        })),
        maxItems: 2,
        moreLabel: `more result${numFinalFilteredCount > 2 ? "s" : ""}`,
      },
    },
  });

  return {
    toolName: "Azure Search",
    steps,
    summary: `${numFinalFilteredCount} of ${numOriginalResults} results shown`,
    triggerLabel: "Search details",
  };
}

// Main function to run the test
async function runTest() {
  console.log("===== Testing Tool Step Visualization with Real Data =====\n");
  console.log(`Running Azure Search Graph tool with query: "${query}"`);

  let toolOutputJsonString = "";

  try {
    // Execute the search script with the query using tsx and preloading environment variables
    const command = `npx tsx -r "${preloadScriptPath}" "${searchScriptPath}" --query "${query}" --no-interactive`;
    console.log(`Executing command: ${command}`);
    const { stdout, stderr } = await exec(command);

    // Stderr from the child process might contain useful logs from test-azure-search-graph.mjs
    if (stderr) {
      console.error(
        "Stderr from search script (-r preload test-azure-search-graph.mjs):",
        stderr,
      );
    }

    toolOutputJsonString = stdout.trim(); // stdout should be the raw JSON string from the tool

    // Save the raw JSON output to a file for inspection
    const outputFile = path.join(__dirname, "real-tool-output.json");
    fs.writeFileSync(outputFile, toolOutputJsonString);
    console.log(`Raw tool JSON output saved to: ${outputFile}`);

    // Attempt to parse the JSON string output from the tool
    let searchData;
    try {
      searchData = JSON.parse(toolOutputJsonString);
    } catch (parseError) {
      console.error("Error parsing JSON output from tool:", parseError);
      console.error(
        "Raw output received from child script:",
        toolOutputJsonString,
      );
      process.exit(1);
    }

    // Check if the tool itself returned an error structure
    if (searchData.error) {
      console.error(
        "Error reported by AzureSearchGraphTool (via child script):",
        searchData.message,
      );
      if (searchData.stack) console.error("Stack:", searchData.stack);
      process.exit(1);
    }

    // Now, pass this searchData (which is the tool's direct output object) to the parser
    console.log("\nParsing direct tool output with visualization parser...");
    const parsedForVisualization = parseAzureSearchGraphOutput(searchData);

    // Validate and display the parsed output for visualization
    if (parsedForVisualization) {
      console.log("Visualization parsing successful. Validating structure...");
      const validation = validateParsedOutput(parsedForVisualization);

      if (validation.valid) {
        console.log("✅ Visualization Validation PASSED");
        console.log("\nParsed data for visualization:");
        console.log(JSON.stringify(parsedForVisualization, null, 2));
        const vizFile = path.join(
          __dirname,
          "visualization-parsed-output.json",
        );
        fs.writeFileSync(
          vizFile,
          JSON.stringify(parsedForVisualization, null, 2),
        );
        console.log(`Parsed visualization data saved to: ${vizFile}`);
      } else {
        console.error("❌ Visualization Validation FAILED:", validation.reason);
        console.log("\nProblematic output structure for visualization:");
        console.log(JSON.stringify(parsedForVisualization, null, 2));
      }
    } else {
      console.error("❌ Visualization Parsing FAILED - null result returned");
    }

    console.log("\n===== Test Complete =====");
    return parsedForVisualization;
  } catch (error) {
    // This catch block handles errors from exec or other parts of runTest
    console.error("Test script runTest() failed:", error);
    console.error(
      "Raw output that might have caused exec error (if any):",
      toolOutputJsonString,
    );
    process.exit(1);
  }
}

// Run the test
runTest().catch((error) => {
  // Catch unhandled promise rejections from runTest itself.
  console.error("Unhandled error in runTest promise chain:", error);
  process.exit(1);
});
