#!/bin/bash

# Make sure we're in the project root
cd "$(dirname "$0")/../../.."

# Pre-set Azure OpenAI credentials
export AZURE_OPENAI_API_KEY="********************************"
export AZURE_OPENAI_ENDPOINT="https://ft-gpt40mini.openai.azure.com/"
export AZURE_OPENAI_API_DEPLOYMENT_NAME="gpt-4.1-nano"
export AZURE_OPENAI_API_VERSION="2023-12-01-preview"

# Check if user provided a query
if [ "$#" -gt 0 ]; then
  # Run with user-provided query
  echo "Running session generation test with user query: \"$*\""
  node app/api/coaching/test-session-standalone.mjs "$*"
else
  # Run the standalone JavaScript version with default tests
  echo "Running session generation test with default queries..."
  node app/api/coaching/test-session-standalone.mjs
fi 