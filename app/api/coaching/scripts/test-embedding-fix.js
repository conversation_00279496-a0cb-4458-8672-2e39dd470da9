import dotenv from "dotenv";
import { generateEmbedding } from "../lib/utils/embeddingUtil.js";

// Try to load environment variables from .env file
try {
  dotenv.config();
} catch (e) {
  console.log(
    "No .env file found or error loading it. Using process.env directly.",
  );
}

async function testEmbeddingWithUrlExtraction() {
  console.log("\n=== TESTING EMBEDDING WITH URL EXTRACTION ===");

  const apiKey = process.env.EMBEDDING_API_KEY;
  const endpoint = process.env.EMBEDDING_ENDPOINT;

  console.log("Using environment variables:");
  console.log(`EMBEDDING_API_KEY: ${apiKey ? "***" : "Not set"}`);
  console.log(`EMBEDDING_ENDPOINT: ${endpoint || "Not set"}`);
  console.log(
    `AZURE_EMBEDDING_DEPLOYMENT_ID: ${process.env.AZURE_EMBEDDING_DEPLOYMENT_ID || "Not set"}`,
  );

  if (!apiKey || !endpoint) {
    console.error("ERROR: Missing required environment variables");
    return;
  }

  try {
    console.log("\nAttempting to generate embedding...");
    const vector = await generateEmbedding(
      "This is a test of the embedding API with URL-based model extraction",
      apiKey,
      endpoint,
    );

    if (vector) {
      console.log("\n✅ SUCCESS! Generated embedding vector.");
      console.log(`Vector length: ${vector.length}`);
      console.log(`First 5 dimensions: ${vector.slice(0, 5).join(", ")}...`);
    } else {
      console.log("\n❌ FAILED: Could not generate embedding vector.");
    }
  } catch (error) {
    console.error("\n❌ ERROR during embedding generation:", error);
  }
}

// Run the test
testEmbeddingWithUrlExtraction();
