#!/bin/bash

# Helper script to run the High Intensity Cycling Session search test

# Make this script executable:
# chmod +x run-cycling-intensity-search.sh

# Set up environment variables if not already present
export NODE_PATH=${NODE_PATH:-"/usr/local/bin/node"}
export NPX_PATH=${NPX_PATH:-"/usr/local/bin/npx"}

# Check if the API key is set
if [[ -z "${AIRTABLE_API_KEY}" ]]; then
  echo "Warning: AIRTABLE_API_KEY environment variable is not set."
  echo "         The script will use a default value, which might not work."
  echo "         Set it with: export AIRTABLE_API_KEY=your_api_key"
  echo ""
fi

# Parse command line arguments to form a query
QUERY=""
if [ $# -gt 0 ]; then
  QUERY="$*"
fi

# Run the TypeScript file using npx and tsx (which handles ESM modules)
echo "Running High Intensity Cycling Session Search..."
echo "Installing tsx if needed..."
npm install -g tsx

if [ -z "$QUERY" ]; then
  npx tsx app/api/coaching/tools/test-high-intensity-cycling.ts
else
  npx tsx app/api/coaching/tools/test-high-intensity-cycling.ts "$QUERY" 
fi 