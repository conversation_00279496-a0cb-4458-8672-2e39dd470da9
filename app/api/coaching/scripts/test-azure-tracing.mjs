/**
 * Test Azure AI Tracing with LangGraph
 *
 * This script implements a basic LangGraph workflow and
 * integrates Azure AI tracing to validate the tracing implementation.
 */

// defineConfig is not needed
// import { defineConfig } from "@langchain/langgraph";
import { END, StateGraph, messagesStateReducer } from "@langchain/langgraph";
import { AzureChatOpenAI } from "@langchain/openai";
import { HumanMessage, AIMessage } from "@langchain/core/messages";
import { RunnableSequence } from "@langchain/core/runnables";
import * as dotenv from "dotenv";

// Initialize environment variables
dotenv.config({ path: ".env.local" });

// Import Azure AI Tracing
import {
  initAzureAITracing,
  traceOperation,
  traceAgentNode,
  recordGraphExecution,
  recordLLMGeneration,
} from "../../lib/azure-ai-tracing.js";

// ======================================
// Initialize Tracing
// ======================================
initAzureAITracing();

// ======================================
// Model Configuration
// ======================================
const chatModel = new AzureChatOpenAI({
  azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY,
  azureOpenAIApiVersion: "2023-07-01-preview", // Default version if not specified
  azureOpenAIApiDeploymentName:
    process.env.AZURE_DEPLOYMENT_NAME || "gpt-4.1-nano",
  azureOpenAIApiInstanceName: new URL(
    process.env.AZURE_OPENAI_ENDPOINT || "",
  ).hostname.split(".")[0],
  azureOpenAIApiEndpoint: process.env.AZURE_OPENAI_ENDPOINT,
  temperature: 0.2,
});

// ======================================
// Define State Graph
// ======================================
const graphState = {
  // track messages for prompt history
  messages: messagesStateReducer,
  // track current decision on where to route next
  decision: {
    reducer: (prev, next) => next,
    default: () => null,
  },
};

// Create a node for task planning
const plannerNode = async (state) => {
  return traceAgentNode("planner", "test-user", "test-session", async () => {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];

    console.log("🔍 PLANNER NODE RUNNING");
    console.log(`Input: ${lastMessage.content}`);

    const startTime = Date.now();

    // Run the model to determine the high-level plan
    const result = await chatModel.invoke([
      ...messages,
      new HumanMessage(
        "You are a planning agent. Based on the user request, break this into a 3-step plan. " +
          "Return JUST the 3 steps as a numbered list with no additional explanation.",
      ),
    ]);

    const durationMs = Date.now() - startTime;

    // Record telemetry
    recordLLMGeneration(
      "azure-gpt-4",
      100, // Approximate token counts for demo
      50,
      durationMs,
      messages,
      result,
    );

    // Return the updated state
    return {
      messages: [...messages, result],
      decision: "research", // Always route to research node in this demo
    };
  });
};

// Create a node for research
const researchNode = async (state) => {
  return traceAgentNode("research", "test-user", "test-session", async () => {
    const messages = state.messages;

    console.log("📚 RESEARCH NODE RUNNING");

    const startTime = Date.now();

    // Run the model for research
    const result = await chatModel.invoke([
      ...messages,
      new HumanMessage(
        "You are a research agent. Based on the user request and plan, " +
          "provide relevant information that would help address the request. " +
          "Be concise but thorough.",
      ),
    ]);

    const durationMs = Date.now() - startTime;

    // Record telemetry
    recordLLMGeneration(
      "azure-gpt-4",
      120, // Approximate token counts
      80,
      durationMs,
      messages,
      result,
    );

    // Return the updated state
    return {
      messages: [...messages, result],
      decision: "response", // Route to response node
    };
  });
};

// Create a node for final response
const responseNode = async (state) => {
  return traceAgentNode("response", "test-user", "test-session", async () => {
    const messages = state.messages;

    console.log("✍️ RESPONSE NODE RUNNING");

    const startTime = Date.now();

    // Run the model for final response
    const result = await chatModel.invoke([
      ...messages,
      new HumanMessage(
        "You are a final response agent. Based on the research and plan, " +
          "provide a concise, helpful response to the user's original request. " +
          "Be friendly and conversational.",
      ),
    ]);

    const durationMs = Date.now() - startTime;

    // Record telemetry
    recordLLMGeneration(
      "azure-gpt-4",
      150, // Approximate token counts
      100,
      durationMs,
      messages,
      result,
    );

    // Return the updated state with END to signal completion
    return {
      messages: [...messages, result],
      decision: END,
    };
  });
};

// ======================================
// Define Graph Structure
// ======================================
const builder = new StateGraph({ channels: graphState });

// Add nodes
builder.addNode("planner", plannerNode);
builder.addNode("research", researchNode);
builder.addNode("response", responseNode);

// Set the entry point
builder.setEntryPoint("planner");

// Add edges
builder.addEdge("planner", "research");
builder.addEdge("research", "response");

// Build the graph
const graph = builder.compile();

// ======================================
// Run the Graph
// ======================================
async function runGraphWithTracing(userQuery) {
  console.log(`\n=== RUNNING GRAPH FOR QUERY: "${userQuery}" ===\n`);

  // Record the start time for overall execution
  const startTime = Date.now();

  try {
    // Define initial state with user message
    const initialState = {
      messages: [new HumanMessage(userQuery)],
    };

    // Trace the entire execution
    const result = await traceOperation(
      "graph.execution",
      async () => graph.invoke(initialState),
      {
        input_query: userQuery,
        execution_type: "test",
      },
    );

    // Record overall execution telemetry
    const durationMs = Date.now() - startTime;
    recordGraphExecution(
      "test-graph",
      "test-user",
      "test-session",
      durationMs,
      { query_length: userQuery.length },
    );

    console.log("\n=== EXECUTION COMPLETED ===");

    // Extract just the AI messages for display
    const aiMessages = result.messages
      .filter((msg) => msg._getType() === "ai")
      .map((msg) => msg.content);

    console.log("\nAI RESPONSES:");
    aiMessages.forEach((content, i) => {
      console.log(`\n[Message ${i + 1}]:`);
      console.log(content);
    });

    console.log(`\nExecution time: ${durationMs}ms`);

    return result;
  } catch (error) {
    console.error("\n❌ ERROR DURING EXECUTION:");
    console.error(error);

    // Record error in telemetry
    const durationMs = Date.now() - startTime;
    recordGraphExecution(
      "test-graph-error",
      "test-user",
      "test-session",
      durationMs,
      {
        error: error.message,
        query: userQuery,
      },
    );
  }
}

// ======================================
// Main Execution
// ======================================
const testQueries = [
  "How can I improve my public speaking skills?",
  "What are effective time management strategies for busy professionals?",
];

async function main() {
  console.log("🚀 STARTING AZURE AI TRACING TEST WITH LANGGRAPH");

  for (const query of testQueries) {
    await runGraphWithTracing(query);
  }

  console.log("\n✅ TEST COMPLETED");

  // Allow time for telemetry to flush
  console.log("\nWaiting for telemetry to flush...");
  await new Promise((resolve) => setTimeout(resolve, 3000));
}

main().catch(console.error);
