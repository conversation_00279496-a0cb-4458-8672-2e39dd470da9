"use strict";

// Set up environment variables
process.env.NEXT_PUBLIC_AIRTABLE_API_KEY =
  "**********************************************************************************";
process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID = "appkOxteq7fiscoye";
process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID = "appg6TiMRFQVf0OXd";
process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID = "appbhSTOiO1EHDFrK";

// Required imports for LangGraph
const { AzureChatOpenAI } = require("@langchain/openai");
const {
  StateGraph,
  END,
  START,
  MessagesAnnotation,
} = require("@langchain/langgraph");
const { ToolNode } = require("@langchain/langgraph/prebuilt");
const { HumanMessage, AIMessage } = require("@langchain/core/messages");
const {
  StdioClientTransport,
} = require("@modelcontextprotocol/sdk/client/stdio.js");
const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const path = require("path");
const { z } = require("zod");
const { DynamicStructuredTool } = require("@langchain/core/tools");

// Azure OpenAI Configuration
const AZURE_OPENAI_ENDPOINT = "https://ft-gpt40mini.openai.azure.com/";
const AZURE_OPENAI_DEPLOYMENT = "gpt-4.1-nano";
const AZURE_OPENAI_API_VERSION = "2025-01-01-preview";
const AZURE_OPENAI_API_KEY = "********************************";

// Define correct Zod schemas for tools
const toolSchemas = {
  search_cycling_sessions: z.object({
    skillCategory: z.string().optional().nullable(),
    duration: z.string().optional().nullable(),
    limit: z
      .number()
      .int()
      .positive()
      .optional()
      .nullable()
      .default(10)
      .describe("Maximum number of records to return (default 10, max 100)."),
  }),
  search_cycling_segments: z.object({
    segmentId: z.string().optional().nullable(),
    sessionId: z.string().optional().nullable(),
    segmentOrder: z.string().optional().nullable(),
    duration: z.string().optional().nullable(),
    limit: z
      .number()
      .int()
      .positive()
      .optional()
      .nullable()
      .default(10)
      .describe("Maximum number of records to return (default 10, max 100)."),
  }),
  // Add minimal schemas for other tools
  default: z.object({}).describe("Default empty schema"),
};

function createAzureChatOpenAI(options = {}) {
  console.log(`[SimpleGraph] Creating AzureChatOpenAI with options:`, options);

  const instanceNameMatch = AZURE_OPENAI_ENDPOINT.match(
    /https:\/\/([^.]+)\.openai\.azure\.com/,
  );
  const instanceName = instanceNameMatch?.[1] || "ft-gpt40mini";

  console.log(`[SimpleGraph] Azure OpenAI Configuration:`);
  console.log(`  - API Version: ${AZURE_OPENAI_API_VERSION}`);
  console.log(`  - Deployment Name: ${AZURE_OPENAI_DEPLOYMENT}`);
  console.log(`  - Instance Name: ${instanceName}`);
  console.log(`  - Endpoint URL: ${AZURE_OPENAI_ENDPOINT}`);

  return new AzureChatOpenAI({
    azureOpenAIApiKey: AZURE_OPENAI_API_KEY,
    azureOpenAIApiVersion: AZURE_OPENAI_API_VERSION,
    modelName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiDeploymentName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiInstanceName: instanceName,
    temperature: 0,
    cache: true,
    maxRetries: 3,
    ...options,
  });
}

async function runSimpleLangGraph() {
  console.log("[SimpleGraph] Starting simplified LangGraph with MCP tools");
  let client = null;
  let transport = null;
  let mcpTools = [];

  try {
    // 1. Initialize the MCP server and client
    console.log("[SimpleGraph] Initializing MCP client...");

    const serverPath = path.join(
      process.cwd(),
      "mcp-server",
      "build",
      "server.js",
    );
    transport = new StdioClientTransport({
      command: "node",
      args: [serverPath],
      env: {
        ...process.env,
        NEXT_PUBLIC_AIRTABLE_API_KEY: process.env.NEXT_PUBLIC_AIRTABLE_API_KEY,
        NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID:
          process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID,
        NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID:
          process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID,
        NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID:
          process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID,
      },
      cwd: process.cwd(),
    });

    client = new Client({
      name: "langgraph-mcp-test",
      version: "1.0.0",
    });

    console.log("[SimpleGraph] Connecting to MCP server...");
    await client.connect(transport);
    console.log("[SimpleGraph] Connected to MCP server");

    // 2. List the tools available from MCP server
    console.log("[SimpleGraph] Listing available tools...");
    const listToolsResponse = await client.listTools();

    if (!listToolsResponse.tools || listToolsResponse.tools.length === 0) {
      throw new Error("No tools found on MCP server");
    }

    console.log(`[SimpleGraph] Found ${listToolsResponse.tools.length} tools`);

    // 3. Create a single tool for cycling sessions to simplify
    console.log("[SimpleGraph] Setting up cycling sessions tool...");

    // Find the tool in the list
    const cyclingSessionsTool = listToolsResponse.tools.find(
      (tool) => tool.name === "search_cycling_sessions",
    );

    if (!cyclingSessionsTool) {
      throw new Error("Cycling sessions tool not found in available tools");
    }

    // Create wrapped tool with proper Zod schema
    const wrappedTool = new DynamicStructuredTool({
      name: "search_cycling_sessions",
      description:
        cyclingSessionsTool.description ||
        "Search for cycling sessions based on criteria",
      schema: toolSchemas.search_cycling_sessions,
      func: async (args) => {
        console.log(
          `[SimpleGraph] Calling search_cycling_sessions with args:`,
          JSON.stringify(args),
        );
        try {
          // Explicitly call the tool with arguments
          const result = await client.callTool({
            name: "search_cycling_sessions",
            arguments: args || {},
          });

          // Format and log the result
          console.log(
            "[SimpleGraph] Tool result received:",
            typeof result === "object"
              ? JSON.stringify(result).slice(0, 200) + "..."
              : result.slice(0, 200) + "...",
          );

          // Convert to string if needed
          if (typeof result === "object") {
            return JSON.stringify(result);
          }
          return result;
        } catch (error) {
          console.error(
            "[SimpleGraph] Error calling cycling sessions tool:",
            error,
          );
          return `Error searching for cycling sessions: ${error.message}`;
        }
      },
    });

    mcpTools = [wrappedTool];
    console.log(
      "[SimpleGraph] Created wrapped cycling sessions tool with proper schema",
    );

    // 4. Create Azure OpenAI model and bind tool
    console.log("[SimpleGraph] Setting up Azure OpenAI with tools...");
    const llm = createAzureChatOpenAI({
      maxTokens: 1500,
      timeout: 25000, // 25 seconds timeout
    });

    const llmWithTools = llm.bindTools(mcpTools);
    console.log("[SimpleGraph] Created Azure OpenAI model with bound tools");

    // 5. Create LangGraph workflow
    console.log("[SimpleGraph] Creating LangGraph workflow...");

    // Define the LLM node
    const llmNode = async (state) => {
      console.log(
        `[SimpleGraph] LLM Node - Processing ${state.messages.length} messages`,
      );
      try {
        const response = await llmWithTools.invoke(state.messages);
        console.log(
          "[SimpleGraph] LLM Node - Generated response:",
          response.tool_calls
            ? `${response.tool_calls.length} tool calls`
            : response.content.slice(0, 100) + "...",
        );
        return { messages: [response] };
      } catch (error) {
        console.error("[SimpleGraph] Error in LLM node:", error);
        // Return a fallback message to avoid breaking the graph
        return {
          messages: [
            new AIMessage(
              "I'm having trouble processing your request right now. Let me try using the cycling sessions tool directly.",
            ),
          ],
        };
      }
    };

    // Create the workflow
    const workflow = new StateGraph(MessagesAnnotation)
      .addNode("llm", llmNode)
      .addNode("tools", new ToolNode(mcpTools))
      .addEdge(START, "llm")
      .addEdge("tools", "llm");

    // Add conditional routing
    workflow.addConditionalEdges(
      "llm",
      (state) => {
        const lastMessage = state.messages[state.messages.length - 1];
        if (
          lastMessage._getType() === "ai" &&
          lastMessage.tool_calls &&
          lastMessage.tool_calls.length > 0
        ) {
          console.log(
            "[SimpleGraph] Detected tool calls, routing to tools node",
          );
          return "tools";
        } else {
          console.log("[SimpleGraph] No tool calls, ending workflow");
          return END;
        }
      },
      {
        tools: "tools",
        [END]: END,
      },
    );

    // Compile the workflow
    const app = workflow.compile();
    console.log("[SimpleGraph] LangGraph workflow compiled");

    // 6. Run the workflow with a test query
    const testQuery =
      "What intermediate cycling sessions are available that are around 60 minutes long? Limit to 3 sessions.";
    console.log(`\n[SimpleGraph] Running test query: "${testQuery}"`);

    const result = await app.invoke({
      messages: [new HumanMessage(testQuery)],
    });

    // 7. Display results
    console.log("\n[SimpleGraph] Workflow execution completed");
    console.log(`[SimpleGraph] Final message count: ${result.messages.length}`);

    // Find the last AI message (the final response)
    const finalMessage = result.messages[result.messages.length - 1];
    console.log("\n[SimpleGraph] FINAL RESPONSE:");
    console.log(
      typeof finalMessage.content === "string"
        ? finalMessage.content
        : JSON.stringify(finalMessage.content, null, 2),
    );
  } catch (error) {
    console.error("[SimpleGraph] Error during execution:", error);
  } finally {
    // 8. Cleanup
    if (transport) {
      console.log("[SimpleGraph] Closing MCP connection...");
      try {
        await transport.close();
        console.log("[SimpleGraph] MCP connection closed");
      } catch (closeError) {
        console.error(
          "[SimpleGraph] Error closing MCP connection:",
          closeError,
        );
      }
    }
    console.log("[SimpleGraph] Test completed");
  }
}

// Run the example
runSimpleLangGraph().catch((error) => {
  console.error("Uncaught error:", error);
  process.exit(1);
});

// Handle process termination
process.on("SIGINT", async () => {
  console.log("[SimpleGraph] Received SIGINT, shutting down...");
  process.exit(0);
});
