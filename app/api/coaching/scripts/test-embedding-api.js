import fetch from "node-fetch";

async function testAzureEmbeddingAPI() {
  const apiKey = process.env.EMBEDDING_API_KEY;
  const endpoint = process.env.EMBEDDING_ENDPOINT;
  // AZURE_EMBEDDING_DEPLOYMENT_ID is the deployment ID of your embedding model in Azure OpenAI
  // This is often the same string that might be part of your endpoint URL for a specific deployment.
  const modelDeploymentId =
    process.env.AZURE_EMBEDDING_DEPLOYMENT_ID || "text-embedding-3-large"; // Default if not set

  console.log("--- Azure Embedding API Test Script ---");
  console.log("Attempting to use the following configuration:");
  console.log(
    `EMBEDDING_API_KEY: ${apiKey ? "***********" : "Not Set (CRITICAL)"}`,
  );
  console.log(`EMBEDDING_ENDPOINT: ${endpoint || "Not Set (CRITICAL)"}`);
  console.log(
    `AZURE_EMBEDDING_DEPLOYMENT_ID (for request body model): ${modelDeploymentId}`,
  );
  console.log("----------------------------------------");

  if (!apiKey || !endpoint) {
    console.error(
      "Error: EMBEDDING_API_KEY and EMBEDDING_ENDPOINT environment variables must be set.",
    );
    console.log("Please set them and try again.");
    console.log("Example:");
    console.log("  export EMBEDDING_API_KEY='your_api_key'");
    console.log("  export EMBEDDING_ENDPOINT='your_endpoint_url'");
    console.log("  export AZURE_EMBEDDING_DEPLOYMENT_ID='your_deployment_id'");
    return;
  }

  const sampleText = "This is a sample text to test embedding.";
  const requestBody = {
    input: sampleText,
    model: modelDeploymentId, // This is the deployment ID / model name Azure expects in the body
  };

  console.log("\nMaking API Call...");
  console.log(`Endpoint URL: ${endpoint}`);
  console.log("Request Headers:", {
    "Content-Type": "application/json",
    "api-key": apiKey ? "***********" : "Not Set",
  });
  console.log("Request Body:", JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    console.log("\n--- API Response ---");
    console.log(`Status Code: ${response.status}`);
    console.log(`Status Text: ${response.statusText}`);

    const responseBody = await response.text(); // Read as text first to avoid JSON parse error if not JSON
    console.log("Response Body (Raw):");
    console.log(responseBody);

    if (response.ok) {
      console.log("\nSuccessfully called the embedding API!");
      try {
        const jsonData = JSON.parse(responseBody);
        console.log("\nResponse Body (Parsed JSON):");
        console.log(JSON.stringify(jsonData, null, 2));
        if (jsonData.data && jsonData.data[0] && jsonData.data[0].embedding) {
          console.log(
            `Embedding vector (first 5 dimensions): ${jsonData.data[0].embedding.slice(0, 5).join(", ")}...`,
          );
        }
      } catch (e) {
        console.warn(
          "\nWarning: Response body was not valid JSON, printed raw body above.",
        );
      }
    } else {
      console.error(
        "\nError calling the embedding API. See status code and response body above for details.",
      );
      console.error("Common causes for 404 (Resource Not Found):");
      console.error(
        "  1. The EMBEDDING_ENDPOINT URL is incorrect (e.g., wrong resource name, region, or path).",
      );
      console.error(
        "     Verify this URL carefully against your Azure portal deployment details.",
      );
      console.error(
        "  2. The deployment ID specified in the EMBEDDING_ENDPOINT URL or in the AZURE_EMBEDDING_DEPLOYMENT_ID (used in the request body's 'model' field) is incorrect or does not exist for your Azure OpenAI resource.",
      );
      console.error(
        "  3. The API version in the EMBEDDING_ENDPOINT URL (if present as a query parameter like ?api-version=YYYY-MM-DD) is incompatible or incorrect for the operation/model.",
      );
      console.error(
        "  4. The EMBEDDING_API_KEY does not have permissions for the specified endpoint/deployment.",
      );
    }
  } catch (error) {
    console.error("\n--- Network or other error during API Call ---");
    console.error(error);
    console.error("Check your network connection and the endpoint URL.");
  }
}

testAzureEmbeddingAPI();
