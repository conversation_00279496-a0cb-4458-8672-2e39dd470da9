import { <PERSON><PERSON>raph, END } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { AIMessage, HumanMessage, ToolMessage } from "@langchain/core/messages";
import { DynamicTool } from "@langchain/core/tools";
import { createAzureChatOpenAI } from "../lib/services/azure-openai-service.ts";
import mcpToolsModule from "../tools/mcp-tools.cjs"; // .cjs can usually be imported by ESM
import path from "path";
import { fileURLToPath } from "url";

// Replicate __dirname if needed, though not currently used for critical logic
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

// Copied from app/api/coaching/lib/agents/prompts/strength-coach.prompt.ts
const strengthCoachPromptString = `You are a Strength Coach expert in resistance training, hypertrophy, and athletic performance. Your primary role is to provide precise, evidence-based advice tailored to the user.

**CRITICAL INSTRUCTION: INFORMATION RETRIEVAL & TOOL USAGE**
**Core Principle:** Your goal is to provide the most accurate and relevant information. You have access to a variety of specialized tools. For each user query, carefully analyze the request and the descriptions of ALL your available tools (using their exact provided names) to determine the best course of action.

1.  **For Factual Information & Explanations:**
    *   If the user asks for general factual information, explanations of concepts, research findings, or details about strength training principles, exercises, programming, or physiology (e.g., "What is RPE?", "Explain muscle hypertrophy", "Find research on X", "Tell me about Y"), you should **strongly prioritize using the specific tool named 'azure_cognitive_search'**. This is your primary tool for these types of queries.
    *   Do not rely solely on your internal knowledge for these types of broad informational queries. Ensure you use the tool named 'azure_cognitive_search'.

2.  **For Specific Data & Actions (including Sessions & Segments):**
    *   If the user's query implies a need for specific data (e.g., retrieving workout session details, segment information, user assessments, equipment recommendations) or requires a specific action (e.g., creating a program, generating a session plan), **consult the descriptions of all your available tools (using their exact provided names).**
    *   Select and use the most appropriate tool(s) to fulfill the request. This may include tools for searching specific types of data like training sessions or segments, performing assessments, generating plans, or recommending equipment.
    *   **For searching strength & conditioning sessions:** Use the 'search_sc_sessions' tool to find relevant strength training session templates.
    *   **For searching strength & conditioning segments:** Use the 'search_sc_segments' tool to find specific strength training segment templates for exercises, circuits, or workout components.

3.  **Synthesize & Respond:**
    *   Combine information from any tools used (search results, data from session/segment tools, assessment outputs, etc.) with your expertise.
    *   Connect your advice to the User Profile and Current Plan where relevant.
    *   Provide specific, actionable guidance.

4.  **Multi-Agent Coordination:**
    *   If responding as part of a sequence or following another coach's input, ensure your contribution adds unique strength-specific insights. Integrate information from any tools you deem necessary (based on the principles above, using their exact names) and relate it to the user's overall context and ongoing conversation.

**User Profile**
{{userProfileInfo}}
##

## Current Plan
{{currentPlanInfo}}
##

**General Workflow:**
1.  **Analyze User Question:** Determine if it's a general informational query, a request for specific data (like sessions, segments, assessments), or an action-oriented request (like plan creation).
2.  **Consult Tool Descriptions:** Review the descriptions of ALL your available tools, noting their exact names.
3.  **Prioritize the tool named 'azure_cognitive_search' (for general information):** If the query is broadly informational, use the tool explicitly named \`azure_cognitive_search\`.
4.  **Utilize Other Specific Tools (for data/actions):** If the query requires specific data retrieval (e.g., session details, segment data) or actions, use the most relevant specialized tool(s) based on their descriptions and exact names.
5.  **Synthesize & Respond:** Formulate your response using tool outputs and your expertise, tailored to the user.

**Available Tools:**
- **azure_cognitive_search:** MANDATORY FIRST STEP for all informational/explanatory questions about strength training. Use this to find information on strength training principles, exercises, programming, or physiology. Input should be a concise search query.
- assess_strength: Evaluate strength capabilities and provide assessment. Input should be user data or observations relevant to strength.
- create_strength_program: Create training programs tailored to user goals. Input should be user goals, current fitness level, and any constraints.
- strength_equipment: Recommend appropriate strength training equipment. Input should be user needs or context for equipment.
- **search_sc_sessions:** Find complete strength & conditioning workout sessions when the user wants training templates or session plans. Input should be a descriptive query for the type of session needed (e.g., "beginner full body strength", "hypertrophy leg day").
- **search_sc_segments:** Find specific strength & conditioning workout segments or exercise clusters when needed. Input should be a descriptive query for the type of segment (e.g., "squat variations", "core stability exercises").

**MCP Tools for Strength Coach:**
- Use 'search_sc_sessions' to find complete strength & conditioning workout sessions. This is helpful when the user asks about full workouts, training templates, or complete session plans.
- Use 'search_sc_segments' to find specific strength & conditioning workout segments or exercise clusters. This is helpful when the user asks about specific exercises, movement patterns, or targeted routines for particular muscle groups.

**Remember: Your primary responsibility is to leverage your available tools effectively by using their EXACT provided names. Always refer to tool descriptions to make informed decisions about which tool to use.**`;

const userProfilePlaceholder =
  "User is a 30-year-old male, intermediate lifter, looking to increase overall strength and muscle mass. Trains 3-4 times a week. No known injuries.";
const currentPlanPlaceholder =
  "Currently on a 3-day full-body split. Week 2 of 8. Focusing on compound lifts.";

const channels = {
  messages: {
    value: (x, y) => x.concat(y),
    default: () => [],
  },
  userProfileInfo: {
    value: (x, y) => y,
    default: () => userProfilePlaceholder,
  },
  currentPlanInfo: {
    value: (x, y) => y,
    default: () => currentPlanPlaceholder,
  },
};

async function strengthAgentNode(state) {
  console.log("\n--- Calling Strength Agent Node ---");
  const { messages, userProfileInfo, currentPlanInfo } = state;

  const populatedPrompt = strengthCoachPromptString
    .replace("{{userProfileInfo}}", userProfileInfo)
    .replace("{{currentPlanInfo}}", currentPlanInfo);

  const llmMessages = [new HumanMessage(populatedPrompt)];
  messages.forEach((msg) => {
    if (msg._getType() === "human" || msg._getType() === "tool") {
      llmMessages.push(msg);
    } else if (msg._getType() === "ai" && msg.content) {
      if (!msg.tool_calls || msg.tool_calls.length === 0) {
        llmMessages.push(new AIMessage(msg.content));
      }
    }
  });

  if (
    llmMessages.length > 1 &&
    llmMessages[llmMessages.length - 1]._getType() === "human" &&
    llmMessages[0]._getType() === "human"
  ) {
    const systemP = llmMessages[0].content;
    const currentConversation = llmMessages.slice(1);
    const response = await llmWithTools.invoke([
      new HumanMessage(systemP),
      ...currentConversation,
    ]);
    return { messages: [response] };
  } else {
    const lastUserMessage = messages.findLast((m) => m._getType() === "human");
    const systemP = new HumanMessage(populatedPrompt);
    const conversationMessages = [
      ...messages.filter(
        (m) => m._getType() !== "human" && m._getType() !== "system",
      ),
      lastUserMessage,
    ].filter(Boolean);
    const response = await llmWithTools.invoke([
      systemP,
      ...conversationMessages,
    ]);
    return { messages: [response] };
  }
}

function shouldCallTools(state) {
  console.log("\n--- Deciding: Should Call Tools? ---");
  const { messages } = state;
  const lastMessage = messages[messages.length - 1];
  if (
    lastMessage &&
    lastMessage.tool_calls &&
    lastMessage.tool_calls.length > 0
  ) {
    console.log("Decision: Yes, call tools.");
    return "call_tools";
  }
  console.log("Decision: No, finish.");
  return END;
}

let llmWithTools;
const allTestTools = [];

async function setupLlmAndTools() {
  const mockAzureSearch = new DynamicTool({
    name: "azure_cognitive_search",
    description:
      "MANDATORY FIRST STEP for all informational/explanatory questions about strength training...",
    func: async (input) => {
      console.log(
        `[MockTool azure_cognitive_search] Called with: ${JSON.stringify(input)}`,
      );
      return "Mocked Azure Cognitive Search results: Found information about the topic.";
    },
  });
  allTestTools.push(mockAzureSearch);

  const mockAssessStrength = new DynamicTool({
    name: "assess_strength",
    description: "Evaluate strength capabilities and provide assessment...",
    func: async (input) => {
      console.log(
        `[MockTool assess_strength] Called with: ${JSON.stringify(input)}`,
      );
      return "Mocked strength assessment: User is strong.";
    },
  });
  allTestTools.push(mockAssessStrength);

  const mockCreateProgram = new DynamicTool({
    name: "create_strength_program",
    description: "Create training programs tailored to user goals...",
    func: async (input) => {
      console.log(
        `[MockTool create_strength_program] Called with: ${JSON.stringify(input)}`,
      );
      return "Mocked strength program: Program created with 3 sets of 5 reps.";
    },
  });
  allTestTools.push(mockCreateProgram);

  const mockStrengthEquipment = new DynamicTool({
    name: "strength_equipment",
    description: "Recommend appropriate strength training equipment...",
    func: async (input) => {
      console.log(
        `[MockTool strength_equipment] Called with: ${JSON.stringify(input)}`,
      );
      return "Mocked equipment recommendation: A barbell and squat rack are recommended.";
    },
  });
  allTestTools.push(mockStrengthEquipment);

  console.log("[TestScript] Attempting to get MCP client to start server...");
  await mcpToolsModule.getClient();
  console.log("[TestScript] MCP client obtained/server started.");

  console.log("[TestScript] Attempting to get MCP tools...");
  const mcpTools = await mcpToolsModule.getTools();
  if (!mcpTools || Object.keys(mcpTools).length === 0) {
    console.error("[TestScript] Failed to get MCP tools from mcpToolsModule!");
    throw new Error("MCP Tools not available for test script");
  }
  console.log(
    "[TestScript] MCP Tools loaded:",
    Object.keys(mcpTools).join(", "),
  );

  if (mcpTools.search_sc_sessions) {
    allTestTools.push(
      new DynamicTool({
        name: "search_sc_sessions",
        description:
          "Find complete strength & conditioning workout sessions. Input: descriptive query.",
        func: async (args) => {
          console.log(
            `[MCPTool search_sc_sessions] Called with: ${typeof args === "object" ? JSON.stringify(args) : args}`,
          );
          if (typeof args === "string") {
            return mcpTools.search_sc_sessions({ query: args });
          }
          return mcpTools.search_sc_sessions(args || {});
        },
      }),
    );
  } else {
    console.warn("[TestScript] MCP tool 'search_sc_sessions' not found!");
  }

  if (mcpTools.search_sc_segments) {
    allTestTools.push(
      new DynamicTool({
        name: "search_sc_segments",
        description:
          "Find specific strength & conditioning workout segments. Input: descriptive query.",
        func: async (args) => {
          console.log(
            `[MCPTool search_sc_segments] Called with: ${typeof args === "object" ? JSON.stringify(args) : args}`,
          );
          if (typeof args === "string") {
            return mcpTools.search_sc_segments({ query: args });
          }
          return mcpTools.search_sc_segments(args || {});
        },
      }),
    );
  } else {
    console.warn("[TestScript] MCP tool 'search_sc_segments' not found!");
  }

  console.log(
    "[TestScript] All tools prepared for LLM binding:",
    allTestTools.map((t) => t.name).join(", "),
  );

  const llm = createAzureChatOpenAI({
    temperature: 0.1,
    maxTokens: 1500,
  });
  llmWithTools = llm.bindTools(allTestTools);
}

const workflow = new StateGraph({ channels });
workflow.addNode("strength_agent", strengthAgentNode);
const toolExecutorNode = new ToolNode([]);
workflow.addNode("call_tools", toolExecutorNode);

workflow.setEntryPoint("strength_agent");
workflow.addConditionalEdges("strength_agent", shouldCallTools, {
  call_tools: "call_tools",
  [END]: END,
});
workflow.addEdge("call_tools", "strength_agent");

async function main() {
  console.log("[TestScript] Starting Strength Coach MCP Args Test...");
  try {
    await setupLlmAndTools();
    toolExecutorNode.tools = allTestTools;

    const app = workflow.compile();
    console.log("[TestScript] Compiled test graph.");

    const userQuery =
      "Find some strength and conditioning sessions for intermediate athletes focusing on power.";
    console.log(`\n[TestScript] Invoking graph with query: "${userQuery}"`);

    const initialState = {
      messages: [new HumanMessage(userQuery)],
      userProfileInfo: userProfilePlaceholder,
      currentPlanInfo: currentPlanPlaceholder,
    };

    const result = await app.invoke(initialState, { recursionLimit: 10 });

    console.log("\n[TestScript] Graph invocation finished.");
    console.log("\n--- Final Result ---");
    console.log(JSON.stringify(result, null, 2));

    const lastAiMessage = result.messages.findLast(
      (m) => m._getType() === "ai",
    );
    if (
      lastAiMessage &&
      lastAiMessage.tool_calls &&
      lastAiMessage.tool_calls.length > 0
    ) {
      console.log("\n--- Tool Calls in Last AI Message ---");
      lastAiMessage.tool_calls.forEach((tc) => {
        console.log(`Tool: ${tc.name}, Args: ${JSON.stringify(tc.args)}`);
      });
    } else if (lastAiMessage) {
      console.log("\n--- Final AI Response ---");
      console.log(lastAiMessage.content);
    }
  } catch (e) {
    console.error("\n[TestScript] Error during main execution:", e);
  } finally {
    console.log("\n[TestScript] Attempting to clean up MCP resources...");
    try {
      await mcpToolsModule.cleanup();
      console.log("[TestScript] MCP cleanup successful.");
    } catch (cleanupError) {
      console.error("[TestScript] Error during MCP cleanup:", cleanupError);
    }
    console.log("[TestScript] Test finished.");
  }
}

main();
