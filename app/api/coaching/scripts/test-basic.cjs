"use strict";

// Super basic script just to test execution
console.log("Hello from test-basic.cjs");
console.log("Node version:", process.version);
console.log("CWD:", process.cwd());

try {
  console.log("Trying to require some packages:");
  console.log("- require('path'):", require("path") ? "loaded" : "failed");
  console.log("- require('fs'):", require("fs") ? "loaded" : "failed");
  console.log("- process.env:", process.env.NODE_ENV || "not set");
} catch (error) {
  console.error("Error loading basic packages:", error);
}

console.log("<PERSON><PERSON><PERSON> completed successfully!");
