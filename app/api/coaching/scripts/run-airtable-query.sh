#!/bin/bash

# Helper script to run Airtable MCP queries

SCRIPT_PATH="app/api/coaching/tools/test-strength-agent-mcp-args.cjs"

if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  node "$SCRIPT_PATH" --help
  exit 0
fi

# If no arguments provided, show a menu
if [ -z "$1" ]; then
  echo "Choose a query to run:"
  echo "1) List all Airtable bases"
  echo "2) List tables in the 'Strength and Conditioning' base"
  echo "3) List records from 'S&C Sessions' table"
  echo "4) Custom query (you'll be prompted to enter)"
  echo ""
  read -p "Enter choice [1-4]: " choice
  
  case $choice in
    1)
      query="List all my Airtable bases."
      ;;
    2)
      query="List all tables in the Strength and Conditioning base with ID appbhSTOiO1EHDFrK."
      ;;
    3)
      query="Get the first 5 records from the S&C Sessions table in the Strength and Conditioning base."
      ;;
    4)
      echo ""
      read -p "Enter your custom query: " query
      ;;
    *)
      echo "Invalid choice. Using default query."
      query="List all my Airtable bases."
      ;;
  esac
else
  # Use the provided query
  query="$*"
fi

echo "Running query: $query"
echo "-------------------------------------------"
node "$SCRIPT_PATH" "$query" 