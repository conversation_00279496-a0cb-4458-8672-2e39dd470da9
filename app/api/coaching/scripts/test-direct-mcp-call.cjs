"use strict";

// Add environment variables from .env.local for Airtable (ensure MCP server has necessary env vars if needed)
process.env.NEXT_PUBLIC_AIRTABLE_API_KEY =
  "**********************************************************************************";
process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID = "appkOxteq7fiscoye";
process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID = "appg6TiMRFQVf0OXd";
process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID = "appbhSTOiO1EHDFrK";

// Use the direct MCP SDK rather than Lang<PERSON>hain adapter
const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const {
  StdioClientTransport,
} = require("@modelcontextprotocol/sdk/client/stdio.js");
const path = require("path");

// --- Main Test Function ---
async function testDirectMcp() {
  console.log("[DirectTest] Starting direct MCP test");
  let client = null;
  let transport = null;

  try {
    // 1. Create the transport pointing to the MCP server script
    const serverPath = path.join(
      process.cwd(),
      "mcp-server",
      "build",
      "server.js",
    );
    console.log(`[DirectTest] Using MCP server at: ${serverPath}`);

    // Instead of creating our own serverProcess, let the StdioClientTransport handle it
    transport = new StdioClientTransport({
      command: "node",
      args: [serverPath],
      env: {
        ...process.env,
        NEXT_PUBLIC_AIRTABLE_API_KEY: process.env.NEXT_PUBLIC_AIRTABLE_API_KEY,
        NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID:
          process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID,
        NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID:
          process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID,
        NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID:
          process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID,
      },
      cwd: process.cwd(),
    });

    // 2. Create the MCP client
    console.log("[DirectTest] Creating MCP Client");
    client = new Client({
      name: "direct-mcp-test",
      version: "1.0.0",
    });

    // 3. Connect to the server
    console.log("[DirectTest] Connecting to MCP server...");
    await client.connect(transport);
    console.log("[DirectTest] Connected successfully to MCP server");

    // 4. List available tools
    console.log("[DirectTest] Listing available tools...");
    const listToolsResponse = await client.listTools();
    console.log(
      `[DirectTest] Found ${listToolsResponse.tools?.length || 0} tools:`,
    );

    if (listToolsResponse.tools && listToolsResponse.tools.length > 0) {
      listToolsResponse.tools.forEach((tool, index) => {
        console.log(
          `   [${index + 1}] ${tool.name}: ${tool.description || "No description"}`,
        );
      });
    }

    // 5. Find the cycling sessions tool
    const targetToolName = "search_cycling_sessions"; // Note: no mcp__athlea__ prefix here
    const targetTool = listToolsResponse.tools?.find(
      (t) => t.name === targetToolName,
    );

    if (!targetTool) {
      throw new Error(
        `Tool "${targetToolName}" not found in MCP server tools.`,
      );
    }

    console.log(`[DirectTest] Found target tool: ${targetTool.name}`);
    console.log(
      `[DirectTest] Tool input schema: ${JSON.stringify(targetTool.inputSchema || {})}`,
    );

    // 6. Call the tool with arguments
    const toolArgs = {
      skillCategory: "Intermediate",
      duration: "60",
      limit: 3,
    };

    console.log(
      `[DirectTest] Calling tool with arguments: ${JSON.stringify(toolArgs)}`,
    );
    const toolResponse = await client.callTool({
      name: targetToolName,
      arguments: toolArgs,
    });

    console.log("\n[DirectTest] Tool response:");
    console.log(JSON.stringify(toolResponse, null, 2));
  } catch (error) {
    console.error("[DirectTest] Error during execution:", error);
  } finally {
    // 7. Cleanup
    console.log("\n[DirectTest] Cleaning up resources...");

    try {
      if (client && transport) {
        console.log("[DirectTest] Closing MCP client connection...");
        await transport.close();
        console.log("[DirectTest] MCP client connection closed");
      }
    } catch (clientError) {
      console.error("[DirectTest] Error closing MCP client:", clientError);
    }

    console.log("[DirectTest] Test finished");
  }
}

// --- Run the Test ---
testDirectMcp();

// --- Process Exit Handlers (Optional but recommended) ---
process.on("uncaughtException", (error) => {
  console.error("[DirectTest] Uncaught exception:", error);
  process.exit(1);
});

process.on("SIGINT", () => {
  console.log("[DirectTest] SIGINT received - shutting down");
  process.exit(0);
});
