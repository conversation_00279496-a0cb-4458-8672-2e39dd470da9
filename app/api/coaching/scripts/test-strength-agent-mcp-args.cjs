"use strict";

// Set the Airtable API Key from user input
process.env.AIRTABLE_API_KEY =
  "**********************************************************************************";

const { StateGraph, END } = require("@langchain/langgraph");
const { ToolNode } = require("@langchain/langgraph/prebuilt");
const { AIMessage, HumanMessage } = require("@langchain/core/messages");
const { AzureChatOpenAI } = require("@langchain/openai");
const { z } = require("zod");
const { DynamicStructuredTool } = require("@langchain/core/tools");
const { MultiServerMCPClient } = require("@langchain/mcp-adapters"); // Using the adapter

// --- Define Zod Schemas for airtable-mcp-server Tools ---
const airtableToolSchemas = {
  // Based on typical Airtable operations, common tools might include:
  // The actual names will be prefixed by the adapter, e.g., mcp__airtable-mcp-server__list_bases
  // We will use the exact name fetched from the server when wrapping.
  list_bases: z
    .object({})
    .describe("Lists all Airtable bases accessible to the API key."),
  list_tables: z
    .object({
      baseId: z.string().describe("The ID of the Airtable base."),
    })
    .describe("Lists all tables in a specified Airtable base."),
  list_records: z
    .object({
      baseId: z.string().describe("The ID of the Airtable base."),
      tableId: z
        .string()
        .describe("The ID or name of the table within the base."),
      maxRecords: z
        .number()
        .int()
        .positive()
        .optional()
        .describe("Maximum number of records to return."),
    })
    .describe("Lists records from a specified table in an Airtable base."),
};

// Configure Azure OpenAI (remains the same)
function createAzureChatOpenAI(options = {}) {
  console.log(
    `[TestAzureOpenAI] Creating AzureChatOpenAI with options:`,
    options,
  );
  const AZURE_OPENAI_ENDPOINT =
    process.env.AZURE_OPENAI_ENDPOINT ||
    "https://ft-gpt40mini.openai.azure.com/";
  const AZURE_OPENAI_DEPLOYMENT =
    process.env.AZURE_DEPLOYMENT_NAME || "gpt-4.1-nano";
  const AZURE_OPENAI_API_VERSION = "2025-01-01-preview";
  const AZURE_OPENAI_API_KEY =
    process.env.AZURE_OPENAI_API_KEY || "********************************";
  const instanceNameMatch = AZURE_OPENAI_ENDPOINT.match(
    /https:\/\/([^.]+)\.openai\.azure\.com/,
  );
  const instanceName = instanceNameMatch?.[1] || "ft-gpt40mini";
  console.log(`[TestAzureOpenAI] Configuration:`);
  console.log(`  - API Version: ${AZURE_OPENAI_API_VERSION}`);
  console.log(`  - Deployment Name: ${AZURE_OPENAI_DEPLOYMENT}`);
  console.log(`  - Instance Name: ${instanceName}`);
  console.log(`  - Endpoint URL: ${AZURE_OPENAI_ENDPOINT}`);
  return new AzureChatOpenAI({
    azureOpenAIApiKey: AZURE_OPENAI_API_KEY,
    azureOpenAIApiVersion: AZURE_OPENAI_API_VERSION,
    modelName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiDeploymentName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiInstanceName: instanceName,
    temperature: 0.0,
    cache: true,
    maxRetries: 3,
    ...options,
  });
}

// Simplified Prompt for Airtable Agent
const airtableAgentPromptString =
  "You are an AI assistant that can interact with Airtable using provided tools.\n" +
  "Based on the user's request, decide which Airtable tool to use.\n" +
  "Available Tools (names might be prefixed, use the name the LLM provides in tool_calls):\n" +
  "- list_bases: Lists all Airtable bases.\n" +
  "- list_tables: Lists tables in a given base (requires baseId).\n" +
  "- list_records: Lists records in a given table (requires baseId and tableId).\n";

// Node that invokes the LLM agent
async function agentNode(state) {
  console.log("\\n--- Calling Agent Node ---");
  const { messages } = state;
  const populatedPrompt = airtableAgentPromptString;
  if (messages.length === 0) {
    return { messages: [new AIMessage("Error: Conversation state is empty.")] };
  }
  const llmMessages = [new HumanMessage(populatedPrompt), ...messages];
  console.log("[Agent Node] Invoking LLM with Airtable tools...");
  const response = await llmWithTools.invoke(llmMessages);
  console.log("[Agent Node] LLM Response (structure check):", {
    id: response.id,
    type: response._getType(),
    tool_calls: response.tool_calls ? response.tool_calls.length : 0,
  });
  if (response.tool_calls && response.tool_calls.length > 0) {
    console.log("[Agent Node] LLM generated tool calls:");
    response.tool_calls.forEach((call, index) => {
      console.log(
        `  - Call #${index + 1}: Name=${call.name}, Args=${JSON.stringify(call.args)}`,
      );
    });
  }
  return { messages: [response] };
}

let llmWithTools;
let mcpAdapterClient = null;
let finalWrappedTools = [];

async function setupLlmAndTools() {
  console.log(
    "[TestScript] Setting up LLM & Tools (airtable-mcp + Re-Wrap)...",
  );

  // 1. Initialize MCP Adapter Client for airtable-mcp-server
  console.log(
    "[TestScript] Initializing MultiServerMCPClient for airtable-mcp-server...",
  );
  if (
    !process.env.AIRTABLE_API_KEY ||
    process.env.AIRTABLE_API_KEY === "YOUR_AIRTABLE_API_KEY_HERE"
  ) {
    // This check remains, but we've set the key at the top now.
    console.warn(
      "Warning: AIRTABLE_API_KEY is using a placeholder or was not set. Ensure it is correctly provided.",
    );
    // No longer throwing an error here to allow the script to attempt connection.
  }

  mcpAdapterClient = new MultiServerMCPClient({
    "airtable-mcp-server": {
      // Use the server name as key for MultiServerMCPClient
      transport: "stdio",
      command: "/Users/<USER>/.nvm/versions/node/v20.18.0/bin/node",
      args: [
        "/Users/<USER>/.nvm/versions/node/v20.18.0/bin/npx",
        "-y",
        "airtable-mcp-server",
      ],
      env: {
        AIRTABLE_API_KEY: process.env.AIRTABLE_API_KEY,
        PATH: process.env.PATH, // Pass the current PATH environment
      },
    },
  });

  console.log(
    "[TestScript] Waiting briefly for airtable-mcp-server to start (via npx)...",
  );
  await new Promise((resolve) => setTimeout(resolve, 10000)); // Increased delay for npx to download and start

  // 2. Get Tools from the Adapter
  console.log(
    "[TestScript] Getting tools from MCP Adapter (airtable-mcp-server)...",
  );
  let adapterProvidedTools = [];
  try {
    adapterProvidedTools = await mcpAdapterClient.getTools();
    if (!adapterProvidedTools || adapterProvidedTools.length === 0) {
      throw new Error("Adapter returned no tools initially.");
    }
    console.log(
      `[TestScript] Adapter provided ${adapterProvidedTools.length} tools initially.`,
    );
  } catch (getToolsError) {
    console.error(
      "[TestScript] Error calling adapter.getTools():",
      getToolsError,
    );
    console.error(
      "[TestScript] Initial adapter.getTools() failed. The underlying issue (like 'npx' not found) must be fixed. Original error message:",
      getToolsError.message,
    );
    throw new Error(
      `Could not retrieve tools from MCP Adapter for airtable-mcp-server. Original error: ${getToolsError.message}. Please check if 'npx' is installed and in your PATH, and that the server can be started.`,
    );
  }

  console.log(
    `[TestScript] Raw tools from Adapter: ${adapterProvidedTools.map((t) => `${t.name}${t.description ? ': "' + t.description.slice(0, 30) + '..."' : ""}`).join(",\n    ")}`,
  );

  // 3. Re-wrap with correct Zod schemas, delegating to original tool.invoke
  finalWrappedTools = [];
  for (const localSchemaName of Object.keys(airtableToolSchemas)) {
    const adapterTool = adapterProvidedTools.find((t) =>
      t.name.endsWith(`__${localSchemaName}`),
    );
    if (!adapterTool) {
      console.warn(
        `[TestScript] Tool for local schema "${localSchemaName}" (expected suffix __${localSchemaName}) not found in adapter tools. Skipping.`,
      );
      continue;
    }
    const zodSchema = airtableToolSchemas[localSchemaName];
    if (!zodSchema) {
      console.warn(
        `[TestScript] No local Zod schema found for "${localSchemaName}", skipping re-wrap.`,
      );
      continue;
    }
    console.log(
      `[TestScript] Re-wrapping adapter tool "${adapterTool.name}" as "${localSchemaName}" (for LLM) with correct Zod schema.`,
    );
    finalWrappedTools.push(
      new DynamicStructuredTool({
        name: adapterTool.name,
        description:
          adapterTool.description ||
          zodSchema.description ||
          `Invoke ${localSchemaName}`,
        schema: zodSchema,
        func: async (args) => {
          console.log(
            `---> [ReWrapped ${adapterTool.name}] Invoking original adapter tool with Zod-validated args:`,
            JSON.stringify(args),
          );
          try {
            const result = await adapterTool.invoke(args);
            console.log(
              `---> [ReWrapped ${adapterTool.name}] Original tool invocation successful. Result (first 300 chars):`,
              String(result).slice(0, 300) +
                (String(result).length > 300 ? "..." : ""),
            );
            return String(result);
          } catch (invokeError) {
            console.error(
              `---> [ReWrapped ${adapterTool.name}] Error during original tool.invoke():`,
              invokeError,
            );
            return `Error invoking ${adapterTool.name} via adapter: ${invokeError.message}`;
          }
        },
      }),
    );
  }

  if (finalWrappedTools.length === 0) {
    throw new Error(
      "No tools could be re-wrapped with Zod schemas. Check tool names and adapter output.",
    );
  }
  console.log(
    `[TestScript] Successfully re-wrapped ${finalWrappedTools.length} tools: ${finalWrappedTools.map((t) => t.name).join(", ")}`,
  );

  // 4. Setup LLM and Bind the final wrapped tools
  console.log("[TestScript] Creating Azure LLM...");
  const llm = createAzureChatOpenAI({
    temperature: 0.0,
    maxTokens: 1500,
    timeout: 45000,
  });

  console.log("[TestScript] Binding re-wrapped tools to LLM...");
  llmWithTools = llm.bindTools(finalWrappedTools);
  console.log("[TestScript] LLM with re-wrapped tools set up.");
}

// --- Graph Definition and Main Execution ---
async function main() {
  // Get query from command line arguments or use default
  const userQuery = process.argv[2] || "List all my Airtable bases.";

  console.log(
    "[TestScript] Starting Airtable Test (airtable-mcp + Re-Wrap)...",
  );
  try {
    await setupLlmAndTools();
    console.log(
      "[TestScript] Creating StateGraph with re-wrapped Airtable tools...",
    );
    const channels = {
      messages: { value: (x, y) => x.concat(y), default: () => [] },
    };
    const workflow = new StateGraph({ channels });
    workflow.addNode("agent", agentNode);
    console.log(
      "[TestScript] Tools being passed to ToolNode:",
      finalWrappedTools.map((t) => t.name),
    );
    const toolNode = new ToolNode(finalWrappedTools);
    workflow.addNode("tools", toolNode);
    workflow.setEntryPoint("agent");
    workflow.addConditionalEdges(
      "agent",
      (state) => {
        const lastMessage = state.messages[state.messages.length - 1];
        if (
          lastMessage &&
          lastMessage._getType() === "ai" &&
          lastMessage.tool_calls &&
          lastMessage.tool_calls.length > 0
        ) {
          console.log("[Graph] Agent calling tools. Routing to tools node.");
          return "tools";
        }
        console.log(
          "[Graph] Agent responded directly or no tools. Routing to END.",
        );
        return END;
      },
      { tools: "tools", [END]: END },
    );
    workflow.addEdge("tools", "agent");
    const app = workflow.compile();
    console.log("[TestScript] Compiled workflow graph successfully.");
    console.log(`[TestScript] Invoking graph with query: "${userQuery}"`);
    const initialState = { messages: [new HumanMessage(userQuery)] };
    const result = await app.invoke(initialState, {
      recursionLimit: 10,
      timeout: 45000,
    });
    console.log(
      "\\n[TestScript] Graph invocation finished. Final State:",
      JSON.stringify(result, null, 2),
    );
    const finalMessages = result.messages || [];
    const lastAiMessage = finalMessages.findLast((m) => m._getType() === "ai");
    if (lastAiMessage) {
      console.log("\\n--- Final AI Response ---");
      console.log(lastAiMessage.content);
      if (lastAiMessage.tool_calls && lastAiMessage.tool_calls.length > 0) {
        console.log(
          "--- Final AI Tool Calls (should be empty if response is final) ---",
        );
        console.log(JSON.stringify(lastAiMessage.tool_calls, null, 2));
      }
    } else {
      console.log("\\n--- No final AI message found in state ---");
    }
  } catch (e) {
    console.error(
      "\\n[TestScript] Error during main execution:",
      e.name,
      e.message,
    );
    if (e.lc_error_code)
      console.error("LangChain Error Code:", e.lc_error_code);
    if (e.stack && !e.message.includes("recursion limit"))
      console.error("[TestScript] Stack trace:", e.stack);
  } finally {
    console.log("\\n[TestScript] Attempting to clean up MCP adapter client...");
    if (mcpAdapterClient) {
      try {
        await mcpAdapterClient.close();
        console.log("[TestScript] MCP adapter client closed successfully.");
      } catch (cleanupError) {
        console.error(
          "[TestScript] Error closing MCP adapter client:",
          cleanupError,
        );
      }
    }
    console.log("[TestScript] Test finished.");
  }
}

// Additional help message if run with --help
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log(`
Usage: node ${process.argv[1]} [query]

Examples:
  node ${process.argv[1]}
    Runs with default query: "List all my Airtable bases."
  
  node ${process.argv[1]} "List tables in base appbhSTOiO1EHDFrK"
    Runs with a custom query to list tables in a specific base.
  
  node ${process.argv[1]} "Get records from the Strength Sessions table in the Strength and Conditioning base"
    Runs a query to fetch records from a specific table.
  `);
  process.exit(0);
}

main();

// --- Process Exit Handlers ---
let isCleaningUp = false;
async function cleanup() {
  if (isCleaningUp) return;
  isCleaningUp = true;
  console.log("[TestScript] Cleaning up MCP adapter client on exit...");
  if (mcpAdapterClient) {
    try {
      await mcpAdapterClient.close();
      console.log("[TestScript] MCP adapter client closed on exit.");
    } catch (e) {
      console.error("Exit cleanup error (MCP Adapter):", e);
    }
  }
}
process.on("uncaughtException", async (error) => {
  console.error("Uncaught Exception:", error);
  await cleanup();
  process.exit(1);
});
process.on("exit", () => {
  console.log("[TestScript] Process exiting.");
});
process.on("SIGINT", async () => {
  console.log("SIGINT received - shutting down");
  await cleanup();
  process.exit(0);
});
