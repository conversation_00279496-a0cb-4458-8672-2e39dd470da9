// test-azure-search-graph.mjs
// This script now assumes environment variables are pre-loaded by a -r script (e.g., preload-env.mjs)
// or via a .env.test file for interactive mode.

import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import fs from "fs";
import readline from "readline";
import { AzureSearchGraphTool } from "../lib/tools/azureSearchGraphTool.ts";

// Attempt to load .env.test if not pre-loaded, useful for direct interactive execution
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const envTestPath = join(__dirname, "../../../../", ".env.test");

if (!process.env.AZURE_SEARCH_API_KEY) {
  // Check if a key var isn't set, indicating preload might not have run
  console.log(
    `[TestScript] Core env vars not detected. Attempting to load from ${envTestPath}`,
  );
  if (fs.existsSync(envTestPath)) {
    const result = dotenv.config({ path: envTestPath });
    if (result.error) {
      console.warn(
        `[TestScript] Error loading .env.test file: ${result.error.message}`,
      );
    }
    if (result.parsed) {
      console.log(
        "[TestScript] ✅ Successfully loaded environment variables from .env.test as a fallback.",
      );
    } else {
      console.warn(
        `[TestScript] .env.test file found at ${envTestPath} but it might be empty or invalid.`,
      );
    }
  } else {
    console.warn(
      `[TestScript] Warning: .env.test file not found at ${envTestPath}. Critical environment variables might be missing if not pre-loaded. `,
    );
  }
} else {
  console.log(
    "[TestScript] Environment variables seem to be pre-loaded or already set.",
  );
}

// Parse command line arguments
const args = process.argv.slice(2);
const argOptions = {
  query: "benefits of high intensity interval training",
  noInteractive: false,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === "--query" && i + 1 < args.length) {
    argOptions.query = args[i + 1];
    i++;
  } else if (args[i] === "--no-interactive") {
    argOptions.noInteractive = true;
  } else if (args[i] === "--help") {
    console.log(`
Usage: tsx -r ./preload-env.mjs test-azure-search-graph.mjs --query <text> [--no-interactive]
Or for interactive mode (relies on .env.test potentially):
Usage: tsx test-azure-search-graph.mjs

Options:
  --query <text>      Run a single query with the specified text (required if --no-interactive)
  --no-interactive    Disable interactive mode (will run the query and exit)
  --help              Show this help message
    `);
    process.exit(0);
  }
}

// Main function to process a single query
async function processSingleQuery(queryText) {
  const originalConsoleLog = console.log;
  if (argOptions.noInteractive) {
    // In non-interactive mode (called by other script), redirect logs to stderr
    // to keep stdout clean for the JSON output.
    console.log = (...args) => console.error("[LOG]", ...args);
  }

  console.log(`[TestScript] Processing query: "${queryText}"`);

  try {
    console.log("[TestScript] Instantiating AzureSearchGraphTool...");
    // AzureSearchGraphTool and its children (AzureSearchTool, agents) will now pick up
    // environment variables set by preload-env.mjs or the .env.test fallback.
    const tool = new AzureSearchGraphTool();
    console.log("[TestScript] AzureSearchGraphTool instantiated.");

    console.log(`[TestScript] Calling tool._call with query: "${queryText}"`);
    const rawJsonOutput = await tool._call(queryText);
    console.log("[TestScript] tool._call completed.");

    if (argOptions.noInteractive) {
      console.log = originalConsoleLog; // Restore console.log before writing to stdout
    }
    process.stdout.write(rawJsonOutput);
  } catch (error) {
    if (argOptions.noInteractive) {
      console.log = originalConsoleLog; // Restore console.log
    }
    console.error(
      "[TestScript] ❌ ERROR during tool execution:",
      error.message,
      error.stack,
    );
    // Output an error structure if the tool fails, so the calling script knows.
    process.stdout.write(
      JSON.stringify({
        error: true,
        message: error.message,
        stack: error.stack,
      }),
    );
    process.exit(1);
  }
}

// Interactive conversation function
async function runConversation() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  console.log("=".repeat(80));
  console.log(
    "🤖 Azure Search Graph Tool - Direct Output Test (Interactive Mode)",
  );
  console.log(
    "This mode may rely on .env.test if environment variables are not pre-loaded.",
  );
  console.log(
    "Enter a query to get the raw JSON output from the tool. Type 'exit' to quit.",
  );
  console.log("=".repeat(80));

  let conversationActive = true;
  while (conversationActive) {
    const inputText = await new Promise((resolve) => {
      rl.question("\n👤 You: ", resolve);
    });

    if (inputText.toLowerCase() === "exit") {
      conversationActive = false;
      console.log("[TestScript] Exiting interactive mode.");
      rl.close();
      break;
    }
    // For interactive mode, processSingleQuery will use normal console.log for its internal steps
    await processSingleQuery(inputText);
    console.log(
      "\n[TestScript] Raw JSON output printed above. Enter next query or 'exit'.",
    );
  }
}

// Execution logic
if (argOptions.noInteractive) {
  if (!argOptions.query) {
    console.error(
      "[TestScript] Error: --query is required when --no-interactive is set.",
    );
    process.exit(1);
  }
  processSingleQuery(argOptions.query).catch((err) => {
    // This is a final catch-all. Ensure console.log is restored if it was changed.
    const originalConsoleLogForFinally = console.log;
    if (console.log.toString() !== originalConsoleLogForFinally.toString()) {
      // basic check if it was replaced
      console.log = originalConsoleLogForFinally;
    }
    console.error(
      "[TestScript] Unhandled error in non-interactive mode:",
      err.message,
      err.stack,
    );
    process.exit(1);
  });
} else {
  runConversation();
}
