#!/bin/bash

# run-standalone-viz-test.sh
# Shell script to run the standalone tool step visualization test

# Get the directory of this script
SCRIPT_DIR="$(dirname "$0")"

echo "Running Standalone Tool Step Visualization Test..."
echo "---------------------------------------------------"

# Remove any previous test outputs
rm -f "$SCRIPT_DIR"/test-output-*.json "$SCRIPT_DIR"/visualization-test-results.json

# Run the test script
node "$SCRIPT_DIR/test-standalone-visualization.cjs"

# Check the exit code
if [ $? -eq 0 ]; then
  echo -e "\n🎉 All tests passed! The tool visualization should work correctly."
  
  # Display the test outputs
  echo -e "\nSummary of test output files:"
  ls -la "$SCRIPT_DIR"/test-output-*.json "$SCRIPT_DIR"/visualization-test-results.json
else
  echo -e "\n❌ Some tests failed. Check the output for details."
fi

# Exit with the status of the node script
exit $? 