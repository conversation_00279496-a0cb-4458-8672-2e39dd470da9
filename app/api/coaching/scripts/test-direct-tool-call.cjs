"use strict";

// Set up environment variables
process.env.NEXT_PUBLIC_AIRTABLE_API_KEY =
  "**********************************************************************************";
process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID = "appkOxteq7fiscoye";
process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID = "appg6TiMRFQVf0OXd";
process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID = "appbhSTOiO1EHDFrK";

// Import required packages
const {
  StdioClientTransport,
} = require("@modelcontextprotocol/sdk/client/stdio.js");
const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const path = require("path");

async function testDirectToolCall() {
  console.log("[DirectTest] Starting direct MCP tool call test");
  let client = null;
  let transport = null;

  try {
    // 1. Set up the MCP client transport
    const serverPath = path.join(
      process.cwd(),
      "mcp-server",
      "build",
      "server.js",
    );
    console.log(`[DirectTest] Using MCP server at: ${serverPath}`);

    transport = new StdioClientTransport({
      command: "node",
      args: [serverPath],
      env: process.env,
      cwd: process.cwd(),
    });

    // 2. Create and connect the client
    client = new Client({
      name: "direct-tool-test",
      version: "1.0.0",
    });

    console.log("[DirectTest] Connecting to MCP server...");
    await client.connect(transport);
    console.log("[DirectTest] Connected to MCP server");

    // 3. List available tools
    console.log("[DirectTest] Listing available tools...");
    const toolsResponse = await client.listTools();

    if (!toolsResponse.tools || toolsResponse.tools.length === 0) {
      throw new Error("No tools found on MCP server");
    }

    console.log(`[DirectTest] Found ${toolsResponse.tools.length} tools`);

    // Log all tool names and schemas
    toolsResponse.tools.forEach((tool, index) => {
      console.log(`[DirectTest] Tool ${index + 1}: ${tool.name}`);
      console.log(`  Description: ${tool.description || "No description"}`);
      console.log(
        `  Input Schema: ${JSON.stringify(tool.inputSchema || {})}\n`,
      );
    });

    // 4. Find the cycling sessions tool
    const targetToolName = "search_cycling_sessions";
    const targetTool = toolsResponse.tools.find(
      (tool) => tool.name === targetToolName,
    );

    if (!targetTool) {
      throw new Error(`Tool "${targetToolName}" not found`);
    }

    console.log(`[DirectTest] Found target tool: ${targetTool.name}`);
    console.log(
      `[DirectTest] Input Schema: ${JSON.stringify(targetTool.inputSchema || {}, null, 2)}`,
    );

    // 5. Call the tool with arguments
    // Test different argument formats
    const testArgFormats = [
      {
        name: "Direct arguments object",
        args: {
          skillCategory: "Intermediate",
          duration: "60",
          limit: 3,
        },
      },
      {
        name: "String arguments",
        args: {
          skillCategory: "Intermediate",
          duration: "60",
          limit: "3", // Note: as string
        },
      },
      {
        name: "Positional arguments",
        args: [
          { name: "skillCategory", value: "Intermediate" },
          { name: "duration", value: "60" },
          { name: "limit", value: 3 },
        ],
      },
    ];

    // Try each format
    for (const testFormat of testArgFormats) {
      try {
        console.log(`\n[DirectTest] Testing format: ${testFormat.name}`);
        console.log(
          `[DirectTest] Arguments: ${JSON.stringify(testFormat.args)}`,
        );

        console.log(`[DirectTest] Calling ${targetToolName} with arguments...`);

        // Common parameters for all calls
        const callParams = {
          name: targetToolName,
        };

        if (Array.isArray(testFormat.args)) {
          // Handle positional parameters
          const argumentsObj = {};
          testFormat.args.forEach((arg) => {
            argumentsObj[arg.name] = arg.value;
          });
          callParams.arguments = argumentsObj;
        } else {
          // Handle object parameters
          callParams.arguments = testFormat.args;
        }

        console.log(
          `[DirectTest] Final call params: ${JSON.stringify(callParams)}`,
        );

        // Call the tool and capture response
        const result = await client.callTool(callParams);

        console.log(`[DirectTest] RESULT for ${testFormat.name}:`);
        console.log(JSON.stringify(result, null, 2));
      } catch (error) {
        console.error(
          `[DirectTest] ERROR with format ${testFormat.name}:`,
          error,
        );
      }

      // Small delay between calls to avoid race conditions
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  } catch (error) {
    console.error("[DirectTest] Error during execution:", error);
  } finally {
    // Clean up resources
    if (transport) {
      console.log("[DirectTest] Closing MCP connection...");
      try {
        await transport.close();
        console.log("[DirectTest] MCP connection closed");
      } catch (closeError) {
        console.error("[DirectTest] Error closing MCP connection:", closeError);
      }
    }
    console.log("[DirectTest] Test completed");
  }
}

// Run the test
testDirectToolCall().catch((error) => {
  console.error("Uncaught error:", error);
  process.exit(1);
});

// Handle process termination
process.on("SIGINT", () => {
  console.log("[DirectTest] Received SIGINT, shutting down...");
  process.exit(0);
});
