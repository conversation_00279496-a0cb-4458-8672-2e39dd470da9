#!/usr/bin/env node

/**
 * Standalone test for the tool step visualization
 *
 * This script uses realistic mock data to test the parsing for tool step visualization
 * without relying on the actual Azure Search infrastructure.
 *
 * Run with: node test-standalone-visualization.cjs
 */

const fs = require("fs");
const path = require("path");

// Realistic data sample that mimics what the actual azureSearchGraphTool produces
const realisticOutputSample = {
  name: "azure_search_graph",
  output: {
    query: "benefits of high intensity interval training",
    results: [
      {
        title: "HIIT Benefits for Endurance Athletes",
        content:
          "High-intensity interval training (HIIT) has been shown to improve VO2 max and endurance performance with less time commitment than traditional endurance training.",
        source: "Journal of Sports Science",
        qualityScore: 0.85,
        url: "https://example.com/hiit-benefits",
        relevanceScore: 0.92,
        credibilityScore: 0.88,
        relevanceReasoning:
          "This article directly addresses the benefits of HIIT for athletes.",
        credibilityReasoning:
          "Published in a peer-reviewed journal with references to scientific studies.",
      },
      {
        title: "HIIT for Weight Management",
        content:
          "HIIT workouts can burn more calories in less time compared to steady-state cardio, and may continue to burn calories hours after the workout through EPOC (excess post-exercise oxygen consumption).",
        source: "American College of Sports Medicine",
        qualityScore: 0.78,
        url: "https://example.com/hiit-weight-management",
        relevanceScore: 0.85,
        credibilityScore: 0.95,
        relevanceReasoning:
          "Directly discusses a key benefit of HIIT (weight management).",
        credibilityReasoning:
          "Content from a highly respected sports medicine organization.",
      },
      {
        title: "HIIT vs Traditional Cardio",
        content:
          "Research shows that HIIT produces similar or greater improvements in cardiovascular fitness while requiring less time than traditional moderate-intensity continuous training.",
        source: "Sports Medicine Review",
        qualityScore: 0.82,
        url: "https://example.com/hiit-vs-cardio",
        relevanceScore: 0.88,
        credibilityScore: 0.86,
        relevanceReasoning:
          "Compares HIIT benefits to traditional training methods.",
        credibilityReasoning:
          "Published in a reputable sports medicine publication.",
      },
    ],
    meta: {
      originalResultCount: 12,
      filteredResultCount: 3,
      relevanceFiltered: 9,
      credibilityEvaluated: true,
    },
  },
};

// Function to validate the structure of parsed tool steps
function validateParsedOutput(parsedOutput) {
  if (!parsedOutput) {
    return { valid: false, reason: "Output is null" };
  }

  // Validate required fields
  if (!parsedOutput.toolName) {
    return { valid: false, reason: "Missing toolName" };
  }

  if (
    !parsedOutput.steps ||
    !Array.isArray(parsedOutput.steps) ||
    parsedOutput.steps.length === 0
  ) {
    return { valid: false, reason: "Missing or empty steps array" };
  }

  // Validate steps structure
  for (let i = 0; i < parsedOutput.steps.length; i++) {
    const step = parsedOutput.steps[i];

    if (typeof step.number !== "number") {
      return { valid: false, reason: `Step ${i} missing number property` };
    }

    if (!step.title) {
      return { valid: false, reason: `Step ${i} missing title` };
    }

    if (!step.content) {
      return { valid: false, reason: `Step ${i} missing content` };
    }

    if (!step.content.type) {
      return { valid: false, reason: `Step ${i} content missing type` };
    }

    // Validate specific content types
    if (
      step.content.type === "dots" &&
      (!step.content.props || !step.content.props.total)
    ) {
      return {
        valid: false,
        reason: `Step ${i} dots content missing required props`,
      };
    }

    if (
      step.content.type === "items" &&
      (!step.content.props || !step.content.props.items)
    ) {
      return {
        valid: false,
        reason: `Step ${i} items content missing required props`,
      };
    }

    if (step.content.type === "text" && !step.content.value) {
      return { valid: false, reason: `Step ${i} text content missing value` };
    }
  }

  return { valid: true };
}

// The actual parseToolOutputToSteps function (simplified for this test)
function parseToolOutputToSteps(toolCall) {
  if (!toolCall || !toolCall.output) return null;

  switch (toolCall.name) {
    case "azure_search_graph":
      let parsedGraphOutput = toolCall.output;
      if (typeof toolCall.output === "string") {
        try {
          console.log(
            "[parseToolOutputToSteps] Attempting JSON.parse for string output",
          );
          parsedGraphOutput = JSON.parse(toolCall.output);
        } catch (error) {
          console.error(
            "[parseToolOutputToSteps] JSON.parse failed:",
            error.message,
          );
          return null;
        }
      }
      return parseAzureSearchGraphOutput(parsedGraphOutput);

    case "data_analysis_tool":
      console.log("Parsing data_analysis_tool output - to be implemented");
      return null;
    default:
      console.warn(
        `[parseToolOutputToSteps] No parser implemented for tool: ${toolCall.name}`,
      );
      return null;
  }
}

// The simplified version of parseAzureSearchGraphOutput (compatible with the real data)
function parseAzureSearchGraphOutput(data) {
  if (!data || typeof data !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] Input data is not an object or is null",
    );
    return null;
  }

  // Check for expected structure
  if (!data.query || typeof data.query !== "string") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.query is missing or not a string",
    );
    return null;
  }

  if (!data.results || !Array.isArray(data.results)) {
    console.warn(
      "[parseAzureSearchGraphOutput] data.results is missing or not an array",
    );
    return null;
  }

  if (!data.meta || typeof data.meta !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.meta is missing or not an object",
    );
    return null;
  }

  // Add checks for critical meta fields
  if (
    typeof data.meta.originalResultCount !== "number" ||
    typeof data.meta.filteredResultCount !== "number" ||
    typeof data.meta.relevanceFiltered !== "number" ||
    typeof data.meta.credibilityEvaluated !== "boolean"
  ) {
    console.warn(
      "[parseAzureSearchGraphOutput] One or more critical meta fields are missing or incorrect types",
    );
    console.warn("meta data structure:", JSON.stringify(data.meta, null, 2));
    return null;
  }

  const { query, results, meta } = data;
  const steps = [];
  let currentStep = 1;

  const numOriginalResults = meta.originalResultCount;
  const numRelevanceRemoved = meta.relevanceFiltered;
  const calculatedResultsAfterRelevance =
    numOriginalResults - numRelevanceRemoved;
  const numFinalFilteredCount = meta.filteredResultCount;

  steps.push({
    number: currentStep++,
    title: "Initial Search",
    description: `Searched for: "${query}". Found ${numOriginalResults} docs.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: numOriginalResults,
        highlightedLabel: "found",
        regularColor: "bg-transparent",
      },
    },
  });

  steps.push({
    number: currentStep++,
    title: "Relevance Filter",
    description: `${calculatedResultsAfterRelevance} docs after relevance filter.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: calculatedResultsAfterRelevance,
        highlightedLabel: "relevant",
        nonHighlightedLabel: "filtered",
      },
    },
  });

  if (meta.credibilityEvaluated) {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Sources evaluated.",
      content: {
        type: "text",
        value: "Source credibility assessed.",
      },
    });
  } else {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Skipped.",
      content: {
        type: "text",
        value: "Credibility check skipped.",
      },
    });
  }

  steps.push({
    number: currentStep++,
    title: "Final Selection",
    description: `Presenting top ${numFinalFilteredCount} documents.`,
    content: {
      type: "items",
      props: {
        items: results.map((r) => ({
          title: r.title,
          content: r.content,
          source: r.source || r.url,
        })),
        maxItems: 2,
        moreLabel: `more result${numFinalFilteredCount > 2 ? "s" : ""}`,
      },
    },
  });

  return {
    toolName: "Azure Search",
    steps,
    summary: `${numFinalFilteredCount} of ${numOriginalResults} results shown`,
    triggerLabel: "Search details",
  };
}

// Create different test variations
const testVariations = [
  {
    name: "Object Output",
    data: realisticOutputSample,
  },
  {
    name: "String Output",
    data: {
      name: "azure_search_graph",
      output: JSON.stringify(realisticOutputSample.output),
    },
  },
  {
    name: "Different Result Count",
    data: {
      name: "azure_search_graph",
      output: {
        ...realisticOutputSample.output,
        meta: {
          ...realisticOutputSample.output.meta,
          originalResultCount: 20,
          filteredResultCount: 5,
          relevanceFiltered: 15,
        },
        results: realisticOutputSample.output.results.slice(0, 2), // Fewer results
      },
    },
  },
  {
    name: "No Credibility Evaluation",
    data: {
      name: "azure_search_graph",
      output: {
        ...realisticOutputSample.output,
        meta: {
          ...realisticOutputSample.output.meta,
          credibilityEvaluated: false,
        },
      },
    },
  },
  {
    name: "Missing Meta Fields",
    data: {
      name: "azure_search_graph",
      output: {
        query: "benefits of HIIT",
        results: realisticOutputSample.output.results,
        meta: {
          // Deliberately missing some required fields
          originalResultCount: 10,
        },
      },
    },
  },
];

// Run tests
console.log("===== Standalone Tool Step Visualization Test =====\n");

const results = [];

for (const [index, test] of testVariations.entries()) {
  console.log(`\nTest ${index + 1}: ${test.name}`);

  const parsedOutput = parseToolOutputToSteps(test.data);
  const validation = parsedOutput
    ? validateParsedOutput(parsedOutput)
    : { valid: false, reason: "Null output" };

  const testResult = {
    name: test.name,
    passed: test.name.includes("Missing")
      ? parsedOutput === null
      : validation.valid,
    validationResult: validation,
    parsedOutput,
  };

  results.push(testResult);

  if (testResult.passed) {
    console.log(`✅ TEST PASSED: ${test.name}`);
  } else {
    console.log(`❌ TEST FAILED: ${test.name}`);
    console.log(`   Reason: ${validation.reason || "Parsing returned null"}`);
  }

  // For successful cases, save the output to a file
  if (parsedOutput) {
    const outputFileName = `test-output-${index + 1}.json`;
    fs.writeFileSync(outputFileName, JSON.stringify(parsedOutput, null, 2));
    console.log(`   Output saved to: ${outputFileName}`);
  }
}

// Summary
console.log("\n===== Test Summary =====");
const passedCount = results.filter((r) => r.passed).length;
console.log(`${passedCount} of ${results.length} tests passed`);

if (passedCount === results.length) {
  console.log("\n✅ ALL TESTS PASSED");
  // Create a consolidated file with test outputs
  const allResults = {
    testDate: new Date().toISOString(),
    results: results.map((r) => ({
      name: r.name,
      passed: r.passed,
      validationResult: r.validationResult,
      output: r.parsedOutput,
    })),
  };

  fs.writeFileSync(
    "visualization-test-results.json",
    JSON.stringify(allResults, null, 2),
  );
  console.log("Full test results saved to: visualization-test-results.json");
} else {
  console.log("\n❌ SOME TESTS FAILED");
  process.exit(1);
}
