"use strict";

// Debug setup
console.log("Starting script...");
console.log("Node version:", process.version);
console.log("CWD:", process.cwd());

// Track what packages are loaded
try {
  console.log("Loading @langchain/langgraph...");
  const langraph = require("@langchain/langgraph");
  console.log("- Loaded langgraph successfully");

  console.log("Loading @langchain/openai...");
  const langchainOpenai = require("@langchain/openai");
  console.log("- Loaded openai successfully");

  console.log("Loading zod...");
  const zod = require("zod");
  console.log("- Loaded zod successfully");

  console.log("Loading @langchain/core/tools...");
  const langchainTools = require("@langchain/core/tools");
  console.log("- Loaded tools successfully");

  console.log("Loading @langchain/core/messages...");
  const langchainMessages = require("@langchain/core/messages");
  console.log("- Loaded messages successfully");
} catch (error) {
  console.error("Error loading dependencies:", error);
  process.exit(1);
}

/**
 * LangGraph Example with Calculator Tools Integration
 *
 * This example demonstrates how to use LangGraph with calculator tools.
 */

// Required imports for LangGraph
const { AzureChatOpenAI } = require("@langchain/openai");
const {
  StateGraph,
  END,
  START,
  MessagesAnnotation,
} = require("@langchain/langgraph");
const { ToolNode } = require("@langchain/langgraph/prebuilt");
const { HumanMessage, AIMessage } = require("@langchain/core/messages");
const { z } = require("zod");
const { DynamicStructuredTool } = require("@langchain/core/tools");

// Azure OpenAI Configuration
const AZURE_OPENAI_ENDPOINT = "https://ft-gpt40mini.openai.azure.com/";
const AZURE_OPENAI_DEPLOYMENT = "gpt-4.1-nano";
const AZURE_OPENAI_API_VERSION = "2025-01-01-preview";
const AZURE_OPENAI_API_KEY = "********************************";

// Create Azure OpenAI client
function createAzureChatOpenAI(options = {}) {
  console.log(`Creating AzureChatOpenAI with options:`, options);

  const instanceNameMatch = AZURE_OPENAI_ENDPOINT.match(
    /https:\/\/([^.]+)\.openai\.azure\.com/,
  );
  const instanceName = instanceNameMatch?.[1] || "ft-gpt40mini";

  console.log(`Azure OpenAI Configuration:`);
  console.log(`  - API Version: ${AZURE_OPENAI_API_VERSION}`);
  console.log(`  - Deployment Name: ${AZURE_OPENAI_DEPLOYMENT}`);
  console.log(`  - Instance Name: ${instanceName}`);
  console.log(`  - Endpoint URL: ${AZURE_OPENAI_ENDPOINT}`);

  return new AzureChatOpenAI({
    azureOpenAIApiKey: AZURE_OPENAI_API_KEY,
    azureOpenAIApiVersion: AZURE_OPENAI_API_VERSION,
    modelName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiDeploymentName: AZURE_OPENAI_DEPLOYMENT,
    azureOpenAIApiInstanceName: instanceName,
    temperature: 0,
    cache: true,
    maxRetries: 3,
    ...options,
  });
}

/**
 * Example demonstrating how to use calculator tools with LangGraph
 */
async function runExample() {
  try {
    console.log("Creating calculator tools...");

    // Define calculator tools with proper Zod schemas
    const calculatorTools = [
      new DynamicStructuredTool({
        name: "calculator_add",
        description: "Add two numbers together",
        schema: z.object({
          a: z.number().describe("First number"),
          b: z.number().describe("Second number"),
        }),
        func: async ({ a, b }) => {
          console.log(`Calculating ${a} + ${b}`);
          return (a + b).toString();
        },
      }),
      new DynamicStructuredTool({
        name: "calculator_subtract",
        description: "Subtract one number from another",
        schema: z.object({
          a: z.number().describe("Number to subtract from"),
          b: z.number().describe("Number to subtract"),
        }),
        func: async ({ a, b }) => {
          console.log(`Calculating ${a} - ${b}`);
          return (a - b).toString();
        },
      }),
      new DynamicStructuredTool({
        name: "calculator_multiply",
        description: "Multiply two numbers",
        schema: z.object({
          a: z.number().describe("First number"),
          b: z.number().describe("Second number"),
        }),
        func: async ({ a, b }) => {
          console.log(`Calculating ${a} * ${b}`);
          return (a * b).toString();
        },
      }),
      new DynamicStructuredTool({
        name: "calculator_divide",
        description: "Divide one number by another",
        schema: z.object({
          a: z.number().describe("Number to divide"),
          b: z.number().describe("Number to divide by"),
        }),
        func: async ({ a, b }) => {
          if (b === 0) {
            return "Error: Cannot divide by zero";
          }
          console.log(`Calculating ${a} / ${b}`);
          return (a / b).toString();
        },
      }),
      new DynamicStructuredTool({
        name: "calculator_square_root",
        description: "Calculate the square root of a number",
        schema: z.object({
          number: z.number().describe("Number to find the square root of"),
        }),
        func: async ({ number }) => {
          if (number < 0) {
            return "Error: Cannot calculate square root of a negative number";
          }
          console.log(`Calculating sqrt(${number})`);
          return Math.sqrt(number).toString();
        },
      }),
    ];

    console.log(`Created ${calculatorTools.length} calculator tools`);

    // Rest of the function remains the same...
    console.log("=== USING CALCULATOR TOOLS WITH LANGGRAPH ===");

    // Create Azure LLM and bind tools
    console.log("Creating Azure OpenAI model with calculator tools...");
    const llm = createAzureChatOpenAI({
      maxTokens: 1500,
      timeout: 25000,
    });

    const llmWithTools = llm.bindTools(calculatorTools);
    console.log(`Bound ${calculatorTools.length} calculator tools to LLM`);

    // Create a tool node for the LangGraph
    const toolNode = new ToolNode(calculatorTools);

    // ================================================
    // Create a LangGraph agent flow
    // ================================================
    console.log("\n=== CREATING LANGGRAPH AGENT FLOW ===");

    // Define the function that calls the model
    const llmNode = async (state) => {
      console.log(`Calling LLM with ${state.messages.length} messages`);
      try {
        const response = await llmWithTools.invoke(state.messages);
        console.log(
          "LLM response:",
          response.tool_calls
            ? `${response.tool_calls.length} tool calls`
            : response.content.slice(0, 100) + "...",
        );
        return { messages: [response] };
      } catch (error) {
        console.error("Error in LLM node:", error);
        return {
          messages: [
            new AIMessage(
              "I'm having trouble processing your request. Let me try using a basic calculation tool.",
            ),
          ],
        };
      }
    };

    // Create a new graph with MessagesAnnotation
    const workflow = new StateGraph(MessagesAnnotation)
      // Add the nodes to the graph
      .addNode("llm", llmNode)
      .addNode("tools", toolNode)

      // Add edges - these define how nodes are connected
      .addEdge(START, "llm")
      .addEdge("tools", "llm")

      // Conditional routing based on whether the LLM wants to call tools
      .addConditionalEdges("llm", (state) => {
        const lastMessage = state.messages[state.messages.length - 1];

        // If the last message has tool calls, route to the tools node
        if (
          lastMessage._getType() === "ai" &&
          lastMessage.tool_calls &&
          lastMessage.tool_calls.length > 0
        ) {
          console.log("Tool calls detected, routing to tools node");
          return "tools";
        }

        // Otherwise, end the workflow
        console.log("No tool calls, ending the workflow");
        return END;
      });

    // Compile the graph
    const app = workflow.compile();

    // Define queries for testing
    const queries = [
      "If Sally has a total of 420324 apples and mark steals 7824 of them, how many does she have left?",
      "What is 1328 divided by 42?",
      "What is the square root of 1024?",
    ];

    // Test the LangGraph agent with the queries
    console.log("\n=== RUNNING LANGGRAPH AGENT ===");

    for (const query of queries) {
      console.log(`\nQuery: ${query}`);

      try {
        // Run the LangGraph agent with the query
        const result = await app.invoke({
          messages: [new HumanMessage(query)],
        });

        // Display the results
        console.log(`\nFinal Messages (${result.messages.length}):`);

        result.messages.forEach((msg, i) => {
          const msgType = msg._getType ? msg._getType() : "unknown";

          console.log(
            `[${i}] ${msgType}: ${
              typeof msg.content === "string"
                ? msg.content
                : JSON.stringify(msg.content)
            }`,
          );

          // Log tool calls if present
          if (msg.tool_calls && msg.tool_calls.length > 0) {
            console.log(
              `Tool calls: ${JSON.stringify(msg.tool_calls, null, 2)}`,
            );
          }
        });

        const finalMessage = result.messages[result.messages.length - 1];
        console.log(`\nResult: ${finalMessage.content}`);
      } catch (error) {
        console.error(`Error processing query "${query}":`, error);
      }
    }
  } catch (error) {
    console.error("Error in runExample:", error);
    process.exit(1);
  } finally {
    console.log("Example completed.");
  }
}

// First, wrap in try-catch to catch any initialization errors
try {
  console.log("Starting runExample...");
  // Error handling for the main function
  runExample().catch((error) => {
    console.error("Uncaught error in main function:", error);
    process.exit(1);
  });
} catch (error) {
  console.error("Error initializing the script:", error);
  process.exit(1);
}

// Handle process termination
process.on("SIGINT", () => {
  console.log("Received SIGINT, shutting down...");
  process.exit(0);
});
