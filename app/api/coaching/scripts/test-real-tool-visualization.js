#!/usr/bin/env node

/**
 * Test script for the tool step visualization using real Azure Search Graph tool
 *
 * This script runs a real query against the Azure Search Graph tool and tests
 * the parsing of the actual tool output into visualization steps.
 *
 * Run with: node test-real-tool-visualization.js "your search query"
 */

// Import required modules
const fs = require("fs");
const path = require("path");
const { promisify } = require("util");
const exec = promisify(require("child_process").exec);

// Get query from command line args
const args = process.argv.slice(2);
let query = "benefits of high intensity interval training"; // Default query
if (args.length > 0) {
  query = args[0];
  console.log(`Using provided query: "${query}"`);
} else {
  console.log(`Using default query: "${query}"`);
}

// The path to the test-azure-search-graph.mjs script relative to this script
const searchScriptPath = path.join(__dirname, "test-azure-search-graph.mjs");

// Function to validate the structure of parsed tool steps
function validateParsedOutput(parsedOutput) {
  if (!parsedOutput) {
    return { valid: false, reason: "Output is null" };
  }

  // Validate required fields
  if (!parsedOutput.toolName) {
    return { valid: false, reason: "Missing toolName" };
  }

  if (
    !parsedOutput.steps ||
    !Array.isArray(parsedOutput.steps) ||
    parsedOutput.steps.length === 0
  ) {
    return { valid: false, reason: "Missing or empty steps array" };
  }

  // Validate steps structure
  for (let i = 0; i < parsedOutput.steps.length; i++) {
    const step = parsedOutput.steps[i];

    if (typeof step.number !== "number") {
      return { valid: false, reason: `Step ${i} missing number property` };
    }

    if (!step.title) {
      return { valid: false, reason: `Step ${i} missing title` };
    }

    if (!step.content) {
      return { valid: false, reason: `Step ${i} missing content` };
    }

    if (!step.content.type) {
      return { valid: false, reason: `Step ${i} content missing type` };
    }

    // Validate specific content types
    if (
      step.content.type === "dots" &&
      (!step.content.props || !step.content.props.total)
    ) {
      return {
        valid: false,
        reason: `Step ${i} dots content missing required props`,
      };
    }

    if (
      step.content.type === "items" &&
      (!step.content.props || !step.content.props.items)
    ) {
      return {
        valid: false,
        reason: `Step ${i} items content missing required props`,
      };
    }

    if (step.content.type === "text" && !step.content.value) {
      return { valid: false, reason: `Step ${i} text content missing value` };
    }
  }

  return { valid: true };
}

// The simplified version of parseAzureSearchGraphOutput (compatible with the real data)
function parseAzureSearchGraphOutput(data) {
  if (!data || typeof data !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] Input data is not an object or is null",
    );
    return null;
  }

  // Check for expected structure
  if (!data.query || typeof data.query !== "string") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.query is missing or not a string",
    );
    return null;
  }

  if (!data.results || !Array.isArray(data.results)) {
    console.warn(
      "[parseAzureSearchGraphOutput] data.results is missing or not an array",
    );
    return null;
  }

  if (!data.meta || typeof data.meta !== "object") {
    console.warn(
      "[parseAzureSearchGraphOutput] data.meta is missing or not an object",
    );
    return null;
  }

  // Add checks for critical meta fields (adjust based on actual structure)
  if (
    typeof data.meta.originalResultCount !== "number" ||
    typeof data.meta.filteredResultCount !== "number" ||
    typeof data.meta.relevanceFiltered !== "number" ||
    typeof data.meta.credibilityEvaluated !== "boolean"
  ) {
    console.warn(
      "[parseAzureSearchGraphOutput] One or more critical meta fields are missing or incorrect types",
    );
    console.warn("meta data structure:", JSON.stringify(data.meta, null, 2));
    return null;
  }

  const { query, results, meta } = data;
  const steps = [];
  let currentStep = 1;

  const numOriginalResults = meta.originalResultCount;
  const numRelevanceRemoved = meta.relevanceFiltered;
  const calculatedResultsAfterRelevance =
    numOriginalResults - numRelevanceRemoved;
  const numFinalFilteredCount = meta.filteredResultCount;

  steps.push({
    number: currentStep++,
    title: "Initial Search",
    description: `Searched for: "${query}". Found ${numOriginalResults} docs.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: numOriginalResults,
        highlightedLabel: "found",
        regularColor: "bg-transparent",
      },
    },
  });

  steps.push({
    number: currentStep++,
    title: "Relevance Filter",
    description: `${calculatedResultsAfterRelevance} docs after relevance filter.`,
    content: {
      type: "dots",
      props: {
        total: numOriginalResults,
        highlighted: calculatedResultsAfterRelevance,
        highlightedLabel: "relevant",
        nonHighlightedLabel: "filtered",
      },
    },
  });

  if (meta.credibilityEvaluated) {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Sources evaluated.",
      content: {
        type: "text",
        value: "Source credibility assessed.",
      },
    });
  } else {
    steps.push({
      number: currentStep++,
      title: "Credibility Check",
      description: "Skipped.",
      content: {
        type: "text",
        value: "Credibility check skipped.",
      },
    });
  }

  steps.push({
    number: currentStep++,
    title: "Final Selection",
    description: `Presenting top ${numFinalFilteredCount} documents.`,
    content: {
      type: "items",
      props: {
        items: results.map((r) => ({
          title: r.title,
          content: r.content,
          source: r.source || r.url,
        })),
        maxItems: 2,
        moreLabel: `more result${numFinalFilteredCount > 2 ? "s" : ""}`,
      },
    },
  });

  return {
    toolName: "Azure Search",
    steps,
    summary: `${numFinalFilteredCount} of ${numOriginalResults} results shown`,
    triggerLabel: "Search details",
  };
}

// Function to extract the results from the output of the search script
function extractSearchResults(output) {
  try {
    // Find the JSON output bounds in the text using markers
    const startMarker = "🤖 Assistant Response:";
    const startIndex = output.indexOf(startMarker);

    if (startIndex === -1) {
      throw new Error("Could not find start of assistant response");
    }

    // Extract the response section
    const responseSection = output.substring(startIndex + startMarker.length);

    // Parse the results section to extract the actual search data
    // This is heuristic and depends on the exact output format
    const resultSection = responseSection.trim();

    // Extract the relevance filtered count
    const relevanceFilteredMatch = resultSection.match(
      /Filtered out (\d+) irrelevant results/,
    );
    const relevanceFiltered = relevanceFilteredMatch
      ? parseInt(relevanceFilteredMatch[1])
      : 0;

    // Count the number of results by looking for "RESULT X:" patterns
    const resultMatches = resultSection.match(/RESULT \d+:/g);
    const resultCount = resultMatches ? resultMatches.length : 0;

    // Extract each result's data
    const results = [];
    const resultBlocks = resultSection.split(/RESULT \d+:/g).slice(1); // Skip the first split which is empty

    for (const block of resultBlocks) {
      const titleMatch = block.match(/Title: (.+)/);
      const contentMatch = block.match(/Content: (.+)/);
      const sourceMatch = block.match(/Source: (.+)/);
      const urlMatch = block.match(/URL: (.+)/);

      if (titleMatch && contentMatch) {
        results.push({
          title: titleMatch[1].trim(),
          content: contentMatch[1].trim(),
          source: sourceMatch ? sourceMatch[1].trim() : undefined,
          url: urlMatch ? urlMatch[1].trim() : undefined,
        });
      }
    }

    // Check if credibility was evaluated (look for Credibility: pattern)
    const credibilityEvaluated = resultSection.includes("Credibility:");

    // Construct the data structure expected by the parser
    return {
      query,
      results,
      meta: {
        originalResultCount: resultCount + relevanceFiltered,
        filteredResultCount: resultCount,
        relevanceFiltered: relevanceFiltered,
        credibilityEvaluated,
      },
    };
  } catch (error) {
    console.error("Error extracting search results:", error);
    return null;
  }
}

// Main function to run the test
async function runTest() {
  console.log("===== Testing Tool Step Visualization with Real Data =====\n");
  console.log(`Running Azure Search Graph tool with query: "${query}"`);

  try {
    // Execute the search script with the query
    const { stdout, stderr } = await exec(
      `node ${searchScriptPath} --query "${query}" --no-interactive`,
    );

    if (stderr) {
      console.error("Error running search script:", stderr);
    }

    // Save the raw output to a file for inspection
    const outputFile = path.join(__dirname, "search-output.txt");
    fs.writeFileSync(outputFile, stdout);
    console.log(`Raw search output saved to: ${outputFile}`);

    // Extract search results from the script output
    const searchData = extractSearchResults(stdout);
    if (!searchData) {
      console.error("Failed to extract search data from output");
      process.exit(1);
    }

    // Save the extracted data to a file for inspection
    const dataFile = path.join(__dirname, "extracted-data.json");
    fs.writeFileSync(dataFile, JSON.stringify(searchData, null, 2));
    console.log(`Extracted search data saved to: ${dataFile}`);

    // Parse the extracted data using our visualization parser
    console.log("\nParsing extracted data with visualization parser...");
    const parsedOutput = parseAzureSearchGraphOutput(searchData);

    // Validate and display the parsed output
    if (parsedOutput) {
      console.log("Parsing successful. Validating structure...");
      const validation = validateParsedOutput(parsedOutput);

      if (validation.valid) {
        console.log("✅ Validation PASSED");
        console.log("\nParsed visualization data:");
        console.log(JSON.stringify(parsedOutput, null, 2));
      } else {
        console.error("❌ Validation FAILED:", validation.reason);
        console.log("\nProblematic output structure:");
        console.log(JSON.stringify(parsedOutput, null, 2));
      }
    } else {
      console.error("❌ Parsing FAILED - null result returned");
    }

    console.log("\n===== Test Complete =====");
    return parsedOutput;
  } catch (error) {
    console.error("Test failed:", error);
    process.exit(1);
  }
}

// Run the test
runTest().catch(console.error);
