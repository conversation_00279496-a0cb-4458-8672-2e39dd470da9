import { StateGraph, START, END, Annotation } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { Tool } from "@langchain/core/tools";
import { ChatOpenAI } from "@langchain/openai";
import { azureSearchTool } from "../tools";
import { trace } from "@opentelemetry/api";

// --- Environment Variable Validation ---
const OPENAI_API_KEY_FOR_CHAT = process.env.AZURE_OPENAI_API_KEY; // Assuming Azure key can be used if base URL is set
const AZURE_OPENAI_API_ENDPOINT_FOR_WARN =
  process.env.AZURE_OPENAI_API_ENDPOINT; // Keep for warning
const AZURE_OPENAI_DEPLOYMENT_NAME_FOR_WARN =
  process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME;
const AZURE_OPENAI_API_VERSION_FOR_WARN = process.env.AZURE_OPENAI_API_VERSION;

if (!OPENAI_API_KEY_FOR_CHAT) {
  console.warn(
    "OPENAI_API_KEY (expected from AZURE_OPENAI_API_KEY) for evaluation agent is missing. Evaluation may not function correctly.",
  );
}
// Warning about Azure specific vars if user intends to use Azure with ChatOpenAI via OPENAI_BASE_URL
if (
  !AZURE_OPENAI_API_ENDPOINT_FOR_WARN ||
  !AZURE_OPENAI_DEPLOYMENT_NAME_FOR_WARN ||
  !AZURE_OPENAI_API_VERSION_FOR_WARN
) {
  console.warn(
    "One or more Azure specific OpenAI environment variables (ENDPOINT, DEPLOYMENT_NAME, API_VERSION) are missing. If using ChatOpenAI with Azure, ensure OPENAI_API_BASE_URL is set correctly.",
  );
}

// --- State Definition ---
interface AzureSearchInputParams {
  query: string;
  index_name?: string;
  top_k?: number;
  vector_search?: boolean;
}

interface AzureSearchResult {
  rawOutput: string;
  parsedResults?: any[];
  searchPerformed: boolean;
  error?: string;
}

interface EvaluationInput {
  originalQuery: string;
  searchQueryUsed: string;
  searchResults: AzureSearchResult;
  conversationHistory?: BaseMessage[];
}

interface EvaluationOutput {
  isSatisfactory: boolean;
  feedback?: string;
  suggestedQuery?: string | null;
  confidenceScore?: number;
}

// Define State as an interface
export interface State {
  initialUserQuery: string; // Should be provided at invocation
  currentSearchQuery: string; // Set by prepareSearchInputNode
  searchParams: AzureSearchInputParams | null;
  searchOperationResult: AzureSearchResult | null;
  evaluationInput: EvaluationInput | null;
  evaluationResult: EvaluationOutput | null;
  finalAnswer: string | null;
  errorMessages: string[];
  messages: BaseMessage[];
  retryCount: number;
}

// Channel definitions for StateGraph
// Using 'value' for reducer function as indicated by linter error
export const AzureSearchGraphStateChannels = {
  initialUserQuery: { value: (_prev: string, next: string) => next },
  currentSearchQuery: { value: (_prev: string, next: string) => next },
  searchParams: {
    value: (
      _prev: AzureSearchInputParams | null,
      next: AzureSearchInputParams | null,
    ) => next,
    default: () => null,
  },
  searchOperationResult: {
    value: (_prev: AzureSearchResult | null, next: AzureSearchResult | null) =>
      next,
    default: () => null,
  },
  evaluationInput: {
    value: (_prev: EvaluationInput | null, next: EvaluationInput | null) =>
      next,
    default: () => null,
  },
  evaluationResult: {
    value: (_prev: EvaluationOutput | null, next: EvaluationOutput | null) =>
      next,
    default: () => null,
  },
  finalAnswer: {
    value: (_prev: string | null, next: string | null) => next,
    default: () => null,
  },
  errorMessages: {
    value: (current: string[], update?: string[]) => {
      // Changed from current: string[] | null
      const newErrorMessages = update ?? [];
      // Ensure current is always an array before spreading
      return [...(current || []), ...newErrorMessages];
    },
    default: () => [],
  },
  messages: {
    value: (current: BaseMessage[], update?: BaseMessage[]) => {
      // Changed from current: BaseMessage[] | null
      const newMessages = update ?? [];
      return [...(current || []), ...newMessages];
    },
    default: () => [],
  },
  retryCount: {
    value: (_prev: number, next: number) => next,
    default: () => 0,
  },
};

// --- LLM for Evaluation Agent ---
const evaluationLlm = new ChatOpenAI({
  openAIApiKey: OPENAI_API_KEY_FOR_CHAT,
  temperature: 0.3,
  modelName: "gpt-3.5-turbo", // Or any other model user has access to
});

// --- Node Functions ---
// Modify the node functions to return the state directly
function prepareSearchInputNode(state: State): State {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.prepareSearchInputNode", {
    attributes: {
      node_type: "graph_node",
      graph_name: "azureSearchGraph",
      function_name: "prepareSearchInputNode",
      input_state_initial_query: state.initialUserQuery,
      input_state_retry_count: state.retryCount,
      input_state_suggested_query:
        state.evaluationResult?.suggestedQuery || "N/A",
    },
  });

  console.log("[Graph] In prepareSearchInputNode");
  const queryToUse =
    state.retryCount > 0 && state.evaluationResult?.suggestedQuery
      ? state.evaluationResult.suggestedQuery
      : state.initialUserQuery;

  const searchParams: AzureSearchInputParams = {
    query: queryToUse!,
    top_k: 5,
    vector_search: true,
  };

  const result = {
    ...state,
    currentSearchQuery: queryToUse!,
    searchParams: searchParams,
    messages: [
      ...state.messages,
      new HumanMessage(
        `(Attempt ${state.retryCount + 1}) Preparing search for: ${queryToUse}`,
      ),
    ],
  };

  span.setAttribute("output_current_search_query", result.currentSearchQuery);
  span.setAttribute(
    "output_search_params",
    JSON.stringify(result.searchParams),
  );
  span.end();
  return result;
}

async function azureSearchNode(state: State): Promise<State> {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.azureSearchNode", {
    attributes: {
      node_type: "graph_node",
      graph_name: "azureSearchGraph",
      function_name: "azureSearchNode",
      input_search_params_present: !!state.searchParams,
      input_search_query: state.searchParams?.query || "N/A",
    },
  });

  console.log("[Graph] In azureSearchNode");
  if (!state.searchParams) {
    const errorResult: Partial<State> = {
      errorMessages: [
        ...(state.errorMessages || []),
        "Search parameters not prepared.",
      ],
    };
    span.setAttribute("error", true);
    span.setAttribute("error_message", "Search parameters not prepared.");
    span.end();
    return { ...state, ...errorResult };
  }

  const { query, ...restParams } = state.searchParams;
  let toolInputString: string;
  if (Object.keys(restParams).length > 0) {
    toolInputString = JSON.stringify({ query, ...restParams });
  } else {
    toolInputString = query;
  }

  try {
    console.log(
      `[Graph] Calling Azure Search Tool with input: ${toolInputString}`,
    );
    const rawOutput = await azureSearchTool._call(toolInputString);
    console.log(
      `[Graph] Azure Search Tool raw output: ${rawOutput.substring(0, 200)}...`,
    );

    let parsedResults: any[] = [];
    let searchError: string | undefined;

    try {
      parsedResults = JSON.parse(rawOutput);
      if (!Array.isArray(parsedResults)) {
        if (typeof parsedResults === "string") {
          searchError = parsedResults;
        } else if (
          parsedResults &&
          typeof parsedResults === "object" &&
          (parsedResults as any).error
        ) {
          searchError = (parsedResults as any).error;
        } else if (
          rawOutput.toLowerCase().startsWith("no relevant documents found") ||
          rawOutput.toLowerCase().startsWith("error:")
        ) {
          searchError = rawOutput;
        }
        parsedResults = [];
      }
    } catch (e) {
      console.warn(
        "[Graph] Failed to parse Azure Search output as JSON. Output might be an error message or no results string.",
        e,
      );
      searchError = rawOutput;
      parsedResults = [];
    }

    const searchOperationResult: AzureSearchResult = {
      rawOutput,
      parsedResults,
      searchPerformed: true,
      error: searchError,
    };

    const resultState: Partial<State> = {
      searchOperationResult,
      messages: [
        ...(state.messages || []),
        new AIMessage(
          `Search completed. Found ${parsedResults.length} potential results.`,
        ),
      ],
    };
    span.setAttribute(
      "output_search_performed",
      searchOperationResult.searchPerformed,
    );
    span.setAttribute(
      "output_parsed_results_count",
      searchOperationResult.parsedResults?.length || 0,
    );
    span.setAttribute(
      "output_search_error",
      searchOperationResult.error || "None",
    );
    span.end();
    return { ...state, ...resultState };
  } catch (error: any) {
    console.error("[Graph] Error in azureSearchNode:", error);
    const errorMessage = error.message || "Unknown error during Azure search";

    const errorState: Partial<State> = {
      searchOperationResult: {
        rawOutput: errorMessage,
        parsedResults: [],
        searchPerformed: true,
        error: errorMessage,
      },
      errorMessages: [...(state.errorMessages || []), errorMessage],
    };
    span.setAttribute("error", true);
    span.setAttribute("error_message", errorMessage);
    span.end();
    return { ...state, ...errorState };
  }
}

function prepareEvaluationInputNode(state: State): State {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.prepareEvaluationInputNode", {
    attributes: {
      node_type: "graph_node",
      graph_name: "azureSearchGraph",
      function_name: "prepareEvaluationInputNode",
      input_search_op_result_present: !!state.searchOperationResult,
    },
  });

  console.log("[Graph] In prepareEvaluationInputNode");
  if (!state.searchOperationResult) {
    const errorResult: Partial<State> = {
      errorMessages: [
        ...(state.errorMessages || []),
        "Search results not available for evaluation.",
      ],
    };
    span.setAttribute("error", true);
    span.setAttribute(
      "error_message",
      "Search results not available for evaluation.",
    );
    span.end();
    return { ...state, ...errorResult };
  }

  const evaluationInput: EvaluationInput = {
    originalQuery: state.initialUserQuery,
    searchQueryUsed: state.currentSearchQuery,
    searchResults: state.searchOperationResult,
  };

  const result = {
    ...state,
    evaluationInput,
  };

  span.setAttribute(
    "output_evaluation_input_original_query",
    result.evaluationInput?.originalQuery,
  );
  span.setAttribute(
    "output_evaluation_input_search_query_used",
    result.evaluationInput?.searchQueryUsed,
  );
  span.end();
  return result;
}

async function evaluationAgentNode(state: State): Promise<State> {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.evaluationAgentNode", {
    attributes: {
      node_type: "graph_node",
      graph_name: "azureSearchGraph",
      function_name: "evaluationAgentNode",
      input_evaluation_input_present: !!state.evaluationInput,
      llm_config_present: !!OPENAI_API_KEY_FOR_CHAT,
    },
  });

  console.log("[Graph] In evaluationAgentNode");
  if (!state.evaluationInput) {
    const errorResult: Partial<State> = {
      errorMessages: [
        ...(state.errorMessages || []),
        "Evaluation input not prepared.",
      ],
    };
    span.setAttribute("error", true);
    span.setAttribute("error_message", "Evaluation input not prepared.");
    span.end();
    return { ...state, ...errorResult };
  }
  if (!OPENAI_API_KEY_FOR_CHAT) {
    console.warn(
      "[Graph] Evaluation LLM not configured (OPENAI_API_KEY missing). Skipping evaluation, marking as satisfactory.",
    );
    const skipResult = {
      ...state,
      evaluationResult: {
        isSatisfactory: true,
        feedback: "Evaluation skipped due to missing LLM config.",
        suggestedQuery: null,
        confidenceScore: 0,
      },
    };
    span.setAttribute("skipped_evaluation", true);
    span.setAttribute("skip_reason", "Missing LLM config");
    span.end();
    return skipResult;
  }

  const { originalQuery, searchQueryUsed, searchResults } =
    state.evaluationInput;

  const promptMessagesForEval: BaseMessage[] = [
    new HumanMessage(
      `Evaluate search results. Original Query: "${originalQuery}". Used Query: "${searchQueryUsed}". Results: ${searchResults.rawOutput.substring(0, 1000)}.
Respond JSON: { "isSatisfactory": bool, "feedback": string, "suggestedQuery": string | null, "confidenceScore": float (0-1) }.`,
    ),
  ];

  try {
    const response = await evaluationLlm.invoke(promptMessagesForEval);
    const evaluationText = response.content as string;
    console.log("[Graph] Evaluation LLM raw response:", evaluationText);

    let parsedJson;
    try {
      parsedJson = JSON.parse(evaluationText);
    } catch (parseError) {
      console.error(
        "[Graph] Failed to parse evaluation LLM response JSON:",
        parseError,
        "Raw text:",
        evaluationText,
      );
      throw new Error(
        `Failed to parse LLM response as JSON. Response: ${evaluationText}`,
      );
    }

    const evaluationOutput: EvaluationOutput = {
      isSatisfactory: parsedJson.isSatisfactory,
      feedback: parsedJson.feedback,
      suggestedQuery:
        parsedJson.suggestedQuery === undefined
          ? null
          : parsedJson.suggestedQuery,
      confidenceScore: parsedJson.confidenceScore,
    };

    const resultState: Partial<State> = {
      evaluationResult: evaluationOutput,
      messages: [
        ...(state.messages || []),
        new AIMessage(`Evaluation complete: ${evaluationOutput.feedback}`),
      ],
    };
    span.setAttribute(
      "output_is_satisfactory",
      evaluationOutput.isSatisfactory,
    );
    span.setAttribute(
      "output_feedback_length",
      evaluationOutput.feedback?.length || 0,
    );
    span.setAttribute(
      "output_suggested_query",
      evaluationOutput.suggestedQuery || "N/A",
    );
    span.setAttribute(
      "output_confidence_score",
      evaluationOutput.confidenceScore || 0,
    );
    span.end();
    return { ...state, ...resultState };
  } catch (error: any) {
    console.error("[Graph] Error in evaluationAgentNode:", error);
    // Create the error state with explicitly defined values
    const errorResult = {
      isSatisfactory: false,
      feedback: `Evaluation failed: ${error.message}`,
      suggestedQuery: null,
      confidenceScore: 0,
    };

    const errorStatePartial: Partial<State> = {
      evaluationResult: errorResult,
      errorMessages: [
        ...(state.errorMessages || []),
        `Evaluation agent error: ${error.message}`,
      ],
    };

    span.setAttribute("error", true);
    span.setAttribute(
      "error_message",
      `Evaluation agent error: ${error.message}`,
    );
    // Now use the local errorResult variable which is guaranteed to be defined
    span.setAttribute("output_is_satisfactory", errorResult.isSatisfactory);
    span.setAttribute("output_confidence_score", errorResult.confidenceScore);
    span.end();
    return { ...state, ...errorStatePartial };
  }
}

function routeAfterEvaluation(
  state: State,
): "formatFinalResponse" | "incrementRetryCounter" {
  console.log("[Graph] In routeAfterEvaluation");
  const maxRetries = 1;

  if (state.evaluationResult?.isSatisfactory) {
    console.log(
      "[Graph] Evaluation satisfactory. Routing to formatFinalResponse.",
    );
    return "formatFinalResponse";
  } else if (
    state.retryCount < maxRetries &&
    state.evaluationResult?.suggestedQuery
  ) {
    console.log(
      "[Graph] Evaluation unsatisfactory, retry possible. Routing to incrementRetryCounter.",
    );
    return "incrementRetryCounter";
  } else {
    console.log(
      "[Graph] Evaluation unsatisfactory, no more retries or no suggested query. Routing to formatFinalResponse.",
    );
    return "formatFinalResponse";
  }
}

function formatFinalResponseNode(state: State): State {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.formatFinalResponseNode", {
    attributes: {
      node_type: "graph_node",
      graph_name: "azureSearchGraph",
      function_name: "formatFinalResponseNode",
      input_search_error: state.searchOperationResult?.error || "None",
      input_parsed_results_count: (
        state.searchOperationResult?.parsedResults || []
      ).length,
      input_evaluation_feedback: state.evaluationResult?.feedback || "N/A",
    },
  });

  console.log("[Graph] In formatFinalResponseNode");
  let finalAnswer: string;

  if (
    state.searchOperationResult?.error &&
    !state.searchOperationResult?.parsedResults?.length
  ) {
    finalAnswer = `Search failed: ${state.searchOperationResult.error}`;
  } else if (
    !state.searchOperationResult?.parsedResults?.length &&
    state.searchOperationResult?.searchPerformed
  ) {
    finalAnswer = `No relevant documents found for query: "${state.currentSearchQuery}".`;
    if (state.evaluationResult?.feedback) {
      finalAnswer += `\nEvaluation: ${state.evaluationResult.feedback}`;
    }
  } else if (state.searchOperationResult?.parsedResults?.length) {
    finalAnswer = `Search Results for "${state.currentSearchQuery}":\n${JSON.stringify(state.searchOperationResult.parsedResults, null, 2)}`;
    if (state.evaluationResult?.feedback) {
      finalAnswer += `\n\nEvaluation: ${state.evaluationResult.feedback}`;
      if (
        !state.evaluationResult.isSatisfactory &&
        state.evaluationResult.suggestedQuery
      ) {
        finalAnswer += `\nYou might want to try searching for: "${state.evaluationResult.suggestedQuery}"`;
      }
    }
  } else if (state.errorMessages && state.errorMessages.length > 0) {
    finalAnswer = `An error occurred: ${state.errorMessages.join("\n")}`;
  } else {
    finalAnswer =
      "Could not retrieve an answer. Search did not yield results or an unexpected error occurred.";
  }

  const result = {
    ...state,
    finalAnswer,
  };

  span.setAttribute(
    "output_final_answer_length",
    result.finalAnswer?.length || 0,
  );
  if (result && result.errorMessages && result.errorMessages.length > 0) {
    console.error("Errors during execution:", result.errorMessages);
    span.setAttribute("execution_errors", JSON.stringify(result.errorMessages));
  }
  span.end();
  return result as State;
}

function incrementRetryCounterNode(state: State): State {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.incrementRetryCounterNode", {
    attributes: {
      node_type: "graph_node",
      graph_name: "azureSearchGraph",
      function_name: "incrementRetryCounterNode",
      input_retry_count: state.retryCount,
    },
  });

  // Ensure we have a definite number value for retryCount
  const newRetryCount = state.retryCount + 1;

  const result: Partial<State> = {
    retryCount: newRetryCount,
  };

  // Use the local newRetryCount which is guaranteed to be a number
  span.setAttribute("output_new_retry_count", newRetryCount);
  span.end();
  return { ...state, ...result };
}

function routeFromAzureSearch(
  state: State,
): "prepareEvaluationInput" | "formatFinalResponse" {
  if (
    state.searchOperationResult?.error &&
    !state.searchOperationResult?.parsedResults?.length
  ) {
    console.log(
      "[Graph] Azure Search Node resulted in an error with no results. Routing to formatFinalResponse.",
    );
    return "formatFinalResponse";
  }
  console.log(
    "[Graph] Azure Search Node completed. Routing to prepareEvaluationInput.",
  );
  return "prepareEvaluationInput";
}

// --- Graph Definition ---
// Create a builder
const builder = new StateGraph<State>({
  channels: AzureSearchGraphStateChannels,
})
  .addNode("prepareSearchInput", prepareSearchInputNode)
  .addNode("azureSearch", azureSearchNode)
  .addNode("prepareEvaluationInput", prepareEvaluationInputNode)
  .addNode("evaluationAgent", evaluationAgentNode)
  .addNode("formatFinalResponse", formatFinalResponseNode)
  .addNode("incrementRetryCounter", incrementRetryCounterNode);

// Add edges between nodes
builder
  .addEdge(START, "prepareSearchInput")
  .addEdge("prepareSearchInput", "azureSearch")
  .addConditionalEdges("azureSearch", routeFromAzureSearch, {
    prepareEvaluationInput: "prepareEvaluationInput",
    formatFinalResponse: "formatFinalResponse",
  })
  .addEdge("prepareEvaluationInput", "evaluationAgent")
  .addConditionalEdges("evaluationAgent", routeAfterEvaluation, {
    incrementRetryCounter: "incrementRetryCounter",
    formatFinalResponse: "formatFinalResponse",
  })
  .addEdge("incrementRetryCounter", "prepareSearchInput")
  .addEdge("formatFinalResponse", END);

// Compile the graph
export const azureSearchGraph = builder.compile();

// --- Example Usage (for testing) ---
async function runGraph(initialQuery: string) {
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("azureSearchGraph.runGraph", {
    attributes: {
      node_type: "graph_runner",
      graph_name: "azureSearchGraph",
      function_name: "runGraph",
      input_initial_query: initialQuery,
    },
  });

  console.log(
    `\n🚀 --- Running Azure Search Graph for query: "${initialQuery}" ---`,
  );

  const initialState: State = {
    initialUserQuery: initialQuery,
    currentSearchQuery: initialQuery, // Set initially
    searchParams: null,
    searchOperationResult: null,
    evaluationInput: null,
    evaluationResult: null,
    finalAnswer: null,
    errorMessages: [],
    messages: [new HumanMessage(initialQuery)],
    retryCount: 0,
  };

  try {
    const result = await azureSearchGraph.invoke(initialState);
    console.log("\n🏁 --- Graph Execution Result ---");
    console.log("Final Answer:", result.finalAnswer);
    if (result.errorMessages && result.errorMessages.length > 0) {
      console.error("Errors during execution:", result.errorMessages);
      span.setAttribute(
        "execution_errors",
        JSON.stringify(result.errorMessages),
      );
    }
    span.setAttribute(
      "output_final_answer_length",
      result.finalAnswer?.length || 0,
    );
    span.end();
    return result;
  } catch (e: any) {
    console.error("\n💥 --- Graph Invocation Error ---");
    console.error(e);
    span.setAttribute("error", true);
    span.setAttribute("error_message", e.message || String(e));
    if (e.message?.includes("interrupt")) {
      console.log("Graph execution was interrupted.");
      span.setAttribute("interrupted", true);
    }
    span.end();
    return null;
  }
}

/**
 * To make this graph usable as a tool itself within a larger LangGraph or agent system,
 * you might wrap its invocation in a LangChain Tool class.
 *
 * import { z } from "zod"; // Ensure zod is imported if using this tool wrapper
 * class AzureSearchGraphTool extends Tool {
 *   name = "azure_search_graph_evaluator";
 *   description = "Performs a search using Azure Cognitive Search and evaluates the results. Use for complex queries needing high-quality, validated information.";
 *   // Define schema for the input of this graph tool
 *   schema = z.object({
 *      input: z.string().describe("The user's query to search for.")
 *   }).transform(obj => obj.input); // Tool base class usually expects input directly
 *
 *   async _call(input: string): Promise<string> {
 *     console.log(`[AzureSearchGraphTool] Received input: ${input}`);
 *     const result = await runGraph(input);
 *     return result?.finalAnswer ?? "Graph execution failed or produced no answer.";
 *   }
 * }
 *
 * export const azureSearchGraphToolInstance = new AzureSearchGraphTool();
 */
