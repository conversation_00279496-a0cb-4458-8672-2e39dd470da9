import { azureSearchGraph } from "./azureSearchGraph";
import { HumanMessage } from "@langchain/core/messages";

async function testAzureSearchGraph() {
  // Test queries
  const testQueries = [
    "What are the benefits of high-intensity interval training?",
    "Tell me about protein requirements for muscle building",
    "How to prevent injuries during weightlifting",
  ];

  // Run each test query
  for (const query of testQueries) {
    console.log(`\n🔍 TESTING QUERY: "${query}"`);
    console.log("=".repeat(80));

    // Prepare initial state
    const initialState = {
      initialUserQuery: query,
      currentSearchQuery: query,
      searchParams: null,
      searchOperationResult: null,
      evaluationInput: null,
      evaluationResult: null,
      finalAnswer: null,
      errorMessages: [],
      messages: [new HumanMessage(query)],
      retryCount: 0,
    };

    try {
      console.log("Starting graph execution...");
      const result = await azureSearchGraph.invoke(initialState);

      console.log("\n✅ EXECUTION COMPLETED");
      console.log("-".repeat(40));
      console.log("Final Answer:", result.finalAnswer);

      // Display any errors
      if (result.errorMessages && result.errorMessages.length > 0) {
        console.error("\n⚠️ Errors during execution:");
        result.errorMessages.forEach((err: string, i: number) => {
          console.error(`${i + 1}. ${err}`);
        });
      }

      // Display retry information if applicable
      if (result.retryCount > 0) {
        console.log(`\n🔄 Retries performed: ${result.retryCount}`);
        if (result.evaluationResult) {
          console.log(
            `Evaluation Feedback: ${result.evaluationResult.feedback}`,
          );
          console.log(
            `Suggested Query: ${result.evaluationResult.suggestedQuery}`,
          );
        }
      }
    } catch (error: any) {
      console.error("\n❌ ERROR DURING EXECUTION:");
      console.error(`Message: ${error.message}`);
      console.error(`Stack: ${error.stack}`);
    }

    console.log("=".repeat(80));
  }
}

// Run the test
console.log("🚀 STARTING AZURE SEARCH GRAPH TEST");
testAzureSearchGraph()
  .then(() => console.log("✅ TEST COMPLETED"))
  .catch((err: Error) => console.error("❌ TEST FAILED:", err))
  .finally(() => console.log("🏁 TEST SCRIPT FINISHED"));
