/**
 * Onboarding state definition for the onboarding flow.
 * Extends the base GraphState with onboarding-specific fields.
 */

import { GraphState, graphChannels } from "./state";
import { BaseMessage } from "@langchain/core/messages";

/**
 * OnboardingState interface extends GraphState with onboarding-specific fields
 */
export interface OnboardingState extends GraphState {
  // Onboarding specific fields
  currentOnboardingStep: number; // Current step in the onboarding flow (1-5)
  userFitnessGoals: string[]; // User's fitness goals collected in step 2
  userTrainingHistory: Record<string, any>; // User's training history collected in step 3
  weeklyPlan: Record<string, any> | null; // Generated weekly plan from step 4
  onboardingComplete: boolean; // Whether onboarding is complete
  stepContent: Record<number, string>; // Content for each step
}

/**
 * The channels for Onboarding StateGraph
 */
export const onboardingChannels = {
  // Inherit base channels
  ...graphChannels,

  // Override isOnboarding default to true for the onboarding graph
  isOnboarding: {
    value: (left: boolean = true, right: boolean = true) => right ?? left,
    default: () => true,
  },

  // Add onboarding-specific channels
  currentOnboardingStep: {
    value: (left: number = 1, right: number) => right ?? left,
    default: () => 1,
  },

  userFitnessGoals: {
    value: (left: string[] = [], right: string[]) => right ?? left,
    default: () => [],
  },

  userTrainingHistory: {
    value: (left: Record<string, any> = {}, right: Record<string, any>) => ({
      ...left,
      ...right,
    }),
    default: () => ({}),
  },

  weeklyPlan: {
    value: (
      left: Record<string, any> | null = null,
      right: Record<string, any> | null,
    ) => right ?? left,
    default: () => null,
  },

  onboardingComplete: {
    value: (left: boolean = false, right: boolean) => right ?? left,
    default: () => false,
  },

  stepContent: {
    value: (
      left: Record<number, string> = {},
      right: Record<number, string>,
    ) => ({
      ...left,
      ...right,
    }),
    default: () => ({}),
  },
};
