/**
 * State definition for coaching graph implementation.
 */

import { END, messagesStateReducer } from "@langchain/langgraph";
import { BaseMessage } from "@langchain/core/messages";

/**
 * GraphState interface defines the shape of the state object used in the graph
 */
export interface GraphState {
  messages: BaseMessage[];
  userProfile: Record<string, any> | null;
  routingDecision: string | string[] | typeof END | null;
  pendingAgents: string[] | null; // To track agents for sequential execution
  plan: string[] | null; // Stores the execution plan from the planning node
  current_step: number | null; // Tracks the current step in the plan

  // Plan generation related fields
  domainContributions?: Record<string, string>; // Stores plan contributions from each domain coach
  requiredDomains?: string[]; // Domains needed for the specific plan
  completedDomains?: string[]; // Domains that have already contributed
  aggregatedPlan?: string | null; // The final combined plan
  proceedToGeneration?: boolean; // Flag to indicate if we should generate the plan
  currentPlan?: any; // Optional current plan data

  // Onboarding related fields
  isOnboarding?: boolean; // Indicates if this is an onboarding session
}

// Add Plan Generation State Fields
export interface PlanGenerationStateFields {
  domainContributions?: Record<string, string>; // Stores plan contributions from each domain coach
  requiredDomains?: string[]; // Domains needed for this specific plan
  completedDomains?: string[]; // Domains that have already contributed
  aggregatedPlan?: string | null; // The final combined plan
  proceedToGeneration?: boolean; // Flag to indicate if we should generate the plan
}

// Combine base state with plan generation fields
export type FullGraphState = GraphState & PlanGenerationStateFields;

/**
 * The channels for StateGraph using the interface properties
 */
export const graphChannels = {
  messages: {
    value: messagesStateReducer,
    default: () => [],
  },
  userProfile: {
    value: (
      left?: Record<string, any> | null,
      right?: Record<string, any> | null,
    ): Record<string, any> | null => right ?? left ?? null,
    default: (): Record<string, any> | null => null,
  },
  routingDecision: {
    value: (
      left?: string | string[] | typeof END | null,
      right?: string | string[] | typeof END | null,
    ): string | string[] | typeof END | null => right ?? left ?? null,
    default: (): string | string[] | typeof END | null => null,
  },
  // Modified channel definition for pendingAgents
  pendingAgents: {
    value: (left: string[] | null, right: string[] | null): string[] | null =>
      right !== undefined ? right : (left ?? null), // Ensure null default if both undefined
    default: (): string[] | null => null,
  },
  // Added channels for plan and current_step
  plan: {
    value: (left: string[] | null, right: string[] | null): string[] | null =>
      right ?? left ?? null,
    default: (): string[] | null => null,
  },
  current_step: {
    value: (left: number | null, right: number | null): number | null =>
      right ?? left ?? null,
    default: (): number | null => null,
  },

  // Plan generation related channels
  domainContributions: {
    value: (
      left?: Record<string, string>,
      right?: Record<string, string>,
    ): Record<string, string> => ({
      ...(left || {}),
      ...(right || {}),
    }),
    default: (): Record<string, string> => ({}),
  },
  requiredDomains: {
    value: (left?: string[], right?: string[]): string[] => right ?? left ?? [],
    default: (): string[] => [],
  },
  completedDomains: {
    value: (left?: string[], right?: string[]): string[] => {
      const leftDomains = left || [];
      const rightDomains = right || [];
      return [...new Set([...leftDomains, ...rightDomains])];
    },
    default: (): string[] => [],
  },
  aggregatedPlan: {
    value: (left?: string | null, right?: string | null): string | null =>
      right ?? left ?? null,
    default: (): string | null => null,
  },
  proceedToGeneration: {
    value: (left?: boolean, right?: boolean): boolean => right ?? left ?? false,
    default: (): boolean => false,
  },
  currentPlan: {
    value: (left: any = null, right: any = null) => right ?? left,
    default: () => null,
  },

  // Add isOnboarding channel
  isOnboarding: {
    value: (left: boolean = false, right: boolean = false) => right ?? left,
    default: () => false,
  },
};
