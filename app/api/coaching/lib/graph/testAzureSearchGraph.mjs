// Define key variables at the top level
const AZURE_SEARCH_API_KEY =
  "CANLcXN6GMNE0aBYYhWthal1jgHou5pXnNPvB0gpAzSeCpcWY";
const AZURE_SEARCH_SERVICE_NAME = "athlea-search-service";
const EMBEDDING_API_KEY = "faa8dBaBcf747bba12e6ddec31c1ba8";
const EMBEDDING_ENDPOINT =
  "https://athlea-openai.openai.azure.com/openai/deployments/text-embedding-3-large/embeddings?api-version=2023-05-15";
const AZURE_OPENAI_API_KEY = "faa8dBaBcf747bba12e6ddec31c1ba8";

// Set process.env variables
process.env.AZURE_SEARCH_API_KEY = AZURE_SEARCH_API_KEY;
process.env.AZURE_SEARCH_SERVICE_NAME = AZURE_SEARCH_SERVICE_NAME;
process.env.EMBEDDING_API_KEY = EMBEDDING_API_KEY;
process.env.EMBEDDING_ENDPOINT = EMBEDDING_ENDPOINT;
process.env.AZURE_OPENAI_API_KEY = AZURE_OPENAI_API_KEY;
process.env.OPENAI_API_KEY = AZURE_OPENAI_API_KEY; // Also set this for the ChatOpenAI model

// Mock the Azure Search Tool class before importing the module
import { Tool } from "@langchain/core/tools";

// Create a mock implementation of the AzureSearchTool
class MockAzureSearchTool extends Tool {
  name = "azure_cognitive_search";
  description = "Search for information in the Azure Cognitive Search index.";

  constructor() {
    super();
    console.log("[MockAzureSearchTool] Initialized successfully");
  }

  async _call(inputString) {
    console.log(`[MockAzureSearchTool] Received query: ${inputString}`);

    let query = inputString;
    try {
      // Check if input is JSON
      if (inputString.trim().startsWith("{")) {
        const parsed = JSON.parse(inputString);
        query = parsed.query || inputString;
      }
    } catch (e) {
      // If parsing fails, use the input string directly
      query = inputString;
    }

    // Mock search results based on query keywords
    const mockResults = [
      {
        title: "HIIT Benefits",
        content:
          "High-intensity interval training (HIIT) offers several benefits: improved cardiovascular health, increased calorie burn, efficient workouts, and improved metabolic function.",
        score: 0.92,
        source: "fitness-database",
        url: "https://example.com/hiit-benefits",
        docId: "doc123",
      },
      {
        title: "Muscle Building Nutrition",
        content:
          "For muscle building, protein requirements typically range from 1.6 to 2.2g per kg of bodyweight daily. Higher intake may benefit during caloric deficits.",
        score: 0.88,
        source: "nutrition-database",
        url: "https://example.com/protein-requirements",
        docId: "doc456",
      },
      {
        title: "Weightlifting Safety",
        content:
          "To prevent injuries during weightlifting: maintain proper form, warm up adequately, start with lighter weights, increase weight gradually, and incorporate rest days.",
        score: 0.85,
        source: "safety-database",
        url: "https://example.com/weightlifting-safety",
        docId: "doc789",
      },
    ];

    // Filter results based on the query
    const filteredResults = mockResults.filter((result) => {
      const queryLower = query.toLowerCase();
      return (
        result.title.toLowerCase().includes(queryLower) ||
        result.content.toLowerCase().includes(queryLower)
      );
    });

    const results =
      filteredResults.length > 0
        ? filteredResults
        : [
            {
              title: "Generic Fitness Advice",
              content:
                "Regular exercise, proper nutrition, and adequate rest are fundamental for fitness progress.",
              score: 0.75,
              source: "general-database",
              url: "https://example.com/general-fitness",
              docId: "doc999",
            },
          ];

    console.log(
      `[MockAzureSearchTool] Returning ${results.length} mock results`,
    );
    return JSON.stringify(results, null, 2);
  }
}

// Replace the real AzureSearchTool with our mock version
import { Tool as OriginalTool } from "@langchain/core/tools";
const originalCreate = Object.create;
Object.create = function (proto, propertiesObject) {
  if (proto === OriginalTool.prototype) {
    const obj = originalCreate.call(this, proto, propertiesObject);
    if (obj.name === "azure_cognitive_search") {
      console.log(
        "[MOCK] Intercepted AzureSearchTool creation, replacing with mock",
      );
      return new MockAzureSearchTool();
    }
    return obj;
  }
  return originalCreate.call(this, proto, propertiesObject);
};

// Now import the graph after the mocking is set up
import { azureSearchGraph } from "./azureSearchGraph.ts";
import { HumanMessage } from "@langchain/core/messages";

// Test the Azure Search Graph
async function testAzureSearchGraph() {
  // Test queries
  const testQueries = [
    "What are the benefits of high-intensity interval training?",
    "Tell me about protein requirements for muscle building",
    "How to prevent injuries during weightlifting",
  ];

  // Run each test query
  for (const query of testQueries) {
    console.log(`\n🔍 TESTING QUERY: "${query}"`);
    console.log("=".repeat(80));

    // Prepare initial state
    const initialState = {
      initialUserQuery: query,
      currentSearchQuery: query,
      searchParams: null,
      searchOperationResult: null,
      evaluationInput: null,
      evaluationResult: null,
      finalAnswer: null,
      errorMessages: [],
      messages: [new HumanMessage(query)],
      retryCount: 0,
    };

    try {
      console.log("Starting graph execution...");
      const result = await azureSearchGraph.invoke(initialState);

      console.log("\n✅ EXECUTION COMPLETED");
      console.log("-".repeat(40));
      console.log("Final Answer:", result.finalAnswer);

      // Display any errors
      if (result.errorMessages && result.errorMessages.length > 0) {
        console.error("\n⚠️ Errors during execution:");
        result.errorMessages.forEach((err, i) => {
          console.error(`${i + 1}. ${err}`);
        });
      }

      // Display retry information if applicable
      if (result.retryCount > 0) {
        console.log(`\n🔄 Retries performed: ${result.retryCount}`);
        if (result.evaluationResult) {
          console.log(
            `Evaluation Feedback: ${result.evaluationResult.feedback}`,
          );
          console.log(
            `Suggested Query: ${result.evaluationResult.suggestedQuery}`,
          );
        }
      }
    } catch (error) {
      console.error("\n❌ ERROR DURING EXECUTION:");
      console.error(`Message: ${error.message}`);
      console.error(`Stack: ${error.stack}`);
    }

    console.log("=".repeat(80));
  }
}

// Run the test
console.log("🚀 STARTING AZURE SEARCH GRAPH TEST");
testAzureSearchGraph()
  .then(() => console.log("✅ TEST COMPLETED"))
  .catch((err) => console.error("❌ TEST FAILED:", err))
  .finally(() => console.log("🏁 TEST SCRIPT FINISHED"));
