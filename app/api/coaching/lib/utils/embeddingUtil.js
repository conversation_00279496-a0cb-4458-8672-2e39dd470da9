import fetch from "node-fetch";
import { trace } from "@opentelemetry/api";

/**
 * Extract deployment ID from Azure OpenAI embedding endpoint URL
 *
 * @param {string} url - The Azure OpenAI embedding endpoint URL
 * @returns {string|null} - The extracted deployment ID or null if not found
 */
function extractDeploymentIdFromUrl(url) {
  if (!url) return null;

  // Match pattern: deployments/DEPLOYMENT_ID/embeddings
  const match = url.match(/\/deployments\/([^\/]+)\/embeddings/);

  if (match && match[1]) {
    return match[1];
  }

  return null;
}

/**
 * Generate embeddings using Azure OpenAI
 *
 * @param {string} text - The text to embed
 * @param {string} [apiKey] - The Azure OpenAI API key, defaults to process.env.EMBEDDING_API_KEY
 * @param {string} [endpoint] - The endpoint URL, defaults to process.env.EMBEDDING_ENDPOINT
 * @param {string} [forcedModelId] - Optional override for model ID, normally extracted from endpoint URL
 * @returns {Promise<number[]|null>} - The embedding vector or null on failure
 */
export async function generateEmbedding(text, apiKey, endpoint, forcedModelId) {
  // Create a tracer and start a span
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("embeddingUtil.generateEmbedding", {
    attributes: {
      component: "embeddingUtil",
      function_name: "generateEmbedding",
      text_length: text ? text.length : 0,
      custom_api_key: !!apiKey,
      custom_endpoint: !!endpoint,
      custom_model_id: !!forcedModelId,
    },
  });

  // Use provided values or fall back to environment variables
  const embeddingApiKey = apiKey || process.env.EMBEDDING_API_KEY;
  const embeddingEndpoint = endpoint || process.env.EMBEDDING_ENDPOINT;

  if (!embeddingApiKey || !embeddingEndpoint) {
    console.error("generateEmbedding: Missing API key or endpoint");
    span.setAttribute("error", true);
    span.setAttribute("error_message", "Missing API key or endpoint");
    span.end();
    return null;
  }

  // Try to extract deployment ID from the URL
  let urlDeploymentId = extractDeploymentIdFromUrl(embeddingEndpoint);

  // Priority: 1. Forced model ID, 2. URL deployment ID, 3. Environment variable, 4. Default
  const modelDeploymentId =
    forcedModelId ||
    urlDeploymentId ||
    process.env.AZURE_EMBEDDING_DEPLOYMENT_ID ||
    "text-embedding-3-large";

  span.setAttribute("model_id", modelDeploymentId);
  span.setAttribute("extracted_deployment_id", urlDeploymentId || "none");

  try {
    console.log(`Generating embedding for: "${text.substring(0, 50)}..."`);
    console.log(`Using embedding endpoint: ${embeddingEndpoint}`);
    console.log(`Using model ID: ${modelDeploymentId}`);
    console.log(
      `(Deployment ID extracted from URL: ${urlDeploymentId || "None"})`,
    );

    const requestBody = {
      input: text,
      model: modelDeploymentId,
    };

    const response = await fetch(embeddingEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "api-key": embeddingApiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `generateEmbedding: Failed (${response.status}): ${errorText}`,
      );

      span.setAttribute("error", true);
      span.setAttribute("error_status", response.status);
      span.setAttribute("error_message", errorText);
      span.end();

      return null;
    }

    const result = await response.json();
    if (result.data && result.data.length > 0 && result.data[0].embedding) {
      console.log(
        `Successfully generated embedding vector of length ${result.data[0].embedding.length}`,
      );

      span.setAttribute("success", true);
      span.setAttribute("vector_length", result.data[0].embedding.length);
      span.end();

      return result.data[0].embedding;
    } else {
      console.error("generateEmbedding: Invalid response structure:", result);

      span.setAttribute("error", true);
      span.setAttribute("error_message", "Invalid response structure");
      span.end();

      return null;
    }
  } catch (error) {
    console.error("generateEmbedding: Error:", error);

    span.setAttribute("error", true);
    span.setAttribute("error_message", String(error));
    span.end();

    return null;
  }
}

export default {
  generateEmbedding,
};
