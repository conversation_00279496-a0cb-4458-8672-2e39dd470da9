import axios from "axios";
import clientPromise from "@/app/lib/mongodb";
import { routeLogger } from "./logger";
import { fetchUserDataFromAPI } from "../../../agent-tools/utils/redux-user-data";

// URL check for server-side
const isServer = typeof window === "undefined";

/**
 * Interface for training profile data
 */
export interface TrainingProfile {
  [domain: string]: {
    [key: string]: any;
  };
}

/**
 * Interface for a training plan phase
 */
export interface PlanPhase {
  name: string;
  duration: number;
  weeks: number;
  disciplines: string[];
  description?: string;
  goal?: string;
  // Domain-specific data for each discipline in this phase
  [domain: string]: any;
}

/**
 * Interface for a complete training plan
 */
export interface TrainingPlan {
  plan_id: string;
  plan_name: string;
  plan_type: string;
  plan_description?: string;
  dateAdded: string;
  current_phase: number;
  phases: PlanPhase[];
  adjustment_history?: Array<{
    date: string;
    adjustments: Array<{
      phase_index: number;
      discipline: string;
      parameters: Record<string, string>;
      impact: string;
      rationale: string;
    }>;
  }>;
  // Other plan metadata
  [key: string]: any;
}

/**
 * Fetch a user's training profile directly from MongoDB when on server
 * @deprecated Use fetchUserDataFromAPI instead
 */
async function fetchTrainingProfileFromDB(
  userId: string,
): Promise<TrainingProfile | null> {
  try {
    routeLogger.info(
      `Fetching training profile from MongoDB for userId: ${userId}`,
    );
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const userInfo = await collection.findOne(
      { user_id: userId },
      { projection: { training_profile: 1, _id: 0 } },
    );

    if (!userInfo || !userInfo.training_profile) {
      routeLogger.warn(`No training profile found in DB for userId: ${userId}`);
      return null;
    }

    routeLogger.info(
      `Successfully fetched training profile from MongoDB for userId: ${userId}`,
    );
    return userInfo.training_profile;
  } catch (error) {
    routeLogger.error("Error fetching training profile from MongoDB:", error);
    return null;
  }
}

/**
 * Fetch a user's current plan directly from MongoDB when on server
 * @deprecated Use fetchUserDataFromAPI instead
 */
async function fetchCurrentPlanFromDB(
  userId: string,
): Promise<TrainingPlan | null> {
  try {
    routeLogger.info(
      `Fetching current plan from MongoDB for userId: ${userId}`,
    );
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const userInfo = await collection.findOne(
      { user_id: userId },
      { projection: { currentPlan: 1, _id: 0 } },
    );

    if (!userInfo || !userInfo.currentPlan) {
      routeLogger.warn(`No current plan found in DB for userId: ${userId}`);
      return null;
    }

    routeLogger.info(
      `Successfully fetched current plan from MongoDB for userId: ${userId}`,
    );
    return userInfo.currentPlan;
  } catch (error) {
    routeLogger.error("Error fetching current plan from MongoDB:", error);
    return null;
  }
}

/**
 * Fetch a user's training profile
 */
export async function fetchTrainingProfile(
  userId: string,
): Promise<TrainingProfile> {
  try {
    routeLogger.info(`Fetching training profile for userId: ${userId}`);

    // In server environment, fetch directly from DB
    if (isServer) {
      // Fetch directly from MongoDB
      const dbProfile = await fetchTrainingProfileFromDB(userId);
      if (dbProfile) {
        routeLogger.info(
          `Using MongoDB training profile data for userId: ${userId}`,
        );
        return dbProfile;
      }

      // Fallback to mock data if DB fails
      routeLogger.warn(
        `Falling back to mock training profile data for userId: ${userId}`,
      );
      return getMockTrainingProfile(userId);
    }

    // Client-side fetching uses axios
    try {
      const response = await axios.get(`/api/users/${userId}/training-profile`);
      routeLogger.info(
        `Successfully fetched client-side training profile for userId: ${userId}`,
      );
      return response.data;
    } catch (apiError) {
      routeLogger.error(
        `Error fetching client-side training profile: ${apiError}`,
      );
      return getMockTrainingProfile(userId);
    }
  } catch (error) {
    routeLogger.error("Error in fetchTrainingProfile:", error);
    return getMockTrainingProfile(userId);
  }
}

/**
 * Fetch a user's current training plan
 */
export async function fetchCurrentPlan(
  userId: string,
): Promise<TrainingPlan | null> {
  try {
    routeLogger.info(`Fetching current plan for userId: ${userId}`);

    // In server environment, fetch directly from DB
    if (isServer) {
      // Fetch directly from MongoDB
      const dbPlan = await fetchCurrentPlanFromDB(userId);
      if (dbPlan) {
        routeLogger.info(
          `Using MongoDB current plan data for userId: ${userId}`,
        );
        return dbPlan;
      }

      // Fallback to mock data if DB fails
      routeLogger.warn(
        `Falling back to mock current plan data for userId: ${userId}`,
      );
      return getMockTrainingPlan(userId);
    }

    // Client-side fetching uses axios
    try {
      const response = await axios.get(`/api/users/${userId}/plan-data`);
      routeLogger.info(
        `Successfully fetched client-side current plan for userId: ${userId}`,
      );
      return response.data;
    } catch (apiError) {
      routeLogger.error(`Error fetching client-side current plan: ${apiError}`);
      return getMockTrainingPlan(userId);
    }
  } catch (error) {
    routeLogger.error("Error in fetchCurrentPlan:", error);
    return getMockTrainingPlan(userId);
  }
}

/**
 * Fetch both training profile and current plan
 */
export async function fetchUserData(userId: string): Promise<{
  trainingProfile: TrainingProfile;
  currentPlan: TrainingPlan | null;
}> {
  try {
    const [trainingProfile, currentPlan] = await Promise.all([
      fetchTrainingProfile(userId),
      fetchCurrentPlan(userId),
    ]);

    return {
      trainingProfile,
      currentPlan,
    };
  } catch (error) {
    console.error("Error fetching user data:", error);
    return {
      trainingProfile: getMockTrainingProfile(userId),
      currentPlan: getMockTrainingPlan(userId),
    };
  }
}

/**
 * Format training profile data for prompt consumption
 */
export function formatTrainingProfileForPrompt(
  profile: TrainingProfile,
): string {
  try {
    if (!profile || Object.keys(profile).length === 0) {
      return "No training profile data available.";
    }

    let profileText = "User Training Profile:\n\n";

    // Process each domain in the profile
    for (const [domain, data] of Object.entries(profile)) {
      if (!data || typeof data !== "object") {
        routeLogger.warn(`Invalid domain data for "${domain}": ${typeof data}`);
        continue;
      }

      profileText += `${domain.toUpperCase()} PROFILE:\n`;

      // Special handling for nutrition data that might be stored character by character
      if (
        domain === "nutrition" &&
        Object.keys(data).every((key) => !isNaN(Number(key)))
      ) {
        // This is likely character-by-character storage - reconstruct the string
        const nutritionText = Object.keys(data)
          .sort((a, b) => Number(a) - Number(b))
          .map((key) => data[key])
          .join("");

        profileText += `- Nutrition Information: ${nutritionText}\n`;
        continue;
      }

      // Format each key-value pair in this domain
      for (const [key, value] of Object.entries(data)) {
        // Format the key for better readability
        const formattedKey = key
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());

        // Format the value based on its type with robust error handling
        let formattedValue = "";
        try {
          if (Array.isArray(value)) {
            // Safe handling of array join
            formattedValue = value
              .filter((item) => item !== undefined && item !== null)
              .map((item) => String(item))
              .join(", ");
          } else if (typeof value === "object" && value !== null) {
            try {
              formattedValue = JSON.stringify(value);
            } catch (jsonError) {
              routeLogger.error(
                `Error stringifying object value for ${key}: ${jsonError}`,
              );
              formattedValue = "[Complex Object]";
            }
          } else {
            formattedValue = String(value);
          }
        } catch (formatError) {
          routeLogger.error(
            `Error formatting value for ${key}: ${formatError}`,
          );
          formattedValue = "[Error: Could not format value]";
        }

        profileText += `- ${formattedKey}: ${formattedValue}\n`;
      }

      profileText += "\n";
    }

    return profileText;
  } catch (error) {
    routeLogger.error(`Error in formatTrainingProfileForPrompt: ${error}`);
    return "Error formatting training profile data.";
  }
}

/**
 * Formats current plan for prompt context
 *
 * @param plan The training plan to format
 * @param currentDomain Optional domain to focus on
 * @returns A formatted string for use in prompts
 */
export function formatCurrentPlanForPrompt(
  plan: TrainingPlan | null,
  currentDomain?: string,
): string {
  try {
    if (!plan) {
      return "No current training plan.";
    }

    let planText = `Current Training Plan: ${plan.plan_name || "Unknown"} (${plan.plan_type || "Unknown"})\n\n`;

    // Add plan description if available
    if (plan.plan_description) {
      planText += `Plan Description: ${plan.plan_description}\n\n`;
    }

    // Add current phase information
    const currentPhaseIndex = plan.current_phase || 0;
    const phases = plan.phases || [];

    if (!Array.isArray(phases)) {
      routeLogger.warn(`Plan phases is not an array: ${typeof phases}`);
      return planText + "Error: Plan phases data invalid.";
    }

    const currentPhase = phases[currentPhaseIndex];

    if (currentPhase) {
      planText += `CURRENT PHASE (${currentPhaseIndex + 1}/${phases.length}): ${currentPhase.name || "Unknown"}\n`;
      planText += `- Duration: ${currentPhase.duration || "Unknown"} weeks\n`;
      planText += `- Goal: ${currentPhase.goal || "Not specified"}\n`;

      // If a domain is specified, focus on that domain's details
      if (currentDomain && currentPhase[currentDomain]) {
        planText += `\n${currentDomain.toUpperCase()} DETAILS:\n`;
        const domainData = currentPhase[currentDomain];

        if (typeof domainData === "object" && domainData !== null) {
          for (const [key, value] of Object.entries(domainData)) {
            try {
              const formattedKey = key
                .replace(/_/g, " ")
                .replace(/\b\w/g, (l) => l.toUpperCase());

              // Safe formatting of value
              let formattedValue = "";
              if (Array.isArray(value)) {
                formattedValue = value
                  .filter((item) => item !== undefined && item !== null)
                  .map((item) => String(item))
                  .join(", ");
              } else if (typeof value === "object" && value !== null) {
                try {
                  formattedValue = JSON.stringify(value);
                } catch (jsonError) {
                  routeLogger.error(
                    `Error stringifying domain data value: ${jsonError}`,
                  );
                  formattedValue = "[Complex Object]";
                }
              } else {
                formattedValue = String(value);
              }

              planText += `- ${formattedKey}: ${formattedValue}\n`;
            } catch (keyError) {
              routeLogger.error(
                `Error formatting domain key-value: ${keyError}`,
              );
              planText += `- Error formatting field\n`;
            }
          }
        } else {
          planText += `- ${domainData}\n`;
        }
      }
      // Otherwise, list all disciplines
      else {
        try {
          const disciplines = currentPhase.disciplines || [];
          if (Array.isArray(disciplines)) {
            const safeList = disciplines
              .filter((d) => d !== undefined && d !== null)
              .map((d) => String(d))
              .join(", ");
            planText += `- Disciplines: ${safeList || "None specified"}\n`;
          } else {
            planText += `- Disciplines: Unknown\n`;
            routeLogger.warn(
              `Invalid disciplines field: ${typeof disciplines}`,
            );
          }
        } catch (disciplinesError) {
          routeLogger.error(
            `Error formatting disciplines: ${disciplinesError}`,
          );
          planText += `- Disciplines: Error formatting\n`;
        }
      }
    }

    // Add recent adjustments if available
    if (
      plan.adjustment_history &&
      Array.isArray(plan.adjustment_history) &&
      plan.adjustment_history.length > 0
    ) {
      try {
        const recentAdjustments = plan.adjustment_history.slice(-1)[0];
        if (
          recentAdjustments &&
          recentAdjustments.adjustments &&
          Array.isArray(recentAdjustments.adjustments)
        ) {
          planText += "\nRECENT ADJUSTMENTS:\n";

          recentAdjustments.adjustments.forEach((adjustment, index) => {
            if (!currentDomain || adjustment.discipline === currentDomain) {
              planText += `- ${adjustment.discipline || "Unknown"}: ${adjustment.rationale || "No rationale provided"}\n`;
            }
          });
        }
      } catch (adjustmentsError) {
        routeLogger.error(`Error formatting adjustments: ${adjustmentsError}`);
        planText += "\nRECENT ADJUSTMENTS: Error formatting adjustments data\n";
      }
    }

    return planText;
  } catch (error) {
    routeLogger.error(`Error in formatCurrentPlanForPrompt: ${error}`);
    return "Error formatting current training plan data.";
  }
}

/**
 * Extract key training insights from profile and plan
 *
 * @param profile The user's training profile
 * @param plan The user's current plan
 * @param domain The current domain being processed
 * @returns Object with key insights for use in prompts
 */
export function extractTrainingInsights(
  profile: TrainingProfile,
  plan: TrainingPlan | null,
  domain?: string,
): string {
  try {
    if (!profile) {
      routeLogger.warn("No profile provided to extractTrainingInsights");
      profile = {};
    }

    // Build a comprehensive text insight rather than a structural object
    let insights = "";

    // Start with basic profile assessment
    const hasProfile = Object.keys(profile).length > 0;
    const hasPlan = !!plan;

    insights += "## User Profile Summary\n";

    // Extract profile information
    if (hasProfile) {
      insights += "Based on the provided user data, ";

      // Extract training level
      let trainingLevel = "beginner";
      if (profile.general?.fitnessLevel) {
        trainingLevel = profile.general.fitnessLevel;
      } else if (profile.strength?.currentLevel) {
        trainingLevel = profile.strength.currentLevel;
      }

      // Extract primary goals
      let primaryGoals: string[] = [];
      if (profile.general?.goals) {
        if (Array.isArray(profile.general.goals)) {
          primaryGoals = profile.general.goals.map((g) => String(g));
        } else if (profile.general.goals) {
          primaryGoals = [String(profile.general.goals)];
        }
      } else if (profile.general?.fitnessGoals) {
        primaryGoals = [String(profile.general.fitnessGoals)];
      }

      // Extract limitations
      let limitations: string[] = [];
      if (profile.general?.limitations) {
        if (Array.isArray(profile.general.limitations)) {
          limitations = profile.general.limitations.map((l) => String(l));
        } else if (profile.general.limitations) {
          limitations = [String(profile.general.limitations)];
        }
      }

      // Add training level
      insights += `the user appears to be at a ${trainingLevel} training level. `;

      // Add goals if available
      if (primaryGoals.length > 0) {
        insights += `Their primary fitness goals include: ${primaryGoals.join(", ")}. `;
      }

      // Add training routine if available
      if (profile.general?.trainingRoutine) {
        insights += `Their current training routine is: ${profile.general.trainingRoutine}. `;
      }

      // Add limitations if available
      if (limitations.length > 0) {
        insights += `They have noted the following limitations: ${limitations.join(", ")}. `;
      }

      // Add dietary info if available
      if (profile.general?.diet) {
        insights += `Dietary information: ${profile.general.diet}. `;
      } else if (profile.nutrition) {
        if (
          typeof profile.nutrition === "object" &&
          Object.keys(profile.nutrition).every((k) => !isNaN(Number(k)))
        ) {
          // Handle character-by-character nutrition data
          const nutritionText = Object.keys(profile.nutrition)
            .sort((a, b) => Number(a) - Number(b))
            .map((key) => profile.nutrition[key])
            .join("");
          insights += `Nutrition information: ${nutritionText}. `;
        } else {
          insights +=
            "Some nutrition information is available in their profile. ";
        }
      }
    } else {
      insights +=
        "Limited profile information is available. Assuming a beginner-to-intermediate fitness level. ";
    }

    // Plan information
    insights += "\n\n## Training Plan Context\n";

    if (hasPlan) {
      // Get plan details
      const planName = plan.plan_name || plan.name || "";
      const planType = plan.plan_type || plan.planType || "";
      const planDesc = plan.plan_description || plan.description || "";
      const currentPhase =
        plan.current_phase !== undefined ? Number(plan.current_phase) : 0;

      insights += `The user is currently following the "${planName}" training plan (${planType}). `;
      if (planDesc) {
        insights += `This plan focuses on: ${planDesc}. `;
      }

      // Phase information
      if (
        plan.phases &&
        plan.phases.length > 0 &&
        currentPhase < plan.phases.length
      ) {
        const phase = plan.phases[currentPhase];
        if (phase) {
          insights += `They are currently in phase ${currentPhase + 1} (${phase.name || "Unnamed Phase"}). `;

          if (phase.goal) {
            insights += `The focus of this phase is: ${phase.goal}. `;
          }

          if (phase.disciplines && Array.isArray(phase.disciplines)) {
            insights += `This phase incorporates: ${phase.disciplines.join(", ")}. `;
          }

          // Domain-specific details if a domain was specified
          if (domain && phase[domain]) {
            insights += `Specific ${domain} parameters for this phase: ${JSON.stringify(phase[domain])}. `;
          }
        }
      }

      // Weekly training details
      if (plan.weeklyVolume) {
        insights += `Weekly volume: ${plan.weeklyVolume}. `;
      }
    } else {
      insights += "No specific training plan information is available. ";
    }

    // Final recommendations based on all data
    insights += "\n\n## Workout Recommendations\n";

    if (hasProfile || hasPlan) {
      insights +=
        "Based on this user's profile and training plan, workouts should: ";

      // Level-specific recommendations
      const trainingLevel =
        profile.general?.fitnessLevel ||
        profile.strength?.currentLevel ||
        (plan?.level || "beginner").toLowerCase();

      if (trainingLevel.includes("advanc")) {
        insights +=
          "Be appropriately challenging for an advanced athlete with technical focus. ";
        insights +=
          "Include complex movement patterns and higher intensity techniques. ";
        insights +=
          "Incorporate periodization appropriate to their current training phase. ";
      } else if (trainingLevel.includes("intermed")) {
        insights +=
          "Build upon established fitness foundations with progressive overload. ";
        insights +=
          "Include moderate complexity exercises with focus on technique refinement. ";
      } else {
        insights +=
          "Focus on foundational movement patterns and proper technique. ";
        insights += "Gradually introduce progressive overload principles. ";
      }

      // Training routine integration
      if (
        profile.general?.trainingRoutine &&
        profile.general.trainingRoutine.toLowerCase().includes("cycling")
      ) {
        insights +=
          "Consider their cycling training when programming strength work to avoid overtraining. ";
      }
    }

    return insights;
  } catch (error) {
    routeLogger.error(`Error in extractTrainingInsights: ${error}`);
    return "Unable to extract detailed training insights. Generic workout recommendations will be provided.";
  }
}

/**
 * Generate mock training profile data for testing
 */
function getMockTrainingProfile(userId: string): TrainingProfile {
  return {
    general: {
      fitnessLevel: "intermediate",
      experience: "3 years",
      age: 32,
      goals: ["strength gain", "endurance improvement", "weight management"],
      limitations: [],
      preferredWorkoutDuration: "45-60 minutes",
      workoutsPerWeek: 4,
    },
    strength: {
      currentLevel: "intermediate",
      preferredExercises: ["squats", "deadlifts", "bench press", "rows"],
      maxWeights: {
        squat: "185 lbs",
        deadlift: "225 lbs",
        benchPress: "155 lbs",
      },
      focusAreas: ["lower body", "core strength"],
    },
    cardio: {
      currentLevel: "intermediate",
      preferredActivities: ["running", "cycling"],
      recentActivities: [
        {
          type: "run",
          distance: "5km",
          time: "28:30",
          date: "2023-06-15",
        },
        {
          type: "cycling",
          distance: "20km",
          time: "55:45",
          date: "2023-06-12",
        },
      ],
      targetHeartRate: "130-150 bpm",
    },
    recovery: {
      sleepQuality: "good",
      averageSleepHours: 7,
      stressLevel: "moderate",
      preferredRecoveryActivities: ["stretching", "foam rolling"],
    },
    nutrition: {
      dietaryPreferences: ["high protein", "moderate carbs"],
      allergies: [],
      hydrationLevel: "good",
      supplementation: ["protein", "creatine"],
    },
  };
}

/**
 * Generate mock training plan data for testing
 */
function getMockTrainingPlan(userId: string): TrainingPlan {
  const today = new Date();
  const dateAdded = new Date(today);
  dateAdded.setMonth(today.getMonth() - 2);

  return {
    plan_id: `plan-${userId}-1`,
    plan_name: "Progressive Strength Building Program",
    plan_type: "strength",
    plan_description:
      "A 12-week program focused on building strength and muscle with periodization",
    dateAdded: dateAdded.toISOString(),
    current_phase: 1,
    phases: [
      {
        name: "Foundation Phase",
        duration: 4,
        weeks: 4,
        disciplines: ["strength", "cardio", "recovery"],
        description: "Focus on form and establishing baseline strength",
        goal: "Build proper form and muscular endurance",
        strength: {
          frequency: 3,
          sets: "3-4",
          reps: "12-15",
          intensity: "moderate",
          rest: "60-90 seconds",
          exercises: [
            "goblet squats",
            "dumbbell rows",
            "push-ups",
            "lunges",
            "planks",
          ],
        },
        cardio: {
          frequency: 2,
          duration: "20-30 minutes",
          type: "steady state",
          intensity: "moderate",
        },
      },
      {
        name: "Hypertrophy Phase",
        duration: 4,
        weeks: 4,
        disciplines: ["strength", "cardio", "recovery"],
        description: "Increase volume to stimulate muscle growth",
        goal: "Muscle development and increased strength",
        strength: {
          frequency: 4,
          sets: "4",
          reps: "8-12",
          intensity: "moderate-high",
          rest: "90-120 seconds",
          exercises: [
            "barbell squats",
            "dumbbell bench press",
            "barbell rows",
            "romanian deadlifts",
          ],
        },
        cardio: {
          frequency: 2,
          duration: "20-25 minutes",
          type: "intervals",
          intensity: "moderate-high",
        },
      },
      {
        name: "Strength Phase",
        duration: 4,
        weeks: 4,
        disciplines: ["strength", "cardio", "recovery"],
        description: "Focus on heavy lifting to maximize strength gains",
        goal: "Peak strength development",
        strength: {
          frequency: 4,
          sets: "4-5",
          reps: "4-6",
          intensity: "high",
          rest: "3-5 minutes",
          exercises: [
            "squats",
            "bench press",
            "deadlifts",
            "overhead press",
            "weighted pull-ups",
          ],
        },
        cardio: {
          frequency: 1,
          duration: "15-20 minutes",
          type: "HIIT",
          intensity: "high",
        },
      },
    ],
    adjustment_history: [
      {
        date: new Date(today.setDate(today.getDate() - 14)).toISOString(),
        adjustments: [
          {
            phase_index: 0,
            discipline: "strength",
            parameters: {
              frequency: "3→4",
              sets: "3→4",
            },
            impact: "positive",
            rationale: "User showing good recovery and adaptation",
          },
        ],
      },
    ],
  };
}
