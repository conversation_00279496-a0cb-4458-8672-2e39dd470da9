import axios from "axios";
import clientPromise from "@/app/lib/mongodb";
// Assuming logger might be defined elsewhere or not needed in this specific file
// import { routeLogger } from "../../tools/logger"; // Corrected path or remove if unused

// Re-export interfaces if needed, or define them here
// Define interfaces directly if they are simple enough and not shared widely,
// or adjust import path if they live elsewhere (e.g., a dedicated types file)
// Assuming TrainingProfile and TrainingPlan interfaces are complex and might be needed elsewhere
// Let's assume they will be defined in a shared types location or the original file for now.
// We need to resolve the import path or define them.
// For now, using 'any' as placeholder to fix linting, but should be revisited.

// Placeholder types - replace with actual import/definition
type TrainingProfile = any;
type TrainingPlan = any;

// Simplified logger for now - replace with actual import if available
const routeLogger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
};

// URL check for server-side
const isServer = typeof window === "undefined";

/**
 * Fetch a user's training profile directly from MongoDB when on server
 * @deprecated Use fetchUserDataFromAPI instead
 */
async function fetchTrainingProfileFromDB(
  userId: string,
): Promise<TrainingProfile | null> {
  try {
    routeLogger.info(
      `Fetching training profile from MongoDB for userId: ${userId}`,
    );
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const userInfo = await collection.findOne(
      { user_id: userId },
      { projection: { training_profile: 1, _id: 0 } },
    );

    if (!userInfo || !userInfo.training_profile) {
      routeLogger.warn(`No training profile found in DB for userId: ${userId}`);
      return null;
    }

    routeLogger.info(
      `Successfully fetched training profile from MongoDB for userId: ${userId}`,
    );
    return userInfo.training_profile;
  } catch (error) {
    routeLogger.error("Error fetching training profile from MongoDB:", error);
    return null;
  }
}

/**
 * Fetch a user's current plan directly from MongoDB when on server
 * @deprecated Use fetchUserDataFromAPI instead
 */
async function fetchCurrentPlanFromDB(
  userId: string,
): Promise<TrainingPlan | null> {
  try {
    routeLogger.info(
      `Fetching current plan from MongoDB for userId: ${userId}`,
    );
    const client = await clientPromise;
    const db = client.db("AthleaUserData");
    const collection = db.collection("users");

    const userInfo = await collection.findOne(
      { user_id: userId },
      { projection: { currentPlan: 1, _id: 0 } },
    );

    if (!userInfo || !userInfo.currentPlan) {
      routeLogger.warn(`No current plan found in DB for userId: ${userId}`);
      return null;
    }

    routeLogger.info(
      `Successfully fetched current plan from MongoDB for userId: ${userId}`,
    );
    return userInfo.currentPlan;
  } catch (error) {
    routeLogger.error("Error fetching current plan from MongoDB:", error);
    return null;
  }
}

// Mock functions should ideally be moved to a separate mock file or test setup
// For now, keeping them here for simplicity during refactor
function getMockTrainingProfile(userId: string): TrainingProfile {
  // Simplified mock profile
  return {
    general: {
      userId: userId,
      name: "Mock User",
      experienceLevel: "Intermediate",
    },
    cardio: {
      vo2MaxEstimate: 45,
      preferredActivities: ["running", "cycling"],
    },
  };
}

function getMockTrainingPlan(userId: string): TrainingPlan {
  // Simplified mock plan
  return {
    plan_id: `mock-plan-${userId}`,
    plan_name: "Mock Intermediate Plan",
    plan_type: "General Fitness",
    dateAdded: new Date().toISOString(),
    current_phase: 0,
    phases: [
      {
        name: "Foundation",
        duration: 4,
        weeks: 4,
        disciplines: ["cardio", "strength"],
        description: "Build base fitness",
        goal: "Consistency",
        cardio: { weeklyHours: 3, longRunDistance: 10 },
        strength: { sessionsPerWeek: 2, focus: "Full Body" },
      },
    ],
  };
}

/**
 * Fetch a user's training profile
 */
async function fetchTrainingProfile(userId: string): Promise<TrainingProfile> {
  try {
    routeLogger.info(`Fetching training profile for userId: ${userId}`);

    // In server environment, fetch directly from DB
    if (isServer) {
      const dbProfile = await fetchTrainingProfileFromDB(userId);
      if (dbProfile) {
        routeLogger.info(
          `Using MongoDB training profile data for userId: ${userId}`,
        );
        return dbProfile;
      }
      routeLogger.warn(
        `Falling back to mock training profile data for userId: ${userId}`,
      );
      return getMockTrainingProfile(userId); // Fallback to mock
    }

    // Client-side fetching (assuming this util is only used server-side now)
    // If needed client-side, this logic might need adjustment or separation
    routeLogger.warn(
      "Client-side profile fetch called from server-side util? Falling back to mock.",
    );
    return getMockTrainingProfile(userId);
  } catch (error) {
    routeLogger.error("Error in fetchTrainingProfile:", error);
    return getMockTrainingProfile(userId);
  }
}

/**
 * Fetch a user's current training plan
 */
async function fetchCurrentPlan(userId: string): Promise<TrainingPlan | null> {
  try {
    routeLogger.info(`Fetching current plan for userId: ${userId}`);

    // In server environment, fetch directly from DB
    if (isServer) {
      const dbPlan = await fetchCurrentPlanFromDB(userId);
      if (dbPlan) {
        routeLogger.info(
          `Using MongoDB current plan data for userId: ${userId}`,
        );
        return dbPlan;
      }
      routeLogger.warn(
        `Falling back to mock current plan data for userId: ${userId}`,
      );
      return getMockTrainingPlan(userId); // Fallback to mock
    }

    // Client-side fetching (assuming this util is only used server-side now)
    routeLogger.warn(
      "Client-side plan fetch called from server-side util? Falling back to mock.",
    );
    return getMockTrainingPlan(userId);
  } catch (error) {
    routeLogger.error("Error in fetchCurrentPlan:", error);
    return getMockTrainingPlan(userId);
  }
}

/**
 * Fetch both training profile and current plan. This is the primary export.
 */
export async function fetchUserData(userId: string): Promise<{
  trainingProfile: TrainingProfile;
  currentPlan: TrainingPlan | null;
}> {
  try {
    // On the server, fetch profile and plan (potentially from DB or fall back to mock)
    const [trainingProfile, currentPlan] = await Promise.all([
      fetchTrainingProfile(userId),
      fetchCurrentPlan(userId),
    ]);

    return {
      trainingProfile,
      currentPlan,
    };
  } catch (error) {
    routeLogger.error("Error fetching user data:", error);
    // Fallback to mock data in case of any error during Promise.all or fetches
    return {
      trainingProfile: getMockTrainingProfile(userId),
      currentPlan: getMockTrainingPlan(userId),
    };
  }
}
