/**
 * Simple logger utility for Mastra implementation
 * Provides consistent logging across the application with timestamps and categories
 */

// Different log levels
export enum LogLevel {
  DEBUG = "DEBUG",
  INFO = "INFO",
  WARN = "WARN",
  ERROR = "ERROR",
}

// Logger options
export interface LoggerOptions {
  enabled?: boolean;
  level?: LogLevel;
  prefix?: string;
}

// Default options
const DEFAULT_OPTIONS: LoggerOptions = {
  enabled: true,
  level: LogLevel.INFO,
  prefix: "MASTRA",
};

/**
 * Create a logger with specific options
 */
export function createLogger(options: LoggerOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };

  const formatMessage = (level: LogLevel, message: string, metadata?: any) => {
    const timestamp = new Date().toISOString();
    let formatted = `[${timestamp}] [${config.prefix}] [${level}] ${message}`;

    if (metadata) {
      try {
        const metadataStr =
          typeof metadata === "string"
            ? metadata
            : JSON.stringify(metadata, null, 2);
        formatted += `\n${metadataStr}`;
      } catch (e) {
        formatted += `\n[Metadata could not be stringified]`;
      }
    }

    return formatted;
  };

  // Check if the provided level should be logged based on the configured level
  const shouldLog = (level: LogLevel): boolean => {
    if (!config.enabled) return false;

    const levels = Object.values(LogLevel);
    const configLevelIdx = levels.indexOf(config.level || LogLevel.INFO);
    const levelIdx = levels.indexOf(level);

    return levelIdx >= configLevelIdx;
  };

  return {
    debug: (message: string, metadata?: any) => {
      if (shouldLog(LogLevel.DEBUG)) {
        console.debug(formatMessage(LogLevel.DEBUG, message, metadata));
      }
    },

    info: (message: string, metadata?: any) => {
      if (shouldLog(LogLevel.INFO)) {
        console.info(formatMessage(LogLevel.INFO, message, metadata));
      }
    },

    warn: (message: string, metadata?: any) => {
      if (shouldLog(LogLevel.WARN)) {
        console.warn(formatMessage(LogLevel.WARN, message, metadata));
      }
    },

    error: (message: string, metadata?: any) => {
      if (shouldLog(LogLevel.ERROR)) {
        console.error(formatMessage(LogLevel.ERROR, message, metadata));
      }
    },

    // Create a child logger with a different prefix
    child: (prefix: string) => {
      return createLogger({
        ...config,
        prefix: `${config.prefix}:${prefix}`,
      });
    },
  };
}

// Create a default logger instance
export const logger = createLogger();

// Export specific loggers for different components
export const workflowLogger = logger.child("WORKFLOW");
export const agentLogger = logger.child("AGENT");
export const toolLogger = logger.child("TOOL");
export const routeLogger = logger.child("ROUTE");
