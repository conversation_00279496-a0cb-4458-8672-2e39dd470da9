/**
 * Azure OpenAI Service
 *
 * This file now re-exports the global Azure OpenAI service to maintain backward compatibility
 * while ensuring consistent configuration across the application.
 */

// Import and re-export the global Azure OpenAI service
import {
  getAzureOpenAIClient,
  createAzureChatOpenAI,
  langchainAzureOpenAIConfig,
} from "../../../lib/global-azure-openai";

// Create wrapper functions that add coaching-specific logging
const coachingGetAzureOpenAIClient = (
  ...args: Parameters<typeof getAzureOpenAIClient>
) => {
  console.log(
    "[CoachingAPI:AzureService] Getting Azure OpenAI client from global service",
  );
  return getAzureOpenAIClient(...args);
};

const coachingCreateAzureChatOpenAI = (
  ...args: Parameters<typeof createAzureChatOpenAI>
) => {
  console.log(
    "[CoachingAPI:AzureService] Creating AzureChatOpenAI from global service with options:",
    args[0] ? JSON.stringify(args[0]) : "default options",
  );
  return createAzureChatOpenAI(...args);
};

// Export the wrapped functions instead of direct re-exports
export {
  coachingGetAzureOpenAIClient as getAzureOpenAIClient,
  coachingCreateAzureChatOpenAI as createAzureChatOpenAI,
  langchainAzureOpenAIConfig,
};

// Log that the coaching system is using the global service
console.log("[CoachingAPI] Using global Azure OpenAI service configuration");
