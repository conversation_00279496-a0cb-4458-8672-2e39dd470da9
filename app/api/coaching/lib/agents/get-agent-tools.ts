// Tools primarily from app/api/coaching/lib/tools/index.ts (or equivalent)
import {
  strengthAssessmentTool,
  strengthProgramTool,
  cardioAssessmentTool,
  cardioProgramTool,
  macroCalculatorTool,
  mealPlanTool,
  recoveryAssessmentTool,
  recoveryPlanTool,
  mentalAssessmentTool,
  mentalTrainingPlanTool,
  cyclingAssessmentTool,
  cyclingProgramTool,
  // azureSearchTool, // No longer directly needed from here if using graph tool
  // azureSearchGraphTool as AzureSearchGraphToolClass, // This was importing the class, not instance
} from "../tools";

// Import the AzureSearchGraphTool instance from the main tools barrel file
import { azureSearchGraphTool } from "../../tools";

// Tools primarily from app/api/coaching/tools.ts
import {
  strengthEquipmentTool as rootStrengthEquipmentTool,
  runningEquipmentTool as rootRunningEquipmentTool,
  cyclingEquipmentTool as rootCyclingEquipmentTool,
  elevationTool as rootElevationTool,
  azureMapsTool as rootAzureMapsTool,
  researchTool as rootResearchTool,
  // Keeping direct Airtable search tools commented out or removed if MCP is preferred
  // searchRunningSessionsTool as rootSearchRunningSessionsTool,
  // searchRunningSegmentsTool as rootSearchRunningSegmentsTool,
  // searchSCSessionsTool as rootSearchSCSessionsTool,
  // searchSCSegmentsTool as rootSearchSCSegmentsTool,
  // searchCyclingSessionsTool as rootSearchCyclingSessionsTool,
  // searchCyclingSegmentsTool as rootSearchCyclingSegmentsTool,
  strengthSessionGenerationTool as rootStrengthSessionGenerationTool,
  runningSessionGenerationTool as rootRunningSessionGenerationTool,
  cyclingSessionGenerationTool as rootCyclingSessionGenerationTool,
  nutritionSessionGenerationTool as rootNutritionSessionGenerationTool,
  recoverySessionGenerationTool as rootRecoverySessionGenerationTool,
} from "../../tools";

import { Tool } from "@langchain/core/tools";
import { reasoningNode } from "../agents/reasoning-node";
import { createSpecializedAgents } from "./create-specialized-agents";

// Import our specific Airtable MCP integration
import {
  connectAirtableMcpTools,
  closeMcpClient,
} from "../tools/airtable-mcp-integration";
import type { MultiServerMCPClient } from "@langchain/mcp-adapters"; // For type only

/**
 * Interface defining the structure for a coach node and its tools
 */
export interface SingleCoachTools {
  coachNode: any;
  tools: Tool[];
  airtableMcpClient?: MultiServerMCPClient | null;
}

/**
 * Get the appropriate coach node and tools based on coach name
 */
export async function getAgentNodeAndTools(
  coachName: string,
): Promise<SingleCoachTools> {
  console.log(
    `[getAgentNodeAndTools] Getting coach node and tools for: ${coachName}`,
  );

  // Use azureSearchGraphTool instead of the direct azureSearchTool in baseToolsList
  const baseToolsList: any[] = [azureSearchGraphTool, rootResearchTool];
  if (reasoningNode instanceof Tool) {
    baseToolsList.push(reasoningNode);
  }
  const baseTools = baseToolsList.filter((t): t is Tool => t instanceof Tool);

  // Connect to our specific Airtable MCP server for Airtable tools
  let airtableSpecificTools: Tool[] = [];
  let airtableMcpClientInstance: MultiServerMCPClient | null = null;

  // Only get Airtable tools for running, cycling, and strength coaches
  const needsAirtableTools =
    coachName.toLowerCase().includes("running") ||
    coachName.toLowerCase().includes("cardio") ||
    coachName.toLowerCase().includes("cycling") ||
    coachName.toLowerCase().includes("strength");

  // Only initialize Airtable MCP client if we need it for this coach
  if (needsAirtableTools) {
    const airtableApiKey = process.env.AIRTABLE_API_KEY;
    if (airtableApiKey) {
      console.log(
        `[getAgentNodeAndTools] Coach ${coachName} needs Airtable tools. Attempting to connect to Airtable MCP server...`,
      );
      try {
        const { tools, client } = await connectAirtableMcpTools(
          airtableApiKey,
          process.env.NODE_PATH,
          process.env.NPX_PATH,
        );
        // Ensure tools from connectAirtableMcpTools are treated as Tool[]
        airtableSpecificTools = tools as unknown as Tool[];
        airtableMcpClientInstance = client;
        console.log(
          `[getAgentNodeAndTools] Successfully connected to Airtable MCP server and got ${airtableSpecificTools.length} Airtable tools.`,
        );
      } catch (error) {
        console.error(
          "[getAgentNodeAndTools] Failed to connect to Airtable MCP server or get tools:",
          error,
        );
        if (airtableMcpClientInstance) {
          await closeMcpClient(airtableMcpClientInstance);
          airtableMcpClientInstance = null;
        }
        airtableSpecificTools = [];
      }
    } else {
      console.log(
        "[getAgentNodeAndTools] AIRTABLE_API_KEY not found. Skipping Airtable MCP server connection.",
      );
    }
  } else {
    console.log(
      `[getAgentNodeAndTools] Coach ${coachName} does not need Airtable tools. Skipping Airtable MCP server connection.`,
    );
  }

  // All additional tools are now just the airtableSpecificTools, but they will only be initialized for relevant coaches
  const allAdditionalTools = [...airtableSpecificTools];

  const specializedAgentToolsInput = {
    strengthAssessmentTool,
    strengthProgramTool,
    strengthEquipmentTool: rootStrengthEquipmentTool,
    cardioAssessmentTool,
    cardioProgramTool,
    runningEquipmentTool: rootRunningEquipmentTool,
    macroCalculatorTool,
    mealPlanTool,
    recoveryAssessmentTool,
    recoveryPlanTool,
    mentalAssessmentTool,
    mentalTrainingPlanTool,
    cyclingAssessmentTool,
    cyclingProgramTool,
    cyclingEquipmentTool: rootCyclingEquipmentTool,
    elevationTool: rootElevationTool,
    azureMapsTool: rootAzureMapsTool,
    azureSearchGraphTool, // Pass the graph tool here for specialized agents if needed
    // azureSearchTool, // Removed direct azureSearchTool
    strengthSessionGenerationTool: rootStrengthSessionGenerationTool,
    runningSessionGenerationTool: rootRunningSessionGenerationTool,
    cyclingSessionGenerationTool: rootCyclingSessionGenerationTool,
    nutritionSessionGenerationTool: rootNutritionSessionGenerationTool,
    recoverySessionGenerationTool: rootRecoverySessionGenerationTool,
  };

  const agents = createSpecializedAgents(
    null,
    specializedAgentToolsInput,
    allAdditionalTools, // Pass only Airtable tools as additional tools
  );

  let coachNode: any;
  let coachSpecificToolsArray: any[] = [];

  switch (coachName.toLowerCase()) {
    case "strength_coach":
    case "strength":
      coachNode = agents.strengthCoachNode;
      coachSpecificToolsArray = [
        strengthAssessmentTool,
        strengthProgramTool,
        rootStrengthEquipmentTool,
        rootElevationTool,
        rootStrengthSessionGenerationTool,
      ];
      break;
    case "cardio_coach":
    case "cardio":
    case "running_coach":
    case "running":
      coachNode = agents.runningCoachNode;
      coachSpecificToolsArray = [
        cardioAssessmentTool,
        cardioProgramTool,
        rootRunningEquipmentTool,
        rootElevationTool,
        rootAzureMapsTool,
        rootRunningSessionGenerationTool,
        // searchRunningSessionsTool, // Prefer MCP if available, or ensure this is different
        // searchRunningSegmentsTool,
      ];
      break;
    case "cycling_coach":
    case "cycling":
      coachNode = agents.cyclingCoachNode;
      coachSpecificToolsArray = [
        cyclingAssessmentTool,
        cyclingProgramTool,
        rootCyclingEquipmentTool,
        rootElevationTool,
        rootAzureMapsTool,
        rootCyclingSessionGenerationTool,
        // searchCyclingSessionsTool, // Prefer MCP if available
        // searchCyclingSegmentsTool,
      ];
      break;
    case "nutrition_coach":
    case "nutrition":
      coachNode = agents.nutritionCoachNode;
      coachSpecificToolsArray = [
        macroCalculatorTool,
        mealPlanTool,
        rootNutritionSessionGenerationTool,
      ];
      break;
    case "recovery_coach":
    case "recovery":
      coachNode = agents.recoveryCoachNode;
      coachSpecificToolsArray = [
        recoveryAssessmentTool,
        recoveryPlanTool,
        strengthAssessmentTool, // Example: Recovery coach might also assess strength in context of recovery
        rootRecoverySessionGenerationTool,
      ];
      break;
    case "mental_coach":
    case "mental":
      coachNode = agents.mentalCoachNode;
      coachSpecificToolsArray = [mentalAssessmentTool, mentalTrainingPlanTool];
      break;
    default:
      throw new Error(`Unknown coach type: ${coachName}`);
  }
  const coachSpecificTools = coachSpecificToolsArray.filter(
    (t): t is Tool => t instanceof Tool,
  );

  // agents.allTools from createSpecializedAgents already includes base tools + airtableSpecificTools.
  // We then add coach-specific tools that might not be in the base set.
  const uniqueToolsSet = new Set([...agents.allTools, ...coachSpecificTools]);
  const uniqueTools = Array.from(uniqueToolsSet);

  console.log(
    `[getAgentNodeAndTools] Tools provided for ${coachName} single coach graph (including Airtable MCP if connected):`,
    uniqueTools.map((t) => t.name),
  );

  return {
    coachNode,
    tools: uniqueTools,
    airtableMcpClient: airtableMcpClientInstance,
  };
}
