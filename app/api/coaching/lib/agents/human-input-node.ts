/**
 * This file defines the humanInputNode function used to handle human-in-the-loop interactions
 * in the supervision graph.
 */

import { interrupt } from "@langchain/langgraph";
import { GraphState } from "../graph/state";
import { trace } from "@opentelemetry/api";

/**
 * Safely get the length of a JSON stringified object, handling circular references
 * @param obj The object to stringify
 * @returns The length of the stringified object, or -1 if it can't be stringified
 */
function safeJsonStringifyLength(obj: any): number {
  try {
    // Use a replacer function to handle circular references
    const cache = new Set();
    const safeStringify = JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        // If we've seen this object before, return a placeholder
        if (cache.has(value)) {
          return "[Circular Reference]";
        }
        cache.add(value);
      }
      return value;
    });
    return safeStringify.length;
  } catch (e) {
    console.warn(
      "Could not safely stringify object for length calculation:",
      e,
    );
    // Return -1 to indicate error, or some other sentinel value
    return -1;
  }
}

/**
 * A node that requests human input when clarification is needed
 *
 * @param state The current graph state
 * @returns A partial state update (empty since interrupt pauses execution)
 */
export async function humanInputNode(
  state: GraphState,
): Promise<Partial<GraphState>> {
  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("humanInputNode", {
    attributes: {
      node_type: "human_input_node",
      function_name: "humanInputNode",
      input_length: safeJsonStringifyLength(state),
    },
  });
  // --- TRACING END ---

  console.log("[humanInputNode] Requesting human input...");
  // Find the last message from the supervisor which likely contains the question
  const lastSupervisorMsg = state.messages
    .filter((msg) => msg.name === "head_coach")
    .pop();

  let question = "Please provide more details."; // Default question
  // Check if content is a string before using string methods
  if (lastSupervisorMsg && typeof lastSupervisorMsg.content === "string") {
    const contentStr = lastSupervisorMsg.content;
    question = contentStr.startsWith("Reasoning: ")
      ? contentStr.substring(11) // Remove "Reasoning: "
      : contentStr;
  } else if (lastSupervisorMsg) {
    console.warn(
      "[humanInputNode] Supervisor message content was not a simple string:",
      lastSupervisorMsg.content,
    );
    // Could potentially try to extract text from complex content if needed
  }

  // Trigger the interrupt with the determined question.
  // NOTE: We don't actually await the interrupt promise here,
  // as the graph execution will pause externally.
  span.setAttribute("question_to_interrupt", question);
  span.end();
  interrupt(question); // This throws GraphInterrupt
  console.log("[humanInputNode] Interrupt triggered. Waiting for resume.");
  // Return an empty object as no state needs modification here, just pausing.
  // This part is unlikely to be reached if interrupt always throws.
  // However, to be safe and explicit with tracing if it *could* be reached:
  span.setAttribute("output_length", safeJsonStringifyLength({}));
  // span.end(); // Span already ended before interrupt
  return {};
}
