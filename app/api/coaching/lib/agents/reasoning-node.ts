import { <PERSON>t<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import {
  BaseMessage,
  AIMessage,
  SystemMessage,
} from "@langchain/core/messages";
import { RunnableConfig } from "@langchain/core/runnables";
import { GraphState } from "../graph/state";
import { createAzureChatOpenAI } from "../services/azure-openai-service";
import { trace } from "@opentelemetry/api";

/**
 * Reasoning node that analyzes the user request and provides structured thought process
 *
 * @param state The current state of the graph
 * @param config Optional configuration including user profile and current plan
 * @returns Partial graph state with the reasoning message
 */
export async function reasoningNode(
  state: GraphState,
  config?: RunnableConfig,
): Promise<Partial<GraphState>> {
  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("reasoningNode", {
    attributes: {
      node_type: "reasoning_node",
      function_name: "reasoningNode",
      // Safely get state length without potential circular references
      input_state_length: safeJsonStringifyLength(state),
      // Avoid stringifying the entire config - just indicate if it exists
      config_present: !!config,
      // Add user ID for correlation if available
      user_id:
        config?.configurable?.userId || (state as any)?.userId || "anonymous",
      // Add thread ID for conversation correlation
      thread_id:
        config?.configurable?.thread_id ||
        config?.configurable?.threadId ||
        (state as any)?.threadId ||
        "unknown",
      // Extract message count for visibility
      message_count: Array.isArray(state.messages) ? state.messages.length : 0,
    },
  });
  // --- TRACING END ---

  const nodeName = "ReasoningAgent";
  console.log(`--- Running ${nodeName} Node ---`);

  const userProfile = config?.configurable?.userProfile || state.userProfile;
  const currentPlanInfo = config?.configurable?.currentPlan;
  const messages = state.messages || [];
  const latestMessage = messages[messages.length - 1];

  // Add latest user message to span if available
  if (
    latestMessage &&
    latestMessage._getType &&
    latestMessage._getType() === "human"
  ) {
    span.setAttribute(
      "latest_user_message",
      typeof latestMessage.content === "string"
        ? latestMessage.content.substring(0, 300)
        : JSON.stringify(latestMessage.content).substring(0, 300),
    );
  }

  const reasoningLlm = createAzureChatOpenAI({
    temperature: 0.7,
    maxTokens: 4000,
  });

  // Improved system prompt with better structure and optional physiological/mental reference
  const reasoningPromptTemplate = `You are an analytical assistant for a fitness coaching application. Your task is to analyze the user's latest request, provide structured reasoning about the situation, and identify any physiological or mental aspects mentioned.

User Profile:
{{userProfileInfo}}

Current Plan Context:
{{currentPlanInfo}}

Conversation History (including latest message):
{{message_history}}

Analyze the latest user message within the context of the history and profile/plan. Generate a well-structured analysis using proper markdown formatting.

Include the following sections:

## Request Summary
- Briefly summarize what the user is asking for in their message: "{{latest_user_message_content}}"

## Clarity Assessment
- Is the request clear or ambiguous?
- What specific information is provided vs. what might be missing?

## Domain Analysis
- What fitness or wellness domains does this request relate to? (strength, cardio, nutrition, recovery, mental, etc.)
- *If applicable*: What specific physiological body parts or mental aspects are referenced? (e.g., knees, back, motivation, stress)

## Context Relevance
- How does this request relate to the user's profile or current plan?
- Any relevant historical context from previous messages?

## Next Steps Analysis
- What would be the most helpful response approach?
- Is clarification needed before proceeding?

Structure your response using proper markdown formatting with clear sections and bullet points. Be concise but thorough.`;

  let promptInput = reasoningPromptTemplate;

  // Replace template variables
  promptInput = promptInput.replace(
    "{{userProfileInfo}}",
    userProfile ? JSON.stringify(userProfile, null, 2) : "Not Available",
  );

  promptInput = promptInput.replace(
    "{{currentPlanInfo}}",
    currentPlanInfo
      ? JSON.stringify(currentPlanInfo, null, 2)
      : "Not Available",
  );

  // Prepare history string with proper formatting
  const historyString =
    messages.length > 0
      ? messages
          .map(
            (m: BaseMessage) =>
              `${m._getType() === "human" ? "User" : m.name || "Assistant"}: ${typeof m.content === "string" ? m.content : "[non-string content]"}`,
          )
          .join("\n")
      : "No history yet.";

  promptInput = promptInput.replace("{{message_history}}", historyString);

  const latestUserContent = latestMessage?.content?.toString() || "N/A";
  promptInput = promptInput.replace(
    "{{latest_user_message_content}}",
    latestUserContent,
  );

  // Add the prompt template to tracing
  span.setAttribute(
    "system_prompt_template",
    reasoningPromptTemplate.substring(0, 500),
  );
  span.setAttribute("system_prompt_filled", promptInput.substring(0, 1000));

  const llmMessages = [new SystemMessage(promptInput)];
  let reasoningText = "Analyzing request..."; // Default fallback
  let llmError = null;

  try {
    console.log(`  > Calling ${nodeName} LLM...`);
    // Record start time for duration tracking
    const startTime = Date.now();
    const response = await reasoningLlm.invoke(llmMessages);
    // Calculate duration
    const duration = Date.now() - startTime;
    span.setAttribute("llm_response_time_ms", duration);

    // Safely handle the response content for logging
    const contentStr =
      typeof response.content === "string"
        ? response.content
        : JSON.stringify(response.content);

    console.log(
      `  > ${nodeName} LLM Response Summary:`,
      contentStr.substring(0, 150) + "...",
    );

    if (
      typeof response.content === "string" &&
      response.content.trim() !== ""
    ) {
      reasoningText = response.content.trim();
      // Add sections found in the response to telemetry
      const sections = extractMarkdownSections(reasoningText);
      if (Object.keys(sections).length > 0) {
        span.setAttribute("reasoning_sections", JSON.stringify(sections));
      }
    } else {
      console.warn(`  > ${nodeName} LLM returned empty or non-string content.`);
    }
  } catch (error) {
    console.error(`Error during ${nodeName} LLM call:`, error);
    reasoningText = "Error during analysis.";
    llmError = error instanceof Error ? error.message : String(error);
    span.setAttribute("error", true);
    span.setAttribute("error_message", llmError);
  }

  // Create the reasoning message with domain metadata
  const reasoningMessage = new AIMessage({
    content: reasoningText,
    name: nodeName,
    additional_kwargs: {
      type: "reasoning_analysis",
      domain: "reasoning",
    },
  });

  // Safely log the truncated reasoning text
  const truncatedText = reasoningText.substring(0, 100);
  console.log(
    `  > ${nodeName} generated reasoning (truncated): ${truncatedText}...`,
  );

  // Return only the new message to be appended
  const result: Partial<GraphState> = {
    messages: [reasoningMessage],
  };

  // Add final attributes for tracing
  span.setAttribute("output_length", safeJsonStringifyLength(result));
  span.setAttribute("reasoning_text_length", reasoningText.length);
  span.setAttribute("reasoning_text_preview", reasoningText.substring(0, 500));

  if (llmError) {
    span.setAttribute("llm_call_error", llmError);
  }

  span.end();
  return result;
}

/**
 * Extract section titles and a short preview of their content from markdown text
 * @param markdownText The markdown text to parse
 * @returns An object with section titles as keys and content previews as values
 */
function extractMarkdownSections(markdownText: string): Record<string, string> {
  const sections: Record<string, string> = {};
  const sectionRegex = /##\s+([^\n]+)([^#]*?)(?=##|$)/g;

  let match;
  while ((match = sectionRegex.exec(markdownText)) !== null) {
    const sectionTitle = match[1].trim();
    const sectionContent = match[2].trim();

    // Store the section title and a preview of its content
    sections[sectionTitle] = sectionContent.substring(0, 100);
  }

  return sections;
}

/**
 * Safely get the length of a JSON stringified object, handling circular references
 * @param obj The object to stringify
 * @returns The length of the stringified object, or -1 if it can't be stringified
 */
function safeJsonStringifyLength(obj: any): number {
  try {
    // Use a replacer function to handle circular references
    const cache = new Set();
    const safeStringify = JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        // If we've seen this object before, return a placeholder
        if (cache.has(value)) {
          return "[Circular Reference]";
        }
        cache.add(value);
      }
      return value;
    });
    return safeStringify.length;
  } catch (e) {
    console.warn(
      "Could not safely stringify object for length calculation:",
      e,
    );
    // Return -1 to indicate error, or some other sentinel value
    return -1;
  }
}
