/**
 * This file defines the supervisor<PERSON><PERSON>, which is the central coordinator
 * in the coaching graph.
 */

import { ChatOpenAI } from "@langchain/openai";
import { END, Command } from "@langchain/langgraph";
import {
  AIMessage,
  SystemMessage,
  ToolMessage,
  BaseMessage,
} from "@langchain/core/messages";
import { RunnableConfig } from "@langchain/core/runnables";
import { GraphState } from "../graph/state";
// Import our Azure OpenAI configuration
import { createAzureChatOpenAI } from "../services/azure-openai-service";
import { trace } from "@opentelemetry/api";

// Define agentNodeNames globally to identify valid agent nodes
export const agentNodeNames = [
  "head_coach",
  "strength_coach",
  "cardio_coach",
  "running_coach",
  "cycling_coach",
  "nutrition_coach",
  "recovery_coach",
  "mental_coach",
  "planningNode",
  "generate_plan",
  "human_input_node",
  "call_tool",
  "aggregate_responses",
  "plan_generation_subgraph", // Added subgraph entry point
  "__end__",
];

/**
 * Safely get the length of a JSON stringified object, handling circular references
 * @param obj The object to stringify
 * @returns The length of the stringified object, or -1 if it can't be stringified
 */
function safeJsonStringifyLength(obj: any): number {
  try {
    // Use a replacer function to handle circular references
    const cache = new Set();
    const safeStringify = JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        // If we've seen this object before, return a placeholder
        if (cache.has(value)) {
          return "[Circular Reference]";
        }
        cache.add(value);
      }
      return value;
    });
    return safeStringify.length;
  } catch (e) {
    console.warn(
      "Could not safely stringify object for length calculation:",
      e,
    );
    // Return -1 to indicate error, or some other sentinel value
    return -1;
  }
}

/**
 * The supervisor node function - REFACTORED to handle clarification questions
 *
 * @param state The current graph state including the plan
 * @param config Optional runnable configuration
 * @returns A Command object with routing instructions
 */
export const supervisorNode = async (
  state: GraphState,
): Promise<Partial<GraphState> | Command> => {
  const { messages } = state;

  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("supervisorNode", {
    attributes: {
      agent_name: "head_coach", // As this is the supervisor node
      step_name: "supervisorNode",
      input_length: safeJsonStringifyLength(state),
      // Add more context information
      message_count: Array.isArray(messages) ? messages.length : 0,
      user_id: (state as any)?.userId || "anonymous",
      is_onboarding: !!state.isOnboarding,
      has_plan: Array.isArray(state.plan) && state.plan.length > 0,
      current_routing_decision: state.routingDecision || "none",
      has_pending_agents: !!state.pendingAgents,
    },
  });

  // Add details about current plan if available
  if (Array.isArray(state.plan) && state.plan.length > 0) {
    span.setAttribute("plan", JSON.stringify(state.plan));
  }

  // Add details about pending agents if available
  if (state.pendingAgents) {
    span.setAttribute("pending_agents", JSON.stringify(state.pendingAgents));
  }
  // --- TRACING END ---

  console.log("[Supervisor] Processing state");

  const supervisorName = "head_coach";
  console.log(`[${supervisorName}] Node executing.`);
  // --- Get userProfile and currentPlan from config ---
  const userProfile = state.userProfile;
  const currentPlan = state.currentPlan;
  // --- Get state values ---
  const plan = state.plan || []; // Get plan from state
  const currentMessages = messages || [];

  // <<< ADDED: Onboarding Check >>>
  if (state.isOnboarding === true) {
    console.log(
      "[Supervisor] Onboarding is active. Routing directly to plan generation subgraph.",
    );
    // You might want to check if userProfile exists here too if it's critical for the subgraph
    // if (!userProfile) {
    //   console.warn("[Supervisor] Onboarding active but no user profile found. Routing to clarification first?");
    //   // Handle this case - maybe route to clarification to get profile info?
    //   // For now, proceeding to plan generation
    // }

    // Return a Command to route directly to the subgraph entry point
    const command = new Command({
      goto: "plan_generation_subgraph", // Route to the plan generation entry point
      update: {
        pendingAgents: null, // Clear any pending agents
        messages: [
          ...currentMessages, // Keep existing messages
          new SystemMessage(
            "Starting collaborative plan generation for onboarding.",
          ),
        ],
        userProfile: userProfile, // Pass profile
        // Explicitly clear plan and step from previous turns if necessary
        plan: [],
        current_step: null,
        routingDecision: "planGeneration", // <<< SET routingDecision
      },
    });

    // Record routing decision in telemetry
    span.setAttribute("routing_decision", "plan_generation_subgraph");
    span.setAttribute("routing_reason", "onboarding_active");
    span.setAttribute("output_length", safeJsonStringifyLength(command));
    span.end();

    return command;
  }
  // <<< END OF ONBOARDING CHECK >>>

  console.log(`[${supervisorName}] Received plan: ${JSON.stringify(plan)}`);
  console.log(
    `[${supervisorName}] Current messages count: ${currentMessages.length}`,
  );

  // --- Initial Pre-processing: Filter stale ToolMessages ---
  const cleanedMessages = currentMessages.filter((msg, index, arr) => {
    if (!(msg instanceof ToolMessage)) return true;
    const prevMsg = arr[index - 1];
    if (
      prevMsg instanceof AIMessage &&
      prevMsg.tool_calls?.some((tc) => tc.id === msg.tool_call_id)
    ) {
      return true; // Keep valid ToolMessage
    }
    console.log(
      `[${supervisorName}] Filtering stale ToolMessage ID: ${msg.tool_call_id}`,
    );
    return false; // Filter stale/invalid ToolMessage
  });

  // --- Check for ToolMessage to route back to originating agent ---
  const lastMessage = cleanedMessages[cleanedMessages.length - 1];
  if (lastMessage instanceof ToolMessage) {
    const toolCallId = lastMessage.tool_call_id;
    span.setAttribute("last_message_type", "tool_message");
    span.setAttribute("tool_call_id", toolCallId || "unknown");

    let originatingAgentName: string | null = null;
    for (let i = cleanedMessages.length - 2; i >= 0; i--) {
      const msg = cleanedMessages[i];
      // Make sure name exists on AIMessage before trying to check includes
      if (
        msg instanceof AIMessage &&
        msg.tool_calls?.some((tc) => tc.id === toolCallId) &&
        msg.name &&
        agentNodeNames.includes(msg.name)
      ) {
        originatingAgentName = msg.name || null;
        break;
      }
    }
    if (originatingAgentName) {
      // No need to check agentNodeNames again here
      console.log(
        `[${supervisorName}] Routing back to originating agent after tool call: ${originatingAgentName}`,
      );
      // Pass userProfile in the update when routing back
      const command = new Command({
        // <<< Return Command explicitly
        goto: originatingAgentName, // <<< Route back to the agent
        update: {
          messages: [
            ...cleanedMessages,
            // Optionally add a message indicating return from tool
            // new AIMessage({ name: supervisorName, content: "Tool execution complete.", additional_kwargs: { type: "tool_complete" } })
          ],
          userProfile: userProfile || null,
          routingDecision: null, // Clear routing decision
        },
      });

      // Record routing decision in telemetry
      span.setAttribute("routing_decision", originatingAgentName);
      span.setAttribute("routing_reason", "tool_message_response");
      span.setAttribute("output_length", safeJsonStringifyLength(command));
      span.end();

      return command;
    } else {
      console.warn(
        `[${supervisorName}] Could not find originating agent for ToolMessage ID: ${lastMessage.tool_call_id}. Proceeding.`,
      );
      span.setAttribute("tool_originator_found", false);
    }
  }

  // --- Check if we are in an agent sequence ---
  const currentPendingAgents = state.pendingAgents;
  if (currentPendingAgents && currentPendingAgents.length > 0) {
    console.log(
      `[${supervisorName}] Continuing agent sequence. Pending: ${currentPendingAgents.join(", ")}`,
    );
    const remainingAgents = currentPendingAgents.slice(1);
    let nextNode: string;
    let updatePayload: Partial<GraphState> = {
      userProfile: userProfile || null,
      messages: cleanedMessages, // Pass cleaned history
      routingDecision: null, // Clear routing decision during sequence
    };
    if (remainingAgents.length > 0) {
      nextNode = remainingAgents[0];
      updatePayload.pendingAgents = remainingAgents;
      console.log(`[${supervisorName}] Routing to next agent: ${nextNode}`);
      span.setAttribute("routing_type", "agent_sequence_continuation");
    } else {
      const lastAgentInSequence = currentPendingAgents[0];
      if (lastAgentInSequence === "human_input_node") {
        nextNode = "__end__";
        console.log(
          `[${supervisorName}] Agent sequence ended after human input. Routing to END.`,
        );
        updatePayload.routingDecision = "end"; // Set decision for END
        span.setAttribute(
          "routing_type",
          "agent_sequence_end_after_human_input",
        );
      } else {
        nextNode = "aggregate_responses";
        console.log(
          `[${supervisorName}] Agent sequence complete. Routing to ${nextNode}.`,
        );
        updatePayload.routingDecision = "aggregate"; // Set decision for aggregation
        span.setAttribute("routing_type", "agent_sequence_aggregation");
      }
      updatePayload.pendingAgents = null;
    }
    // Return command for sequence transition
    const command = new Command({
      goto: nextNode,
      update: updatePayload,
    });

    // Record routing decision in telemetry
    span.setAttribute("routing_decision", nextNode);
    span.setAttribute("pending_agents_remaining", remainingAgents.length);
    span.setAttribute("output_length", safeJsonStringifyLength(command));
    span.end();

    return command;
  }

  // --- NEW TURN: Execute plan from planningNode ---
  console.log(
    `[${supervisorName}] Starting new turn execution based on plan: ${JSON.stringify(plan)}`,
  );

  // <<< ADDED: Onboarding Check >>>
  if (state.isOnboarding && userProfile && currentMessages.length > 0) {
    // Check if onboarding and profile/messages exist
    console.log(
      `[${supervisorName}] Onboarding active. Checking if plan generation is ready...`,
    );
    const shouldGenerate = await shouldGeneratePlanForOnboarding(
      userProfile,
      currentMessages,
    );
    if (shouldGenerate) {
      console.log(
        `[${supervisorName}] Sufficient info gathered during onboarding. Routing to plan generation subgraph.`,
      );

      // Create a message to inform the user
      const planInitMessage = new AIMessage({
        name: supervisorName,
        content:
          "Great! It looks like I have enough information now. I'll create a comprehensive training plan tailored to your needs. This will involve multiple coaches contributing their expertise. Let me put that together for you.",
        additional_kwargs: { type: "system_plan_initiation" },
      });

      // Return Command to route to subgraph
      const command = new Command({
        goto: "plan_generation_subgraph",
        update: {
          messages: [...cleanedMessages, planInitMessage],
          pendingAgents: null, // Ensure pending agents are cleared
          userProfile: userProfile, // Pass profile
        },
      });

      // Record routing decision in telemetry
      span.setAttribute("routing_decision", "plan_generation_subgraph");
      span.setAttribute("routing_reason", "onboarding_info_sufficient");
      span.setAttribute("output_length", safeJsonStringifyLength(command));
      span.end();

      return command;
    }
    console.log(
      `[${supervisorName}] Not enough info for plan generation yet, proceeding with normal flow.`,
    );
    span.setAttribute("onboarding_info_sufficient", false);
  }
  // <<< END Onboarding Check >>>

  let gotoDecision: string | null = null;
  let updatePayload: Partial<GraphState> = {
    messages: cleanedMessages, // Start with cleaned messages
    pendingAgents: null, // Default to null unless routing to sequence
    userProfile: userProfile || null, // Pass profile
    routingDecision: null, // Default routing decision
    plan: state.plan, // Preserve plan by default
  };

  // <<< NEW: Check for plan generation first >>>
  if (plan.length === 1 && plan[0] === "generate_plan") {
    console.log(
      `[${supervisorName}] Plan requires plan generation. Routing to subgraph.`,
    );
    gotoDecision = "plan_generation_subgraph";
    updatePayload.pendingAgents = null;
    updatePayload.routingDecision = "planGeneration"; // <<< SET routingDecision
    updatePayload.plan = []; // <<< CLEAR the plan state

    span.setAttribute("routing_reason", "generate_plan_directive");
  }
  // <<< END NEW CHECK >>>
  else if (plan.length === 1 && plan[0] === "clarification") {
    // --- Generate Clarification Question ---
    console.log(
      `[${supervisorName}] Plan requires clarification. Generating question...`,
    );
    gotoDecision = "human_input_node";
    updatePayload.routingDecision = "human_input"; // Set decision
    span.setAttribute("routing_reason", "clarification_needed");

    const supervisorLLM = createAzureChatOpenAI({
      temperature: 0.7,
      maxTokens: 4000,
    });

    // Prompt focused SOLELY on asking a good clarifying question
    const clarificationPrompt = `You are the Head Fitness Coach acting as a conversation coordinator. The user's last message was unclear or too brief. Your task is to ask ONE concise, specific, and helpful clarifying question to understand what the user needs next. Do NOT answer any fitness questions. ONLY ask the clarifying question.

Consider the conversation history and the user's context (profile/plan if available) to make your question relevant.

User Profile:
{{userProfileInfo}}

Current Plan Context:
{{currentPlanInfo}}

Conversation History:
{{message_history}}

Based ONLY on the history and context, generate the single clarifying question you need to ask the user. Output ONLY the question text.`;

    // Inject context
    let finalClarificationPrompt = clarificationPrompt;
    finalClarificationPrompt = finalClarificationPrompt.replace(
      "{{userProfileInfo}}",
      userProfile ? JSON.stringify(userProfile, null, 2) : "Not Available",
    );
    finalClarificationPrompt = finalClarificationPrompt.replace(
      "{{currentPlanInfo}}",
      currentPlan ? JSON.stringify(currentPlan, null, 2) : "Not Available",
    );
    // Prepare message history for the prompt (excluding tool messages for clarity)
    const historyForPrompt = cleanedMessages
      .map(
        (m: BaseMessage) =>
          `${m._getType() === "human" ? "User" : m.name || "Assistant"}: ${typeof m.content === "string" ? m.content : "[non-string content]"}`,
      )
      .join("\n");

    finalClarificationPrompt = finalClarificationPrompt.replace(
      "{{message_history}}",
      historyForPrompt || "No history yet.",
    );

    // Add prompt to telemetry
    span.setAttribute(
      "clarification_prompt_length",
      finalClarificationPrompt.length,
    );

    try {
      const llmMessages = [new SystemMessage(finalClarificationPrompt)];
      console.log(
        `[${supervisorName}] Calling LLM to generate clarification question.`,
      );

      // Time the LLM call
      const startTime = Date.now();
      const result = await supervisorLLM.invoke(llmMessages);
      const duration = Date.now() - startTime;
      span.setAttribute("clarification_llm_time_ms", duration);

      const questionText = result.content as string;

      if (questionText && questionText.trim() !== "") {
        console.log(`[${supervisorName}] Generated question: ${questionText}`);
        const questionMessage = new AIMessage({
          name: supervisorName,
          content: questionText.trim(),
          additional_kwargs: { type: "clarification_request" },
        });
        // Add the question to the messages in the update payload
        updatePayload.messages = [...cleanedMessages, questionMessage];

        // Add question to telemetry
        span.setAttribute(
          "clarification_question",
          questionText.trim().substring(0, 300),
        );
      } else {
        console.warn(
          `[${supervisorName}] LLM failed to generate a question. Using default.`,
        );
        const defaultQuestion = new AIMessage({
          name: supervisorName,
          content: "What would you like to focus on?",
          additional_kwargs: { type: "clarification_request" },
        });
        updatePayload.messages = [...cleanedMessages, defaultQuestion];
        span.setAttribute("used_default_question", true);
      }
    } catch (error) {
      console.error(
        `[${supervisorName}] Error generating clarification question:`,
        error,
      );
      const errorQuestion = new AIMessage({
        name: supervisorName,
        content:
          "Sorry, I had trouble understanding. Could you please rephrase?",
        additional_kwargs: { type: "clarification_request_error" },
      });
      updatePayload.messages = [...cleanedMessages, errorQuestion];
      span.setAttribute(
        "clarification_llm_error",
        error instanceof Error ? error.message : String(error),
      );
    }
  } else if (plan.length > 0 && plan[0] !== "__end__") {
    // --- Route to Coach Sequence ---
    // Filter plan against known agent nodes BEFORE setting pending agents
    const validAgentNodesInPlan = plan.filter(
      (node) =>
        typeof node === "string" &&
        agentNodeNames.includes(node) &&
        node !== "head_coach" &&
        node !== "generate_plan",
    );

    if (validAgentNodesInPlan.length > 0) {
      console.log(
        `[${supervisorName}] Plan involves coaches: ${validAgentNodesInPlan.join(", ")}. Routing to first: ${validAgentNodesInPlan[0]}`,
      );
      gotoDecision = validAgentNodesInPlan[0];
      updatePayload.pendingAgents = validAgentNodesInPlan;
      updatePayload.routingDecision = "agent_sequence"; // Set decision

      span.setAttribute("routing_reason", "coach_sequence");
      span.setAttribute(
        "coach_sequence",
        JSON.stringify(validAgentNodesInPlan),
      );
    } else {
      console.warn(
        `[${supervisorName}] Plan contained no valid coach nodes or only __end__. Plan: ${JSON.stringify(plan)}. Defaulting to END.`,
      );
      gotoDecision = "__end__";
      updatePayload.routingDecision = "end"; // Set decision for END

      span.setAttribute("routing_reason", "no_valid_coaches_in_plan");
    }
  } else {
    // --- Plan is empty or explicitly "__end__" ---
    console.log(
      `[${supervisorName}] Plan is empty or END. Ending conversation.`,
    );
    gotoDecision = "__end__";
    // Optionally add a final message if needed, otherwise keep messages as is
    // const finalMessage = new AIMessage({ name: supervisorName, content: "Okay, let me know if you need anything else!"});
    // updatePayload.messages = [...cleanedMessages, finalMessage];
    updatePayload.routingDecision = "end"; // Set decision for END

    span.setAttribute("routing_reason", "end_or_empty_plan");
  }

  // --- Construct and return Command ---
  // Ensure current_step is reset when not actively in a sequence or paused
  updatePayload.current_step = null; // Reset step tracking for the next turn

  console.log(
    `[${supervisorName}] Final decision: Route to ${gotoDecision}. Update payload: ${JSON.stringify(updatePayload)}`,
  );

  // Check if we're receiving results from the plan generation subgraph
  if (
    state.routingDecision === "from_plan_generation" &&
    state.aggregatedPlan
  ) {
    console.log(
      "[Supervisor] Received completed plan from Plan Generation Subgraph",
    );

    // Create a message to present the plan to the user
    const planPresentationMessage = new AIMessage(
      `Here's your customized training plan:\n\n${state.aggregatedPlan}\n\nThis plan has been collaboratively created by our specialized coaches, each contributing their expertise to provide you with a comprehensive approach. Let me know if you'd like any adjustments or have questions about specific parts of the plan.`,
    );

    // Clear the routing decision and return to normal flow
    const resultState: Partial<GraphState> = {
      messages: [...cleanedMessages, planPresentationMessage],
      routingDecision: null,
      aggregatedPlan: null, // Clear the plan after presenting it
    };

    span.setAttribute("routing_reason", "plan_generation_complete");
    span.setAttribute("aggregated_plan_present", true);
    span.setAttribute("output_length", safeJsonStringifyLength(resultState));
    span.end();

    return resultState;
  }

  // Return Command with the determined goto and update payload
  const command = new Command({
    goto: gotoDecision || "__end__",
    update: updatePayload,
  });

  // Final telemetry
  span.setAttribute("routing_decision", gotoDecision || "__end__");
  span.setAttribute("output_length", safeJsonStringifyLength(command));
  span.end();

  return command;
};

/**
 * Determines if we should trigger automatic plan generation based on the onboarding status
 * @param userProfile The user's profile data
 * @param messages The conversation history
 * @returns boolean Whether to trigger plan generation
 */
async function shouldGeneratePlanForOnboarding(
  userProfile: Record<string, any>,
  messages: BaseMessage[],
): Promise<boolean> {
  console.log(
    "[shouldGeneratePlanForOnboarding] Evaluating if ready for plan generation",
  );

  try {
    // Use LLM to determine if we have enough information to generate a meaningful plan
    const llm = createAzureChatOpenAI({
      temperature: 0.1,
    });

    // Prepare profile data for evaluation
    const profileData = JSON.stringify(userProfile, null, 2);

    // Prepare message history summary
    const messageHistorySummary = messages
      .map(
        (msg) =>
          `${msg._getType() === "human" ? "User" : "Coach"}: ${typeof msg.content === "string" ? msg.content : "[non-string content]"}`,
      )
      .join("\n")
      .slice(-2000); // Limit length to avoid token issues

    const prompt = `You are evaluating whether there is sufficient information to generate a personalized fitness plan.

User Profile:
${profileData}

Conversation History (excerpt):
${messageHistorySummary}

Analyze the information above and determine if there is enough context to create a meaningful, personalized fitness plan. Consider factors like:
1. User's fitness goals
2. Fitness level and experience
3. Available time and equipment
4. Any injuries or limitations
5. Other relevant contextual information

You must return ONLY "YES" if there is sufficient information to generate a good plan, or "NO" if more information is needed. Do not add any explanation.`;

    const response = await llm.invoke([new SystemMessage(prompt)]);
    const decision = (response.content as string).trim().toUpperCase();

    console.log(`[shouldGeneratePlanForOnboarding] AI decision: ${decision}`);

    return decision === "YES";
  } catch (error) {
    console.error(
      "[shouldGeneratePlanForOnboarding] Error in AI evaluation:",
      error,
    );

    // Fallback to basic check if AI evaluation fails
    const hasProfile = userProfile && Object.keys(userProfile).length > 0;
    const hasMessages = messages.length > 5;

    console.log(
      `[shouldGeneratePlanForOnboarding] Fallback decision: ${hasProfile && hasMessages}`,
    );

    return hasProfile && hasMessages;
  }
}
