import { AzureChatOpenAI } from "@langchain/openai";
import { trace } from "@opentelemetry/api";

// Azure OpenAI Configuration - Match global settings
const AZURE_OPENAI_API_KEY =
  process.env.AZURE_OPENAI_API_KEY || "********************************";
const AZURE_OPENAI_ENDPOINT =
  process.env.AZURE_OPENAI_ENDPOINT || "https://ft-gpt40mini.openai.azure.com/";
const AZURE_OPENAI_DEPLOYMENT =
  process.env.AZURE_DEPLOYMENT_NAME || "gpt-4.1-nano";
const AZURE_OPENAI_API_VERSION = "2025-01-01-preview";

interface CredibilityFactor {
  score: number;
  reasoning: string;
}

interface CredibilityAssessment {
  factors: Record<string, CredibilityFactor>;
  score: number;
  reasoning: string;
}

/**
 * CredibilityAgent evaluates the credibility of search results
 * by analyzing source information and content
 */
export class CredibilityAgent {
  private llm: AzureChatOpenAI;
  private credibilityCriteria: Record<
    string,
    { weight: number; description: string }
  >;

  constructor(config: any = {}) {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("CredibilityAgent.constructor", {
      attributes: {
        node_type: "agent_constructor",
        class_name: "CredibilityAgent",
        config_provided: !!config,
        config_temp: config?.temperature,
        config_maxTokens: config?.maxTokens,
      },
    });

    this.llm = new AzureChatOpenAI({
      azureOpenAIApiKey: AZURE_OPENAI_API_KEY,
      azureOpenAIApiVersion: AZURE_OPENAI_API_VERSION,
      modelName: AZURE_OPENAI_DEPLOYMENT,
      azureOpenAIApiDeploymentName: AZURE_OPENAI_DEPLOYMENT,
      azureOpenAIApiInstanceName:
        AZURE_OPENAI_ENDPOINT.match(
          /https:\/\/([^.]+)\.openai\.azure\.com/,
        )?.[1] || "ft-gpt40mini",
      temperature: config.temperature || 0.2,
      maxRetries: 3,
      maxTokens: config.maxTokens || 2000,
    });

    console.log(
      "[CredibilityAgent] Initialized with Azure OpenAI configuration",
    );

    this.credibilityCriteria = {
      peerReviewed: {
        weight: 3,
        description: "Is from a peer-reviewed journal or publication",
      },
      recentPublication: {
        weight: 2,
        description: "Was published within the last 5 years",
      },
      authorityDomain: {
        weight: 2,
        description: "Comes from an authoritative domain (.edu, .gov, etc.)",
      },
      citesReferences: {
        weight: 2,
        description: "Contains references to scientific literature",
      },
      expertAuthor: {
        weight: 2,
        description: "Is written by a recognized expert in the field",
      },
      neutralTone: {
        weight: 1,
        description: "Presents information in a neutral, unbiased tone",
      },
      comprehensiveContent: {
        weight: 1,
        description: "Provides comprehensive coverage of the topic",
      },
    };

    span.end();
  }

  async evaluateCredibility(searchResults: any[]): Promise<any[]> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("CredibilityAgent.evaluateCredibility", {
      attributes: {
        node_type: "agent_method",
        class_name: "CredibilityAgent",
        function_name: "evaluateCredibility",
        input_search_results_count: searchResults ? searchResults.length : 0,
      },
    });

    if (
      !searchResults ||
      !Array.isArray(searchResults) ||
      searchResults.length === 0
    ) {
      span.setAttribute("output_results_count", 0);
      span.end();
      return [];
    }

    console.log(
      `[CredibilityAgent] Evaluating credibility of ${searchResults.length} search results`,
    );

    const deduplicated = this._deduplicateResults(searchResults);

    if (deduplicated.length < searchResults.length) {
      console.log(
        `[CredibilityAgent] Removed ${searchResults.length - deduplicated.length} duplicate results`,
      );
    }

    const evaluatedResults = await Promise.all(
      deduplicated.map(async (result) => {
        try {
          const credibilityAssessment =
            await this._assessResultCredibility(result);
          return {
            ...result,
            credibilityScore: credibilityAssessment.score,
            credibilityReasoning: credibilityAssessment.reasoning,
            credibilityFactors: credibilityAssessment.factors,
          };
        } catch (error: any) {
          console.error(
            `[CredibilityAgent] Error evaluating result: ${error.message}`,
          );
          return {
            ...result,
            credibilityScore: 0.5,
            credibilityReasoning: "Error during credibility evaluation",
          };
        }
      }),
    );

    const finalResults = evaluatedResults.sort(
      (a, b) => b.credibilityScore - a.credibilityScore,
    );
    span.setAttribute("output_results_count", finalResults.length);
    span.end();
    return finalResults;
  }

  private _deduplicateResults(results: any[]): any[] {
    if (!results || results.length <= 1) {
      return results;
    }
    const seenUrls = new Set<string>();
    const seenContentHashes = new Set<string>();
    const deduplicated: any[] = [];

    for (const result of results) {
      if (result.url && seenUrls.has(result.url)) {
        continue;
      }
      const contentHash = this._generateContentHash(result.content || "");
      if (seenContentHashes.has(contentHash)) {
        continue;
      }
      deduplicated.push(result);
      if (result.url) seenUrls.add(result.url);
      seenContentHashes.add(contentHash);
    }
    return deduplicated;
  }

  private _generateContentHash(content: string): string {
    const firstChars = content.trim().substring(0, 100);
    const lengthIndicator = content.length.toString();
    return `${firstChars}:${lengthIndicator}`;
  }

  private async _assessResultCredibility(
    result: any,
  ): Promise<CredibilityAssessment> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("CredibilityAgent._assessResultCredibility", {
      attributes: {
        node_type: "agent_internal_method",
        class_name: "CredibilityAgent",
        function_name: "_assessResultCredibility",
        input_result_title: result?.title?.substring(0, 50) || "N/A",
        input_result_url: result?.url || "N/A",
      },
    });

    const { title, content, source, url } = result;
    const prompt = `
You are a credibility assessment expert. Evaluate the credibility of the following information:

TITLE: ${title || "No title provided"}
CONTENT EXCERPT: ${content ? content.substring(0, 500) + (content.length > 500 ? "..." : "") : "No content provided"}
SOURCE: ${source || "Unknown source"}
URL: ${url || "No URL provided"}

Assess the credibility of this information based on the following criteria:
${Object.entries(this.credibilityCriteria)
  .map(
    ([key, criterion]) =>
      `- ${criterion.description} (weight: ${criterion.weight})`,
  )
  .join("\n")}

For each criterion, provide:
1. A score from 0 to 10 (where 0 is not credible at all, 10 is highly credible)
2. Brief reasoning for your score

Then provide an overall credibility score from 0.0 to 1.0, calculated by the weighted average of the individual scores.

Format your response as a JSON object:
{
  "factors": {
    "peerReviewed": { "score": <0-10>, "reasoning": "<reasoning>" },
    "recentPublication": { "score": <0-10>, "reasoning": "<reasoning>" },
    ...other criteria
  },
  "score": <0.0-1.0>,
  "reasoning": "<overall reasoning for the credibility assessment>"
}
`;
    let responseText: string | undefined = undefined;

    try {
      const response = await this.llm.invoke(prompt, { callbacks: [] });
      responseText = response.content as string;

      // Clean the response text a bit more proactively
      responseText = responseText.trim();
      if (responseText.startsWith("```json")) {
        responseText = responseText.substring(7);
      } else if (responseText.startsWith("```")) {
        responseText = responseText.substring(3);
      }
      if (responseText.endsWith("```")) {
        responseText = responseText.substring(0, responseText.length - 3);
      }
      responseText = responseText.trim(); // Trim again after removing backticks

      let credibilityAssessment: CredibilityAssessment;
      try {
        credibilityAssessment = JSON.parse(responseText);
      } catch (e: any) {
        console.error(
          `[CredibilityAgent] Initial JSON.parse failed. Error: ${e.message}. Raw text after cleaning:`,
          responseText,
        );
        const jsonMatch = responseText.match(/{[\s\S]*?}/);
        if (jsonMatch && jsonMatch[0]) {
          console.log(
            "[CredibilityAgent] Found a JSON-like structure with regex match:",
            jsonMatch[0],
          );
          try {
            credibilityAssessment = JSON.parse(jsonMatch[0]);
          } catch (e2: any) {
            console.error(
              `[CredibilityAgent] JSON.parse failed even with regex match. Error: ${e2.message}. Matched text:`,
              jsonMatch[0],
            );
            throw new Error(
              `Failed to parse credibility JSON after multiple attempts. Original text: ${responseText}`,
            );
          }
        } else {
          console.error(
            "[CredibilityAgent] No JSON-like structure found with regex after initial parse failed. Text:",
            responseText,
          );
          throw new Error(
            `Failed to find or parse credibility JSON. Original text: ${responseText}`,
          );
        }
      }

      if (
        credibilityAssessment.score === undefined ||
        typeof credibilityAssessment.score !== "number"
      ) {
        console.warn(
          "[CredibilityAgent] Parsed assessment missing valid score:",
          credibilityAssessment,
        );
        throw new Error("Invalid or missing score in credibility assessment");
      }
      span.setAttribute("llm_assessment_score", credibilityAssessment.score);
      span.setAttribute(
        "llm_assessment_reasoning_length",
        credibilityAssessment.reasoning?.length || 0,
      );
      span.end();
      return credibilityAssessment;
    } catch (error: any) {
      const originalTextSnippet =
        typeof responseText === "string" && responseText
          ? responseText.substring(0, 200)
          : "N/A (responseText not available or error before assignment)";
      console.error(
        `[CredibilityAgent] Assessment error: ${error.message}. Original text (snippet): ${originalTextSnippet}`,
      );
      const errorAssessment = {
        factors: Object.fromEntries(
          Object.keys(this.credibilityCriteria).map((key) => [
            key,
            { score: 5, reasoning: "Unable to assess due to error" },
          ]),
        ) as Record<string, CredibilityFactor>,
        score: 0.5,
        reasoning: `Error during assessment: ${error.message}`,
      };
      span.setAttribute("error", true);
      span.setAttribute("error_message", error.message);
      span.setAttribute("llm_assessment_score", errorAssessment.score);
      span.end();
      return errorAssessment;
    }
  }
}

export const credibilityAgent = new CredibilityAgent();
export default credibilityAgent;
