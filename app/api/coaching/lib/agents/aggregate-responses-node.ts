/**
 * This file defines the aggregateResponsesNode function used to process and clean up
 * responses from multiple agents in the supervision graph.
 */

import { END } from "@langchain/langgraph";
import { ToolMessage } from "@langchain/core/messages";
import { RunnableConfig } from "@langchain/core/runnables";
import { GraphState } from "../graph/state";
import { trace } from "@opentelemetry/api";

/**
 * Safely get the length of a JSON stringified object, handling circular references
 * @param obj The object to stringify
 * @returns The length of the stringified object, or -1 if it can't be stringified
 */
function safeJsonStringifyLength(obj: any): number {
  try {
    // Use a replacer function to handle circular references
    const cache = new Set();
    const safeStringify = JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        // If we've seen this object before, return a placeholder
        if (cache.has(value)) {
          return "[Circular Reference]";
        }
        cache.add(value);
      }
      return value;
    });
    return safeStringify.length;
  } catch (e) {
    console.warn(
      "Could not safely stringify object for length calculation:",
      e,
    );
    // Return -1 to indicate error, or some other sentinel value
    return -1;
  }
}

/**
 * A node that aggregates responses from multiple agents and cleans up the state
 * by removing ToolMessages before persisting.
 *
 * @param state The current graph state
 * @param config Optional runnable configuration
 * @returns A partial state update with cleaned messages
 */
export async function aggregateResponsesNode(
  state: GraphState,
  config?: RunnableConfig,
): Promise<Partial<GraphState>> {
  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("aggregateResponsesNode", {
    attributes: {
      node_type: "aggregation_node",
      function_name: "aggregateResponsesNode",
      input_length: safeJsonStringifyLength(state),
      config_present: !!config,
    },
  });
  // --- TRACING END ---

  // Ensure return type is correct
  console.log(
    "[aggregateResponsesNode] Aggregating final specialist responses and cleaning state...",
  );
  console.log(
    `[aggregateResponsesNode] Received state with pendingAgents: ${JSON.stringify(state.pendingAgents)}`,
  );

  const currentMessages = state.messages;

  // --- NEW FILTERING LOGIC ---
  // Filter the *entire* history to remove all ToolMessages before persisting.
  const finalMessagesToPersist = currentMessages.filter((msg) => {
    // Keep Human messages and AI messages (even if they had tool calls, for context)
    // Explicitly remove *all* ToolMessages
    return !(msg instanceof ToolMessage);
  });
  // --- END NEW FILTERING LOGIC ---

  console.log(
    `[aggregateResponsesNode] Filtered ToolMessages. Final count to persist: ${finalMessagesToPersist.length} (Original: ${currentMessages.length})`,
  );
  console.log(
    `[aggregateResponsesNode] Last few messages to persist:`,
    JSON.stringify(
      finalMessagesToPersist.slice(-5).map((m) => ({
        type: m._getType(),
        name: m.name,
        content:
          typeof m.content === "string"
            ? m.content.slice(0, 50) + "..."
            : "[complex]",
      })),
    ),
  );

  // Construct the final state update object
  const finalUpdate: Partial<GraphState> = {
    messages: finalMessagesToPersist, // <-- Return the CLEANED messages
    pendingAgents: null, // Ensure pending is cleared
    routingDecision: END, // Explicitly set routing to END after aggregation
  };

  console.log(
    `[aggregateResponsesNode] Returning update: ${JSON.stringify(finalUpdate)}`,
  );

  // Return the final update object containing the cleaned messages.
  span.setAttribute("output_length", safeJsonStringifyLength(finalUpdate));
  span.end();
  return finalUpdate;
}
