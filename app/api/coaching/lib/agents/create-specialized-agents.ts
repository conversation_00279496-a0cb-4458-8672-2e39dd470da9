/**
 * This file defines the createSpecializedAgents function that creates specialized agent nodes
 * based on domain-specific tools and prompts.
 */

import { ChatOpenAI } from "@langchain/openai";
import { createReactAgentNode } from "./create-react-agent-node";

// Import prompts
import { strengthCoachPrompt } from "./prompts/strength-coach.prompt";
import { cardioCoachPrompt } from "./prompts/cardio-coach.prompt";
import { cyclingCoachPrompt } from "./prompts/cycling-coach.prompt";
import { nutritionCoachPrompt } from "./prompts/nutrition-coach.prompt";
import { recoveryCoachPrompt } from "./prompts/recovery-coach.prompt";
import { mentalCoachPrompt } from "./prompts/mental-coach.prompt";

// Import our Azure OpenAI configuration
import { createAzureChatOpenAI } from "../services/azure-openai-service";

/**
 * Create specialized agents with tools
 *
 * @param streamCallback Optional callback for streaming responses
 * @param tools Domain-specific tools to be used by agents
 * @param additionalTools Additional tools to be merged with the existing tools
 * @returns Object containing agent nodes and all tools
 */
export function createSpecializedAgents(
  streamCallback: any,
  {
    strengthAssessmentTool,
    strengthProgramTool,
    strengthEquipmentTool,
    cardioAssessmentTool,
    cardioProgramTool,
    runningEquipmentTool,
    macroCalculatorTool,
    mealPlanTool,
    recoveryAssessmentTool,
    recoveryPlanTool,
    mentalAssessmentTool,
    mentalTrainingPlanTool,
    cyclingAssessmentTool,
    cyclingProgramTool,
    cyclingEquipmentTool,
    elevationTool,
    azureMapsTool,
    azureSearchTool,
    azureSearchGraphTool,
    // Add session generation tools
    strengthSessionGenerationTool,
    runningSessionGenerationTool,
    cyclingSessionGenerationTool,
    nutritionSessionGenerationTool,
    recoverySessionGenerationTool,
  }: Record<string, any>,
  additionalTools: any[] = [],
) {
  console.log("Creating specialized agents with domain-specific tools...");

  // Combine base tools with any additionally provided tools (like Airtable MCP tools)
  // This list is used for binding to the LLM, so all agents can *potentially* see all tools.
  const baseAgentTools = [
    strengthAssessmentTool,
    strengthProgramTool,
    strengthEquipmentTool,
    cardioAssessmentTool,
    cardioProgramTool,
    runningEquipmentTool,
    macroCalculatorTool,
    mealPlanTool,
    recoveryAssessmentTool,
    recoveryPlanTool,
    mentalAssessmentTool,
    mentalTrainingPlanTool,
    cyclingAssessmentTool,
    cyclingProgramTool,
    cyclingEquipmentTool,
    elevationTool,
    azureMapsTool,
    azureSearchTool,
    azureSearchGraphTool,
    // Add session generation tools to base tools
    strengthSessionGenerationTool,
    runningSessionGenerationTool,
    cyclingSessionGenerationTool,
    nutritionSessionGenerationTool,
    recoverySessionGenerationTool,
  ].filter(Boolean) as any[]; // Filter out any undefined tools

  const allToolsForLLMBinding = [
    ...new Set([...baseAgentTools, ...additionalTools]),
  ].filter(Boolean) as any[];

  console.log(
    "[createSpecializedAgents] All tools being bound to LLM (includes base + additional/MCP tools):",
    allToolsForLLMBinding
      .map((t: any) => t?.name || "unknown_tool_no_name")
      .join(", "),
  );

  // Find the specific Airtable search tool from the combined list for explicit assignment
  // The exact name depends on the MCP adapter prefix and the tool name from airtable-mcp-server
  const airtableSearchRecordsTool = allToolsForLLMBinding.find(
    (t: any) =>
      t?.name?.includes("airtable-mcp-server") &&
      t?.name?.includes("search_records"),
  );
  const airtableListRecordsTool = allToolsForLLMBinding.find(
    (t: any) =>
      t?.name?.includes("airtable-mcp-server") &&
      t?.name?.includes("list_records"),
  );
  const airtableListTablesTool = allToolsForLLMBinding.find(
    (t: any) =>
      t?.name?.includes("airtable-mcp-server") &&
      t?.name?.includes("list_tables"),
  );
  const airtableListBasesTool = allToolsForLLMBinding.find(
    (t: any) =>
      t?.name?.includes("airtable-mcp-server") &&
      t?.name?.includes("list_bases"),
  );

  if (airtableSearchRecordsTool) {
    console.log(
      `[createSpecializedAgents] Found Airtable MCP search_records tool: ${airtableSearchRecordsTool.name}`,
    );
  } else {
    console.warn(
      "[createSpecializedAgents] Airtable MCP search_records tool NOT found in allToolsForLLMBinding. Relevant coaches might not use it effectively.",
    );
  }
  if (airtableListRecordsTool) {
    console.log(
      `[createSpecializedAgents] Found Airtable MCP list_records tool: ${airtableListRecordsTool.name}`,
    );
  } else {
    console.warn(
      "[createSpecializedAgents] Airtable MCP list_records tool NOT found in allToolsForLLMBinding. Relevant coaches might not use it effectively.",
    );
  }
  if (airtableListTablesTool) {
    console.log(
      `[createSpecializedAgents] Found Airtable MCP list_tables tool: ${airtableListTablesTool.name}`,
    );
  } else {
    console.warn(
      "[createSpecializedAgents] Airtable MCP list_tables tool NOT found in allToolsForLLMBinding. Relevant coaches might not use it effectively.",
    );
  }
  if (airtableListBasesTool) {
    console.log(
      `[createSpecializedAgents] Found Airtable MCP list_bases tool: ${airtableListBasesTool.name}`,
    );
  } else {
    console.warn(
      "[createSpecializedAgents] Airtable MCP list_bases tool NOT found in allToolsForLLMBinding.",
    );
  }

  // Define domain-specific tool lists
  const strengthBaseTools = [
    strengthAssessmentTool,
    strengthProgramTool,
    strengthEquipmentTool,
    strengthSessionGenerationTool, // Add session generation tool
    azureSearchGraphTool,
  ];
  const strengthTools = [...strengthBaseTools.filter(Boolean)];
  // Add Airtable tools only to strength coach
  if (airtableSearchRecordsTool) strengthTools.push(airtableSearchRecordsTool);
  if (airtableListRecordsTool) strengthTools.push(airtableListRecordsTool);
  if (airtableListTablesTool) strengthTools.push(airtableListTablesTool);
  if (airtableListBasesTool) strengthTools.push(airtableListBasesTool);

  console.log(
    "[createSpecializedAgents] Strength Coach Tools:",
    strengthTools.map((t) => t.name).join(", "),
  );

  const cardioBaseTools = [
    cardioAssessmentTool,
    cardioProgramTool,
    runningEquipmentTool,
    runningSessionGenerationTool, // Add session generation tool
    elevationTool,
    azureMapsTool,
    azureSearchGraphTool,
  ];
  const cardioTools = [...cardioBaseTools.filter(Boolean)];
  // Add Airtable tools only to cardio coach
  if (airtableSearchRecordsTool) cardioTools.push(airtableSearchRecordsTool);
  if (airtableListRecordsTool) cardioTools.push(airtableListRecordsTool);
  if (airtableListTablesTool) cardioTools.push(airtableListTablesTool);
  if (airtableListBasesTool) cardioTools.push(airtableListBasesTool);

  console.log(
    "[createSpecializedAgents] Cardio/Running Coach Tools:",
    cardioTools.map((t) => t.name).join(", "),
  );

  // NO Airtable tools for nutrition coach
  const nutritionTools = [
    macroCalculatorTool,
    mealPlanTool,
    azureSearchGraphTool,
    nutritionSessionGenerationTool,
  ].filter(Boolean) as any[];
  console.log(
    "[createSpecializedAgents] Nutrition Coach Tools:",
    nutritionTools.map((t) => t.name).join(", "),
  );

  // NO Airtable tools for recovery coach
  const recoveryTools = [
    recoveryAssessmentTool,
    recoveryPlanTool,
    recoverySessionGenerationTool, // Add session generation tool
    azureSearchGraphTool,
  ].filter(Boolean) as any[];
  console.log(
    "[createSpecializedAgents] Recovery Coach Tools:",
    recoveryTools.map((t) => t.name).join(", "),
  );

  // NO Airtable tools for mental coach
  const mentalTools = [
    mentalAssessmentTool,
    mentalTrainingPlanTool,
    azureSearchGraphTool,
  ].filter(Boolean) as any[];
  console.log(
    "[createSpecializedAgents] Mental Coach Tools:",
    mentalTools.map((t) => t.name).join(", "),
  );

  // Explicitly add the Airtable search tool to cyclingTools if found
  const cyclingBaseTools = [
    cyclingAssessmentTool,
    cyclingProgramTool,
    cyclingEquipmentTool,
    cyclingSessionGenerationTool, // Add session generation tool
    elevationTool,
    azureMapsTool,
    azureSearchGraphTool,
  ];
  const cyclingTools = [...cyclingBaseTools.filter(Boolean)];
  // Add Airtable tools only to cycling coach
  if (airtableSearchRecordsTool) cyclingTools.push(airtableSearchRecordsTool);
  if (airtableListRecordsTool) cyclingTools.push(airtableListRecordsTool);
  if (airtableListTablesTool) cyclingTools.push(airtableListTablesTool);
  if (airtableListBasesTool) cyclingTools.push(airtableListBasesTool);

  console.log(
    "[createSpecializedAgents] Cycling Coach Tools (after attempting to add Airtable search):",
    cyclingTools.map((t: any) => t.name).join(", "),
  );

  // ... (rest of the LLM setup and agent node creation) ...
  console.log("Using Azure OpenAI gpt-4o-mini for agent responses");
  const llmBase = createAzureChatOpenAI({
    temperature: 0.7,
    maxTokens: 4000,
  });

  // Bind ALL tools to the base LLM instance.
  const llmWithTools = llmBase.bindTools(allToolsForLLMBinding) as any;

  // Strength Coach - Use imported prompt
  const strengthCoachNode = createReactAgentNode({
    llm: llmWithTools,
    tools: strengthTools,
    name: "strength_coach",
    promptTemplate: strengthCoachPrompt,
  });

  // Cardio Coach - Use imported prompt
  const runningCoachNode = createReactAgentNode({
    llm: llmWithTools,
    tools: cardioTools,
    name: "running_coach",
    promptTemplate: cardioCoachPrompt,
  });

  // Cycling Coach - Use imported prompt
  const cyclingCoachNode = createReactAgentNode({
    llm: llmWithTools,
    tools: cyclingTools, // Now includes Airtable search tool if found
    name: "cycling_coach",
    promptTemplate: cyclingCoachPrompt,
  });

  // Nutrition Coach - Use imported prompt
  const nutritionCoachNode = createReactAgentNode({
    llm: llmWithTools,
    tools: nutritionTools,
    name: "nutrition_coach",
    promptTemplate: nutritionCoachPrompt,
  });

  // Recovery Coach - Use imported prompt
  const recoveryCoachNode = createReactAgentNode({
    llm: llmWithTools,
    tools: recoveryTools,
    name: "recovery_coach",
    promptTemplate: recoveryCoachPrompt,
  });

  // Mental Coach - Use imported prompt
  const mentalCoachNode = createReactAgentNode({
    llm: llmWithTools,
    tools: mentalTools,
    name: "mental_coach",
    promptTemplate: mentalCoachPrompt,
  });

  // Return nodes AND the list of all tools for the graph
  return {
    strengthCoachNode,
    runningCoachNode,
    cyclingCoachNode,
    nutritionCoachNode,
    recoveryCoachNode,
    mentalCoachNode,
    allTools: allToolsForLLMBinding,
  };
}
