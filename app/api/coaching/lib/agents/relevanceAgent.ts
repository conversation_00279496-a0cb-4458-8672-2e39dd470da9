import { AzureChatOpenAI } from "@langchain/openai";
import { trace } from "@opentelemetry/api";

// Azure OpenAI Configuration - Match global settings
const AZURE_OPENAI_API_KEY =
  process.env.AZURE_OPENAI_API_KEY || "********************************";
const AZURE_OPENAI_ENDPOINT =
  process.env.AZURE_OPENAI_ENDPOINT || "https://ft-gpt40mini.openai.azure.com/";
const AZURE_OPENAI_DEPLOYMENT =
  process.env.AZURE_DEPLOYMENT_NAME || "gpt-4.1-nano";
const AZURE_OPENAI_API_VERSION = "2025-01-01-preview";

export interface RelevanceAssessmentItem {
  docId?: string;
  title?: string;
  content_preview?: string; // A short preview of the document content
  score: number; // Standardized field name for relevance score
  reasoning: string; // Standardized field name for relevance reasoning
  keepResult: boolean;
  // Include other original properties from the search result if needed
  [key: string]: any;
}

export interface RelevanceAgentResult {
  filteredResults: any[]; // These are the results that passed the relevance threshold
  explanation: string;
  originalResultCount: number;
  keptResultCount: number;
  all_assessments: RelevanceAssessmentItem[]; // All results with their relevance assessment
}

/**
 * RelevanceAgent filters and ranks search results based on their relevance to the user's query
 */
export class RelevanceAgent {
  private llm: AzureChatOpenAI;
  private relevanceThreshold: number;
  private contextAware: boolean;

  constructor(config: any = {}) {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("RelevanceAgent.constructor", {
      attributes: {
        node_type: "agent_constructor",
        class_name: "RelevanceAgent",
        config_provided: !!config,
        config_temp: config?.temperature,
        config_maxTokens: config?.maxTokens,
        config_relevanceThreshold: config?.relevanceThreshold,
        config_contextAware: config?.contextAware !== false,
      },
    });

    this.llm = new AzureChatOpenAI({
      azureOpenAIApiKey: AZURE_OPENAI_API_KEY,
      azureOpenAIApiVersion: AZURE_OPENAI_API_VERSION,
      modelName: AZURE_OPENAI_DEPLOYMENT,
      azureOpenAIApiDeploymentName: AZURE_OPENAI_DEPLOYMENT,
      azureOpenAIApiInstanceName:
        AZURE_OPENAI_ENDPOINT.match(
          /https:\/\/([^.]+)\.openai\.azure\.com/,
        )?.[1] || "ft-gpt40mini",
      temperature: config.temperature || 0.2,
      maxRetries: 3,
      maxTokens: config.maxTokens || 2000,
    });

    console.log("[RelevanceAgent] Initialized with Azure OpenAI configuration");

    this.relevanceThreshold = config.relevanceThreshold || 0.6;
    this.contextAware = config.contextAware !== false;

    span.end();
  }

  async evaluateRelevance(
    searchResults: any[],
    query: string,
    conversationContext: any = null,
  ): Promise<RelevanceAgentResult> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("RelevanceAgent.evaluateRelevance", {
      attributes: {
        node_type: "agent_method",
        class_name: "RelevanceAgent",
        function_name: "evaluateRelevance",
        input_search_results_count: searchResults ? searchResults.length : 0,
        input_query_length: query?.length || 0,
        input_conversation_context_present: !!conversationContext,
        context_aware_enabled: this.contextAware,
      },
    });

    try {
      if (
        !searchResults ||
        !Array.isArray(searchResults) ||
        searchResults.length === 0
      ) {
        console.log(
          `[RelevanceAgent] No search results to evaluate or invalid searchResults input`,
        );
        const result: RelevanceAgentResult = {
          filteredResults: [],
          explanation: "No search results to evaluate.",
          originalResultCount: 0,
          keptResultCount: 0,
          all_assessments: [], // Ensure all_assessments is returned even in early exits
        };
        span.setAttribute(
          "output_original_results_count",
          result.originalResultCount,
        );
        span.setAttribute("output_kept_results_count", result.keptResultCount);
        span.end();
        return result;
      }

      console.log(
        `[RelevanceAgent] Evaluating relevance of ${searchResults.length} search results for query: "${query}"`,
      );

      // Log the structure of search results
      console.log(
        `[RelevanceAgent] Search results structure sample: ${JSON.stringify(searchResults[0], null, 2).substring(0, 500)}...`,
      );

      // Log search result types and availability of key fields
      const searchResultsAnalysis = searchResults.map((result, index) => {
        const hasTitle = !!result.title;
        const hasContent = !!result.content;
        return {
          index,
          hasTitle,
          titleLength: hasTitle ? result.title.length : 0,
          hasContent,
          contentLength: hasContent ? result.content.length : 0,
        };
      });
      console.log(
        `[RelevanceAgent] Search results analysis: ${JSON.stringify(searchResultsAnalysis)}`,
      );

      const previousQueries =
        conversationContext && this.contextAware
          ? conversationContext.previousQueries || []
          : [];

      const contextEnhancedQuery = this._enhanceQueryWithContext(
        query,
        previousQueries,
      );

      console.log(
        `[RelevanceAgent] After query enhancement: "${contextEnhancedQuery}"`,
      );

      const evaluatedResults = await Promise.all(
        searchResults.map(async (result, index) => {
          try {
            console.log(
              `[RelevanceAgent] Processing search result ${index + 1}/${searchResults.length}`,
            );
            const relevanceAssessment = await this._assessResultRelevance(
              result,
              contextEnhancedQuery,
              query,
            );
            // Map to RelevanceAssessmentItem structure
            return {
              ...result, // Spread original result properties first
              docId: result.id || result.chunk_id || `doc_${index}`, // Prefer id or chunk_id
              title: result.title,
              content_preview: result.content
                ? result.content.substring(0, 200) +
                  (result.content.length > 200 ? "..." : "")
                : undefined,
              score: relevanceAssessment.score, // Standardized field name
              reasoning: relevanceAssessment.reasoning, // Standardized field name
              keepResult: relevanceAssessment.score >= this.relevanceThreshold,
            } as RelevanceAssessmentItem;
          } catch (error: any) {
            console.error(
              `[RelevanceAgent] Error evaluating result ${index}: ${error.message}`,
            );
            // Map error case to RelevanceAssessmentItem structure
            return {
              ...result,
              docId: result.id || result.chunk_id || `doc_${index}`,
              title: result.title,
              content_preview: result.content
                ? result.content.substring(0, 200) +
                  (result.content.length > 200 ? "..." : "")
                : undefined,
              score: 0.5, // Default score on error
              reasoning: "Error during relevance evaluation",
              keepResult: true, // Or false, depending on desired behavior for errors
            } as RelevanceAssessmentItem;
          }
        }),
      );

      console.log(
        `[RelevanceAgent] All ${evaluatedResults.length} results evaluated`,
      );

      // Log summary of evaluation results
      const evaluationSummary = evaluatedResults.map((result, index) => ({
        index,
        title: result.title?.substring(0, 50) || "No title",
        score: result.score,
        keepResult: result.keepResult,
      }));
      console.log(
        `[RelevanceAgent] Evaluation summary: ${JSON.stringify(evaluationSummary)}`,
      );

      const filteredResults = evaluatedResults
        .filter((result) => result.keepResult)
        .sort((a, b) => b.score - a.score);

      console.log(
        `[RelevanceAgent] After filtering: ${filteredResults.length} results remain out of ${evaluatedResults.length}`,
      );

      const explanation = this._generateExplanation(
        evaluatedResults,
        filteredResults,
        query,
      );

      console.log(`[RelevanceAgent] Generated explanation: "${explanation}"`);

      return {
        filteredResults,
        explanation,
        originalResultCount: searchResults.length,
        keptResultCount: filteredResults.length,
        all_assessments: evaluatedResults, // Return all evaluated results
      };
    } catch (error: any) {
      console.error(`[RelevanceAgent] Evaluation error: ${error.message}`);
      console.error(`[RelevanceAgent] Error stack: ${error.stack}`);
      // Return all results with an explanation of the error
      const result = {
        filteredResults: searchResults.map((sr) => ({
          ...sr,
          score: 0,
          reasoning: "Error in agent",
          keepResult: false,
        })), // Map to avoid type issues if structure differs
        explanation: `Error during relevance evaluation: ${error.message}. All results are being returned unfiltered.`,
        originalResultCount: searchResults.length,
        keptResultCount: searchResults.length,
        all_assessments: searchResults.map((sr, index) => ({
          // Provide basic assessment items on error
          ...sr,
          docId: sr.id || sr.chunk_id || `doc_${index}`,
          title: sr.title,
          score: 0,
          reasoning: "Evaluation agent failed",
          keepResult: false,
        })) as RelevanceAssessmentItem[],
      };

      span.setAttribute("error", true);
      span.setAttribute("error_message", error.message);
      span.setAttribute(
        "output_original_results_count",
        result.originalResultCount,
      );
      span.setAttribute("output_kept_results_count", result.keptResultCount);
      span.end();

      return result;
    }
  }

  private _enhanceQueryWithContext(
    query: string,
    previousQueries: string[],
  ): string {
    if (!previousQueries || previousQueries.length === 0) {
      return query;
    }

    const recentQuery = previousQueries[previousQueries.length - 1];
    const followUpIndicators = [
      "it",
      "this",
      "that",
      "these",
      "those",
      "they",
      "them",
      "more",
      "also",
      "additional",
      "another",
    ];
    const queryLower = query.toLowerCase();
    const isLikelyFollowUp =
      followUpIndicators.some((indicator) => queryLower.includes(indicator)) ||
      queryLower.split(" ").length <= 3;

    if (isLikelyFollowUp) {
      console.log(
        `[RelevanceAgent] Enhanced query with context: "${recentQuery}"`,
      );
      return `${query} (in the context of: ${recentQuery})`;
    }
    return query;
  }

  private async _assessResultRelevance(
    result: any,
    contextEnhancedQuery: string,
    originalQuery: string,
  ): Promise<any> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("RelevanceAgent._assessResultRelevance", {
      attributes: {
        node_type: "agent_internal_method",
        class_name: "RelevanceAgent",
        function_name: "_assessResultRelevance",
        input_result_title: result?.title?.substring(0, 50) || "N/A",
        input_original_query_length: originalQuery?.length || 0,
        input_context_enhanced_query_length: contextEnhancedQuery?.length || 0,
      },
    });

    const { title, content } = result;

    // Log the input data being evaluated
    console.log(
      `[RelevanceAgent] Assessing relevance for content with title: "${title?.substring(0, 50)}${title?.length > 50 ? "..." : ""}"`,
    );
    console.log(
      `[RelevanceAgent] First 100 chars of content: "${content?.substring(0, 100)}${content?.length > 100 ? "..." : ""}"`,
    );

    const prompt = `
You are a relevance assessment expert. Evaluate how relevant the following search result is to the user's query:

USER QUERY: ${originalQuery}
${contextEnhancedQuery !== originalQuery ? `CONTEXT-ENHANCED QUERY: ${contextEnhancedQuery}` : ""}

SEARCH RESULT:
TITLE: ${title || "No title provided"}
CONTENT EXCERPT: ${content ? content.substring(0, 800) + (content.length > 800 ? "..." : "") : "No content provided"}

Assess the relevance of this search result to the user's query on a scale from 0.0 to 1.0:
- 0.0: Completely irrelevant, contains no information related to the query
- 0.3: Tangentially related but not directly addressing the query
- 0.6: Moderately relevant, addresses some aspects of the query
- 0.8: Highly relevant, directly addresses the main points of the query
- 1.0: Perfect match, comprehensively answers the user's query

The user's actual information need is to know about ${originalQuery}. Consider whether this result provides useful information for that need.

Format your response as a JSON object:
{
  "score": <0.0-1.0>,
  "reasoning": "<detailed explanation of why this result is or isn't relevant>",
  "keepResult": <true/false>
}
`;

    try {
      const response = await this.llm.invoke(prompt, { callbacks: [] });
      const responseText = response.content as string;

      // Log the raw LLM response
      console.log(
        `[RelevanceAgent] Raw LLM response (first 200 chars): ${responseText.substring(0, 200)}${responseText.length > 200 ? "..." : ""}`,
      );

      const jsonMatch =
        responseText.match(/```json\n([\s\S]*?)```/) ||
        responseText.match(/```\n([\s\S]*?)```/) ||
        responseText.match(/{[\s\S]*?}/);

      // Log if a JSON match was found
      console.log(
        `[RelevanceAgent] JSON match found: ${jsonMatch ? "yes" : "no"}`,
      );
      if (jsonMatch) {
        console.log(
          `[RelevanceAgent] JSON match (first 100 chars): ${jsonMatch[0].substring(0, 100)}${jsonMatch[0].length > 100 ? "..." : ""}`,
        );
      }

      let relevanceAssessment;
      if (jsonMatch && jsonMatch[0]) {
        try {
          const cleanedJson = jsonMatch[0].replace(/```json\n|```\n|```/g, "");
          console.log(
            `[RelevanceAgent] Cleaned JSON (first 100 chars): ${cleanedJson.substring(0, 100)}${cleanedJson.length > 100 ? "..." : ""}`,
          );

          relevanceAssessment = JSON.parse(cleanedJson);
        } catch (e: any) {
          console.warn(
            `[RelevanceAgent] JSON parse error from match: ${e.message}`,
          );
          console.log(
            `[RelevanceAgent] Attempting to parse entire response as fallback`,
          );
          relevanceAssessment = JSON.parse(responseText);
        }
      } else {
        console.error(
          `[RelevanceAgent] LLM returned empty or non-string response: ${responseText}`,
        );
        console.log(`[RelevanceAgent] Attempting to parse entire response`);
        relevanceAssessment = JSON.parse(responseText);
      }

      // Log the parsed assessment
      console.log(
        `[RelevanceAgent] Parsed assessment: ${JSON.stringify(relevanceAssessment)}`,
      );
      console.log(
        `[RelevanceAgent] Score type: ${typeof relevanceAssessment.score}, Value: ${relevanceAssessment.score}`,
      );

      if (
        relevanceAssessment.score === undefined ||
        relevanceAssessment.score === null ||
        typeof relevanceAssessment.score !== "number" ||
        isNaN(relevanceAssessment.score)
      ) {
        console.warn(
          `[RelevanceAgent] Invalid score detected - undefined: ${relevanceAssessment.score === undefined}, null: ${relevanceAssessment.score === null}, type: ${typeof relevanceAssessment.score}, isNaN: ${isNaN(relevanceAssessment.score as any)}`,
        );
        console.warn(
          "[RelevanceAgent] Invalid score in relevance assessment, using default score of 0.5",
        );
        relevanceAssessment.score = 0.5;
      }

      relevanceAssessment.score = Math.max(
        0,
        Math.min(1, relevanceAssessment.score),
      );

      console.log(
        `[RelevanceAgent] Final score after validation: ${relevanceAssessment.score}`,
      );

      if (relevanceAssessment.keepResult === undefined) {
        relevanceAssessment.keepResult =
          typeof relevanceAssessment.score === "number" &&
          relevanceAssessment.score >= this.relevanceThreshold;
        console.log(
          `[RelevanceAgent] keepResult was undefined, set to ${relevanceAssessment.keepResult} (threshold: ${this.relevanceThreshold})`,
        );
      }

      console.log(
        `[RelevanceAgent] Final assessment - score: ${relevanceAssessment.score}, keep: ${relevanceAssessment.keepResult}`,
      );

      span.setAttribute("llm_assessment_score", relevanceAssessment.score);
      span.setAttribute(
        "llm_assessment_reasoning_length",
        relevanceAssessment.reasoning?.length || 0,
      );
      span.setAttribute(
        "llm_assessment_keep_result",
        relevanceAssessment.keepResult,
      );
      span.end();
      return relevanceAssessment;
    } catch (error: any) {
      console.error(`[RelevanceAgent] Assessment error: ${error.message}`);
      console.error(`[RelevanceAgent] Error stack: ${error.stack}`);

      const errorAssessment = {
        score: 0.5,
        reasoning: `Error during assessment: ${error.message}`,
        keepResult: true,
      };
      span.setAttribute("error", true);
      span.setAttribute("error_message", error.message);
      span.setAttribute("llm_assessment_score", errorAssessment.score);
      span.setAttribute(
        "llm_assessment_keep_result",
        errorAssessment.keepResult,
      );
      span.end();
      return errorAssessment;
    }
  }

  private _generateExplanation(
    evaluatedResults: any[],
    filteredResults: any[] | null | undefined,
    query: string,
  ): string {
    // Ensure filteredResults is an array
    const safeFilteredResults = filteredResults || [];

    const removedResults = evaluatedResults.filter(
      (result) => !result.keepResult,
    );
    if (removedResults.length === 0) {
      return `All ${evaluatedResults.length} results were relevant to your query about "${query}".`;
    }
    const explanation = `Filtered ${removedResults.length} out of ${evaluatedResults.length} results that weren't relevant to your query about "${query}".`;
    if (removedResults.length > 0 && safeFilteredResults.length === 0) {
      return `${explanation} Unfortunately, none of the search results were sufficiently relevant to your query. Consider reformulating your question.`;
    }
    return explanation;
  }
}

export const relevanceAgent = new RelevanceAgent();
export default relevanceAgent;
