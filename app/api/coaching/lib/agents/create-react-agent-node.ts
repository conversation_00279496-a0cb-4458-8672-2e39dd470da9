/**
 * This file defines the createReactAgentNode function used to create agent nodes in the coaching graph.
 * Each node is an LLM with tools bound to it, a name, and a prompt template.
 */

import { AIMessage } from "@langchain/core/messages";
import { BaseLanguageModel } from "@langchain/core/language_models/base";
import { BaseMessage } from "@langchain/core/messages";
import { Tool } from "@langchain/core/tools";
import { GraphState } from "../graph/state";
import { RunnableConfig } from "@langchain/core/runnables";
import {
  traceAgentNode,
  recordLLMGeneration,
} from "../../../lib/azure-ai-tracing.js";
import { trace } from "@opentelemetry/api";

/**
 * Safely get the length of a JSON stringified object, handling circular references
 * @param obj The object to stringify
 * @returns The length of the stringified object, or -1 if it can't be stringified
 */
function safeJsonStringifyLength(obj: any): number {
  try {
    // Use a replacer function to handle circular references
    const cache = new Set();
    const safeStringify = JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        // If we've seen this object before, return a placeholder
        if (cache.has(value)) {
          return "[Circular Reference]";
        }
        cache.add(value);
      }
      return value;
    });
    return safeStringify.length;
  } catch (e) {
    console.warn(
      "Could not safely stringify object for length calculation:",
      e,
    );
    // Return -1 to indicate error, or some other sentinel value
    return -1;
  }
}

/**
 * Safely extract tool details for telemetry
 * @param tools Array of LangChain tools
 * @returns Simplified tool information for tracing
 */
function extractToolDetails(
  tools: Tool[],
): Array<{ name: string; description?: string }> {
  if (!tools || !Array.isArray(tools)) return [];

  return tools.map((tool) => {
    try {
      // Extract just the basic properties to avoid schema-related type issues
      return {
        name: tool.name,
        description: tool.description?.substring(0, 300),
      };
    } catch (e) {
      return { name: tool.name || "unknown" };
    }
  });
}

/**
 * Create a React-style agent node that can be used in a LangGraph.
 *
 * @param params Configuration for the agent node
 * @returns A function that processes the graph state and returns an updated state
 */
export function createReactAgentNode({
  llm,
  tools,
  name,
  promptTemplate,
}: {
  llm: BaseLanguageModel;
  tools: Tool[];
  name: string;
  promptTemplate: string;
}) {
  return async (
    state: GraphState,
    config?: RunnableConfig,
  ): Promise<Partial<GraphState>> => {
    // --- TRACING START (standard OpenTelemetry) ---
    const standardTracer = trace.getTracer("athlea-ai-coach");
    const parentSpan = standardTracer.startSpan(`agentNode:${name}`, {
      attributes: {
        node_type: "react_agent_node",
        function_name: "createReactAgentNode_execution",
        agent_name: name,
        input_state_length: safeJsonStringifyLength(state),
        input_config_length: config ? safeJsonStringifyLength(config) : 0,
        user_id: config?.configurable?.userId || "anonymous",
        thread_id:
          config?.configurable?.thread_id ||
          config?.configurable?.threadId ||
          "unknown",
        tools_count: tools.length,
        tools_names: tools.map((t) => t.name).join(","),
        // Include latest message if available (simplified to avoid type issues)
        latest_message:
          state.messages?.length > 0
            ? JSON.stringify({
                type:
                  state.messages[state.messages.length - 1]._getType?.() ||
                  "unknown",
                content_preview:
                  typeof state.messages[state.messages.length - 1].content ===
                  "string"
                    ? String(
                        state.messages[state.messages.length - 1].content,
                      ).substring(0, 300)
                    : "[non-string content]",
              })
            : "no messages",
      },
    });

    // Add detailed tool information to the span
    const toolDetails = extractToolDetails(tools);
    if (toolDetails.length > 0) {
      parentSpan.setAttribute("tools_details", JSON.stringify(toolDetails));
    }
    // --- TRACING END ---

    // Extract userId and threadId from the configuration for tracing
    const userId = config?.configurable?.userId || "anonymous";
    const threadId =
      config?.configurable?.thread_id ||
      config?.configurable?.threadId ||
      "unknown";

    // Wrap the agent execution with tracing
    return traceAgentNode(
      name,
      userId,
      threadId,
      async () => {
        try {
          console.log(
            `${name} node: Processing state with ${state.messages.length} messages`,
          );

          // Extract user profile and current plan from state for template interpolation
          const userProfile = state.userProfile || {};
          const userProfileInfo = JSON.stringify(userProfile, null, 2);

          // Current plan might be in the config
          const currentPlan = config?.configurable?.currentPlan || {};
          const currentPlanInfo = JSON.stringify(currentPlan, null, 2);

          // Replace placeholders in the prompt template
          let finalPrompt = promptTemplate
            .replace("{{userProfileInfo}}", userProfileInfo)
            .replace("{{currentPlanInfo}}", currentPlanInfo);

          // Add system prompt to parent span for detailed tracing
          parentSpan.setAttribute("system_prompt", finalPrompt);

          // Create the system message with the agent's prompt
          const systemMessage = {
            role: "system",
            content: finalPrompt,
          };

          // Prepare the messages for the LLM call
          const messages = [systemMessage, ...state.messages];

          // Capture start time for duration measurement
          const startTime = Date.now();

          // Invoke the LLM with the messages
          const result = await llm.invoke(messages);

          // Calculate duration
          const duration = Date.now() - startTime;

          // Add LLM response information to parent span
          if (result.content && typeof result.content === "string") {
            parentSpan.setAttribute(
              "llm_response_content",
              result.content.substring(0, 1000),
            );
          }

          // Check if there are tool calls in the result and add them to span
          const toolCalls =
            result.tool_calls ||
            (result as any).tool_calls ||
            (result as any).additional_kwargs?.tool_calls;

          if (toolCalls && toolCalls.length > 0) {
            parentSpan.setAttribute("tool_calls_present", true);
            parentSpan.setAttribute("tool_calls_count", toolCalls.length);
            parentSpan.setAttribute(
              "tool_calls_details",
              JSON.stringify(toolCalls),
            );

            // Add each tool call as an event for better visibility (with proper typing)
            interface ToolCall {
              name?: string;
              function?: { name?: string; arguments?: string };
              args?: string | Record<string, unknown>;
              id?: string;
            }

            // Add each tool call as an event for better visibility
            (toolCalls as ToolCall[]).forEach((tool: ToolCall, idx: number) => {
              parentSpan.addEvent(`tool_call_${idx}`, {
                name: tool.name || tool.function?.name || "unknown",
                args:
                  typeof tool.args === "string"
                    ? tool.args
                    : typeof tool.function?.arguments === "string"
                      ? tool.function.arguments
                      : JSON.stringify(
                          tool.args || tool.function?.arguments || {},
                        ),
              });
            });
          }

          // Record LLM generation details for tracing
          try {
            // Extract token info if available
            const tokenInfo = (result as any).usage || {};

            recordLLMGeneration(
              (llm as any).modelName || "unknown-model",
              tokenInfo.prompt_tokens || 0,
              tokenInfo.completion_tokens || 0,
              duration,
              state.messages, // Only use confirmed BaseMessage array
              result,
              {
                "llm.agent_name": name,
                "llm.has_tool_calls": !!(
                  result.tool_calls ||
                  (result as any).tool_calls ||
                  (result as any).additional_kwargs?.tool_calls
                ),
                "llm.system_prompt_length": finalPrompt.length,
                "llm.message_count": messages.length,
              },
            );
          } catch (tracingError) {
            console.warn(
              `Failed to record LLM generation metrics for ${name}:`,
              tracingError,
            );
          }

          // Return an updated state with the AI message
          const resultState: Partial<GraphState> = {
            messages: [
              new AIMessage({
                content: result.content,
                name,
                // Pass tool calls if they exist
                ...(toolCalls && { tool_calls: toolCalls }),
                // Pass additional_kwargs which might contain tool info
                additional_kwargs: (result as any).additional_kwargs,
              }),
            ],
          };
          parentSpan.setAttribute(
            "output_length",
            safeJsonStringifyLength(resultState),
          );
          parentSpan.setAttribute(
            "llm_result_content_length",
            typeof result.content === "string" ? result.content.length : 0,
          );
          parentSpan.end();
          return resultState;
        } catch (error) {
          console.error(`Error in ${name} node:`, error);
          const errorResult: Partial<GraphState> = {
            messages: [
              new AIMessage({
                content: `Error in ${name} agent: ${error instanceof Error ? error.message : "Unknown error"}`,
                name,
              }),
            ],
          };
          parentSpan.setAttribute("error", true);
          parentSpan.setAttribute(
            "error_message",
            error instanceof Error ? error.message : String(error),
          );
          parentSpan.setAttribute(
            "output_length",
            safeJsonStringifyLength(errorResult),
          );
          parentSpan.end();
          return errorResult;
        }
      },
      // Add additional metadata for the agent execution
      {
        "agent.tools_count": tools.length,
        "agent.tools": tools.map((t) => t.name).join(","),
        "agent.prompt_template_preview": promptTemplate.substring(0, 300),
      },
    );
  };
}
