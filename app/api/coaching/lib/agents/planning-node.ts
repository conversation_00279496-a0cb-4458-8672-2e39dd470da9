import { ChatOpenAI } from "@langchain/openai";
import { SystemMessage, BaseMessage } from "@langchain/core/messages";
import { RunnableConfig } from "@langchain/core/runnables";
import { z } from "zod";
import { GraphState } from "../graph/state";
// Import our Azure OpenAI configuration
import { createAzureChatOpenAI } from "../services/azure-openai-service";
import { trace } from "@opentelemetry/api";

// Define Tool Schema
const planSchema = z
  .object({
    steps: z
      .array(z.string())
      .describe(
        "An array of steps for the plan. Each step should be a valid node name (e.g., 'strength_coach', 'nutrition_coach', 'generate_plan') or 'clarification'. Use 'generate_plan' ONLY if a comprehensive NEW plan is requested. List multiple coaches if needed for specific tasks.",
      ),
  })
  .describe("Sets the execution plan based on the user request analysis.");

// List of valid agent node names
const agentNodeNames = [
  "head_coach",
  "strength_coach",
  "running_coach",
  "cycling_coach",
  "nutrition_coach",
  "recovery_coach",
  "mental_coach",
  "planningNode",
  "reasoningNode",
  "generate_plan",
];

/**
 * Planning node for the coaching graph
 * Analyzes user request and determines which specialists should handle it
 */
export async function planningNode(
  state: GraphState,
  config?: RunnableConfig,
): Promise<Partial<GraphState>> {
  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("planningNode", {
    attributes: {
      node_type: "planning_node",
      function_name: "planningNode",
      // Safely get state length without potential circular references
      input_state_length: safeJsonStringifyLength(state),
      // Avoid stringifying the entire config - just indicate if it exists
      config_present: !!config,
      // Add user ID and thread for correlation
      user_id:
        config?.configurable?.userId || (state as any)?.userId || "anonymous",
      thread_id:
        config?.configurable?.thread_id ||
        config?.configurable?.threadId ||
        (state as any)?.threadId ||
        "unknown",
      // Add message count for debugging
      message_count: Array.isArray(state.messages) ? state.messages.length : 0,
    },
  });
  // --- TRACING END ---

  // Only returns plan and step
  console.log("--- Running Planning Node --- (Plan Only Version)");
  const userProfile = config?.configurable?.userProfile || state.userProfile;
  const currentPlanInfo = config?.configurable?.currentPlan;
  const messages = state.messages || [];
  const latestMessage = messages[messages.length - 1];

  // Add latest user message to span if available
  if (
    latestMessage &&
    latestMessage._getType &&
    latestMessage._getType() === "human"
  ) {
    span.setAttribute(
      "latest_user_message",
      typeof latestMessage.content === "string"
        ? latestMessage.content.substring(0, 300)
        : JSON.stringify(latestMessage.content).substring(0, 300),
    );
  }

  // Ensure agentNodeNames is defined/accessible
  if (typeof agentNodeNames === "undefined") {
    console.error("FATAL: agentNodeNames not defined in planningNode scope!");
    // Return minimal state to avoid breaking graph execution entirely, supervisor can handle it
    span.setAttribute("error", true);
    span.setAttribute("error_message", "agentNodeNames not defined in scope");
    span.setAttribute(
      "output_length",
      safeJsonStringifyLength({ plan: ["clarification"], current_step: 0 }),
    );
    span.end();
    return { plan: ["clarification"], current_step: 0 };
  }

  const planningLlm = createAzureChatOpenAI({
    temperature: 0.7,
    maxTokens: 4000,
  });

  // Bind the tool to the LLM
  const planningLlmWithTool = planningLlm.bindTools([
    {
      type: "function",
      function: {
        name: "set_execution_plan",
        description: planSchema.description,
        parameters: {
          type: "object",
          properties: {
            steps: {
              type: "array",
              items: { type: "string" },
              description: planSchema.shape.steps.description,
            },
          },
          required: ["steps"],
        },
      },
    },
  ]);

  // Revised prompt Attempt 5 - Emphasize Context for General Suggestions
  const planningPromptTemplate = `You are the Planning Agent for a fitness coaching system. Your SOLE role is to analyze the user's latest request, the conversation history, and context, then determine the execution plan.

User Profile:
{{userProfileInfo}}

Current Plan Context:
{{currentPlanInfo}}

Conversation History (excluding last message):
{{message_history}}

User's Latest Request: {{latest_user_message}}

Available Specialist Nodes: strength_coach, running_coach, cycling_coach, nutrition_coach, recovery_coach, mental_coach
Other Routing Options: head_coach (ONLY for truly general chat or existing plan META-discussion, NOT for domain-specific suggestions), clarification (if truly ambiguous)

Instructions:
1.  **Analyze Request & Context:** Understand the user's core need based on their latest message AND the preceding conversation context (including any reasoning provided). **Crucially, if the user asks for "general suggestions" or similar broad terms, determine the underlying topic from the context.**

2.  **Determine Appropriate Plan - CRITICAL RULES:**
    *   **RULE 1 (CLARIFICATION NEEDED):** If the request is genuinely too ambiguous to determine the domain OR if the Head Coach explicitly asked for clarification in the previous turn, return ["clarification"].
    *   **RULE 2 (SPECIFIC DOMAIN TASK/QUESTION):** If the request (considering context) clearly asks for advice, information, or a task within one or more specialist domains (e.g., "find running routes", "protein sources", "improve cycling form", "general suggestions about running routes"), return the relevant specialist coach node(s) (e.g., ["running_coach"], ["nutrition_coach"], ["cycling_coach", "recovery_coach"]). **Do NOT default to head_coach if the topic is identifiable.**
    *   **RULE 3 (EXISTING PLAN / GENERAL CHAT):** If the request is about discussing the EXISTING plan structure, progress updates, or truly general conversation NOT specific to any coach's domain (e.g., "How are you?", "Tell me about my plan summary"), return ["head_coach"].

3.  **Output Format:** Return ONLY a JSON object matching the schema: { "steps": ["node_name1", "node_name2", ...] }. Use ONLY valid node names listed above.

Example Analysis (User asks "General suggestions" after discussing running routes):
- Latest Message: "General suggestions"
- Context: Previous messages were about finding 4km mountain running routes near Weybridge.
- Analysis: The user still wants running route suggestions, just phrased generally.
- Decision: Route to the specialist. Output: { "steps": ["running_coach"] }`;

  let promptInput = planningPromptTemplate;
  // Replace template variables
  promptInput = promptInput.replace(
    "{{userProfileInfo}}",
    userProfile ? JSON.stringify(userProfile, null, 2) : "Not Available",
  );
  promptInput = promptInput.replace(
    "{{currentPlanInfo}}",
    currentPlanInfo
      ? JSON.stringify(currentPlanInfo, null, 2)
      : "Not Available",
  );
  promptInput = promptInput.replace(
    "{{message_history}}",
    messages
      .slice(0, -1)
      .map(
        (m: BaseMessage) =>
          `${m._getType()}: ${typeof m.content === "string" ? m.content.substring(0, 150) + "..." : "[non-string content]"}`,
      )
      .join("\n"),
  );
  promptInput = promptInput.replace(
    "{{latest_user_message}}",
    latestMessage?.content?.toString() || "No latest message",
  );

  // Add the prompt template to tracing
  span.setAttribute(
    "system_prompt_template",
    planningPromptTemplate.substring(0, 500),
  );
  span.setAttribute("system_prompt_filled", promptInput.substring(0, 1000));

  const llmMessages = [new SystemMessage(promptInput)];

  let parsedPlan: string[] = ["head_coach"]; // Default fallback in case tool call fails completely

  try {
    console.log("  > Calling Planning LLM with Tool Binding...");
    // Time the LLM call
    const startTime = Date.now();
    const response = await planningLlmWithTool.invoke(llmMessages);
    const duration = Date.now() - startTime;
    span.setAttribute("llm_response_time_ms", duration);

    console.log(
      "  > Planning LLM Full Response:",
      JSON.stringify(response, null, 2),
    );

    // Add response content to trace for debugging
    if (typeof response.content === "string") {
      span.setAttribute(
        "llm_response_content",
        response.content.substring(0, 500),
      );
    }

    // Extract plan ONLY from tool call
    const toolCalls = response.tool_calls;
    if (
      toolCalls &&
      toolCalls.length > 0 &&
      toolCalls[0].name === "set_execution_plan"
    ) {
      const planArgs = toolCalls[0].args;

      // Add tool call details to telemetry
      span.setAttribute("tool_call_present", true);
      span.setAttribute("tool_call_name", toolCalls[0].name);
      span.setAttribute("tool_call_args", JSON.stringify(planArgs));

      if (planArgs && Array.isArray(planArgs.steps)) {
        // Validate steps
        const validNodes = [
          ...agentNodeNames,
          "clarification",
          "human_input_node",
          "__end__",
        ];
        const validatedSteps = planArgs.steps.filter(
          (step: any) =>
            typeof step === "string" && validNodes.includes(step.toLowerCase()),
        );

        if (validatedSteps.length > 0) {
          parsedPlan = validatedSteps;
          console.log("  > Plan extracted from tool call:", parsedPlan);
          span.setAttribute("validated_steps_count", validatedSteps.length);
        } else {
          console.warn(
            "Tool call 'steps' contained no valid node names. Defaulting plan to clarification.",
          );
          parsedPlan = ["clarification"]; // Default to clarification if validation fails
          span.setAttribute("validation_failed", true);
          span.setAttribute("validation_reason", "no_valid_steps");
        }
      } else {
        console.warn(
          "Tool call 'set_execution_plan' missing valid 'steps' array. Defaulting plan to clarification.",
        );
        parsedPlan = ["clarification"]; // Default to clarification
        span.setAttribute("validation_failed", true);
        span.setAttribute("validation_reason", "missing_steps_array");
      }
    } else {
      console.warn(
        "No valid 'set_execution_plan' tool call found in LLM response. Defaulting plan to clarification.",
      );
      // Default to clarification if the tool wasn't called
      parsedPlan = ["clarification"];
      span.setAttribute("tool_call_present", false);
    }
  } catch (error) {
    console.error("Error during Planning LLM call:", error);
    // Default to clarification on error
    parsedPlan = ["clarification"];
    span.setAttribute("error", true);
    span.setAttribute(
      "error_message",
      error instanceof Error ? error.message : String(error),
    );
  }

  // Ensure plan is not empty
  if (!parsedPlan || parsedPlan.length === 0) {
    console.warn("Final plan is empty, defaulting to clarification");
    parsedPlan = ["clarification"];
    span.setAttribute("empty_plan_fixed", true);
  }

  console.log("  > Final Plan Steps:", parsedPlan);

  // Return ONLY the plan and step, NO messages
  const result: Partial<GraphState> = {
    plan: parsedPlan,
    current_step: 0,
  };
  span.setAttribute("output_length", safeJsonStringifyLength(result));
  span.setAttribute("parsed_plan", JSON.stringify(parsedPlan));

  // Add individual plan steps as attributes for easier querying
  parsedPlan.forEach((step, index) => {
    span.setAttribute(`plan_step_${index}`, step);
  });

  span.end();
  return result;
}

/**
 * Safely get the length of a JSON stringified object, handling circular references
 * @param obj The object to stringify
 * @returns The length of the stringified object, or -1 if it can't be stringified
 */
function safeJsonStringifyLength(obj: any): number {
  try {
    // Use a replacer function to handle circular references
    const cache = new Set();
    const safeStringify = JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        // If we've seen this object before, return a placeholder
        if (cache.has(value)) {
          return "[Circular Reference]";
        }
        cache.add(value);
      }
      return value;
    });
    return safeStringify.length;
  } catch (e) {
    console.warn(
      "Could not safely stringify object for length calculation:",
      e,
    );
    // Return -1 to indicate error, or some other sentinel value
    return -1;
  }
}
