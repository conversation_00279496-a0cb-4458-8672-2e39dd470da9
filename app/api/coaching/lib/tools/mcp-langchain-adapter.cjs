"use strict";

const path = require("path");
const { MultiServerMCPClient } = require("@langchain/mcp-adapters");

// Determine project root path
const projectRoot = process.cwd();

// Path to MCP server
const mcpServerRoot = path.join(projectRoot, "mcp-server");
const serverScriptPath = path.join(mcpServerRoot, "build", "server.js");

// Singleton client instance
let clientInstance = null;
let toolsCache = null;
let initializationPromise = null;

/**
 * Gets or creates the MultiServerMCPClient instance.
 * @returns {Promise<MultiServerMCPClient>} The MultiServerMCPClient instance
 */
async function getClient() {
  if (clientInstance) return clientInstance;
  if (initializationPromise) return initializationPromise;

  console.log("[MCP-LangChain] Initializing MCP client");
  initializationPromise = (async () => {
    try {
      const config = {
        // Global tool configuration options - similar to the example
        throwOnLoadError: false, // Don't throw errors if tools fail to load
        prefixToolNameWithServerName: true, // Prefix tool names with server name
        additionalToolNamePrefix: "mcp", // Add mcp prefix to all tool names

        // Server configuration
        mcpServers: {
          athlea: {
            transport: "stdio",
            command: "node",
            args: [serverScriptPath],
            env: {
              ...process.env,
              NEXT_PUBLIC_AIRTABLE_API_KEY:
                process.env.NEXT_PUBLIC_AIRTABLE_API_KEY,
              NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID:
                process.env.NEXT_PUBLIC_AIRTABLE_CYCLING_BASE_ID,
              NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID:
                process.env.NEXT_PUBLIC_AIRTABLE_RUNNING_BASE_ID,
              NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID:
                process.env.NEXT_PUBLIC_AIRTABLE_STRENGTH_BASE_ID,
            },
            cwd: projectRoot,
            restart: {
              enabled: true,
              maxAttempts: 3,
              delayMs: 1000,
            },
          },
        },
      };

      console.log(
        "[MCP-LangChain] Creating MultiServerMCPClient with config:",
        JSON.stringify(config, null, 2),
      );

      clientInstance = new MultiServerMCPClient(config);

      // This is now just initializeConnections() as per the example
      await clientInstance.initializeConnections();
      console.log("[MCP-LangChain] MCP client initialized successfully");
      return clientInstance;
    } catch (error) {
      console.error("[MCP-LangChain] Failed to initialize MCP client:", error);
      initializationPromise = null;
      clientInstance = null;
      throw error;
    }
  })();
  return initializationPromise;
}

/**
 * Loads tools from the MCP server.
 * @returns {Promise<Array<import("@langchain/core/tools").Tool>>} Array of Tool instances
 */
async function getTools() {
  if (toolsCache) return toolsCache;

  try {
    const client = await getClient();
    if (!client) {
      console.error(
        "[MCP-LangChain] MCP client not available. Cannot get tools.",
      );
      return [];
    }

    console.log(
      "[MCP-LangChain] Loading tools from MCP server using client.getTools()",
    );

    // Use getTools as in the example, with options that match our config
    const mcpLangChainTools = await client.getTools();

    console.log(
      `[MCP-LangChain] Loaded ${mcpLangChainTools.length} tools from MCP server.`,
    );

    // Log tool details for debugging
    if (mcpLangChainTools.length > 0) {
      console.log("[MCP-LangChain] Tools loaded:");
      mcpLangChainTools.forEach((tool) => {
        console.log(`  - ${tool.name}: ${tool.description}`);
      });
    } else {
      console.log("[MCP-LangChain] No tools were loaded from the MCP server.");
    }

    toolsCache = mcpLangChainTools;
    return toolsCache;
  } catch (error) {
    console.error("[MCP-LangChain] Error getting MCP tools:", error);
    // Log more details about the error
    if (error.name === "MCPClientError") {
      console.error(
        `[MCP-LangChain] MCPClientError (${error.serverName}):`,
        error.message,
      );
    } else if (error.name === "ToolException") {
      console.error("[MCP-LangChain] ToolException:", error.message);
    } else if (error.name === "ZodError" && error.issues) {
      console.error("[MCP-LangChain] ZodError:", error.issues);
      error.issues.forEach((issue) => {
        console.error(
          `  - Path: ${issue.path.join(".")}, Error: ${issue.message}`,
        );
      });
    }
    toolsCache = [];
    return [];
  }
}

/**
 * Closes the MCP client connection.
 * @returns {Promise<void>}
 */
async function closeClient() {
  if (clientInstance) {
    try {
      console.log("[MCP-LangChain] Closing MCP client");
      await clientInstance.close();
      console.log("[MCP-LangChain] MCP client closed successfully");
      clientInstance = null;
      toolsCache = null;
      initializationPromise = null;
    } catch (error) {
      console.error("[MCP-LangChain] Error closing MCP client:", error);
    }
  }
}

module.exports = {
  getClient,
  getTools,
  closeClient,
};
