// Type definitions for nutrition session structures

export interface MealIngredient {
  name: string;
  quantity: string;
  unit?: string;
  calories: number;
}

export interface MealItem {
  description: string;
  ingredients: MealIngredient[];
  preparation?: string;
  total_calories: number;
}

export interface Meal {
  items: MealItem[];
  meal_calories: number;
}

export interface DailyPlan {
  breakfast: Meal;
  morning_snack: Meal;
  lunch: Meal;
  afternoon_snack: Meal;
  dinner: Meal;
  daily_total_calories: number;
}

export interface NutritionSession {
  day: string;
  session_type: "nutrition";
  meals: DailyPlan;
  rationale: string;
  // Additional fields that may be present in current implementation
  caloric_intake?: string;
  macronutrients?: {
    protein: string;
    carbohydrates: string;
    fat: string;
  };
}
