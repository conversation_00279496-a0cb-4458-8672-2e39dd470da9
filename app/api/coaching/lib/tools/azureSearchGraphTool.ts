import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@langchain/core/tools";
import { z } from "zod";
import { relevanceAgent, RelevanceAgentResult } from "../agents/relevanceAgent";
import { credibilityAgent } from "../agents/credibilityAgent";
import { azureSearchTool } from "./azureSearchTool";
import { trace } from "@opentelemetry/api";

// --- Enhanced Azure Search Tool with Agents Schema ---
const enhancedAzureSearchSchema = z.object({
  query: z.string().describe("The search query to perform."),
  context: z
    .record(z.any())
    .optional()
    .describe("Optional conversation context for follow-up questions."),
  filter_irrelevant: z
    .boolean()
    .optional()
    .default(true)
    .describe("Whether to filter irrelevant results using relevance agent."),
  evaluate_credibility: z
    .boolean()
    .optional()
    .default(true)
    .describe("Whether to evaluate source credibility."),
  relevance_threshold: z
    .number()
    .optional()
    .default(0.6)
    .describe("Minimum relevance score [0-1] to keep results (default: 0.6)."),
  return_reasoning: z
    .boolean()
    .optional()
    .default(false)
    .describe("Include agent reasoning in results."),
  max_results: z
    .number()
    .optional()
    .default(5)
    .describe("Maximum number of results to return."),
  vector_search: z
    .boolean()
    .optional()
    .default(true)
    .describe("Whether to use vector search."),
});

// --- AzureSearchGraphTool Class ---
export class AzureSearchGraphTool extends Tool {
  name = "azure_search_graph";
  description = `Search for information using Azure Cognitive Search with intelligent filtering and source evaluation.
This tool enhances search results by:
1. Filtering irrelevant results based on your query
2. Evaluating source credibility based on academic standards
3. Ranking results by combined relevance and credibility

Example input: {"query": "benefits of HIIT", "filter_irrelevant": true, "evaluate_credibility": true}
Or simply: "benefits of HIIT" for default settings`;

  // Fix schema to match what the Tool base class expects, using transform pattern
  schema = z
    .object({
      input: z
        .string()
        .optional()
        .describe("The search query or JSON string with search parameters"),
    })
    .transform((obj) => obj.input);

  private conversationContext: Record<string, any> = {
    previousQueries: [],
    relevantTopics: [],
  };

  constructor(params?: ToolParams) {
    super(params);
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchGraphTool.constructor", {
      attributes: {
        node_type: "tool_constructor",
        class_name: "AzureSearchGraphTool",
      },
    });

    console.log(
      "[AzureSearchGraphTool] Initialized with intelligent filtering agents",
    );

    span.end();
  }

  private _createErrorResponse(
    query: string,
    message: string,
    originalResultCount = 0,
    relevanceFilteredCount = 0,
    filterIrrelevant = false,
    evaluateCredibility = false,
    allRelevanceAssessments?: any[],
  ): string {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchGraphTool._createErrorResponse", {
      attributes: {
        node_type: "tool_method",
        class_name: "AzureSearchGraphTool",
        function_name: "_createErrorResponse",
        query,
        message,
        originalResultCount,
        relevanceFilteredCount,
        filterIrrelevant,
        evaluateCredibility,
      },
    });

    const metaResponse: any = {
      originalResultCount: originalResultCount,
      filteredResultCount: 0,
      relevanceFiltered: relevanceFilteredCount,
      credibilityEvaluated: evaluateCredibility,
      error: true,
      message: message,
    };

    if (allRelevanceAssessments && allRelevanceAssessments.length > 0) {
      metaResponse.relevance_assessments = allRelevanceAssessments;
    }

    const response = JSON.stringify(
      {
        query,
        results: [],
        meta: metaResponse,
      },
      null,
      2,
    );

    span.end();
    return response;
  }

  // Main method that accepts tool input
  async _call(inputString: string | undefined): Promise<string> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchGraphTool._call", {
      attributes: {
        node_type: "tool_method",
        class_name: "AzureSearchGraphTool",
        function_name: "_call",
        input_length: inputString?.length || 0,
      },
    });

    console.log(`[AzureSearchGraphTool._call] Received input: ${inputString}`);

    if (!inputString) {
      span.setAttribute("error", true);
      span.setAttribute("error_reason", "Missing input");
      span.end();
      return this._createErrorResponse(
        "Unknown query (parsing failed)",
        "Input string is required.",
      );
    }

    let parsedInput: z.infer<typeof enhancedAzureSearchSchema>;
    let queryForErrorResponse = "Unknown query (parsing failed)";

    try {
      // Parse input - either JSON or direct query string
      if (inputString.trim().startsWith("{")) {
        try {
          const jsonInput = JSON.parse(inputString);
          console.log(`[AzureSearchGraphTool] Parsed JSON input:`, jsonInput);

          if (jsonInput.query) {
            // Direct schema format
            parsedInput = enhancedAzureSearchSchema.parse(jsonInput);
            queryForErrorResponse = jsonInput.query;
          } else if (jsonInput.input && typeof jsonInput.input === "string") {
            try {
              // Try parsing input field as JSON
              const nestedInput = JSON.parse(jsonInput.input);
              parsedInput = enhancedAzureSearchSchema.parse(nestedInput);
              queryForErrorResponse = nestedInput.query || jsonInput.input;
            } catch (nestedError) {
              // If not valid JSON inside input, treat input field as direct query
              parsedInput = enhancedAzureSearchSchema.parse({
                query: jsonInput.input,
              });
              queryForErrorResponse = jsonInput.input;
            }
          } else {
            // Fallback - treat the entire JSON string as query
            parsedInput = enhancedAzureSearchSchema.parse({
              query: inputString,
            });
            queryForErrorResponse = inputString;
          }
        } catch (jsonError) {
          console.error("Failed to parse JSON input:", jsonError);
          // If JSON parsing fails, treat as direct query
          parsedInput = enhancedAzureSearchSchema.parse({
            query: inputString,
          });
          queryForErrorResponse = inputString;
        }
      } else {
        // Direct simple string query with defaults
        parsedInput = enhancedAzureSearchSchema.parse({
          query: inputString,
        });
        queryForErrorResponse = inputString;
      }
    } catch (error) {
      const errorMessage = `Error: Failed to parse input: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[AzureSearchGraphTool] ${errorMessage}`);
      span.setAttribute("error", true);
      span.setAttribute("error_reason", "Parse error");
      span.setAttribute("error_message", errorMessage);
      span.end();
      return this._createErrorResponse(queryForErrorResponse, errorMessage);
    }

    const {
      query,
      context,
      filter_irrelevant,
      evaluate_credibility,
      relevance_threshold,
      return_reasoning,
      max_results,
      vector_search,
    } = parsedInput;

    span.setAttribute("query", query);
    span.setAttribute("filter_irrelevant", filter_irrelevant);
    span.setAttribute("evaluate_credibility", evaluate_credibility);
    span.setAttribute("max_results", max_results);
    span.setAttribute("vector_search", vector_search);

    // Update conversation context
    if (context) {
      this.conversationContext = {
        ...this.conversationContext,
        ...context,
      };
    }

    // Add current query to conversation context
    if (!this.conversationContext.previousQueries) {
      this.conversationContext.previousQueries = [];
    }
    this.conversationContext.previousQueries.push(query);

    try {
      // Step 1: Call Azure Search Tool
      console.log(
        `[AzureSearchGraphTool] Executing search for query: "${query}"`,
      );

      // Format parameters for Azure Search Tool
      const searchParams = {
        query: query,
        top_k: max_results,
        vector_search: vector_search,
      };

      // Execute the search using existing azureSearchTool
      const searchSpan = tracer.startSpan(
        "AzureSearchGraphTool.azureSearchTool._call",
        {
          attributes: {
            node_type: "tool_child_operation",
            parent_tool: "AzureSearchGraphTool",
            child_tool: "azureSearchTool",
            query,
            top_k: max_results,
            vector_search,
          },
        },
      );

      const searchResultsStr = await azureSearchTool._call(
        JSON.stringify(searchParams),
      );

      searchSpan.end();

      let searchResults: any[] = [];
      let initialResultCount = 0;

      try {
        const parsedToolOutput = JSON.parse(searchResultsStr);
        if (Array.isArray(parsedToolOutput)) {
          searchResults = parsedToolOutput;
          initialResultCount = searchResults.length;
        } else if (
          typeof parsedToolOutput === "object" &&
          parsedToolOutput !== null &&
          "error" in parsedToolOutput &&
          parsedToolOutput.error
        ) {
          // If azureSearchTool itself returned a structured error
          console.warn(
            `[AzureSearchGraphTool] AzureSearchTool returned an error: ${parsedToolOutput.message}`,
          );
          span.setAttribute("error", true);
          span.setAttribute("error_reason", "Search tool error");
          span.setAttribute(
            "error_message",
            parsedToolOutput.message || "Error from underlying search tool",
          );
          span.end();
          return this._createErrorResponse(
            query,
            parsedToolOutput.message || "Error from underlying search tool.",
            0,
            0,
            filter_irrelevant,
            evaluate_credibility,
          );
        } else {
          // Unexpected non-array, non-error object from azureSearchTool
          console.warn(
            `[AzureSearchGraphTool] AzureSearchTool returned unexpected non-array object:`,
            parsedToolOutput,
          );
          span.setAttribute("error", true);
          span.setAttribute(
            "error_reason",
            "Unexpected search tool response format",
          );
          span.end();
          return this._createErrorResponse(
            query,
            "AzureSearchTool returned an unexpected data format.",
            0,
            0,
            filter_irrelevant,
            evaluate_credibility,
          );
        }
      } catch (e) {
        // If searchResultsStr is not JSON (e.g., a plain error string from azureSearchTool)
        console.warn(
          `[AzureSearchGraphTool] Search failed or returned non-JSON: ${searchResultsStr}`,
        );
        span.setAttribute("error", true);
        span.setAttribute("error_reason", "Invalid JSON from search tool");
        span.end();
        return this._createErrorResponse(
          query,
          searchResultsStr,
          0,
          0,
          filter_irrelevant,
          evaluate_credibility,
        );
      }

      if (initialResultCount === 0) {
        span.setAttribute("error", true);
        span.setAttribute("error_reason", "No search results");
        span.end();
        return this._createErrorResponse(
          query,
          `No results initially found by AzureSearchTool for query: "${query}"`,
          0,
          0,
          filter_irrelevant,
          evaluate_credibility,
        );
      }

      span.setAttribute("initial_result_count", initialResultCount);

      let relevanceFilteredResults = searchResults;
      let relevanceExplanation = "";
      let numRemovedByRelevance = 0;
      let allRelevanceAssessments: any[] | undefined = undefined;

      if (filter_irrelevant) {
        console.log(
          `[AzureSearchGraphTool] Filtering results using relevance agent`,
        );

        // Add detailed logging of searchResults before passing to relevanceAgent
        console.log(
          "[AzureSearchGraphTool] Documents PRE-FILTERING by RelevanceAgent:",
          JSON.stringify(searchResults, null, 2),
        );

        try {
          console.log(
            "[AzureSearchGraphTool] Calling relevanceAgent.evaluateRelevance with query:",
            query,
          );

          const relevanceSpan = tracer.startSpan(
            "AzureSearchGraphTool.relevanceAgent.evaluateRelevance",
            {
              attributes: {
                node_type: "tool_child_operation",
                parent_tool: "AzureSearchGraphTool",
                child_agent: "relevanceAgent",
                query,
                input_results_count: searchResults.length,
              },
            },
          );

          const relevanceEval = await relevanceAgent.evaluateRelevance(
            searchResults,
            query,
            this.conversationContext,
          );

          relevanceSpan.end();

          console.log(
            "[AzureSearchGraphTool] RelevanceAgent evaluation result:",
            JSON.stringify(relevanceEval, null, 2),
          );

          // Check if relevanceEval has the expected structure
          console.log("[AzureSearchGraphTool] RelevanceEval properties check:");
          console.log(
            `- Has filteredResults: ${relevanceEval && "filteredResults" in relevanceEval}`,
          );
          console.log(
            `- filteredResults is array: ${relevanceEval && "filteredResults" in relevanceEval && Array.isArray(relevanceEval.filteredResults)}`,
          );
          console.log(
            `- filteredResults length: ${relevanceEval && "filteredResults" in relevanceEval && Array.isArray(relevanceEval.filteredResults) ? relevanceEval.filteredResults.length : "N/A"}`,
          );
          console.log(
            `- Has explanation: ${relevanceEval && "explanation" in relevanceEval}`,
          );
          console.log(
            `- Has originalResultCount: ${relevanceEval && "originalResultCount" in relevanceEval}`,
          );
          console.log(
            `- Has keptResultCount: ${relevanceEval && "keptResultCount" in relevanceEval}`,
          );

          if (relevanceEval) {
            relevanceFilteredResults = relevanceEval.filteredResults;
            relevanceExplanation = relevanceEval.explanation;
            if (relevanceEval.all_assessments) {
              allRelevanceAssessments = relevanceEval.all_assessments;
              console.log(
                `[AzureSearchGraphTool] Captured ${allRelevanceAssessments.length} individual relevance assessments.`,
              );
            } else {
              console.warn(
                "[AzureSearchGraphTool] relevanceEval.all_assessments was undefined even though relevanceEval was present.",
              );
            }
            numRemovedByRelevance =
              (relevanceEval.originalResultCount || searchResults.length) -
              (relevanceEval.keptResultCount ||
                relevanceFilteredResults.length);
          } else {
            console.warn(
              "[AzureSearchGraphTool] relevanceEval was undefined. Skipping relevance filtering logic.",
            );
            // Fallback if relevanceEval is unexpectedly undefined
            numRemovedByRelevance = 0;
          }

          console.log(
            `[AzureSearchGraphTool] Relevance agent kept ${relevanceFilteredResults.length} of ${searchResults.length} results`,
          );

          span.setAttribute("relevance_filtered_count", numRemovedByRelevance);
          span.setAttribute(
            "relevance_kept_count",
            relevanceFilteredResults.length,
          );

          // Log first filtered result if available
          if (relevanceFilteredResults.length > 0) {
            console.log(
              `[AzureSearchGraphTool] First filtered result sample: ${JSON.stringify(relevanceFilteredResults[0], null, 2).substring(0, 300)}...`,
            );
          }
        } catch (error) {
          console.error(
            "[AzureSearchGraphTool] Error during relevance evaluation:",
            error,
          );
          console.error(
            "[AzureSearchGraphTool] Error stack:",
            (error as Error).stack,
          );
          // Continue with unfiltered results
          relevanceExplanation =
            "Error during relevance evaluation. Using unfiltered results.";

          span.setAttribute("relevance_error", true);
          span.setAttribute(
            "relevance_error_message",
            (error as Error).message,
          );
        }
      }

      if (relevanceFilteredResults.length === 0) {
        const message =
          `No relevant results found after filtering. ${relevanceExplanation}`.trim();
        span.setAttribute("error", true);
        span.setAttribute(
          "error_reason",
          "No relevant results after filtering",
        );
        span.end();
        return this._createErrorResponse(
          query,
          message,
          initialResultCount,
          numRemovedByRelevance,
          filter_irrelevant,
          evaluate_credibility,
          allRelevanceAssessments,
        );
      }

      // Step 3: Apply Credibility Agent if enabled
      let finalResults = relevanceFilteredResults;

      if (evaluate_credibility) {
        console.log(
          `[AzureSearchGraphTool] Evaluating credibility of ${relevanceFilteredResults.length} results`,
        );

        const credibilitySpan = tracer.startSpan(
          "AzureSearchGraphTool.credibilityAgent.evaluateCredibility",
          {
            attributes: {
              node_type: "tool_child_operation",
              parent_tool: "AzureSearchGraphTool",
              child_agent: "credibilityAgent",
              input_results_count: relevanceFilteredResults.length,
            },
          },
        );

        const credibilityEvaluationObject: any =
          await credibilityAgent.evaluateCredibility(relevanceFilteredResults);

        credibilitySpan.end();

        console.log(
          "[AzureSearchGraphTool] Raw output from credibilityAgent.evaluateCredibility:",
          JSON.stringify(credibilityEvaluationObject, null, 2),
        );

        if (Array.isArray(credibilityEvaluationObject)) {
          // If the object is directly an array, use it
          finalResults = credibilityEvaluationObject;
          console.log(
            `[AzureSearchGraphTool] CredibilityAgent returned an array, using directly. Count: ${finalResults.length}`,
          );
        } else if (
          credibilityEvaluationObject &&
          typeof credibilityEvaluationObject === "object"
        ) {
          console.log(
            "[AzureSearchGraphTool] credibilityEvaluationObject is a valid object.",
          );
          if (credibilityEvaluationObject.hasOwnProperty("processedResults")) {
            console.log(
              "[AzureSearchGraphTool] credibilityEvaluationObject has property 'processedResults'.",
            );
            console.log(
              "[AzureSearchGraphTool] Type of credibilityEvaluationObject.processedResults:",
              typeof (credibilityEvaluationObject as any).processedResults,
            );
            console.log(
              "[AzureSearchGraphTool] Is credibilityEvaluationObject.processedResults an array?",
              Array.isArray(
                (credibilityEvaluationObject as any).processedResults,
              ),
            );

            if (
              Array.isArray(
                (credibilityEvaluationObject as any).processedResults,
              )
            ) {
              finalResults = (credibilityEvaluationObject as any)
                .processedResults;
              console.log(
                `[AzureSearchGraphTool] Successfully extracted ${finalResults.length} processed results from credibilityAgent.`,
              );
            } else {
              console.error(
                "[AzureSearchGraphTool] credibilityEvaluationObject.processedResults IS NOT AN ARRAY. Defaulting to empty array.",
                "Value:",
                (credibilityEvaluationObject as any).processedResults,
              );
              finalResults = []; // Keep as empty or handle as error
            }
          } else {
            console.warn(
              "[AzureSearchGraphTool] credibilityEvaluationObject DOES NOT HAVE property 'processedResults' and is not an array. Using relevanceFilteredResults.",
              "Object keys:",
              Object.keys(credibilityEvaluationObject),
            );
            // Fallback or specific handling if structure is unexpected
            // For now, let's assume if no processedResults and not an array, we might want to use relevanceFilteredResults or log an error
            finalResults = relevanceFilteredResults; // Or consider it an error/empty results
          }
        } else {
          console.error(
            "[AzureSearchGraphTool] credibilityAgent.evaluateCredibility did not return a valid object or array. Using relevanceFilteredResults.",
            "Received:",
            credibilityEvaluationObject,
          );
          finalResults = relevanceFilteredResults; // Fallback to results before credibility step
        }

        console.log(
          `[AzureSearchGraphTool] Credibility evaluation complete. Number of results for quality scoring: ${finalResults.length}`,
        );

        span.setAttribute("credibility_results_count", finalResults.length);
      }

      // Step 4: Combine scores and rank results
      finalResults = finalResults.map((result) => {
        const relevanceScore = result.relevanceScore || 0.5;
        const credibilityScore = result.credibilityScore || 0.5;
        const qualityScore = relevanceScore * 0.6 + credibilityScore * 0.4;

        return {
          // Spread all existing result properties first
          ...result,
          // Then ensure our specific scores and reasoning are present/overridden if necessary
          qualityScore,
          relevanceScore, // Already part of result if it came through relevance/credibility agents
          credibilityScore, // Already part of result if it came through credibility agent
          relevanceReasoning: result.relevanceReasoning || undefined,
          credibilityReasoning: result.credibilityReasoning || undefined,
        };
      });

      // Sort by quality score
      finalResults.sort(
        (a, b) => (b.qualityScore || 0) - (a.qualityScore || 0),
      );

      // Store the full sorted results for display in ToolStepTracer
      const allSortedResults = [...finalResults];

      // Limit only if max_results is specified and greater than 0
      // This will limit the results returned to the AI but allow all results to be displayed in the UI
      if (max_results > 0) {
        finalResults = finalResults.slice(0, max_results);
      }

      // Step 5: Format and return results
      const response = {
        query,
        results: finalResults.map((result) => {
          // Define the formatted result with proper type
          interface FormattedResult {
            title: any;
            content: any;
            qualityScore: number;
            source: any;
            url: string;
            relevanceScore?: number;
            credibilityScore?: number;
            relevanceReasoning?: string;
            credibilityReasoning?: string;
            docId?: string;
          }

          // Log the result object being mapped here
          console.log(
            "[AzureSearchGraphTool] Mapping result for final response:",
            JSON.stringify(result, null, 2),
          );

          const formattedResult: FormattedResult = {
            title: result.title,
            content: result.content,
            qualityScore: Math.round((result.qualityScore || 0) * 100) / 100,
            source: result.source,
            url: result.url || "",
            docId: result.id || result.doc_id || result.title,
            relevanceScore:
              result.relevanceScore !== undefined
                ? Math.round(result.relevanceScore * 100) / 100
                : undefined,
            credibilityScore:
              result.credibilityScore !== undefined
                ? Math.round(result.credibilityScore * 100) / 100
                : undefined,
            relevanceReasoning: result.relevanceReasoning, // Directly pass from result
            credibilityReasoning: result.credibilityReasoning, // Directly pass from result
          };

          // The return_reasoning flag from tool input is for the AI agent consuming the tool,
          // not necessarily for display in the tracer if the fields already exist.
          // However, to be safe, if return_reasoning is true, we ensure they are included.
          if (return_reasoning) {
            if (result.relevanceReasoning) {
              formattedResult.relevanceReasoning = result.relevanceReasoning;
            }
            if (result.credibilityReasoning) {
              formattedResult.credibilityReasoning =
                result.credibilityReasoning;
            }
          }

          // Log the formattedResult before it's returned by the map function
          console.log(
            "[AzureSearchGraphTool] Formatted result for final response:",
            JSON.stringify(formattedResult, null, 2),
          );

          return formattedResult;
        }),
        meta: {
          originalResultCount: initialResultCount,
          filteredResultCount: finalResults.length,
          relevanceFiltered: numRemovedByRelevance,
          credibilityEvaluated: evaluate_credibility,
          message:
            finalResults.length === 0
              ? relevanceExplanation ||
                "No results to display after all filters."
              : undefined,
          relevanceExplanation: filter_irrelevant
            ? relevanceExplanation
            : undefined,
          // Add the captured relevance assessments to the meta object
          relevance_assessments: allRelevanceAssessments,
        },
      };

      // Log the complete response object before stringifying
      console.log(
        "[AzureSearchGraphTool] Final response object before stringify:",
        JSON.stringify(response, null, 2),
      );

      // Build final output with metadata
      const output = {
        query: query,
        results: finalResults,
        meta: {
          originalResultCount: initialResultCount,
          filteredResultCount: finalResults.length,
          relevanceFiltered: numRemovedByRelevance,
          credibilityEvaluated: evaluate_credibility,
          relevance_assessments: allRelevanceAssessments,
          message: response.meta.message,
          error: !!response.meta.message,
        },
      };

      console.log(
        "[AzureSearchGraphTool] Final output with relevance_assessments:",
        JSON.stringify(
          {
            query: output.query,
            resultsCount: output.results.length,
            meta: {
              ...output.meta,
              relevance_assessments_count: output.meta.relevance_assessments
                ? output.meta.relevance_assessments.length
                : 0,
              has_relevance_assessments: !!output.meta.relevance_assessments,
            },
          },
          null,
          2,
        ),
      );

      span.setAttribute("final_results_count", finalResults.length);
      span.setAttribute(
        "has_relevance_assessments",
        !!output.meta.relevance_assessments,
      );
      span.end();

      return JSON.stringify(output, null, 2);
    } catch (error) {
      console.error(
        "[AzureSearchGraphTool] Error in _call main try-catch:",
        error,
      );
      span.setAttribute("error", true);
      span.setAttribute("error_reason", "Exception in main execution");
      span.setAttribute(
        "error_message",
        error instanceof Error ? error.message : String(error),
      );
      span.end();
      return this._createErrorResponse(
        query,
        `Error during search graph execution: ${error instanceof Error ? error.message : String(error)}`,
        0,
        0,
        filter_irrelevant,
        evaluate_credibility,
      );
    }
  }
}
