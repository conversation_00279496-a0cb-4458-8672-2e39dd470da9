import { z } from "zod";
import {
  DynamicStructuredTool,
  type ToolInterface,
} from "@langchain/core/tools";
import { MultiServerMCPClient } from "@langchain/mcp-adapters";
// The options type for MultiServerMCPClient is often inferred from its constructor or defined internally.
// We will rely on TypeScript's inference for clientOptions.

// --- Define Zod Schemas (similar to the cjs script) ---
const airtableToolSchemas = {
  list_bases: z
    .object({})
    .describe("Lists all Airtable bases accessible to the API key."),
  list_tables: z
    .object({
      baseId: z.string().describe("The ID of the Airtable base."),
    })
    .describe("Lists all tables in a specified Airtable base."),
  list_records: z
    .object({
      baseId: z.string().describe("The ID of the Airtable base."),
      tableId: z
        .string()
        .describe("The ID or name of the table within the base."),
      maxRecords: z
        .number()
        .int()
        .positive()
        .optional()
        .describe("Maximum number of records to return. Default is 100."),
    })
    .describe("Lists records from a specified table in an Airtable base."),
  // Add search_records schema for more advanced searching
  search_records: z
    .object({
      baseId: z.string().describe("The ID of the Airtable base."),
      tableId: z
        .string()
        .describe("The ID or name of the table within the base."),
      filterByFormula: z
        .string()
        .describe(
          "Airtable formula to filter records. Example: {IntensityLevel}='4' for high intensity.",
        ),
      maxRecords: z
        .number()
        .int()
        .positive()
        .optional()
        .describe("Maximum number of records to return. Default is 100."),
      view: z
        .string()
        .optional()
        .describe(
          "The name or ID of a view in the specified table. Only records in this view will be returned.",
        ),
      sort: z
        .array(
          z.object({
            field: z.string().describe("The name of the field to sort by."),
            direction: z
              .enum(["asc", "desc"])
              .optional()
              .describe("The sort direction. Default is asc (ascending)."),
          }),
        )
        .optional()
        .describe(
          "List of sort objects that define the sort order for the records.",
        ),
    })
    .describe(
      "Searches for records in a specified table using a formula filter.",
    ),
  // Add other schemas here if needed (e.g., search_records, get_record etc.)
  // based on the tools provided by airtable-mcp-server
};

type AirtableToolSchemaName = keyof typeof airtableToolSchemas;

// Global client reference to prevent multiple instances
let globalMcpClient: MultiServerMCPClient | null = null;
let globalToolsPromise: Promise<DynamicStructuredTool[]> | null = null;
let isConnecting = false;

/**
 * Helper function to suggest Airtable parameters for common query types
 */
function suggestAirtableQueryParameters(
  schemaKey: string,
  baseId: string,
  searchTerm?: string,
): string {
  // No suggestions for certain schema types
  if (schemaKey === "list_bases" || !baseId) return "";

  // Create suggestions based on the search term and baseId
  if (searchTerm && searchTerm.toLowerCase().includes("hill")) {
    switch (baseId) {
      case "appkOxteq7fiscoye": // Cycling
        return `Suggested parameters for hill-related cycling sessions:
          baseId: "appkOxteq7fiscoye"
          tableId: "Cycling Sessions"
          filterByFormula: 'OR(SEARCH("hill", {SessionName}) > 0, SEARCH("climb", {SessionName}) > 0)'
          maxRecords: 5`;
      case "appg6TiMRFQVf0OXd": // Running
        return `Suggested parameters for hill-related running sessions:
          baseId: "appg6TiMRFQVf0OXd"
          tableId: "Running Sessions"
          filterByFormula: 'OR(SEARCH("hill", {SessionName}) > 0, SEARCH("sprint", {SessionName}) > 0)'
          maxRecords: 5`;
      case "appbhSTOiO1EHDFrK": // Strength
        return `Suggested parameters for hill-related strength sessions:
          baseId: "appbhSTOiO1EHDFrK"
          tableId: "S&C Sessions"
          filterByFormula: 'OR(SEARCH("hill", {SessionName}) > 0, SEARCH("incline", {SessionName}) > 0)'
          maxRecords: 5`;
      default:
        return "";
    }
  }

  return "";
}

// We will return an array of DynamicStructuredTool specifically.
export async function connectAirtableMcpTools(
  airtableApiKey: string,
  nodePath?: string,
  npxPath?: string,
  serverStartupDelayMs: number = 10000,
): Promise<{ tools: DynamicStructuredTool[]; client: MultiServerMCPClient }> {
  console.log("[AirtableIntegration] Connecting to Airtable MCP server...");

  // Return existing client and tools if already available
  if (globalMcpClient && globalToolsPromise) {
    console.log("[AirtableIntegration] Using existing MCP client and tools");
    const tools = await globalToolsPromise;
    return { tools, client: globalMcpClient };
  }

  // If connection is in progress, wait for it to complete
  if (isConnecting && globalToolsPromise) {
    console.log(
      "[AirtableIntegration] Connection already in progress, waiting for it to complete",
    );
    const tools = await globalToolsPromise;
    return { tools, client: globalMcpClient! };
  }

  isConnecting = true;

  if (!airtableApiKey || !airtableApiKey.startsWith("pat")) {
    isConnecting = false;
    throw new Error(
      "Invalid or missing Airtable API Key provided to connectAirtableMcpTools.",
    );
  }

  // Command should be npx itself, args are for npx
  const command = npxPath || "npx"; // Use provided npxPath or expect npx in PATH
  const args = ["-y", "airtable-mcp-server"];

  // Log the nodePath for debugging, but it's not directly used in the command if npx is the command
  if (nodePath) {
    console.log(
      `[AirtableIntegration] Received nodePath: ${nodePath} (not directly used if command is npx)`,
    );
  }

  console.log(
    `[AirtableIntegration] Using command: ${command}, args: ${args.join(" ")}`,
  );

  const clientOptions = {
    "airtable-mcp-server": {
      transport: "stdio" as const,
      command: command, // This will be 'npx' or the full path to npx
      args: args, // Args for npx command
      env: {
        AIRTABLE_API_KEY: airtableApiKey,
        PATH: process.env.PATH || "",
      },
    },
  };

  const mcpClient = new MultiServerMCPClient(clientOptions);
  globalMcpClient = mcpClient;

  // Create a promise that will resolve with the wrapped tools
  globalToolsPromise = initializeAirtableTools(mcpClient, serverStartupDelayMs);

  // Return placeholder tools array that will be replaced with actual tools once ready
  const placeholderTools: DynamicStructuredTool[] = Object.keys(
    airtableToolSchemas,
  ).map((schemaKey) => {
    return new DynamicStructuredTool({
      name: `mcp__airtable-mcp-server__${schemaKey}`,
      description: `Tool for ${schemaKey} (initializing...)`,
      schema: airtableToolSchemas[schemaKey as AirtableToolSchemaName],
      func: async (args: any) => {
        // Wait for the real tools to be available before executing
        if (!globalToolsPromise) {
          throw new Error("Airtable MCP server initialization failed");
        }
        const realTools = await globalToolsPromise;
        const realTool = realTools.find(
          (t) => t.name === `mcp__airtable-mcp-server__${schemaKey}`,
        );
        if (!realTool) {
          throw new Error(`Tool ${schemaKey} not initialized yet`);
        }
        return realTool.invoke(args);
      },
    });
  });

  // Start the server initialization process in the background
  console.log(
    "[AirtableIntegration] Started background initialization of Airtable MCP server",
  );

  return { tools: placeholderTools, client: mcpClient };
}

// Helper function to initialize the tools in the background
async function initializeAirtableTools(
  mcpClient: MultiServerMCPClient,
  serverStartupDelayMs: number,
): Promise<DynamicStructuredTool[]> {
  try {
    console.log(
      `[AirtableIntegration] Waiting ${serverStartupDelayMs}ms for server to start...`,
    );
    await new Promise((resolve) => setTimeout(resolve, serverStartupDelayMs));

    console.log("[AirtableIntegration] Getting tools from MCP Adapter...");
    // The getTools() method from MultiServerMCPClient should return a compatible type (e.g., BaseTool[] or ToolInterface[])
    const adapterProvidedTools =
      (await mcpClient.getTools()) as ToolInterface[];
    if (!adapterProvidedTools || adapterProvidedTools.length === 0) {
      throw new Error(
        "Adapter returned no tools. Check connection and server logs.",
      );
    }
    console.log(
      `[AirtableIntegration] Adapter provided ${adapterProvidedTools.length} tools.`,
    );
    console.log(
      `[AirtableIntegration] Raw tool names: ${adapterProvidedTools.map((t) => t.name).join(", ")}`,
    );

    const finalWrappedTools: DynamicStructuredTool[] = []; // Using ToolInterface
    for (const localSchemaName in airtableToolSchemas) {
      const schemaKey = localSchemaName as AirtableToolSchemaName;

      let adapterToolNameSuffix = `__${schemaKey}`;
      // *** Modification: If our local schema is 'search_records', map it to the adapter's 'list_records' tool ***
      if (schemaKey === "search_records") {
        console.log(
          `[AirtableIntegration] Mapping local 'search_records' schema to adapter's 'list_records' tool for formula-based searching.`,
        );
        adapterToolNameSuffix = `__list_records`;
      }

      const adapterTool = adapterProvidedTools.find((t) =>
        t.name.endsWith(adapterToolNameSuffix),
      );

      if (!adapterTool) {
        console.warn(
          `[AirtableIntegration] Tool for schema "${schemaKey}" not found in adapter tools. Skipping.`,
        );
        continue;
      }

      const zodSchema = airtableToolSchemas[schemaKey];
      console.log(
        `[AirtableIntegration] Re-wrapping tool "${adapterTool.name}" with schema "${schemaKey}".`,
      );

      // Determine the correct name for the final wrapped tool.
      // For our local 'search_records' schema, we want the tool to be publicly named 'mcp__airtable-mcp-server__search_records',
      // even if it uses the adapter's 'list_records' tool internally.
      const finalToolName =
        schemaKey === "search_records"
          ? `mcp__airtable-mcp-server__search_records`
          : adapterTool.name;

      const wrappedTool = new DynamicStructuredTool({
        name: finalToolName, // Use the determined final name
        description:
          adapterTool.description ||
          zodSchema.description ||
          `Invoke ${schemaKey}`,
        schema: zodSchema,
        func: async (args: z.infer<typeof zodSchema>) => {
          // Infer args type from schema
          console.log(
            `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Original Zod-validated args:`,
            JSON.stringify(args),
          );

          // Enhanced debugging for Cycling Coach - track which base/table is being accessed
          if (args && typeof args === "object" && "baseId" in args) {
            const baseId = (args as any).baseId;
            const tableId = (args as any).tableId || "N/A";
            const filterByFormula = (args as any).filterByFormula || "N/A";

            console.log(`---> [DEBUG] Tool "${schemaKey}" accessing Airtable with:
              * baseId: ${baseId}
              * tableId: ${tableId}
              * filterByFormula: ${filterByFormula}
              * Expected cycling baseId: appkOxteq7fiscoye
              * Expected cycling tableId: Cycling Sessions
              * Matches expected cycling base? ${baseId === "appkOxteq7fiscoye" ? "YES" : "NO"}
            `);

            // Log warning for list_bases/list_tables which should be restricted
            if (schemaKey === "list_bases" || schemaKey === "list_tables") {
              console.log(
                `---> [WARNING] Agent attempting to use ${schemaKey} which should be restricted. This may cause issues with specific table access.`,
              );
            }

            // Detect hill-related searches for our specific issue
            if (
              (schemaKey === "search_records" ||
                schemaKey === "list_records") &&
              tableId !== "N/A"
            ) {
              // Look for hill-related queries in the filter formula or table name
              const isHillRelated =
                (filterByFormula !== "N/A" &&
                  filterByFormula.toLowerCase().includes("hill")) ||
                (tableId && tableId.toLowerCase().includes("hill"));

              // Get suggestions for proper hill search query
              const suggestions = suggestAirtableQueryParameters(
                schemaKey,
                baseId,
                isHillRelated ? "hill" : undefined,
              );

              if (isHillRelated) {
                console.log(
                  `---> [DEBUG] Hill-related search detected with formula: ${filterByFormula}`,
                );
                if (suggestions) {
                  console.log(`---> [DEBUG] ${suggestions}`);
                }
              } else if (filterByFormula === "N/A" || filterByFormula === "") {
                console.log(
                  `---> [WARNING] No filterByFormula provided for ${schemaKey}. For specific queries like "hill sessions", a filter is recommended.`,
                );
                console.log(
                  `---> [DEBUG] Example filterByFormula for Cycling hill sessions: 'OR(SEARCH("hill", {SessionName}) > 0, SEARCH("climb", {SessionName}) > 0)'`,
                );
              }
            }
          }

          let processedArgs = { ...args };

          // Specifically preprocess args for search_records if that's the current tool being wrapped
          if (schemaKey === "search_records") {
            // Type assertion to access specific properties of search_records args
            // This ensures we are referencing the correct type for processedArgs.
            const searchArgsSpecific = processedArgs as z.infer<
              typeof airtableToolSchemas.search_records
            >;

            if (
              searchArgsSpecific.view === "" ||
              searchArgsSpecific.view === "All"
            ) {
              console.log(
                `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Removing '${searchArgsSpecific.view}' view parameter for mcp server compatibility.`,
              );
              delete searchArgsSpecific.view;
            }
            if (
              searchArgsSpecific.sort &&
              Array.isArray(searchArgsSpecific.sort) &&
              searchArgsSpecific.sort.length === 0
            ) {
              console.log(
                `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Removing empty 'sort' array parameter for mcp server compatibility.`,
              );
              delete searchArgsSpecific.sort;
            }
          }

          // For list_records and our mapped search_records (which now uses list_records mcp tool),
          // if maxRecords is undefined, let's default it to 100.
          if (schemaKey === "list_records" || schemaKey === "search_records") {
            const listOrSearchArgs = processedArgs as
              | z.infer<typeof airtableToolSchemas.list_records>
              | z.infer<typeof airtableToolSchemas.search_records>;
            if (listOrSearchArgs.maxRecords === undefined) {
              // Check on the correctly typed object
              console.log(
                `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Defaulting maxRecords to 100.`,
              );
              listOrSearchArgs.maxRecords = 100;
            }
          }

          try {
            console.log(
              `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Invoking original adapter tool with processed args:`,
              JSON.stringify(processedArgs),
            );
            const result = await adapterTool.invoke(processedArgs as any); // Use processedArgs
            console.log(
              `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Invocation successful.`,
            );
            // Log result summary for debugging
            const resultStr =
              typeof result === "string" ? result : JSON.stringify(result);
            const isTruncated = resultStr.length > 100;
            console.log(
              `---> [DEBUG] Tool "${schemaKey}" result summary: ${resultStr.substring(0, 100)}${isTruncated ? "... (truncated)" : ""}`,
            );

            // For list_records/search_records, log the count of returned records if it's an array
            if (
              (schemaKey === "list_records" ||
                schemaKey === "search_records") &&
              Array.isArray(result)
            ) {
              console.log(
                `---> [DEBUG] Query returned ${result.length} records`,
              );

              // If no records were found, show some debug info
              if (result.length === 0) {
                console.log(`---> [DEBUG] No records found for query. Possible issues:
                  1. The specified baseId/tableId may be incorrect
                  2. The filterByFormula syntax might be wrong
                  3. There might be no matching records
                  4. Table fields might be different than expected
                  Check your baseId: ${(processedArgs as any).baseId || "N/A"}
                  Check your tableId: ${(processedArgs as any).tableId || "N/A"}
                  Check your filter: ${(processedArgs as any).filterByFormula || "N/A"}`);
              }
            }

            return result;
          } catch (invokeError: any) {
            console.error(
              `---> [Wrapped ${adapterTool.name} (mapped from ${schemaKey})] Error during original tool.invoke():`,
              invokeError,
            );
            return `Error invoking ${adapterTool.name} (mapped from ${schemaKey}): ${invokeError.message}`;
          }
        },
      });
      finalWrappedTools.push(wrappedTool);
    }

    if (finalWrappedTools.length === 0) {
      console.error(
        "[AirtableIntegration] No tools could be re-wrapped. Available adapter tools:",
        adapterProvidedTools.map((t) => t.name),
      );
      throw new Error(
        "Failed to re-wrap any Airtable tools with Zod schemas. Check tool names and schemas.",
      );
    }

    console.log(
      `[AirtableIntegration] Successfully re-wrapped ${finalWrappedTools.length} tools: ${finalWrappedTools.map((t) => t.name).join(", ")}`,
    );

    isConnecting = false;
    return finalWrappedTools;
  } catch (error) {
    console.error("[AirtableIntegration] Error initializing tools:", error);
    isConnecting = false;
    throw error;
  }
}

/**
 * Closes the MCP client connection.
 * @param client The MultiServerMCPClient instance to close.
 */
export async function closeMcpClient(
  client: MultiServerMCPClient | null,
): Promise<void> {
  if (client) {
    console.log("[AirtableIntegration] Closing MCP adapter client...");
    try {
      await client.close();
      // Reset global references
      if (client === globalMcpClient) {
        globalMcpClient = null;
        globalToolsPromise = null;
        isConnecting = false;
      }
      console.log(
        "[AirtableIntegration] MCP adapter client closed successfully.",
      );
    } catch (cleanupError) {
      console.error(
        "[AirtableIntegration] Error closing MCP adapter client:",
        cleanupError,
      );
    }
  } else {
    console.log(
      "[AirtableIntegration] closeMcpClient called with null client.",
    );
  }
}
