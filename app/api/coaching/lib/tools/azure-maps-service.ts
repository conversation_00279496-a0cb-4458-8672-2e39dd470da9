/**
 * AzureMapsService: A utility service for interacting with Azure Maps API
 *
 * This service provides functionality for location search, geocoding, and weather data
 * from Azure Maps.
 */

import axios from "axios";

export interface GeocodeResult {
  position: {
    lat: number;
    lon: number;
  };
  address: {
    freeformAddress?: string;
    countryCode?: string;
    country?: string;
    localName?: string;
  };
}

export interface LocationResult {
  id: string;
  type: string;
  score: number;
  dist?: number;
  position: {
    lat: number;
    lon: number;
  };
  address: {
    freeformAddress?: string;
    streetName?: string;
    municipalitySubdivision?: string;
    municipality?: string;
    countrySubdivision?: string;
    countryCode?: string;
  };
  poi?: {
    name: string;
    categorySet: { id: number }[];
    categories: string[];
  };
}

export interface WeatherResult {
  temperature: {
    value: number;
    unit: string;
  };
  humidity: {
    value: number;
    unit: string;
  };
  windSpeed: {
    value: number;
    unit: string;
  };
  precipitation: {
    value: number;
    unit: string;
  };
  condition: string;
  feelsLike: {
    value: number;
    unit: string;
  };
}

export class AzureMapsService {
  private subscriptionKey: string;
  private baseUrl: string = "https://atlas.microsoft.com/";

  constructor(subscriptionKey: string) {
    this.subscriptionKey = subscriptionKey;
  }

  /**
   * Geocode an address to get coordinates
   */
  async geocodeAddress(address: string): Promise<GeocodeResult | null> {
    try {
      const response = await axios.get(`${this.baseUrl}search/address/json`, {
        params: {
          "subscription-key": this.subscriptionKey,
          "api-version": "1.0",
          query: address,
          limit: 1,
        },
      });

      if (
        response.data &&
        response.data.results &&
        response.data.results.length > 0
      ) {
        const result = response.data.results[0];
        return {
          position: {
            lat: result.position.lat,
            lon: result.position.lon,
          },
          address: {
            freeformAddress: result.address.freeformAddress,
            countryCode: result.address.countryCode,
            country: result.address.country,
            localName: result.address.localName,
          },
        };
      }

      return null;
    } catch (error) {
      console.error("Error geocoding address:", error);
      throw new Error(
        `Failed to geocode address: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Search for nearby points of interest
   */
  async searchNearby(
    lat: number,
    lon: number,
    query: string,
    radius: number = 10000,
  ): Promise<LocationResult[]> {
    try {
      const response = await axios.get(`${this.baseUrl}search/nearby/json`, {
        params: {
          "subscription-key": this.subscriptionKey,
          "api-version": "1.0",
          lat: lat,
          lon: lon,
          radius: radius,
          query: query,
          limit: 25,
        },
      });

      if (response.data && response.data.results) {
        return response.data.results.map((result: any) => ({
          id: result.id || "",
          type: result.type || "",
          score: result.score || 0,
          dist: result.dist,
          position: {
            lat: result.position.lat,
            lon: result.position.lon,
          },
          address: {
            freeformAddress: result.address?.freeformAddress,
            streetName: result.address?.streetName,
            municipalitySubdivision: result.address?.municipalitySubdivision,
            municipality: result.address?.municipality,
            countrySubdivision: result.address?.countrySubdivision,
            countryCode: result.address?.countryCode,
          },
          poi: result.poi
            ? {
                name: result.poi.name,
                categorySet: result.poi.categorySet,
                categories: result.poi.categories,
              }
            : undefined,
        }));
      }

      return [];
    } catch (error) {
      console.error("Error searching nearby:", error);
      throw new Error(
        `Failed to search nearby: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Get current weather for a location
   */
  async getCurrentWeather(
    lat: number,
    lon: number,
  ): Promise<WeatherResult | null> {
    try {
      const response = await axios.get(
        `${this.baseUrl}weather/currentConditions/json`,
        {
          params: {
            "subscription-key": this.subscriptionKey,
            "api-version": "1.0",
            query: `${lat},${lon}`,
          },
        },
      );

      console.log(
        "Azure Maps Weather API raw response:",
        JSON.stringify(response.data, null, 2),
      );

      if (
        response.data &&
        response.data.results &&
        response.data.results.length > 0
      ) {
        const result = response.data.results[0];
        console.log("Weather result object:", JSON.stringify(result, null, 2));

        // Create a safe data extraction helper function
        const getSafeValue = (
          obj: any,
          path: string,
          defaultVal: any = { value: 0, unit: "" },
        ) => {
          try {
            const parts = path.split(".");
            let current = obj;
            for (const part of parts) {
              if (current[part] === undefined) return defaultVal;
              current = current[part];
            }
            return current;
          } catch {
            return defaultVal;
          }
        };

        // Try to extract the current API format values while providing fallbacks
        const temperature = getSafeValue(result, "temperature", {
          value: 0,
          unit: "C",
        });
        const humidity = { value: result.relativeHumidity || 0, unit: "%" };
        const windSpeed = getSafeValue(result, "wind.speed", {
          value: 0,
          unit: "km/h",
        });
        const precipitation = getSafeValue(result, "precipitation", {
          value: 0,
          unit: "mm",
        });
        const feelsLike = getSafeValue(result, "realFeelTemperature", {
          value: 0,
          unit: "C",
        });

        return {
          temperature: {
            value: temperature.value,
            unit: temperature.unit,
          },
          humidity: {
            value: humidity.value,
            unit: humidity.unit,
          },
          windSpeed: {
            value: windSpeed.value,
            unit: windSpeed.unit,
          },
          precipitation: {
            value: precipitation.value,
            unit: precipitation.unit,
          },
          condition: result.phrase || result.iconPhrase || "Unknown",
          feelsLike: {
            value: feelsLike.value,
            unit: feelsLike.unit,
          },
        };
      }

      return null;
    } catch (error) {
      console.error("Error getting weather:", error);
      throw new Error(
        `Failed to get weather: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Get elevation data for a series of points
   * Note: Azure Maps doesn't have a direct elevation profile API, so we get individual elevations
   * and combine them with route data
   */
  async getElevationProfile(
    points: Array<{ lat: number; lon: number }>,
  ): Promise<Array<{ distance: number; elevation: number }>> {
    try {
      if (!points || points.length < 2) {
        throw new Error(
          "At least 2 points are required for an elevation profile",
        );
      }

      // First get the route to calculate distances
      const routeResult = await this.getRoute(points);

      // Then get elevation for each point
      const elevations = await this.getElevations(points);

      if (!routeResult || !elevations || elevations.length === 0) {
        return [];
      }

      // Calculate cumulative distances along the route
      const distances = this.calculateDistances(routeResult, points.length);

      // Combine distances with elevations
      return elevations.map((elevation, index) => ({
        distance: distances[index] || 0,
        elevation: elevation,
      }));
    } catch (error) {
      console.error("Error getting elevation profile:", error);
      throw new Error(
        `Failed to get elevation profile: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Get elevations for multiple points
   */
  private async getElevations(
    points: Array<{ lat: number; lon: number }>,
  ): Promise<number[]> {
    try {
      // Format points for Azure Maps API
      const positionString = points
        .map((point) => `${point.lat},${point.lon}`)
        .join(",");

      const response = await axios.get(`${this.baseUrl}elevation/line/json`, {
        params: {
          "subscription-key": this.subscriptionKey,
          "api-version": "1.0",
          points: positionString,
        },
      });

      if (response.data && response.data.data) {
        return response.data.data;
      }

      return [];
    } catch (error) {
      console.error("Error getting elevations:", error);
      throw error;
    }
  }

  /**
   * Get a route between points
   */
  private async getRoute(
    points: Array<{ lat: number; lon: number }>,
  ): Promise<any> {
    try {
      // Format waypoints for Azure Maps API
      const waypointsArray = points.map((point) => ({
        lat: point.lat,
        lon: point.lon,
      }));

      const response = await axios.post(
        `${this.baseUrl}route/directions/json`,
        {
          supportingPoints: waypointsArray,
          routeType: "shortest",
          travelMode: "pedestrian",
        },
        {
          params: {
            "subscription-key": this.subscriptionKey,
            "api-version": "1.0",
          },
        },
      );

      if (
        response.data &&
        response.data.routes &&
        response.data.routes.length > 0
      ) {
        return response.data.routes[0];
      }

      return null;
    } catch (error) {
      console.error("Error getting route:", error);
      throw error;
    }
  }

  /**
   * Calculate cumulative distances between points based on the route
   */
  private calculateDistances(route: any, pointCount: number): number[] {
    if (!route || !route.legs) {
      // If no route data, estimate distances using linear spacing
      return Array.from({ length: pointCount }, (_, i) => i * 100);
    }

    try {
      // Use the route's leg and point data to get accurate distances
      const legDistances = route.legs.map(
        (leg: any) => leg.travelDistance || 0,
      );
      const totalDistance = legDistances.reduce(
        (a: number, b: number) => a + b,
        0,
      );

      // Distribute distances proportionally based on the route legs
      const result: number[] = [0]; // First point is at distance 0
      let currentDist = 0;

      for (let i = 1; i < pointCount; i++) {
        // For simplicity, distribute points evenly along the total distance
        // In a real implementation, you would match them to the actual route points
        currentDist = (i / (pointCount - 1)) * totalDistance;
        result.push(currentDist);
      }

      return result;
    } catch (error) {
      console.error("Error calculating distances:", error);
      // Fallback to a simple distance calculation
      return Array.from({ length: pointCount }, (_, i) => i * 100);
    }
  }
}
