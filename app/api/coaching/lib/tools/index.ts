/**
 * This file contains tool definitions used throughout the supervision API
 */

import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { azureSearchTool } from "./azureSearchTool";
import { AzureSearchGraphTool } from "./azureSearchGraphTool";

// Export tool instances
export { azureSearchTool };
export const azureSearchGraphTool = new AzureSearchGraphTool();

// Strength training tools
export const strengthAssessmentTool = tool(
  async (args) => {
    console.log(
      `StrengthTool: Assessing current strength level for ${args.athlete_type}`,
    );
    return `Based on your ${args.athlete_type} status, I estimate your strength level as intermediate with good potential for hypertrophy.`;
  },
  {
    name: "assess_strength",
    description: "Evaluate an athlete's current strength level",
    schema: z.object({
      athlete_type: z
        .string()
        .describe(
          "Type of athlete (e.g., 'beginner', 'intermediate', 'advanced')",
        ),
    }),
  },
);

export const strengthProgramTool = tool(
  async (args) => {
    console.log(
      `StrengthTool: Creating ${args.program_type} program for ${args.athlete_type}`,
    );
    return `Custom ${args.program_type} strength training program designed for ${args.athlete_type} athletes focusing on ${args.focus_areas.join(", ")}.`;
  },
  {
    name: "create_strength_program",
    description: "Create a strength training program",
    schema: z.object({
      athlete_type: z
        .string()
        .describe(
          "Type of athlete (e.g., 'beginner', 'intermediate', 'advanced')",
        ),
      program_type: z
        .string()
        .describe(
          "Type of program (e.g., 'hypertrophy', 'strength', 'power', 'endurance')",
        ),
      focus_areas: z
        .array(z.string())
        .describe(
          "Body areas to focus on (e.g., ['upper body', 'legs', 'core'])",
        ),
    }),
  },
);

export const strengthEquipmentTool = tool(
  async (args) => {
    console.log(
      `StrengthTool: Recommending equipment for ${args.equipment_type}`,
    );
    return `Here's a recommended setup for ${args.equipment_type} strength training: ${args.budget} budget recommendations for ${args.training_environment}.`;
  },
  {
    name: "recommend_strength_equipment",
    description: "Recommend strength training equipment",
    schema: z.object({
      equipment_type: z
        .string()
        .describe("Type of equipment (e.g., 'weights', 'bodyweight', 'bands')"),
      budget: z
        .string()
        .describe("Budget level (e.g., 'low', 'medium', 'high')"),
      training_environment: z
        .string()
        .describe(
          "Where training will occur (e.g., 'home', 'gym', 'outdoors')",
        ),
    }),
  },
);

// Cardio training tools
export const cardioAssessmentTool = tool(
  async (args) => {
    console.log(
      `CardioTool: Assessing cardiovascular fitness for ${args.athlete_type}`,
    );
    return `Based on your ${args.athlete_type} status, I estimate your cardio fitness level as moderate with good endurance capacity.`;
  },
  {
    name: "assess_cardio",
    description: "Evaluate an athlete's current cardiovascular fitness",
    schema: z.object({
      athlete_type: z
        .string()
        .describe(
          "Type of athlete (e.g., 'beginner', 'intermediate', 'advanced')",
        ),
    }),
  },
);

export const cardioProgramTool = tool(
  async (args) => {
    console.log(
      `CardioTool: Creating ${args.program_type} program for ${args.athlete_type}`,
    );
    return `Custom ${args.program_type} cardio training program designed for ${args.athlete_type} athletes with a focus on ${args.training_type}.`;
  },
  {
    name: "create_cardio_program",
    description: "Create a cardiovascular training program",
    schema: z.object({
      athlete_type: z
        .string()
        .describe(
          "Type of athlete (e.g., 'beginner', 'intermediate', 'advanced')",
        ),
      program_type: z
        .string()
        .describe(
          "Type of program (e.g., 'endurance', 'HIIT', 'interval', 'steady state')",
        ),
      training_type: z
        .string()
        .describe(
          "Preferred cardio type (e.g., 'running', 'cycling', 'swimming', 'mixed')",
        ),
    }),
  },
);

export const runningEquipmentTool = tool(
  async (args) => {
    console.log(`RunningTool: Recommending equipment for ${args.running_type}`);
    return `Here's a recommended running gear setup for ${args.running_type}: ${args.terrain} terrain recommendations for ${args.climate} climate.`;
  },
  {
    name: "recommend_running_equipment",
    description: "Recommend running gear and equipment",
    schema: z.object({
      running_type: z
        .string()
        .describe("Type of running (e.g., 'trail', 'road', 'track')"),
      terrain: z
        .string()
        .describe("Terrain type (e.g., 'flat', 'hilly', 'technical')"),
      climate: z
        .string()
        .describe("Climate conditions (e.g., 'hot', 'cold', 'rainy')"),
    }),
  },
);

// Cycling tools
export const cyclingAssessmentTool = tool(
  async (args) => {
    console.log(
      `CyclingTool: Assessing cycling fitness for ${args.athlete_type}`,
    );
    return `Based on your ${args.athlete_type} status, I estimate your cycling fitness level as moderate with good potential for improvement.`;
  },
  {
    name: "assess_cycling",
    description: "Evaluate an athlete's current cycling fitness",
    schema: z.object({
      athlete_type: z
        .string()
        .describe(
          "Type of athlete (e.g., 'beginner', 'intermediate', 'advanced')",
        ),
    }),
  },
);

export const cyclingProgramTool = tool(
  async (args) => {
    console.log(
      `CyclingTool: Creating ${args.program_type} program for ${args.athlete_type}`,
    );
    return `Custom ${args.program_type} cycling program designed for ${args.athlete_type} athletes with a focus on ${args.cycling_type}.`;
  },
  {
    name: "create_cycling_program",
    description: "Create a cycling training program",
    schema: z.object({
      athlete_type: z
        .string()
        .describe(
          "Type of athlete (e.g., 'beginner', 'intermediate', 'advanced')",
        ),
      program_type: z
        .string()
        .describe(
          "Type of program (e.g., 'endurance', 'HIIT', 'interval', 'steady state')",
        ),
      cycling_type: z
        .string()
        .describe(
          "Preferred cycling type (e.g., 'road', 'mountain', 'gravel', 'indoor')",
        ),
    }),
  },
);

export const cyclingEquipmentTool = tool(
  async (args) => {
    console.log(`CyclingTool: Recommending equipment for ${args.cycling_type}`);
    return `Here's a recommended cycling gear setup for ${args.cycling_type}: ${args.terrain} terrain recommendations for ${args.budget} budget.`;
  },
  {
    name: "recommend_cycling_equipment",
    description: "Recommend cycling gear and equipment",
    schema: z.object({
      cycling_type: z
        .string()
        .describe("Type of cycling (e.g., 'road', 'mountain', 'gravel')"),
      terrain: z
        .string()
        .describe("Terrain type (e.g., 'flat', 'hilly', 'technical')"),
      budget: z
        .string()
        .describe("Budget level (e.g., 'low', 'medium', 'high')"),
    }),
  },
);

// Nutrition tools
export const macroCalculatorTool = tool(
  async (args) => {
    console.log(`NutritionTool: Calculating macros for ${args.goal}`);
    const weight = args.weight_kg;
    let protein, carbs, fats;

    if (args.goal === "muscle_gain") {
      protein = Math.round(weight * 2.2); // 2.2g per kg
      carbs = Math.round(weight * 4.5); // 4.5g per kg
      fats = Math.round(weight * 1); // 1g per kg
    } else if (args.goal === "fat_loss") {
      protein = Math.round(weight * 2.5); // 2.5g per kg
      carbs = Math.round(weight * 2); // 2g per kg
      fats = Math.round(weight * 0.8); // 0.8g per kg
    } else {
      // maintenance
      protein = Math.round(weight * 1.8); // 1.8g per kg
      carbs = Math.round(weight * 3); // 3g per kg
      fats = Math.round(weight * 1); // 1g per kg
    }

    const totalCalories = protein * 4 + carbs * 4 + fats * 9;

    return `Based on your weight of ${weight}kg and goal of ${args.goal}, your recommended daily macros are:
- Protein: ${protein}g (${Math.round(protein * 4)} calories)
- Carbohydrates: ${carbs}g (${Math.round(carbs * 4)} calories)
- Fats: ${fats}g (${Math.round(fats * 9)} calories)
- Total Calories: ${totalCalories}`;
  },
  {
    name: "calculate_macros",
    description: "Calculate macronutrient needs based on body weight and goals",
    schema: z.object({
      weight_kg: z.number().describe("Athlete's weight in kilograms"),
      goal: z
        .string()
        .describe(
          "Nutrition goal (e.g., 'muscle_gain', 'fat_loss', 'maintenance')",
        ),
    }),
  },
);

export const mealPlanTool = tool(
  async (args) => {
    console.log(`NutritionTool: Creating meal plan for ${args.goal}`);
    return `Custom meal plan for ${args.goal} with ${args.meals_per_day} meals per day, focusing on ${args.dietary_preferences.join(", ")}.`;
  },
  {
    name: "create_meal_plan",
    description: "Create a customized meal plan",
    schema: z.object({
      goal: z
        .string()
        .describe(
          "Nutrition goal (e.g., 'muscle_gain', 'fat_loss', 'maintenance')",
        ),
      meals_per_day: z.number().describe("Number of meals per day"),
      dietary_preferences: z
        .array(z.string())
        .describe(
          "Dietary preferences (e.g., ['high-protein', 'vegetarian', 'low-carb'])",
        ),
    }),
  },
);

// Recovery tools
export const recoveryAssessmentTool = tool(
  async (args) => {
    console.log(
      `RecoveryTool: Assessing recovery needs for ${args.training_intensity}`,
    );
    return `Based on your ${args.training_intensity} training intensity and ${args.recent_injuries.length > 0 ? "history of injuries" : "lack of injuries"}, I recommend prioritizing ${args.recent_injuries.length > 0 ? "rehabilitation and careful progression" : "preventative mobility work"}.`;
  },
  {
    name: "assess_recovery_needs",
    description: "Evaluate an athlete's recovery requirements",
    schema: z.object({
      training_intensity: z
        .string()
        .describe(
          "Current training intensity (e.g., 'light', 'moderate', 'high')",
        ),
      recent_injuries: z
        .array(z.string())
        .describe("List of recent injuries (if any)"),
    }),
  },
);

export const recoveryPlanTool = tool(
  async (args) => {
    console.log(`RecoveryTool: Creating recovery plan for ${args.focus_area}`);
    return `Custom recovery program focusing on ${args.focus_area} using ${args.recovery_methods.join(", ")} methods.`;
  },
  {
    name: "create_recovery_plan",
    description: "Create a recovery plan",
    schema: z.object({
      focus_area: z
        .string()
        .describe(
          "Area to focus recovery on (e.g., 'general', 'injury-prevention', 'rehabilitation')",
        ),
      recovery_methods: z
        .array(z.string())
        .describe(
          "Recovery methods to incorporate (e.g., ['stretching', 'massage', 'foam rolling'])",
        ),
    }),
  },
);

// Mental training tools
export const mentalAssessmentTool = tool(
  async (args) => {
    console.log(
      `MentalTool: Assessing mental state for ${args.challenge_area}`,
    );
    return `Based on your challenges with ${args.challenge_area}, I recommend focusing on developing mental skills in ${args.desired_outcome} areas.`;
  },
  {
    name: "assess_mental_state",
    description: "Evaluate an athlete's mental training needs",
    schema: z.object({
      challenge_area: z
        .string()
        .describe(
          "Current mental challenge (e.g., 'pre-competition anxiety', 'focus', 'confidence')",
        ),
      desired_outcome: z
        .string()
        .describe(
          "Desired mental skill outcome (e.g., 'calm under pressure', 'improved focus')",
        ),
    }),
  },
);

export const mentalTrainingPlanTool = tool(
  async (args) => {
    console.log(
      `MentalTool: Creating mental training plan for ${args.focus_area}`,
    );
    return `Custom mental training program focusing on ${args.focus_area} using ${args.training_methods.join(", ")} techniques.`;
  },
  {
    name: "create_mental_training_plan",
    description: "Create a mental training plan",
    schema: z.object({
      focus_area: z
        .string()
        .describe(
          "Area to focus training on (e.g., 'pre-competition anxiety', 'focus', 'confidence')",
        ),
      training_methods: z
        .array(z.string())
        .describe(
          "Mental training methods (e.g., ['visualization', 'mindfulness', 'self-talk'])",
        ),
    }),
  },
);

// Specialized tools
export const elevationTool = tool(
  async (args) => {
    console.log(`Calculating elevation for ${args.route}`);
    // Mock elevation data
    const distance = args.distance_km;
    const elevationGain = Math.round(distance * 20); // 20m per km as example

    return {
      route: args.route,
      distance_km: distance,
      elevation_gain_m: elevationGain,
      profile: `This ${distance}km route has approximately ${elevationGain}m of elevation gain. The steepest section is around km ${Math.round(distance / 3)} with a 7% grade.`,
    };
  },
  {
    name: "calculate_elevation",
    description: "Calculate elevation data for a given route",
    schema: z.object({
      route: z.string().describe("Route name or description"),
      distance_km: z.number().describe("Distance of the route in kilometers"),
    }),
  },
);

// External APIs
export const azureMapsTool = tool(
  async (args) => {
    console.log(
      `Maps: Searching for route between ${args.start} and ${args.end}`,
    );
    return `Route from ${args.start} to ${args.end}, distance: ${Math.round(10 + Math.random() * 20)}km, estimated time: ${Math.round(20 + Math.random() * 60)} minutes.`;
  },
  {
    name: "find_route",
    description: "Find a route between two locations",
    schema: z.object({
      start: z.string().describe("Starting location"),
      end: z.string().describe("Ending location"),
      mode: z
        .string()
        .describe("Transportation mode (e.g., 'walking', 'cycling', 'driving')")
        .optional(),
    }),
  },
);

export const simpleSearchInformationTool = tool(
  async (args) => {
    console.log(`Search: Looking up information about ${args.query}`);
    return `Here's some information about "${args.query}": [Mock search results would appear here with relevant info].`;
  },
  {
    name: "search_information",
    description: "Look up information on a topic",
    schema: z.object({
      query: z.string().describe("Search query or topic to look up"),
    }),
  },
);
