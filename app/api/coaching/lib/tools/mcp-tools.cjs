"use strict";

const path = require("path");
const { spawn } = require("child_process");

// --- Determine Project Root and SDK paths ---
// When running test script directly, __dirname is app/api/coaching/tools, so 4 levels up.
// const projectRoot = path.resolve(__dirname, "..", "..", "..", ".."); // Old way, fragile
const projectRoot = process.cwd(); // Assumes CWD is project root, e.g., /Users/<USER>/athlea-web

const mcpServerRoot = path.join(projectRoot, "mcp-server");
const serverScriptPath = path.join(mcpServerRoot, "src", "server.js");

// --- SDK Imports ---
let Client, StdioClientTransport;
try {
  console.log(`[MCP Tools] Project Root resolved to: ${projectRoot}`);
  console.log(`[MCP Tools] MCP Server Root resolved to: ${mcpServerRoot}`);
  console.log(`[MCP Tools] MCP Server Script Path: ${serverScriptPath}`);

  console.log(
    "[MCP Tools] Attempting to load SDK components using static paths...",
  );
  // const mcpCloneSdkClientPath = path.resolve( // Old way
  //   __dirname,
  //   "../../../../mcp-clone/dist/cjs/client/index.js",
  // );
  // const mcpCloneSdkStdioPath = path.resolve( // Old way
  //   __dirname,
  //   "../../../../mcp-clone/dist/cjs/client/stdio.js",
  // );
  const mcpCloneRoot = path.join(projectRoot, "mcp-clone");
  const mcpCloneSdkClientPath = path.join(
    mcpCloneRoot,
    "dist",
    "cjs",
    "client",
    "index.js",
  );
  const mcpCloneSdkStdioPath = path.join(
    mcpCloneRoot,
    "dist",
    "cjs",
    "client",
    "stdio.js",
  );

  Client = eval("require")(mcpCloneSdkClientPath).Client;
  StdioClientTransport =
    eval("require")(mcpCloneSdkStdioPath).StdioClientTransport;
  console.log(
    "[MCP Tools] Successfully loaded MCP SDK components via static require.",
  );
} catch (e) {
  console.error(
    "[MCP Tools] Failed to load MCP SDK modules. Check paths and ensure the SDK is correctly built/installed.",
    e,
  );
  // Allow module to load, but getClient will fail later if these are undefined.
}

function createMcpTools() {
  let clientInstance;
  let toolsCache = null;
  let connecting = false;
  let connectionPromise = null;

  async function getClient() {
    if (clientInstance) return clientInstance;
    if (connecting) return connectionPromise;

    if (!Client || !StdioClientTransport) {
      console.error(
        "[MCP Tools] MCP SDK components not loaded. Cannot create client.",
      );
      console.log("[MCP Tools] Will use direct API calls instead.");
      return null;
    }

    connecting = true;
    connectionPromise = (async () => {
      try {
        console.log(
          "[MCP Tools] DEBUG: Initializing StdioClientTransport for MCP server...",
        );
        console.log(
          `[MCP Tools] DEBUG: Will spawn process: node ${serverScriptPath}`,
        );
        console.log(`[MCP Tools] DEBUG: Process CWD: ${projectRoot}`);

        // Create the transport with enhanced debugging
        const transport = new StdioClientTransport({
          command: "node",
          args: [serverScriptPath],
          options: {
            cwd: projectRoot,
            env: process.env, // Pass through all environment variables
            shell: true,
          },
          // Add callbacks to capture stdin/stdout for debugging
          onStdoutData: (data) => {
            console.log(`[MCP Server STDOUT] ${data.toString("utf8").trim()}`);
          },
          onStderrData: (data) => {
            console.error(
              `[MCP Server STDERR] ${data.toString("utf8").trim()}`,
            );
          },
        });

        // Create the client with enhanced debugging
        clientInstance = new Client({
          name: "AthleaCoachClient",
          version: "1.0.0",
        });

        // Add debugging for RPC communication
        const origRequest = clientInstance.request;
        clientInstance.request = async (method, params) => {
          console.log(
            `[MCP Client] DEBUG: Sending RPC request: ${method}`,
            params,
          );
          try {
            const result = await origRequest.call(
              clientInstance,
              method,
              params,
            );
            console.log(
              `[MCP Client] DEBUG: RPC response received for ${method}`,
            );
            return result;
          } catch (error) {
            console.error(
              `[MCP Client] DEBUG: RPC error for ${method}:`,
              error,
            );
            throw error;
          }
        };

        console.log(
          "[MCP Tools] DEBUG: Client created, connecting to transport...",
        );
        await clientInstance.connect(transport);
        console.log("[MCP Tools] DEBUG: Client connected successfully!");
        return clientInstance;
      } catch (error) {
        console.error("[MCP Tools] ERROR: Failed to create MCP client:", error);
        console.log("[MCP Tools] Will use direct API calls instead.");
        clientInstance = null;
        return null;
      } finally {
        connecting = false;
      }
    })();
    return connectionPromise;
  }

  async function getTools() {
    if (toolsCache) return toolsCache;

    // Define API endpoints for each workout type
    const apiEndpoints = {
      search_sc_sessions: "/api/query/sc/sessions",
      search_sc_segments: "/api/query/sc/segments",
      search_cycling_sessions: "/api/query/cycling/sessions",
      search_cycling_segments: "/api/query/cycling/segments",
      search_running_sessions: "/api/query/running/sessions",
      search_running_segments: "/api/query/running/segments",
    };

    const langGraphTools = {};
    const currentClient = await getClient();

    if (currentClient) {
      // If client is available, try to get tools from MCP server
      console.log("[MCP Tools] Listing available tools from MCP server...");
      try {
        const toolsResult = await currentClient.listTools();

        if (toolsResult && toolsResult.tools && toolsResult.tools.length > 0) {
          console.log(
            "[MCP Tools] Retrieved tools: ",
            toolsResult.tools.map((t) => t.name).join(", "),
          );

          // Process server-provided tools
          const serverToolNames = toolsResult.tools.map((t) => t.name);

          // Add server-provided tools to our toolset
          for (const tool of toolsResult.tools) {
            const toolName = tool.name;
            langGraphTools[toolName] = createToolFunction(
              currentClient,
              toolName,
            );
          }

          // Add our API-based tools for any missing workout types
          for (const [toolName, endpoint] of Object.entries(apiEndpoints)) {
            if (!serverToolNames.includes(toolName)) {
              console.log(
                `[MCP Tools] Adding custom API-based tool for ${toolName}`,
              );
              langGraphTools[toolName] = createApiBasedToolFunction(
                toolName,
                endpoint,
              );
            }
          }
        } else {
          console.warn("[MCP Tools] No tools returned from MCP server.");
          // Fall back to API-based tools
          for (const [toolName, endpoint] of Object.entries(apiEndpoints)) {
            langGraphTools[toolName] = createApiBasedToolFunction(
              toolName,
              endpoint,
            );
          }
        }
      } catch (error) {
        console.error(
          "[MCP Tools] Error listing tools from MCP server:",
          error,
        );
        // Fall back to API-based tools on error
        for (const [toolName, endpoint] of Object.entries(apiEndpoints)) {
          langGraphTools[toolName] = createApiBasedToolFunction(
            toolName,
            endpoint,
          );
        }
      }
    } else {
      // If client is not available, use API-based tools only
      console.log(
        "[MCP Tools] MCP client not available, using API-based tools only.",
      );
      for (const [toolName, endpoint] of Object.entries(apiEndpoints)) {
        langGraphTools[toolName] = createApiBasedToolFunction(
          toolName,
          endpoint,
        );
      }
    }

    // Ensure we have at least some tools
    if (Object.keys(langGraphTools).length === 0) {
      console.warn(
        "[MCP Tools] No tools found or defined. Adding API-based defaults...",
      );
      for (const [toolName, endpoint] of Object.entries(apiEndpoints)) {
        langGraphTools[toolName] = createApiBasedToolFunction(
          toolName,
          endpoint,
        );
      }
    }

    // Log all available tools
    console.log(
      "[MCP Tools] Final tool set:",
      Object.keys(langGraphTools).join(", "),
    );

    toolsCache = langGraphTools;
    return toolsCache;
  }

  // Helper function to create a tool function using the MCP client
  function createToolFunction(client, toolName) {
    return async (args) => {
      if (!client)
        throw new Error("MCP Client became unavailable before tool call.");
      console.log(`[MCP Tools] Calling MCP tool: ${toolName} with args:`, args);
      try {
        // Fix: We need to send a properly formatted RPC call with the tool name and arguments
        // The correct JSON-RPC format for tools/call is {"params":{"name":"tool_name", "arguments":{...}}}
        const result = await client.request("tools/call", {
          name: toolName,
          arguments: args || {},
        });
        console.log(`[MCP Tools] MCP tool ${toolName} result received.`);
        return result;
      } catch (error) {
        console.error(`[MCP Tools] Error calling MCP tool ${toolName}:`, error);
        throw error; // Re-throw to LangGraph
      }
    };
  }

  // Helper function to create a tool function that calls our API endpoints directly
  function createApiBasedToolFunction(toolName, apiEndpoint) {
    return async (args) => {
      console.log(
        `[MCP Tools] Calling API-based tool: ${toolName} with args:`,
        args,
      );
      try {
        // Prepare query params from args
        const queryParams = new URLSearchParams();
        if (typeof args === "string") {
          queryParams.append("query", args);
        } else if (args && typeof args === "object") {
          Object.entries(args).forEach(([key, value]) => {
            queryParams.append(key, value);
          });
        }

        // Build URL
        const baseUrl =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";
        const url = `${baseUrl}${apiEndpoint}?${queryParams.toString()}`;

        console.log(`[MCP Tools] Fetching from API: ${url}`);

        // Use node-fetch in CommonJS environment
        const http = require("http");
        const https = require("https");

        const result = await new Promise((resolve, reject) => {
          const httpModule = url.startsWith("https") ? https : http;
          const req = httpModule.get(url, (res) => {
            let data = "";
            res.on("data", (chunk) => {
              data += chunk;
            });
            res.on("end", () => {
              if (res.statusCode >= 200 && res.statusCode < 300) {
                try {
                  const parsedData = JSON.parse(data);
                  resolve(parsedData);
                } catch (e) {
                  reject(new Error(`Failed to parse response: ${e.message}`));
                }
              } else {
                reject(new Error(`HTTP error ${res.statusCode}: ${data}`));
              }
            });
          });

          req.on("error", (error) => {
            reject(error);
          });

          req.end();
        });

        console.log(`[MCP Tools] API-based tool ${toolName} result received.`);
        return result;
      } catch (error) {
        console.error(
          `[MCP Tools] Error calling API-based tool ${toolName}:`,
          error,
        );
        return { error: error.message };
      }
    };
  }

  async function cleanup() {
    console.log("[MCP Tools] Cleanup called.");
    connecting = false;
    connectionPromise = null;
    if (clientInstance) {
      try {
        console.log("[MCP Tools] Disconnecting MCP client...");
        if (
          clientInstance.transport &&
          clientInstance.transport.childProcess &&
          !clientInstance.transport.childProcess.killed
        ) {
          console.log(
            "[MCP Tools] Terminating transport child process before disconnect...",
          );
          clientInstance.transport.childProcess.kill();
        }
        if (
          clientInstance.state !== "closed" &&
          typeof clientInstance.close === "function"
        ) {
          await clientInstance.close();
        } else if (typeof clientInstance.disconnect === "function") {
          await clientInstance.disconnect();
        }
        console.log("[MCP Tools] MCP client disconnected/closed.");
      } catch (error) {
        console.error(
          "[MCP Tools] Error disconnecting/closing MCP client:",
          error,
        );
      }
      clientInstance = null;
    }
    toolsCache = null;
  }

  process.on("SIGINT", async () => {
    console.log("[MCP Tools] Received SIGINT, cleaning up...");
    await cleanup();
    process.exit(0);
  });
  process.on("SIGTERM", async () => {
    console.log("[MCP Tools] Received SIGTERM, cleaning up...");
    await cleanup();
    process.exit(0);
  });

  process.on("exit", (code) => {
    console.log(`[MCP Tools] Exiting with code ${code}, attempting cleanup...`);
    cleanup().catch((err) =>
      console.error("[MCP Tools] Error during exit cleanup:", err),
    );
  });

  return {
    getTools,
    cleanup,
    getClient,
  };
}

const mcpToolsInstance = createMcpTools();
module.exports = mcpToolsInstance;
