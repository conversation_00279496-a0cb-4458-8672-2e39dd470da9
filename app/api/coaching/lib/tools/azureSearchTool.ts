import { Tool, Tool<PERSON>arams } from "@langchain/core/tools";
import { z } from "zod";
import {
  SearchClient,
  AzureKeyCredential,
  VectorQuery,
} from "@azure/search-documents";
import fetch from "node-fetch";
import { generateEmbedding } from "../utils/embeddingUtil.js";
import { trace } from "@opentelemetry/api";

// --- Environment Variable Validation ---
const AZURE_SEARCH_SERVICE_NAME = process.env.AZURE_SEARCH_SERVICE_NAME;
const AZURE_SEARCH_API_KEY = process.env.AZURE_SEARCH_API_KEY;
const EMBEDDING_API_KEY = process.env.EMBEDDING_API_KEY;
const EMBEDDING_ENDPOINT = process.env.EMBEDDING_ENDPOINT;
const AZURE_EMBEDDING_DEPLOYMENT_ID_FOR_BODY =
  process.env.AZURE_EMBEDDING_DEPLOYMENT_ID || "text-embedding-3-large";
const DEFAULT_AZURE_SEARCH_INDEX = "docs-chunks-index-v2";

if (
  !AZURE_SEARCH_SERVICE_NAME ||
  !AZURE_SEARCH_API_KEY ||
  !EMBEDDING_API_KEY ||
  !EMBEDDING_ENDPOINT
) {
  console.warn(
    "Azure Search or Embedding environment variables are missing. AzureSearchTool will not function correctly.",
  );
}

// --- Helper Functions --- //
// Note: generateVector function has been removed and replaced with embeddingUtil.js

function cosineSimilarity(vec1: number[], vec2: number[]): number {
  if (!vec1 || !vec2 || vec1.length !== vec2.length) {
    return 0;
  }
  let dotProduct = 0;
  let magnitude1 = 0;
  let magnitude2 = 0;

  for (let i = 0; i < vec1.length; i++) {
    dotProduct += vec1[i] * vec2[i];
    magnitude1 += vec1[i] * vec1[i];
    magnitude2 += vec2[i] * vec2[i];
  }

  magnitude1 = Math.sqrt(magnitude1);
  magnitude2 = Math.sqrt(magnitude2);

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0;
  }

  return dotProduct / (magnitude1 * magnitude2);
}

// --- Define Internal Schema for Parsing ---
const internalAzureSearchToolSchema = z.object({
  query: z.string().describe("The search query string."),
  index_name: z
    .string()
    .optional()
    .describe(
      `Optional Azure Cognitive Search index name. Defaults to '${DEFAULT_AZURE_SEARCH_INDEX}'.`,
    ),
  top_k: z
    .number()
    .int()
    .positive()
    .optional()
    .default(5)
    .describe("The maximum number of results to return."),
  vector_search: z
    .boolean()
    .optional()
    .default(false)
    .describe(
      "Whether to perform a vector similarity search (requires embedding model). Defaults to false.",
    ),
});

// --- Define Document Structure from Search ---
interface SearchDoc {
  id?: string;
  content?: string;
  title?: string;
  source?: string;
  source_id?: string;
  url?: string;
  created_at?: string;
  chunk_id?: string;
  metadata?: string;
  content_vector?: number[];
  physiological_systems?: string[];
  functional_domains?: string[];
  subdomains?: string[];
  sports?: string[];
  graph_node_labels?: string[];
  graph_edge_types?: string[];
  "@search.score"?: number;
  "@search.highlights"?: Record<string, string[] | undefined>;
  [key: string]: any;
}

// --- AzureSearchTool Class ---
export class AzureSearchTool extends Tool {
  name = "azure_cognitive_search";
  description =
    "Search for information in the Azure Cognitive Search index. Use this for domain-specific information about fitness, nutrition, or sports-related topics. Accepts either a direct query string or a JSON format with advanced search parameters.";

  // Fix schema to match what the Tool base class expects, using transform pattern
  schema = z
    .object({
      input: z
        .string()
        .optional()
        .describe("The search query or JSON string with search parameters"),
    })
    .transform((obj) => obj.input);

  private searchClients: Map<string, SearchClient<SearchDoc>>;
  private defaultSearchClient: SearchClient<SearchDoc> | null = null;
  private isInitialized = false;

  constructor(params?: ToolParams) {
    super(params);
    this.searchClients = new Map();

    // --- TRACING START ---
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchTool.constructor", {
      attributes: {
        tool_name: "azure_cognitive_search",
        function_name: "constructor",
        params_provided: !!params,
      },
    });
    // --- TRACING END ---

    // --- DETAILED LOGGING --- START
    console.log(
      "[AzureSearchTool Constructor] Checking Environment Variables:",
    );
    console.log(
      `  AZURE_SEARCH_SERVICE_NAME: ${process.env.AZURE_SEARCH_SERVICE_NAME}`,
    );
    console.log(
      `  AZURE_SEARCH_API_KEY: ${process.env.AZURE_SEARCH_API_KEY ? "*** SET ***" : "!!! MISSING !!!"}`,
    ); // Don't log the key itself
    console.log(
      `  EMBEDDING_API_KEY: ${process.env.EMBEDDING_API_KEY ? "*** SET ***" : "!!! MISSING !!!"}`,
    ); // Don't log the key itself
    console.log(`  EMBEDDING_ENDPOINT: ${process.env.EMBEDDING_ENDPOINT}`);
    // --- DETAILED LOGGING --- END

    if (
      AZURE_SEARCH_SERVICE_NAME &&
      AZURE_SEARCH_API_KEY &&
      EMBEDDING_API_KEY &&
      EMBEDDING_ENDPOINT
    ) {
      try {
        // --- LOGGING SUCCESS ---
        console.log(
          "[AzureSearchTool Constructor] All required environment variables found. Initializing clients...",
        );
        this.defaultSearchClient = this._createSearchClient(
          DEFAULT_AZURE_SEARCH_INDEX,
        );
        this.searchClients.set(
          DEFAULT_AZURE_SEARCH_INDEX,
          this.defaultSearchClient,
        );
        this.isInitialized = true;
        console.log(
          `AzureSearchTool initialized successfully for default index: ${DEFAULT_AZURE_SEARCH_INDEX}`,
        );
        span.setAttribute("is_initialized", true);
        span.setAttribute("default_index", DEFAULT_AZURE_SEARCH_INDEX);
      } catch (error) {
        console.error("AzureSearchTool failed to initialize clients:", error);
        span.setAttribute("error", true);
        span.setAttribute("error_message", String(error));
      }
    } else {
      // --- LOGGING FAILURE ---
      console.error(
        "[AzureSearchTool Constructor] ERROR: One or more environment variables are missing. Tool cannot initialize.",
      );
      console.warn(
        "AzureSearchTool could not initialize due to missing environment variables.",
      );
      span.setAttribute("is_initialized", false);
      span.setAttribute("error", true);
      span.setAttribute("error_message", "Missing environment variables");
    }
    span.end();
  }

  private _createSearchClient(indexName: string): SearchClient<SearchDoc> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchTool._createSearchClient", {
      attributes: {
        tool_name: "azure_cognitive_search",
        function_name: "_createSearchClient",
        index_name: indexName,
      },
    });

    if (!AZURE_SEARCH_SERVICE_NAME || !AZURE_SEARCH_API_KEY) {
      const errorMsg =
        "Cannot create search client: Missing Azure Search environment variables.";
      span.setAttribute("error", true);
      span.setAttribute("error_message", errorMsg);
      span.end();
      throw new Error(errorMsg);
    }
    console.log(`Creating Azure Search client for index: ${indexName}`);

    try {
      const client = new SearchClient<SearchDoc>(
        `https://${AZURE_SEARCH_SERVICE_NAME}.search.windows.net/`,
        indexName,
        new AzureKeyCredential(AZURE_SEARCH_API_KEY),
      );
      span.setAttribute("success", true);
      span.end();
      return client;
    } catch (error) {
      span.setAttribute("error", true);
      span.setAttribute("error_message", String(error));
      span.end();
      throw error;
    }
  }

  private _getSearchClient(indexName?: string): SearchClient<SearchDoc> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchTool._getSearchClient", {
      attributes: {
        tool_name: "azure_cognitive_search",
        function_name: "_getSearchClient",
        requested_index: indexName || DEFAULT_AZURE_SEARCH_INDEX,
      },
    });

    const targetIndex = indexName || DEFAULT_AZURE_SEARCH_INDEX;
    try {
      if (this.searchClients.has(targetIndex)) {
        span.setAttribute("client_exists", true);
        span.end();
        return this.searchClients.get(targetIndex)!;
      }

      span.setAttribute("creating_new_client", true);
      const client = this._createSearchClient(targetIndex);
      this.searchClients.set(targetIndex, client);
      span.end();
      return client;
    } catch (error) {
      span.setAttribute("error", true);
      span.setAttribute("error_message", String(error));
      span.end();
      throw error;
    }
  }

  private _getContent(doc: SearchDoc): string {
    if (doc.content && typeof doc.content === "string") {
      return doc.content;
    }
    console.warn("No content field found in document:", doc.id);
    return "No content available";
  }

  private _getTitle(doc: SearchDoc): string {
    return doc.title || "Untitled Document";
  }

  async _call(inputString: string | undefined): Promise<string> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchTool._call", {
      attributes: {
        tool_name: "azure_cognitive_search",
        function_name: "_call",
        input_length: inputString?.length || 0,
        is_initialized: this.isInitialized,
      },
    });

    if (!this.isInitialized) {
      const errorMsg =
        "Error: Azure Search Tool is not properly initialized due to missing environment variables.";
      span.setAttribute("error", true);
      span.setAttribute("error_message", "Tool not initialized");
      span.end();
      return errorMsg;
    }

    if (!inputString) {
      const errorMsg = "Error: No search query provided.";
      span.setAttribute("error", true);
      span.setAttribute("error_message", "No search query");
      span.end();
      return errorMsg;
    }

    console.log(`[AzureSearchTool._call] Received input: ${inputString}`);

    let parsedInput: z.infer<typeof internalAzureSearchToolSchema>;

    try {
      // Parse input - either JSON or direct query string
      if (inputString.trim().startsWith("{")) {
        try {
          const jsonInput = JSON.parse(inputString);
          console.log(`[AzureSearchTool] Parsed JSON input:`, jsonInput);
          span.setAttribute("input_type", "json");

          if (jsonInput.query) {
            // Direct schema format
            parsedInput = internalAzureSearchToolSchema.parse(jsonInput);
          } else if (jsonInput.input && typeof jsonInput.input === "string") {
            try {
              // Try parsing input field as JSON
              const nestedInput = JSON.parse(jsonInput.input);
              parsedInput = internalAzureSearchToolSchema.parse(nestedInput);
            } catch (nestedError) {
              // If not valid JSON inside input, treat input field as direct query
              parsedInput = internalAzureSearchToolSchema.parse({
                query: jsonInput.input,
              });
            }
          } else {
            // Fallback - treat the entire JSON string as query
            parsedInput = internalAzureSearchToolSchema.parse({
              query: inputString,
            });
          }
        } catch (jsonError) {
          console.error("Failed to parse JSON input:", jsonError);
          // If JSON parsing fails, treat as direct query
          parsedInput = internalAzureSearchToolSchema.parse({
            query: inputString,
          });
          span.setAttribute("json_parse_error", true);
        }
      } else {
        // Direct simple string query with defaults
        parsedInput = internalAzureSearchToolSchema.parse({
          query: inputString,
        });
        span.setAttribute("input_type", "string");
      }

      span.setAttribute("query", parsedInput.query);
      span.setAttribute("vector_search", parsedInput.vector_search || false);
      span.setAttribute("top_k", parsedInput.top_k || 5);

      const {
        query,
        index_name = DEFAULT_AZURE_SEARCH_INDEX,
        top_k = 5,
        vector_search = false,
      } = parsedInput;

      try {
        console.log(
          `[AzureSearchTool] Executing search for query: "${query}" in index "${index_name}", top_k=${top_k}, vector_search=${vector_search}`,
        );

        const client = this._getSearchClient(index_name);
        let searchResults;

        if (vector_search) {
          searchResults = await this._performVectorSearch(
            client,
            query,
            top_k,
            vector_search,
          );
        } else {
          searchResults = await this._performKeywordSearch(
            client,
            query,
            top_k,
          );
        }

        if (!searchResults || searchResults.length === 0) {
          return `No results found for: "${query}"`;
        }

        console.log(
          `[AzureSearchTool] Search successful: ${searchResults.length} results found.`,
        );

        span.setAttribute("results_count", searchResults.length);
        span.end();
        return JSON.stringify(
          searchResults.map((doc) => ({
            content: this._getContent(doc),
            title: this._getTitle(doc),
            source: doc.source || "Unknown",
            url: doc.url || "N/A",
            score: doc["@search.score"],
            highlights: doc["@search.highlights"] || {},
          })),
        );
      } catch (error) {
        const errorMessage = `Error executing search: ${
          error instanceof Error ? error.message : String(error)
        }`;
        console.error(`[AzureSearchTool] ${errorMessage}`);
        span.setAttribute("error", true);
        span.setAttribute("error_message", errorMessage);
        span.end();
        return errorMessage;
      }
    } catch (error) {
      const errorMessage = `Error: Failed to parse input: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[AzureSearchTool] ${errorMessage}`);
      span.setAttribute("error", true);
      span.setAttribute("error_message", errorMessage);
      span.end();
      return errorMessage;
    }
  }

  private async _performKeywordSearch(
    client: SearchClient<SearchDoc>,
    query: string,
    topK: number,
  ): Promise<SearchDoc[]> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchTool._performKeywordSearch", {
      attributes: {
        tool_name: "azure_cognitive_search",
        function_name: "_performKeywordSearch",
        query: query,
        top_k: topK,
      },
    });

    console.log(
      `[AzureSearchTool] Performing keyword search for: "${query}", top_k=${topK}`,
    );

    const searchOptions = {
      top: topK,
      select: [
        "id",
        "content",
        "title",
        "source",
        "url",
        "created_at",
        "chunk_id",
        "physiological_systems",
        "functional_domains",
        "subdomains",
        "sports",
        "graph_node_labels",
        "graph_edge_types",
      ],
      // Using simple query type with string highlightFields
      queryType: "simple" as const,
      highlightFields: "content",
    };

    try {
      const searchResponse = await client.search(query, searchOptions);
      const results: SearchDoc[] = [];

      for await (const result of searchResponse.results) {
        results.push(result.document);
      }

      span.setAttribute("results_count", results.length);
      span.end();
      return results;
    } catch (error) {
      console.error("Error in keyword search:", error);
      span.setAttribute("error", true);
      span.setAttribute("error_message", String(error));
      span.end();
      throw error;
    }
  }

  private async _performVectorSearch(
    client: SearchClient<SearchDoc>,
    query: string,
    topK: number,
    hybridSearch: boolean = false,
  ): Promise<SearchDoc[]> {
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan("AzureSearchTool._performVectorSearch", {
      attributes: {
        tool_name: "azure_cognitive_search",
        function_name: "_performVectorSearch",
        query: query,
        top_k: topK,
        hybrid_search: hybridSearch,
      },
    });

    if (!EMBEDDING_API_KEY || !EMBEDDING_ENDPOINT) {
      throw new Error(
        "Cannot perform vector search: Missing embedding environment variables.",
      );
    }

    try {
      console.log(
        `[AzureSearchTool] Generating embedding for vector search: "${query.substring(
          0,
          50,
        )}..."`,
      );

      // Use the reusable embedding utility, which will extract the model name from the URL
      // We don't need to pass the AZURE_EMBEDDING_DEPLOYMENT_ID_FOR_BODY explicitly
      const queryVector = await generateEmbedding(
        query,
        EMBEDDING_API_KEY,
        EMBEDDING_ENDPOINT,
      );

      if (!queryVector) {
        throw new Error("Failed to generate embedding for query.");
      }

      console.log(
        `[AzureSearchTool] Performing vector search with embedding of length ${queryVector.length}`,
      );

      // Fix VectorQuery to use proper type
      const vectorQuery = {
        vector: queryVector,
        fields: ["content_vector"],
        k: topK,
      };

      const searchOptions = {
        top: topK,
        select: [
          "id",
          "content",
          "title",
          "source",
          "url",
          "created_at",
          "chunk_id",
          "physiological_systems",
          "functional_domains",
          "subdomains",
          "sports",
          "graph_node_labels",
          "graph_edge_types",
        ],
        vectors: [vectorQuery],
        filter: undefined,
      };

      if (hybridSearch) {
        console.log(
          `[AzureSearchTool] Using hybrid search (vector + text): "${query}"`,
        );
        const searchResponse = await client.search(query, searchOptions);
        const results: SearchDoc[] = [];

        for await (const result of searchResponse.results) {
          results.push(result.document);
        }

        span.setAttribute("results_count", results.length);
        span.end();
        return results;
      } else {
        console.log(
          `[AzureSearchTool] Using pure vector search: "${query.substring(
            0,
            50,
          )}..."`,
        );
        const searchResponse = await client.search("*", searchOptions);
        const results: SearchDoc[] = [];

        for await (const result of searchResponse.results) {
          results.push(result.document);
        }

        span.setAttribute("results_count", results.length);
        span.end();
        return results;
      }
    } catch (error) {
      console.error("Error in vector search:", error);
      span.setAttribute("error", true);
      span.setAttribute("error_message", String(error));
      span.end();
      throw error;
    }
  }
}

export const azureSearchTool = new AzureSearchTool();
