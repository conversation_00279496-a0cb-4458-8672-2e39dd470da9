import {
  CompiledStateGraph, // Import CompiledStateGraph
  StateGraph,
  START,
  END,
} from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { BaseCheckpointSaver } from "@langchain/langgraph";
import { AIMessage } from "@langchain/core/messages";
import type { MultiServerMCPClient } from "@langchain/mcp-adapters"; // For type only

// Assuming these types/functions are correctly exported from their respective files
import { GraphState, graphChannels } from "../graph/state"; // Adjust path if needed
import { getAgentNodeAndTools } from "../agents/get-agent-tools"; // Import from the correct implementation
import { reasoningNode } from "../agents/reasoning-node"; // Replace with the correct import path
import { planningNode } from "../agents/planning-node"; // Import planningNode from its new location

// Define the should_continue_or_finish function here since it's no longer exported from route.ts
const should_continue_or_finish = (
  state: GraphState,
): "call_tool" | "finish_sequence" => {
  const { messages } = state;
  const lastMessage = messages[messages.length - 1];

  // Check if the message is an AIMessage with tool_calls
  if (
    lastMessage &&
    lastMessage instanceof AIMessage &&
    lastMessage.tool_calls &&
    lastMessage.tool_calls.length > 0
  ) {
    console.log(
      "[Router decision] Agent requested tool call. Routing to 'call_tool'.",
    );
    return "call_tool";
  }

  // Otherwise, the agent finished its turn, route back to supervisor (via finish_sequence)
  console.log(
    "[Router decision] Agent finished its turn without tool call. Routing to 'finish_sequence' (back to head_coach).",
  );
  return "finish_sequence";
};

// --- Function to create a graph for a SINGLE coach ---
export async function createSingleCoachGraph(
  coachName: string,
  memoryCheckpointer: BaseCheckpointSaver, // Change type to BaseCheckpointSaver
): Promise<{
  graph: CompiledStateGraph<GraphState, Partial<GraphState>>;
  client: MultiServerMCPClient | null | undefined;
}> {
  // <-- Specify State and Update types
  console.log(`Creating SINGLE COACH graph for: ${coachName}`);

  // 1. Get the specific coach node, its tools, and the Airtable MCP client
  const agentData = await getAgentNodeAndTools(coachName);
  if (!agentData) {
    throw new Error(
      `Could not find agent node or tools for coach: ${coachName}`,
    );
  }
  // Destructure airtableMcpClient as well
  const { coachNode, tools: coachTools, airtableMcpClient } = agentData;

  // Log the available tools for debugging
  console.log(
    `[createSingleCoachGraph] Available tools for ${coachName}: ${coachTools.map((t: any) => t.name).join(", ")}`,
  );

  // 2. Create a ToolNode specifically for this coach's tools
  const singleToolExecutorNode = new ToolNode(coachTools);

  // 3. Initialize StateGraph using the same channels/state structure
  const graph = new StateGraph<GraphState>({ channels: graphChannels });

  // 4. Add Nodes
  graph.addNode("reasoningNode", reasoningNode as any); // <<< ADDED Reasoning Node
  graph.addNode("planningNode", planningNode as any); // Add planning node
  graph.addNode(coachName, coachNode as any); // The specific coach node
  graph.addNode("call_tool_single", singleToolExecutorNode as any); // Tool node for this coach

  // 5. Define Edges
  graph.addEdge(START, "reasoningNode" as any); // <<< CHANGED Start edge
  graph.addEdge("reasoningNode" as any, "planningNode" as any); // Add edge from reasoning to planning
  graph.addEdge("planningNode" as any, coachName as any); // Add edge from planning to coach

  // 6. Conditional Edges from the Coach Node
  graph.addConditionalEdges(
    coachName as any,
    // Use the same routing logic as in the multi-agent graph
    should_continue_or_finish, // Reuse existing function
    {
      call_tool: "call_tool_single", // Route to the coach-specific tool node
      finish_sequence: END, // End the graph after the coach finishes (no supervisor)
    } as any,
  );

  // 7. Edge from Tool Executor back to the SAME Coach Node
  graph.addEdge("call_tool_single" as any, coachName as any);

  // 8. Compile the graph with the checkpointer
  console.log(`Compiling SINGLE COACH graph for: ${coachName}`);
  const compiledGraph = graph.compile({ checkpointer: memoryCheckpointer });

  // Return both the compiled graph and the client instance
  return { graph: compiledGraph, client: airtableMcpClient };
}
