/**
 * Plan Generation Subgraph Implementation
 *
 * This subgraph coordinates collaborative plan generation between different domain coaches.
 * Each specialized coach contributes to their area of expertise for the training plan.
 */

import { StateGraph, START, END } from "@langchain/langgraph";
import {
  BaseMessage,
  AIMessage,
  SystemMessage,
  HumanMessage,
} from "@langchain/core/messages";
import { GraphState, graphChannels } from "../graph/state";
import { interrupt } from "@langchain/langgraph";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import { createAzureChatOpenAI } from "../services/azure-openai-service";
import { trace } from "@opentelemetry/api";

/**
 * PlanGenerationState extends GraphState with additional fields needed for plan generation
 */
export interface PlanGenerationState extends GraphState {
  userProfile: Record<string, any>; // User profile information
  domainContributions: Record<string, string>; // Stores plan contributions from each domain coach
  requiredDomains: string[]; // Domains needed for this specific plan
  completedDomains: string[]; // Domains that have already contributed
  aggregatedPlan: string | null; // The final combined plan
  proceedToGeneration: boolean; // Flag to indicate if we should generate the plan
}

/**
 * Plan generation specific channels for the StateGraph
 */
const planGenerationChannels = {
  // Inherit base channels, potentially overriding specific ones below
  ...graphChannels,

  // Override/Define specific channels for plan generation with careful type handling
  messages: graphChannels.messages, // Keep base message handling
  userProfile: graphChannels.userProfile, // Keep base profile handling
  routingDecision: graphChannels.routingDecision, // Keep base routing handling

  domainContributions: {
    value: (
      left: Record<string, string> | undefined,
      right: Record<string, string> | undefined,
    ): Record<string, string> => ({
      ...(left || {}), // Handle undefined left
      ...(right || {}), // Handle undefined right
    }),
    default: () => ({}),
  },
  requiredDomains: {
    value: (
      left: string[] | undefined,
      right: string[] | undefined,
    ): string[] => right ?? left ?? [], // Use right if defined, else left, else empty array
    default: () => [],
  },
  completedDomains: {
    value: (
      left: string[] | undefined,
      right: string[] | undefined,
    ): string[] => {
      const leftDomains = left || [];
      const rightDomains = right || [];
      // Ensure uniqueness
      return [...new Set([...leftDomains, ...rightDomains])];
    },
    default: () => [],
  },
  aggregatedPlan: {
    value: (
      left: string | null | undefined,
      right: string | null | undefined,
    ): string | null => right ?? left ?? null, // Keep latest
    default: () => null,
  },
  proceedToGeneration: {
    value: (left: boolean | undefined, right: boolean | undefined): boolean =>
      right ?? left ?? false, // Keep latest
    default: () => false,
  },
  // Explicitly include other channels from base if needed, or ensure they are handled by the spread
  // pendingAgents: graphChannels.pendingAgents,
  // plan: graphChannels.plan,
  // current_step: graphChannels.current_step,
  // currentPlan: graphChannels.currentPlan,
  // isOnboarding: graphChannels.isOnboarding,
};

/**
 * Creates the model for plan generation nodes
 */
function createPlanModel() {
  return createAzureChatOpenAI({
    temperature: 0.2,
  });
}

/**
 * Plan Initializer Node - REVISED to first ask for clarification, then determine domains.
 */
export const planInitializerNode = async (
  state: GraphState,
): Promise<Partial<GraphState>> => {
  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("planGenerationGraph.planInitializerNode", {
    attributes: {
      node_type: "plan_initializer_node",
      function_name: "planInitializerNode",
      input_state_length: JSON.stringify(state).length,
      user_profile_present: !!state.userProfile,
    },
  });
  // --- TRACING END ---

  console.log(
    "[Plan Initializer Revised] Entering node. Will request clarification.",
  );

  const userProfile = state.userProfile || {};
  const messages = state.messages || [];
  const latestUserMessage = messages
    .filter((m) => m instanceof HumanMessage)
    .pop();

  if (!latestUserMessage) {
    console.warn(
      "[Plan Initializer Revised] No user message found. Cannot proceed.",
    );
    const result = {
      requiredDomains: [],
      completedDomains: [],
      domainContributions: {},
      aggregatedPlan: null,
    };
    span.setAttribute("error", true);
    span.setAttribute("error_message", "No user message found");
    span.setAttribute("output_required_domains_count", 0);
    span.end();
    return result;
  }

  const model = createPlanModel(); // Model for both clarification and domain selection

  // === Step 1: Generate Clarification Question ===
  console.log(
    "[Plan Initializer Revised] Generating clarification question...",
  );
  let clarificationQuestion =
    "To create the best plan for you, could you tell me more about your specific goals, how much time you can commit per week, what equipment you have access to, and your current fitness level?"; // Default question

  try {
    const clarificationPrompt = new SystemMessage(
      `You are the Plan Initializer for Athlea. The user wants a new training plan based on their request below. Your current task is ONLY to ask clarifying questions to gather the necessary details to create a *personalized* and *effective* plan.

User Profile (for context):
${JSON.stringify(userProfile, null, 2)}

User's Initial Request:
"${latestUserMessage.content?.toString() || "No content provided"}"

Based on the request and profile, formulate ONE concise question to the user asking for the key missing information needed to design their plan. Focus on aspects like:
- Specific goals (e.g., run a 10k under 50 mins, gain 5kg muscle, general fitness)
- Time commitment (days/week, duration/session)
- Available equipment (e.g., full gym, dumbbells only, bodyweight only)
- Current fitness level/experience
- Any injuries or limitations

Ask only ONE question. Do not greet the user or add conversational filler. Output only the question text.`,
    );
    const clar_result = await model.invoke([clarificationPrompt]);
    if (
      clar_result.content &&
      typeof clar_result.content === "string" &&
      clar_result.content.trim().length > 0
    ) {
      clarificationQuestion = clar_result.content.trim();
    }
    console.log(
      "[Plan Initializer Revised] Generated Question:",
      clarificationQuestion,
    );
  } catch (error) {
    console.error(
      "[Plan Initializer Revised] Error generating clarification question:",
      error,
    );
    // Use the default question in case of error
  }

  // === Step 2: Interrupt and Get User Clarification ===
  let userClarification: string;
  let updatedMessages: BaseMessage[] = messages; // Initialize with original messages

  console.log("[Plan Initializer Revised] Interrupting for human input...");
  const resumedInput = await interrupt({ question: clarificationQuestion });
  userClarification =
    typeof resumedInput === "string"
      ? resumedInput
      : JSON.stringify(resumedInput);
  console.log(
    "[Plan Initializer Revised] Resumed with clarification:",
    userClarification,
  );

  // Add clarification Q&A to state messages for subsequent nodes
  updatedMessages = [
    ...messages,
    new AIMessage({ name: "planInitializer", content: clarificationQuestion }), // The question asked
    new HumanMessage({ content: userClarification }), // The user's answer
  ];

  // === Step 3: Determine Required Domains using Clarification ===
  console.log(
    "[Plan Initializer Revised] Determining required domains using clarified input...",
  );
  let requiredDomains: string[] = [];
  const domainParser = new JsonOutputParser<{ required_domains: string[] }>();

  try {
    const domainPrompt = new SystemMessage(
      `You are the Plan Initializer for Athlea. Your goal is to identify specialist coach domains needed based on the user's INITIAL request AND their subsequent CLARIFICATION, using their profile for context.

Available Domains:
- strength_coach: For strength, resistance, weightlifting plans.
- cardio_coach: For general cardiovascular fitness plans (e.g., HIIT, general endurance).
- running_coach: For specific running plans (e.g., 5k, marathon, improving pace).
- cycling_coach: For specific cycling plans (e.g., road race, climbing, general cycling fitness).
- nutrition_coach: For nutritional guidance *directly supporting* the plan.
- recovery_coach: For rest/recovery protocols *specifically related* to the plan.
- mental_coach: For mental training/preparation *related* to the plan.

User Profile (for context):
${JSON.stringify(userProfile, null, 2)}

User's Initial Request:
"${latestUserMessage.content?.toString() || "No content provided"}"

User's Clarification Provided:
"${userClarification}"

Instructions:
1.  **Analyze Combined Input:** Consider the initial request AND the user's clarification together.
2.  **Identify Core Domains:** Select the *minimum necessary* specialist domains from the list that directly address the user's needs for the plan based on the combined information.
3.  **Be Specific:** If the request is for "running", use "running_coach". If "weightlifting", use "strength_coach".
4.  **Format Output:** Respond ONLY with a valid JSON object: {"required_domains": ["domain1", "domain2", ...]}. If no specific plan domains are identifiable even after clarification, return {"required_domains": []}.

Output JSON:`,
    );

    const chain = model.pipe(domainParser);
    const result = await chain.invoke([domainPrompt]);
    requiredDomains = result.required_domains || [];
    console.log("[Plan Initializer Revised] Parsed domains:", requiredDomains);

    // Optional validation
    const allowedDomains = [
      "strength_coach",
      "cardio_coach",
      "running_coach",
      "cycling_coach",
      "nutrition_coach",
      "recovery_coach",
      "mental_coach",
    ];
    requiredDomains = requiredDomains.filter((domain) =>
      allowedDomains.includes(domain),
    );
    console.log(
      "[Plan Initializer Revised] Validated domains:",
      requiredDomains,
    );
  } catch (error) {
    console.error(
      "[Plan Initializer Revised] Error determining domains after clarification:",
      error,
    );
    requiredDomains = []; // Default to empty on error
  }

  // Ensure it's always an array
  if (!Array.isArray(requiredDomains)) {
    requiredDomains = [];
  }

  console.log(
    `[Plan Initializer Refined] Final required domains: ${requiredDomains.join(", ") || "None"}`,
  );

  // --- Return updated state ---
  const outputDomains = requiredDomains;

  // Before the function returns:
  span.setAttribute("output_required_domains_count", outputDomains.length);
  span.setAttribute("output_question_asked", true);
  span.setAttribute("output_clarification_received", !!userClarification);
  span.end();

  return {
    messages: updatedMessages, // Return messages WITH the clarification
    requiredDomains,
    completedDomains: [], // Reset
    domainContributions: {}, // Reset
    aggregatedPlan: null, // Reset
  };
};

/**
 * Domain-specific plan generation nodes - Each generates plan content for their domain
 */
const createDomainPlanNode = (domain: string) => {
  return async (state: GraphState): Promise<Partial<GraphState>> => {
    // --- TRACING START ---
    const tracer = trace.getTracer("athlea-ai-coach");
    const span = tracer.startSpan(`planGenerationGraph.${domain}PlanNode`, {
      attributes: {
        node_type: "domain_plan_node",
        function_name: `${domain}PlanNode`,
        domain_name: domain,
        input_state_length: JSON.stringify(state).length,
        user_profile_present: !!state.userProfile,
        existing_contributions_count: Object.keys(
          state.domainContributions || {},
        ).length,
      },
    });
    // --- TRACING END ---

    console.log(`[${domain} Plan Node] Generating domain-specific plan`);

    // Skip if this domain is not required for this plan
    if (!state.requiredDomains?.includes(domain)) {
      console.log(
        `[${domain} Plan Node] Domain not required for this plan, skipping`,
      );
      return {};
    }

    // Skip if this domain has already contributed
    if (state.completedDomains?.includes(domain)) {
      console.log(`[${domain} Plan Node] Domain already contributed, skipping`);
      return {};
    }

    const userProfile = state.userProfile;
    const model = createPlanModel();

    // Format the prompt based on the specific domain
    let domainExpertise = "";
    let focusAreas = "";

    switch (domain) {
      case "strength_coach":
        domainExpertise = "strength and resistance training";
        focusAreas =
          "muscle building, strength development, and resistance training protocols";
        break;
      case "cardio_coach":
        domainExpertise = "cardiovascular fitness";
        focusAreas =
          "heart rate zones, endurance development, and cardiovascular health";
        break;
      case "running_coach":
        domainExpertise = "running training";
        focusAreas =
          "running technique, programming, and progression for various distances";
        break;
      case "cycling_coach":
        domainExpertise = "cycling training";
        focusAreas =
          "cycling technique, programming, and training for various terrains and distances";
        break;
      case "nutrition_coach":
        domainExpertise = "sports nutrition";
        focusAreas =
          "macronutrient planning, meal timing, and dietary recommendations";
        break;
      case "recovery_coach":
        domainExpertise = "recovery protocols";
        focusAreas =
          "rest periods, mobility work, recovery techniques, and injury prevention";
        break;
      case "mental_coach":
        domainExpertise = "mental performance";
        focusAreas =
          "focus training, mental preparation, goal setting, and performance psychology";
        break;
      default:
        domainExpertise = "athletic training";
        focusAreas = "physical performance and athletic development";
    }

    const prompt = new SystemMessage(
      `You are the ${domain.replace("_", " ")} at Athlea, specializing in ${domainExpertise}. 
      Your job is to generate ONLY the ${domainExpertise} portion of a comprehensive training plan for a user.
      
      Focus specifically on ${focusAreas} based on the user's profile, goals, and preferences.
      
      Your response should ONLY cover your domain of expertise (${domainExpertise}) and should be formatted 
      as a professional training plan section that can be integrated with other domains.
      
      DO NOT include introductions, greetings, or explanations - provide ONLY the actual plan content.
      DO NOT try to create a complete plan - focus ONLY on your domain of expertise.
      
      Format your plan section with clear headers, bullet points, and organized structure.`,
    );

    const humanMessage = new HumanMessage(
      `Here is the user profile to create the ${domainExpertise} plan for:\n${JSON.stringify(userProfile, null, 2)}`,
    );

    // Invoke the model and create an AIMessage with the domain as the name
    const response = await model.invoke([prompt, humanMessage]);

    // Create a new AIMessage with the domain name that matches coaching graph convention
    const aiMessage = new AIMessage({
      content: response.content,
      name: domain, // Use the domain name directly (e.g. "strength_coach") for consistency
    });

    // Add the message to the messages array for proper UI rendering
    const updatedMessages = [...(state.messages || []), aiMessage];

    // Store this domain's contribution
    const domainPlan = aiMessage.content as string;
    const updatedDomainContributions = { ...(state.domainContributions || {}) };
    updatedDomainContributions[domain] = domainPlan;

    // Add this domain to completed domains
    const updatedCompletedDomains = [...(state.completedDomains || []), domain];

    console.log(`[${domain} Plan Node] Plan generation complete`);

    // Before returning:
    const result = {
      messages: updatedMessages, // Add the message to the state for rendering
      domainContributions: updatedDomainContributions,
      completedDomains: updatedCompletedDomains,
    };
    span.setAttribute("output_length", JSON.stringify(result).length);
    span.setAttribute(
      "output_has_contribution",
      !!result.domainContributions?.[domain],
    );
    span.end();
    return result;
  };
};

/**
 * Plan Aggregator Node - Combines all domain contributions into a cohesive plan
 */
const planAggregatorNode = async (
  state: GraphState,
): Promise<Partial<GraphState>> => {
  // --- TRACING START ---
  const tracer = trace.getTracer("athlea-ai-coach");
  const span = tracer.startSpan("planGenerationGraph.planAggregatorNode", {
    attributes: {
      node_type: "plan_aggregator_node",
      function_name: "planAggregatorNode",
      input_state_length: JSON.stringify(state).length,
      contributions_count: Object.keys(state.domainContributions || {}).length,
      required_domains_count: (state.requiredDomains || []).length,
      completed_domains_count: (state.completedDomains || []).length,
    },
  });
  // --- TRACING END ---

  console.log(
    "[Plan Aggregator] Aggregating plan contributions from all domains",
  );

  const domainContributions = state.domainContributions || {};
  const userProfile = state.userProfile;
  const model = createPlanModel();
  const requiredDomains = state.requiredDomains || []; // Ensure requiredDomains is an array
  const completedDomains = state.completedDomains || []; // Ensure completedDomains is an array

  // Check if all required domains have contributed
  const allDomainsCompleted =
    requiredDomains.length > 0 && // Only check if requiredDomains is populated
    requiredDomains.every((domain) => completedDomains.includes(domain));

  if (!allDomainsCompleted) {
    console.log(
      "[Plan Aggregator] Not all required domains have contributed yet, waiting",
    );
    // Don't return prematurely, allow state to potentially update further
    // We will only set aggregatedPlan if all domains are complete
  }

  // Only attempt aggregation if all domains are complete
  let aggregatedPlan: string | null = null;
  let finalRoutingDecision: string | null = null; // Initialize routing decision
  let updatedMessages = [...(state.messages || [])];

  if (allDomainsCompleted) {
    console.log(
      "[Plan Aggregator] All required domains complete, proceeding with aggregation.",
    );
    // Format the domain contributions for the aggregator prompt
    let contributionsText = "";
    for (const [domain, contribution] of Object.entries(domainContributions)) {
      contributionsText += `\n\n--- ${domain.replace("_", " ")} contribution ---\n${contribution}`;
    }

    const prompt = new SystemMessage(
      `You are the Plan Aggregator for Athlea's collaborative training plan system. Your job is to combine the separate contributions 
        from different domain coaches into one cohesive, organized, and professional training plan.
        
        The plan should be well-structured, with clear sections, progression, and should maintain internal consistency. Eliminate redundancy, 
        resolve any contradictions between domains, and ensure the final plan is balanced and tailored to the user's specific goals and constraints.
        
        Format the final plan beautifully with clear headers, subheaders, and organized sections. The plan should be ready to present to the user.`,
    );

    const humanMessage = new HumanMessage(
      `Here is the user profile:\n${JSON.stringify(userProfile, null, 2)}\n\nHere are the domain contributions to combine:${contributionsText}`,
    );

    const response = await model.invoke([prompt, humanMessage]);

    // Create a message from the head coach to present the final plan
    const headCoachMessage = new AIMessage({
      content: response.content as string,
      name: "head_coach", // Use head_coach to match the main graph convention
    });

    // Add the message to state for rendering
    updatedMessages.push(headCoachMessage);

    aggregatedPlan = response.content as string;
    finalRoutingDecision = "from_plan_generation"; // Set routing decision only on success
    console.log("[Plan Aggregator] Plan aggregation complete");
  } else {
    console.log(
      "[Plan Aggregator] Condition not met, skipping aggregation logic for now.",
    );
  }

  // Before returning:
  const result = {
    // Return the current state, including potentially null aggregatedPlan
    aggregatedPlan: aggregatedPlan,
    messages: updatedMessages,
    routingDecision: finalRoutingDecision, // Return null if aggregation didn't happen
    // Ensure other state fields are passed through if needed, though LangGraph should handle this
    // Example: requiredDomains: requiredDomains, completedDomains: completedDomains, etc.
  };
  span.setAttribute("output_length", JSON.stringify(result).length);
  span.setAttribute(
    "output_aggregated_plan_length",
    result.aggregatedPlan?.length || 0,
  );
  span.setAttribute(
    "output_routing_decision",
    result.routingDecision || "none",
  );
  span.end();
  return result;
};

/**
 * Creates the plan generation graph - a subgraph for handling collaborative plan generation
 */
export function createPlanGenerationGraph() {
  const graph = new StateGraph<GraphState>({
    channels: planGenerationChannels,
  });

  // Add the nodes
  graph.addNode("planInitializer", planInitializerNode);

  // Add domain-specific plan generation nodes
  graph.addNode("strengthPlanNode", createDomainPlanNode("strength_coach"));
  graph.addNode("cardioPlanNode", createDomainPlanNode("cardio_coach"));
  graph.addNode("runningPlanNode", createDomainPlanNode("running_coach"));
  graph.addNode("cyclingPlanNode", createDomainPlanNode("cycling_coach"));
  graph.addNode("nutritionPlanNode", createDomainPlanNode("nutrition_coach"));
  graph.addNode("recoveryPlanNode", createDomainPlanNode("recovery_coach"));
  graph.addNode("mentalPlanNode", createDomainPlanNode("mental_coach"));

  // Add the aggregator node
  graph.addNode("aggregator", planAggregatorNode);

  // Create a domain router that selects the next domain to process
  const domainRouter = (state: GraphState): string | typeof END => {
    const { requiredDomains = [], completedDomains = [] } = state;

    console.log(
      `[Domain Router] Required: ${JSON.stringify(requiredDomains)}, Completed: ${JSON.stringify(completedDomains)}`,
    );

    // Find domains that still need to be processed
    const remainingDomains = requiredDomains.filter(
      (domain) => !completedDomains.includes(domain),
    );

    // Map domain names to node names
    const domainToNodeMap: Record<string, string> = {
      strength_coach: "strengthPlanNode",
      cardio_coach: "cardioPlanNode",
      running_coach: "runningPlanNode",
      cycling_coach: "cyclingPlanNode",
      nutrition_coach: "nutritionPlanNode",
      recovery_coach: "recoveryPlanNode",
      mental_coach: "mentalPlanNode",
    };

    // If there are domains left to process, route to the next one
    if (remainingDomains.length > 0) {
      const nextDomain = remainingDomains[0]; // Take the first remaining domain
      const nextNode = domainToNodeMap[nextDomain];
      console.log(
        `[Domain Router] Routing to next domain: ${nextDomain} => ${nextNode}`,
      );
      return nextNode || "aggregator";
    }

    // If all domains are completed, go to aggregator
    console.log(
      `[Domain Router] All domains processed. Routing to aggregator.`,
    );
    return "aggregator";
  };

  // Start with the initializer
  graph.addEdge(START, "planInitializer" as any);

  // After initialization, route to the appropriate domain node or aggregator
  graph.addConditionalEdges("planInitializer" as any, domainRouter);

  // After each domain node completes, go back to the router to decide the next step
  // Each domain node will add itself to completedDomains when done
  const domainNodes = [
    "strengthPlanNode",
    "cardioPlanNode",
    "runningPlanNode",
    "cyclingPlanNode",
    "nutritionPlanNode",
    "recoveryPlanNode",
    "mentalPlanNode",
  ];

  for (const nodeName of domainNodes) {
    graph.addConditionalEdges(nodeName as any, domainRouter);
  }

  // End with the aggregator
  graph.addEdge("aggregator" as any, END);

  return graph.compile();
}
