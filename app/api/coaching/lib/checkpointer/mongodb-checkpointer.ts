/**
 * MongoDB Checkpointer for LangGraph State
 */
import {
  BaseCheckpointSaver,
  Checkpoint,
  CheckpointMetadata,
  CheckpointTuple,
} from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import { Collection, Db, MongoClient } from "mongodb";
import SuperJSON from "superjson";
import clientPromise from "@/app/lib/mongodb"; // Assuming lib/mongodb is correct

// Define the structure of the MongoDB document
export interface MongoCheckpointDoc {
  _id: string; // checkpointId (or fallback)
  threadId: string;
  userId?: string; // <-- Add userId (optional for existing docs)
  timestamp: string; // ISO string format from checkpoint.ts
  // Store SuperJSON serialized data as strings
  checkpoint: string;
  parentConfig?: string;
}

// Define the interface for list options based on BaseCheckpointSaver
export interface CheckpointListingOptions {
  limit?: number;
  before?: RunnableConfig; // Use RunnableConfig as hinted in user attempts
}

// Custom MongoDB Checkpointer Class
export class MongoDbCheckpointer extends BaseCheckpointSaver {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private collection: Collection<MongoCheckpointDoc> | null = null;
  private isSetup: Promise<void>;

  constructor() {
    super();
    // No need to bind methods if using arrow functions or if 'this' is correctly handled
    this.isSetup = this._setup();
  }

  private async _setup(): Promise<void> {
    if (this.collection) {
      return;
    }
    try {
      this.client = await clientPromise;
      this.db = this.client.db("athlea"); // Use your database name (e.g., from env vars)
      this.collection = this.db.collection<MongoCheckpointDoc>(
        "langGraphCheckpoints",
      );
      console.log("MongoDB Checkpointer: Connected and collection selected.");
      // Ensure index exists - Indexing on userId, threadId and timestamp
      await this.collection.createIndex(
        { userId: 1, threadId: 1, timestamp: -1 }, // <-- Update index
        { background: true },
      );
      console.log(
        "MongoDB Checkpointer: Index ensured on userId, threadId and timestamp.", // <-- Update log
      );
    } catch (error) {
      console.error(
        "MongoDB Checkpointer: Error setting up MongoDB connection:",
        error,
      );
      throw new Error("Failed to setup MongoDB checkpointer");
    }
  }

  // Implement get (fetches only the Checkpoint)
  async get(config: RunnableConfig): Promise<Checkpoint | undefined> {
    await this.isSetup;
    if (!this.collection) {
      throw new Error("MongoDB collection not initialized for get");
    }
    const threadId = config.configurable?.thread_id;
    const checkpointId = config.configurable?.checkpoint_id;

    let docToFetch: MongoCheckpointDoc | null = null;

    if (checkpointId) {
      // If checkpointId is provided, fetch that specific checkpoint
      console.log(
        `MongoDbCheckpointer [get]: Getting specific checkpoint ID: ${checkpointId} for threadId: ${threadId}`,
      );
      docToFetch = await this.collection.findOne({
        _id: checkpointId,
        threadId: threadId,
      });
      if (!docToFetch) {
        console.log(
          `MongoDbCheckpointer [get]: Specific checkpoint ID ${checkpointId} not found for thread ${threadId}.`,
        );
        // Optional: Fallback to latest if specific not found?
        // For now, return undefined if specific ID not found.
        return undefined;
      }
    } else if (threadId) {
      // If only threadId is provided, fetch the latest checkpoint for that thread
      console.log(
        `MongoDbCheckpointer [get]: Getting latest checkpoint for threadId: ${threadId}`,
      );
      docToFetch = await this.collection.findOne(
        { threadId },
        { sort: { timestamp: -1 } },
      );
      if (!docToFetch) {
        console.log(
          `MongoDbCheckpointer [get]: No checkpoint found for threadId: ${threadId}.`,
        );
        return undefined;
      }
    } else {
      console.warn(
        "MongoDbCheckpointer [get]: Called without thread_id or checkpoint_id.",
      );
      return undefined;
    }

    console.log(
      `MongoDbCheckpointer [get]: Found checkpoint doc with ID: ${docToFetch._id}`,
    );

    try {
      // Deserialize checkpoint using SuperJSON.parse
      const checkpoint: Checkpoint = SuperJSON.parse(docToFetch.checkpoint);
      return checkpoint;
    } catch (e) {
      console.error(
        `MongoDbCheckpointer [get]: Error SuperJSON parsing checkpoint ${docToFetch._id} for thread ${threadId}:`,
        e,
      );
      return undefined;
    }
  }

  // Implement list
  async *list(
    config: RunnableConfig,
    options?: CheckpointListingOptions,
  ): AsyncGenerator<CheckpointTuple> {
    await this.isSetup;
    if (!this.collection) {
      throw new Error("MongoDB collection not initialized");
    }
    const threadId = config.configurable?.thread_id;
    if (!threadId) {
      console.warn(
        "MongoDbCheckpointer: list called without thread_id in config.",
      );
      return; // No thread_id, no checkpoints to list
    }

    console.log(
      `MongoDbCheckpointer: Listing checkpoints for threadId: ${threadId}, options: ${JSON.stringify(options)}`,
    );

    const query: any = { threadId };
    const sortOptions: any = { timestamp: -1 }; // Default sort: newest first

    // Handle 'before' filter using checkpoint ID from the config
    if (options?.before?.configurable?.checkpoint_id) {
      const beforeCheckpointId = options.before.configurable.checkpoint_id;
      console.log(
        `MongoDbCheckpointer: Filtering list before checkpoint ID: ${beforeCheckpointId}`,
      );
      // Find the document corresponding to the 'before' checkpoint ID to get its timestamp
      const beforeDoc = await this.collection.findOne({
        _id: beforeCheckpointId,
        threadId: threadId,
      });
      if (beforeDoc) {
        query.timestamp = { $lt: beforeDoc.timestamp }; // Filter for documents older than the 'before' doc
        console.log(
          `MongoDbCheckpointer: Filtering timestamp < ${beforeDoc.timestamp}`,
        );
      } else {
        console.warn(
          `MongoDbCheckpointer: 'before' checkpoint ID ${beforeCheckpointId} not found for thread ${threadId}. Ignoring 'before' filter.`,
        );
      }
    }

    const cursor = this.collection.find(query).sort(sortOptions);

    if (options?.limit) {
      cursor.limit(options.limit);
      console.log(`MongoDbCheckpointer: Applying limit: ${options.limit}`);
    }

    for await (const doc of cursor) {
      try {
        // Deserialize using SuperJSON.parse
        const checkpoint: Checkpoint = SuperJSON.parse(doc.checkpoint);

        const parentConfigFlat: RunnableConfig | undefined = doc.parentConfig
          ? SuperJSON.parse(doc.parentConfig)
          : undefined;

        // Assign deserialized objects to the tuple
        const tuple: CheckpointTuple = {
          config: {
            configurable: {
              thread_id: doc.threadId,
              checkpoint_id: doc._id,
              ...(parentConfigFlat?.configurable?.checkpoint_id && {
                parent_checkpoint_id:
                  parentConfigFlat.configurable.checkpoint_id,
              }),
            },
          },
          checkpoint: checkpoint, // Use the deserialized object
          parentConfig: parentConfigFlat, // Use the deserialized object
        };
        yield tuple;
      } catch (e) {
        console.error(
          `MongoDbCheckpointer: Error SuperJSON parsing or yielding checkpoint ${doc._id} for thread ${doc.threadId}`,
          e,
        );
        // Decide if you want to skip or throw, skipping for now
      }
    }
    console.log(
      `MongoDbCheckpointer: Finished listing checkpoints for threadId: ${threadId}`,
    );
  }

  // Implement put
  async put(
    config: RunnableConfig,
    checkpoint: Checkpoint,
    metadata: CheckpointMetadata, // Added parameter
    newVersions: any, // Using any since ChannelVersions import failed
  ): Promise<RunnableConfig> {
    await this.isSetup;
    if (!this.collection) {
      throw new Error("MongoDB collection not initialized");
    }
    const threadId = config.configurable?.thread_id;
    const checkpointIdFromConfig = config.configurable?.checkpoint_id;

    // Check for threadId (still required)
    if (!threadId) {
      console.error(
        "MongoDbCheckpointer: Missing thread_id in config for put:",
        config,
      );
      throw new Error("thread_id is required in config for put");
    }

    let checkpointIdToUse: string;
    if (!checkpointIdFromConfig) {
      // Fallback if checkpoint_id is missing in config
      console.warn(
        `MongoDbCheckpointer: Missing checkpoint_id in config for put. Generating fallback ID using threadId and timestamp. Config: ${JSON.stringify(config)}`,
      );
      // Generate a fallback ID - ensure checkpoint.ts is available
      if (!checkpoint || !checkpoint.ts) {
        console.error(
          "MongoDbCheckpointer: Cannot generate fallback ID - checkpoint or checkpoint.ts is missing.",
          checkpoint,
        );
        throw new Error(
          "Cannot generate fallback checkpoint ID without checkpoint timestamp.",
        );
      }
      checkpointIdToUse = `${threadId}:${checkpoint.ts}`;
      console.log(
        `MongoDbCheckpointer: Using fallback checkpoint ID: ${checkpointIdToUse}`,
      );
    } else {
      // Use the ID provided in the config
      checkpointIdToUse = checkpointIdFromConfig;
      console.log(
        `MongoDbCheckpointer: Putting checkpoint ID: ${checkpointIdToUse} for threadId: ${threadId} with timestamp ${checkpoint.ts}`,
      );
    }

    // Determine the parent config to store.
    let parentConfigToStore: RunnableConfig | undefined = undefined;
    if (config.configurable?.parent_checkpoint_id) {
      console.log(
        `MongoDbCheckpointer: Found parent_checkpoint_id ${config.configurable.parent_checkpoint_id} in config.`,
      );
      // Fetch the parent tuple to get its config object.
      const parentTuple = await this.getTuple({
        configurable: {
          thread_id: threadId,
          checkpoint_id: config.configurable.parent_checkpoint_id,
        },
      });
      if (parentTuple) {
        parentConfigToStore = parentTuple.config;
        console.log(
          `MongoDbCheckpointer: Fetched parent config for checkpoint ID: ${parentConfigToStore?.configurable?.checkpoint_id}`,
        );
      } else {
        console.warn(
          `MongoDbCheckpointer: Could not fetch parent tuple for parent_checkpoint_id: ${config.configurable.parent_checkpoint_id}`,
        );
      }
    } else {
      console.log(
        `MongoDbCheckpointer: No parent_checkpoint_id found in config.`,
      );
    }

    // Serialize checkpoint using SuperJSON.stringify
    const serializedCheckpointString = SuperJSON.stringify(checkpoint);

    // Serialize parent config using SuperJSON.stringify
    const serializedParentConfigString = parentConfigToStore
      ? SuperJSON.stringify(parentConfigToStore)
      : undefined;

    // Construct the MongoDB document storing SuperJSON strings
    const doc: MongoCheckpointDoc = {
      _id: checkpointIdToUse,
      threadId: threadId,
      userId: config?.configurable?.userId, // <-- Add userId here
      timestamp: checkpoint.ts,
      checkpoint: serializedCheckpointString, // Store SuperJSON string
      ...(serializedParentConfigString && {
        parentConfig: serializedParentConfigString,
      }), // Store SuperJSON string
    };

    // Upsert the document into the collection
    try {
      const result = await this.collection.updateOne(
        { _id: checkpointIdToUse, threadId: threadId }, // Filter by _id and threadId
        { $set: doc }, // Set the document content using the structure defined in MongoCheckpointDoc
        { upsert: true }, // Create if not exists, update if exists
      );
      console.log(
        `MongoDbCheckpointer: Successfully put checkpoint ID: ${checkpointIdToUse}. Upsert result:`,
        result.acknowledged,
        `Matched: ${result.matchedCount}, Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`,
      );
    } catch (error) {
      console.error(
        `MongoDbCheckpointer: Error putting checkpoint ID: ${checkpointIdToUse} for threadId: ${threadId}`,
        error,
      );
      throw error; // Rethrow the error after logging
    }

    // Return the original config
    return config;
  }

  // Implement putWrites (Placeholder as discussed)
  async putWrites(
    config: RunnableConfig,
    // The 'writes' type is complex and internal; using Record<string, unknown> is a pragmatic approach
    // if the specific PendingWrite type isn't easily importable or usable.
    writes: any[],
    taskId: string,
  ): Promise<void> {
    await this.isSetup; // Ensure setup is complete before proceeding
    const threadId = config.configurable?.thread_id;
    const checkpointId = config.configurable?.checkpoint_id;

    console.warn(
      `MongoDbCheckpointer: putWrites called for taskId: ${taskId}, threadId: ${threadId}, checkpointId: ${checkpointId}. This implementation currently relies on a subsequent 'put' call to save the full state snapshot. Incremental writes are not applied here.`,
    );

    return Promise.resolve();
  }

  // Implement getTuple (Restored)
  async getTuple(config: RunnableConfig): Promise<CheckpointTuple | undefined> {
    await this.isSetup;
    if (!this.collection) {
      throw new Error("MongoDB collection not initialized for getTuple");
    }
    const threadId = config.configurable?.thread_id;
    if (!threadId) {
      console.warn(
        "MongoDbCheckpointer: getTuple called without thread_id in config.",
      );
      return undefined;
    }

    console.log(
      `MongoDbCheckpointer [getTuple]: Getting tuple for threadId: ${threadId}`,
    );

    // Find the latest checkpoint document for the given threadId
    const latestDoc = await this.collection.findOne(
      { threadId },
      { sort: { timestamp: -1 } },
    );

    if (!latestDoc) {
      console.log(
        `MongoDbCheckpointer [getTuple]: No checkpoint found for threadId: ${threadId}`,
      );
      return undefined;
    }

    console.log(
      `MongoDbCheckpointer [getTuple]: Found checkpoint doc with ID: ${latestDoc._id}`,
    );

    try {
      // Deserialize using SuperJSON.parse
      const checkpoint: Checkpoint = SuperJSON.parse(latestDoc.checkpoint);

      const parentConfigFlat: RunnableConfig | undefined =
        latestDoc.parentConfig
          ? SuperJSON.parse(latestDoc.parentConfig)
          : undefined;

      // Assign deserialized objects to the tuple
      const tuple: CheckpointTuple = {
        config: {
          configurable: {
            thread_id: latestDoc.threadId,
            checkpoint_id: latestDoc._id,
            ...(parentConfigFlat?.configurable?.checkpoint_id && {
              parent_checkpoint_id: parentConfigFlat.configurable.checkpoint_id,
            }),
          },
        },
        checkpoint: checkpoint, // Use the deserialized object
        parentConfig: parentConfigFlat, // Use the deserialized object
      };

      return tuple;
    } catch (e) {
      console.error(
        `MongoDbCheckpointer [getTuple]: Error SuperJSON parsing checkpoint ${latestDoc._id} for thread ${threadId}:`,
        e,
      );
      return undefined;
    }
  }

  // Implement getNextVersion (Placeholder)
  // This method might be needed by the BaseCheckpointSaver interface.
  // Matching the signature from the error message: (current?: number, _channel?: any) => number
  getNextVersion(current?: number, _channel?: any): number {
    // Simple incrementing version
    const nextVersion = (current ?? 0) + 1;
    console.log(
      `MongoDbCheckpointer [getNextVersion]: Current: ${current}, Next: ${nextVersion}`,
    );
    return nextVersion;
  }
}
