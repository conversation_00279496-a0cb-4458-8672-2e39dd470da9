"use strict";

const { StateGraph, END } = require("@langchain/graph"); // or require("langchain/graphs") depending on your version
const { RunnableLambda } = require("@langchain/core/runnables");
const mcpAdapter = require("../tools/mcp-langchain-adapter.cjs");

// Define the state for your graph (using JSDoc for type hints if desired)
/**
 * @typedef {object} AppState
 * @property {string} input
 * @property {string} [mcpToolName]
 * @property {Record<string, any>} [mcpToolArgs]
 * @property {any} [mcpResult]
 * @property {string} [error]
 */

async function createMcpWorkflow() {
  // Get MCP tools
  // Note: getTools() is async and initializes the client/server on first call.
  // It's generally better to ensure it's called once during app setup if possible,
  // but calling it here will work due to the singleton nature of mcpAdapter.
  await mcpAdapter.getClient(); // Ensure client is connected and server is running before graph setup
  const tools = await mcpAdapter.getTools();

  if (Object.keys(tools).length === 0) {
    console.warn(
      "[MCP Workflow] No MCP tools loaded. Workflow may not function as expected.",
    );
  }

  // Define the graph
  const workflow = new StateGraph({
    channels: {
      input: null,
      mcpToolName: null,
      mcpToolArgs: null,
      mcpResult: null,
      error: null,
    },
  });

  // Node to decide which MCP tool to call (example logic)
  workflow.addNode(
    "decide_mcp_tool",
    new RunnableLambda({
      /** @param {AppState} state */
      func: async (state) => {
        // Example: Simple logic to pick a tool based on input
        // In a real app, this would come from an LLM or more complex routing
        if (state.input.includes("running session")) {
          return {
            mcpToolName: "search_running_sessions",
            mcpToolArgs: { sessionName: "Tempo", limit: 2 }, // Example args
          };
        }
        // Add more conditions for other tools
        return { error: "No suitable MCP tool found for input." };
      },
    }),
  );

  // Node to execute the chosen MCP tool
  workflow.addNode(
    "execute_mcp_tool",
    new RunnableLambda({
      /** @param {AppState} state */
      func: async (state) => {
        if (!state.mcpToolName || !tools[state.mcpToolName]) {
          return {
            error: `MCP Tool not found or specified: ${state.mcpToolName}`,
          };
        }
        try {
          const result = await tools[state.mcpToolName](
            state.mcpToolArgs || {},
          );
          return { mcpResult: result };
        } catch (e) {
          console.error(
            `[MCP Workflow] Error executing tool ${state.mcpToolName}:`,
            e,
          );
          // Check if e is an error object and has a message property
          const errorMessage =
            e instanceof Error ? e.message : "Failed to execute MCP tool";
          return { error: errorMessage };
        }
      },
    }),
  );

  // Define edges
  workflow.setEntryPoint("decide_mcp_tool");
  workflow.addConditionalEdges(
    "decide_mcp_tool",
    /** @param {AppState} state */
    (state) => (state.error ? "__end__" : "execute_mcp_tool"),
    {
      execute_mcp_tool: "execute_mcp_tool",
      __end__: END,
    },
  );
  workflow.addEdge("execute_mcp_tool", END);

  return workflow.compile();
}

module.exports = { createMcpWorkflow };
