# Coaching API Tools Directory

This directory contains various tools and utilities used by the Coaching API, particularly for agentic features leveraging LangGraph and Myelin Core Platform (MCP).

## Core Components:

*   **`azureSearchTool.ts` / `azureSearchGraphTool.ts`**: Tools for interacting with Azure AI Search. These are likely used by agents to retrieve information or documents relevant to coaching queries. The `Graph` variant might imply integration with graph-based search capabilities or a graph data structure.
*   **`airtable-mcp-integration.ts`**: Integrates with Airtable, likely as a data source or sink, managed via MCP. This could be for fetching workout plans, user data, or logging coaching interactions.
*   **`user-data-utils.ts`**: Utility functions for handling user-specific data. This is crucial for personalizing coaching advice and maintaining user context.
*   **`logger.ts`**: A standardized logger for consistent logging across different tools and services within the coaching API.
*   **`azure-maps-service.ts`**: Provides functionalities related to Azure Maps, possibly for location-based coaching features, finding facilities, or route planning.

## LangGraph & MCP Integration:

Many files in this directory, especially the `.cjs` (CommonJS JavaScript) and test files, relate to integrating external services and MCP tools into LangGraph agents.

*   **`mcp-langchain-adapter.cjs`**: An adapter to make MCP tools callable from LangChain/LangGraph. This is a key piece for bridging the two ecosystems.
*   **`mcp-tools.cjs`**: Seems to be a collection or registration point for MCP tools intended for use by agents.
*   **Various `test-*.cjs` files**: These are test scripts for different integration scenarios:
    *   `test-direct-mcp-call.cjs`: Testing direct invocation of MCP tools.
    *   `test-direct-tool-call.cjs`: Testing direct invocation of LangChain/LangGraph tools.
    *   `test-langgraph-mcp.cjs`: Testing MCP tools being called from within a LangGraph.
    *   `test-mcp-langgraph.cjs`: Similar to the above, focusing on the MCP to LangGraph flow.
    *   `test-simple-mcp-call.cjs`: Basic tests for MCP calls.
    *   `test-strength-agent-mcp-args.cjs`: Specific tests for a "strength agent" using MCP tools and handling arguments.
*   **`simple-langgraph-calculator.cjs`**: A basic LangGraph example, likely used as a template or for understanding fundamental LangGraph mechanics.

## Airtable Specifics:

*   **`airtable-cycling-query-test.js`**: A test script specifically for querying cycling data from Airtable.
*   **`run-airtable-langraph.sh` & `run-airtable-query.sh`**: Shell scripts to execute tests related to Airtable and LangGraph.
*   **`use-airtable-in-agent.md`**: Documentation or notes on how to use Airtable within an agent.

## Azure Search Specifics:

*   **`test-high-intensity-cycling.ts`**: A test focusing on Azure Search for high-intensity cycling workouts.
*   **`run-cycling-intensity-search.sh`**: Shell script to run tests for cycling intensity searches.
*   **`README_cycling_search.md`**: Documentation specific to the cycling search functionality using Azure AI Search.

## Test Utilities:

*   **`test-basic.cjs`**: Very basic test setup or utility.

## Overall Purpose:

This `tools` directory serves as a hub for:
1.  **Defining and implementing tools** that provide specific functionalities (Azure Search, Airtable, User Data, Maps).
2.  **Integrating these tools with the Myelin Core Platform (MCP)**.
3.  **Making MCP tools and other custom tools available to LangGraph agents** for building sophisticated, AI-driven coaching experiences.
4.  **Testing these integrations** thoroughly through various scripts and scenarios.

The presence of both TypeScript (`.ts`) for core tool definitions and JavaScript (`.cjs`, `.js`) for testing and some LangGraph/MCP adapter logic suggests a mixed environment, which is common when integrating different libraries and platforms.

The focus is clearly on creating a robust and flexible system where AI agents can leverage a variety of data sources and services to provide intelligent coaching.

## Key Files (Likely to be imported by the main application):

*   `azureSearchTool.ts`
*   `azureSearchGraphTool.ts`
*   `airtable-mcp-integration.ts`
*   `user-data-utils.ts`
*   `logger.ts`
*   `azure-maps-service.ts`

These would be the primary candidates to be organized into a more structured `lib/tools` or similar directory if refactoring for clarity, while the test scripts and `.cjs` files might be moved to a dedicated `scripts` or `tests` directory. 