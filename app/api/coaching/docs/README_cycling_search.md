# Azure AI Search for Cycling Workout Plans

**Objective:** Utilize Azure AI Search to find relevant cycling workout plans based on user queries, particularly focusing on intensity levels (low, medium, high) and desired workout types or goals.

## Data Source:

-   A collection of cycling workout plans. Each plan should be a document in Azure AI Search.
-   **Key Fields for Searching:**
    *   `id`: Unique identifier for the workout plan.
    *   `name`: Title or name of the workout plan (e.g., "Endurance Ride - Zone 2", "HIIT Intervals - Tabata Style").
    *   `description`: Detailed description of the workout, including structure, intervals, rest periods, and purpose.
    *   `intensity`: Categorical field (e.g., "Low", "Medium", "High", "Zone 1", "Zone 2", "Zone 3", "Zone 4", "Zone 5", "All Out"). This should be a filterable and facetable field.
    *   `duration_minutes`: Numerical field for workout duration in minutes (filterable, sortable).
    *   `workout_type`: Categorical field (e.g., "Endurance", "Tempo", "Threshold", "VO2Max", "Sprint", "Recovery", "Sweet Spot"). Filterable and facetable.
    *   `target_goal`: Text field describing the goal of the workout (e.g., "Improve aerobic base", "Increase FTP", "Develop peak power", "Active recovery"). Searchable.
    *   `tags`: Array of strings for additional keywords (e.g., "long ride", "short intervals", "climbing focus", "time trial prep"). Searchable and filterable.
    *   `structure_details`: Could be a complex type or text field containing interval breakdowns, e.g., "Warm-up: 10min Z1; Main Set: 3x(8min Z4, 4min Z1); Cool-down: 10min Z1".

## Azure AI Search Index Configuration:

-   **Index Name:** `cycling-workout-plans`
-   **Fields (with types and attributes):**
    *   `id`: `Edm.String` (key, retrievable)
    *   `name`: `Edm.String` (searchable, retrievable, sortable)
    *   `description`: `Edm.String` (searchable, retrievable)
    *   `intensity`: `Edm.String` (searchable, retrievable, filterable, facetable, sortable)
    *   `duration_minutes`: `Edm.Int32` (retrievable, filterable, sortable, facetable)
    *   `workout_type`: `Edm.String` (searchable, retrievable, filterable, facetable, sortable)
    *   `target_goal`: `Edm.String` (searchable, retrievable)
    *   `tags`: `Collection(Edm.String)` (searchable, retrievable, filterable, facetable)
    *   `structure_details`: `Edm.String` (searchable, retrievable)
-   **Suggesters:** Consider a suggester on the `name` and `tags` fields for autocomplete functionality.
-   **Scoring Profiles:** May be useful to boost results based on matches in specific fields (e.g., higher weight for matches in `name` or `workout_type` than in `description`).
-   **Analyzers:** Use appropriate language analyzers. For keywords like intensity or workout type, ensure they are tokenized correctly.

## Querying Strategy (from `test-high-intensity-cycling.ts` logic):

The `searchCyclingPlansByIntensity` function in `test-high-intensity-cycling.ts` demonstrates a targeted search.

1.  **Input:** User-specified intensity (e.g., "high"), and potentially other keywords or desired workout characteristics.
2.  **Search Client:** Initialize `SearchClient` for the `cycling-workout-plans` index.
3.  **Search Text (`searchText`):**
    *   Combine the intensity with general terms like "cycling workout plan".
    *   Example: `"high intensity cycling workout plan"` or just `"high intensity"` if the context is already cycling.
    *   If additional keywords are provided by the user (e.g., "sprints", "short"), append them: `"high intensity sprints short cycling workout"`.
4.  **Search Options (`SearchOptions`):**
    *   **`filter`**: Crucial for precise matching on categorical data.
        *   If intensity is "high", the filter might be `intensity eq 'High' or intensity eq 'Zone 4' or intensity eq 'Zone 5' or intensity eq 'VO2Max' or intensity eq 'Sprint'`.
        *   Map colloquial terms ("high", "medium", "low") to specific `intensity` field values.
        *   Allow combining with other filters: `workout_type eq 'HIIT'` or `duration_minutes lt 60`.
    *   **`select`**: Specify which fields to retrieve (e.g., `id, name, description, intensity, duration_minutes, workout_type`).
    *   **`top`**: Limit the number of results.
    *   **`orderBy`**: Could sort by relevance (default), or by fields like `duration_minutes` or a custom scoring profile.
    *   **`includeTotalCount`**: Useful for pagination or understanding the total number of matches.
    *   **`searchFields`**: If not relying solely on filters, specify which fields the `searchText` should target (e.g., `name, description, tags, target_goal, workout_type`).
    *   **`queryType`**: `simple` (default) or `full` (Lucene). `simple` is often sufficient, but `full` allows more complex query syntax if needed.
    *   **`semanticSearch` (if Azure AI Search Semantic Search is enabled and configured):**
        *   Provide `semanticQuery` (often same as `searchText`).
        *   Specify `semanticConfiguration` name.
        *   This can significantly improve relevance for natural language queries.

## Example Query (Conceptual based on the test file's intent):

User says: "I want a high intensity cycling workout, maybe something with sprints."

*   **`searchText`**: `"high intensity sprints cycling workout"`
*   **`filter`**: `search.ismatchscoring('/.*high.*/i', 'intensity', 'full', 'any') or search.ismatchscoring('/.*zone [4-5].*/i', 'intensity', 'full', 'any') or workout_type eq 'Sprint' or workout_type eq 'HIIT'` (This is an example of a more complex filter; simpler `intensity eq 'High'` might be used if data is clean).
    *   A simpler and often more effective approach is to normalize the intensity categories in your data and use exact match filters: `intensity eq 'High'`.
*   **If using Semantic Search:**
    *   `semanticQuery`: `"high intensity cycling workout with sprints"`

## Tool Implementation (`azureSearchTool.ts` or similar):

-   A function/method that takes user query parameters (intensity, keywords, desired duration, etc.).
-   Constructs the `searchText` and `SearchOptions` dynamically.
-   Calls `searchClient.search(searchText, options)`.
-   Processes the results (documents) and returns them in a structured format.
-   Handles errors gracefully.

## Key Learnings from `test-high-intensity-cycling.ts` Context:

*   **Mapping User Input:** The tool needs to map user-friendly terms (like "high intensity") to the actual field values or query logic that Azure AI Search understands.
*   **Combining Search Text and Filters:** Use `searchText` for broad matching and `filter` for precise criteria on structured fields like `intensity`, `workout_type`, `duration_minutes`.
*   **Iterative Refinement:** The effectiveness of the search will depend on the quality of data in the index and the refinement of the query construction logic.
*   **Semantic Search Potential:** For more natural language queries, Semantic Search is a powerful feature to explore for this use case.

This setup allows the coaching agent to effectively query a knowledge base of cycling workouts and recommend suitable plans based on nuanced user requests regarding intensity and other preferences. 