# MCP LangGraph Findings & Learnings

**Date:** 2024-07-09

## Objective:

Explore integrating Myelin Core Platform (MCP) tools as LangChain/LangGraph tools, allowing them to be called by LangGraph agents.

## Key Challenges & Solutions:

1.  **Async Nature of MCP Tools vs. Sync Expectation of some LangChain Tool Wrappers:**
    *   MCP tools are often `async`.
    *   LangChain's `StructuredTool.from_func` expects a synchronous function by default.
    *   **Solution:** Use `StructuredTool.from_coroutine` for async MCP tool functions. This requires the LangGraph agent to also be run asynchronously (e.g., using `agent_executor.ainvoke`).

2.  **MCP Tool Invocation Semantics:**
    *   MCP tools are typically invoked via an MCP client, which handles authentication, routing, etc. (e.g., `mcp_client.tools.namespace.tool_name.invoke({input_data})`).
    *   A direct LangChain tool would wrap the *implementation* of the MCP tool, not the MCP client invocation itself.
    *   **Consideration for Adapter:** To make *any* MCP tool available to LangGraph with its native invocation, an adapter layer is beneficial. This adapter would:
        *   Take the MCP tool name (e.g., "namespace.tool_name") and input arguments.
        *   Internally use the `mcp_client` to call the actual MCP tool.
        *   Return the result.

3.  **Schema Mapping (Pydantic Models):**
    *   MCP tools often define their input/output schemas using Pydantic.
    *   LangChain tools (especially `StructuredTool`) also leverage Pydantic for `args_schema`.
    *   **Solution:** This is a natural fit. The Pydantic model defined for the MCP tool can often be directly reused as the `args_schema` for the LangChain tool wrapper.

## Experiments & Code Snippets:

*(Refer to `test-mcp-langgraph.cjs` and `mcp-langchain-adapter.cjs` for runnable examples)*

### Scenario 1: Directly Wrapping an Async MCP-like Function

```javascript
// Simplified MCP-like async function
async function getWeatherSimple({ city }) {
  console.log(`[ getWeatherSimple ] MCP Sim: Fetching weather for ${city}`);
  await new Promise(resolve => setTimeout(resolve, 500)); // Simulate async call
  if (city.toLowerCase() === "london") return JSON.stringify({ temp: 15, conditions: "Cloudy" });
  if (city.toLowerCase() === "paris") return JSON.stringify({ temp: 20, conditions: "Sunny" });
  return JSON.stringify({ temp: "N/A", conditions: "Unknown" });
}

const { StructuredTool } = require("@langchain/core/tools");
const { z } = require("zod");

const weatherToolAsync = new StructuredTool({
  name: "get_weather_async",
  description: "Gets the weather for a city (async wrapper).",
  schema: z.object({
    city: z.string().describe("The city to get weather for"),
  }),
  coroutine: async ({ city }) => { // Key: use coroutine for async
    console.log("[ weatherToolAsync coroutine ] Calling underlying async function...");
    return await getWeatherSimple({ city });
  }
});

// Usage in an async LangGraph agent execution would be necessary
```

### Scenario 2: An Adapter for Calling *Any* Registered MCP Tool (Conceptual)

*(See `mcp-langchain-adapter.cjs` for a more concrete, albeit simulated, example)*

This adapter tool would be a single LangChain tool given to the agent. Its input would specify the *actual* MCP tool to call and its arguments.

```javascript
const { StructuredTool } = require("@langchain/core/tools");
const { z } = require("zod");

// Assume mcpClient.invoke(toolName, args) exists and calls the real MCP tool
// const mcpClient = require("./mcp_client_mock"); // Your actual MCP client

const mcpAdapterTool = new StructuredTool({
  name: "invoke_mcp_tool",
  description: "Invokes a registered MCP tool by its name and arguments.",
  schema: z.object({
    tool_name: z.string().describe("The fully qualified name of the MCP tool (e.g., 'weather.getWeather')"),
    tool_args: z.any().describe("An object containing the arguments for the MCP tool"),
  }),
  coroutine: async ({ tool_name, tool_args }) => {
    try {
      // In a real scenario, mcpClient would handle looking up and calling the tool
      console.log(`[mcpAdapterTool] Attempting to invoke MCP tool: ${tool_name} with args:`, tool_args);
      // const result = await mcpClient.invoke(tool_name, tool_args); 
      // return JSON.stringify(result);
      // --- Simulation for this example ---
      if (tool_name === "weather.getWeatherSimple" && tool_args.city) {
        return await getWeatherSimple(tool_args);
      }
      return JSON.stringify({ error: "Tool not found or simulated call failed" });
      // --- End Simulation ---
    } catch (error) {
      console.error(`[mcpAdapterTool] Error invoking ${tool_name}:`, error);
      return JSON.stringify({ error: error.message });
    }
  },
});
```

## LangGraph Agent Setup (Illustrative - using the adapter)

```javascript
// ... imports: ChatOpenAI, createReactAgent, SqliteSaver, etc. ...

const tools = [mcpAdapterTool];
const agentExecutor = createReactAgent({ llm, tools, /* ... other config */ });

// Invoking:
const input = { messages: [new HumanMessage("What's the weather like in London using MCP?")] };
// Agent would need to learn to call `invoke_mcp_tool` with tool_name="weather.getWeatherSimple" and tool_args={city: "London"}
```

## Learnings & Next Steps:

*   **Async is Key:** `coroutine` is essential for wrapping async MCP functions.
*   **Adapter Pattern:** An `invoke_mcp_tool` adapter is powerful for exposing a wide range of MCP tools without creating individual LangChain wrappers for each one. This simplifies LangGraph tool management.
    *   The description of this adapter tool needs to be very clear to the LLM so it knows it can request *any* MCP tool through it.
    *   The LLM would need to know the names and expected arguments of available MCP tools (perhaps provided in its system prompt or via another informational tool).
*   **Schema Consistency:** Pydantic/Zod schemas make argument handling robust.
*   **Error Handling:** The adapter should gracefully handle errors from MCP tool invocations.
*   **Security/Permissions:** If MCP tools have their own permission models, the adapter needs to respect them, possibly by passing user context through the `mcp_client`.
*   **Testing:** Thoroughly test the async flow and error propagation.

This approach seems viable for making MCP capabilities available to LangGraph agents. The adapter pattern offers the most scalability. 