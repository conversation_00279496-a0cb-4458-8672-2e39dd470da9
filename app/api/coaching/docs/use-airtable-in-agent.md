\
<!---
integration_type: tool
name: airtable_query_tool
description: This tool queries the Airtable API to retrieve cycling workout data based on user preferences. It is designed to be used within a LangGraph agent to provide personalized workout recommendations.
version: 1.0
author: LangGraph Team
date: 2024-07-10
--->

# Using Airtable Query Tool in LangGraph Agent

This document outlines how to integrate and use the `airtable_query_tool` within a LangGraph agent. The tool fetches cycling workout data from an Airtable base, allowing the agent to provide personalized workout recommendations.

## Prerequisites

-   **Airtable Account and Base:** You need an Airtable account with a base containing cycling workout data. The base should have a table with relevant fields such as `workout_name`, `workout_type`, `duration_minutes`, `intensity`, `focus_area`, etc.
-   **Airtable API Key and Base ID:** Obtain your Airtable API key and the Base ID for your workout data. These will be used to authenticate and access the data.
-   **LangGraph and LangChain Installation:** Ensure you have LangGraph and necessary LangChain packages installed in your Python environment.
    ```bash
    pip install langgraph langchain langchain_openai
    ```
-   **Environment Variables:** Set up environment variables for your Airtable API key and Base ID, and optionally your OpenAI API key if you are using OpenAI models within your agent.
    ```bash
    export AIRTABLE_API_KEY="your_airtable_api_key"
    export AIRTABLE_BASE_ID="your_airtable_base_id"
    export OPENAI_API_KEY="your_openai_api_key" # Optional
    ```

## Tool Definition (`airtable_query_tool.py`)

The `airtable_query_tool` is defined using LangChain's `StructuredTool` or a custom tool implementation. It takes user preferences as input (e.g., desired workout type, duration, intensity) and constructs a formula to query the Airtable API.

```python
from langchain_core.tools import StructuredTool
from pyairtable import Api
import os

# --- MCP Tool Registration (Illustrative) ---
# This section shows how you might register this as an MCP tool.
# The actual registration would happen in your MCP tool management system.

# @mcp_tool.register(
# name="get_cycling_workouts_from_airtable",
# description="Retrieves cycling workouts from Airtable based on specified criteria like workout type, duration, and intensity.",
# input_schema=CyclingWorkoutQuerySchema, # Define this Pydantic model
# output_schema=CyclingWorkoutListSchema # Define this Pydantic model
# )
# async def get_cycling_workouts_from_airtable_mcp(query: CyclingWorkoutQuerySchema) -> CyclingWorkoutListSchema:
# # ... implementation similar to airtable_query_logic ...
# pass
# --- End MCP Tool Registration ---


def airtable_query_logic(workout_type: str = None, duration_minutes: int = None, intensity: str = None, focus_area: str = None):
    """
    Queries Airtable for cycling workouts based on specified criteria.
    """
    api_key = os.getenv("AIRTABLE_API_KEY")
    base_id = os.getenv("AIRTABLE_BASE_ID")
    table_name = "Workouts" # Replace with your actual table name

    if not api_key or not base_id:
        return "Error: AIRTABLE_API_KEY or AIRTABLE_BASE_ID not set."

    api = Api(api_key)
    table = api.table(base_id, table_name)

    filter_parts = []
    if workout_type:
        filter_parts.append(f"({{Workout Type}} = '{workout_type}')")
    if duration_minutes:
        # Example: Allow a +/- 10 minute range for duration
        filter_parts.append(f"AND({{Duration (min)}} >= {duration_minutes - 10}, {{Duration (min)}} <= {duration_minutes + 10})")
    if intensity:
        filter_parts.append(f"({{Intensity}} = '{intensity}')")
    if focus_area:
        filter_parts.append(f"({{Focus Area}} = '{focus_area}')")
    
    formula = None
    if filter_parts:
        formula = "AND(" + ", ".join(filter_parts) + ")"
        # If only one condition, remove AND() for simpler Airtable formula if needed, 
        # but Airtable's formula parser is generally robust.
        # However, the first AND example from pyairtable is `AND({Name}='Bob', {Age}>35)`
        # So if we have one element `AND({Workout Type} = 'Endurance')` this might be wrong.
        # Let's adjust if only one filter part.
        if len(filter_parts) == 1 and filter_parts[0].startswith("AND("): # Our duration adds an AND
             formula = filter_parts[0]
        elif len(filter_parts) == 1:
            formula = filter_parts[0]
        else:
            formula = "AND(" + ", ".join(filter_parts) + ")"


    print(f"Executing Airtable query with formula: {formula}")

    try:
        records = table.all(formula=formula, max_records=5) # Limiting to 5 records for brevity
        
        if not records:
            return "No workouts found matching your criteria."
            
        formatted_workouts = []
        for record in records:
            fields = record.get('fields', {})
            # Ensure all expected fields are present, provide defaults if not
            formatted_workouts.append({
                "name": fields.get("Name", "N/A"),
                "type": fields.get("Workout Type", "N/A"),
                "duration": fields.get("Duration (min)", 0),
                "intensity": fields.get("Intensity", "N/A"),
                "description": fields.get("Description", "N/A"),
                "focus_area": fields.get("Focus Area", "N/A")
            })
        return formatted_workouts
    except Exception as e:
        return f"Error querying Airtable: {str(e)}"

airtable_tool = StructuredTool.from_function(
    func=airtable_query_logic,
    name="query_cycling_workouts",
    description="Queries a cycling workout database for workouts matching criteria like type (e.g., Endurance, HIIT, Recovery), duration (in minutes), intensity (e.g., Low, Medium, High), and focus area (e.g., Climbing, Sprinting)."
    # args_schema=... # Define a Pydantic model for stricter input validation if desired
)

# Example Usage (for testing the tool directly)
if __name__ == "__main__":
    # Test Case 1: General query
    print("Test Case 1: General Endurance workout")
    results = airtable_query_logic(workout_type="Endurance", duration_minutes=60, intensity="Medium")
    print(results)
    print("\n" + "="*20 + "\n")

    # Test Case 2: Specific focus
    print("Test Case 2: HIIT workout for Sprinting")
    results_sprint = airtable_query_logic(workout_type="HIIT", focus_area="Sprinting", intensity="High")
    print(results_sprint)
    print("\n" + "="*20 + "\n")

    # Test Case 3: No matching criteria
    print("Test Case 3: No matching criteria (e.g., very specific unlikely workout)")
    results_none = airtable_query_logic(workout_type="Underwater Basket Weaving", duration_minutes=10)
    print(results_none)
    print("\n" + "="*20 + "\n")

    # Test Case 4: Only duration
    print("Test Case 4: Only duration specified")
    results_duration_only = airtable_query_logic(duration_minutes=45)
    print(results_duration_only)
    print("\n" + "="*20 + "\n")
    
    # Test Case 5: Querying for a type that might not exist or is misspelled
    print("Test Case 5: Misspelled workout type")
    results_typo = airtable_query_logic(workout_type="Enduranc", duration_minutes=90)
    print(results_typo)
    print("\n" + "="*20 + "\n")

```

## Agent Integration

Integrate the `airtable_tool` into your LangGraph agent's toolset.

```python
# In your agent setup file (e.g., agent.py)
from langchain_openai import ChatOpenAI
from langgraph.agent_react.agent import create_react_agent # Or your preferred agent type
from langgraph.checkpoint.sqlite import SqliteSaver # Or your preferred checkpointer
# from .airtable_query_tool import airtable_tool # Assuming the tool is in airtable_query_tool.py

# ... (Import airtable_tool as defined above) ...
# For this example, let's assume airtable_tool is available in the current scope

# Initialize your LLM
llm = ChatOpenAI(model="gpt-4-turbo-preview", temperature=0)

# Define the tools for the agent
tools = [airtable_tool] # Add other tools as needed

# Create the agent
# The system message can be tailored to guide the agent on when and how to use the Airtable tool.
system_message = """You are a helpful assistant that can suggest cycling workouts. 
Use the 'query_cycling_workouts' tool to find suitable workouts based on user preferences.
When a user asks for a workout, try to elicit details like preferred type, duration, intensity, and focus area if not provided.
If multiple workouts are found, you can present a few options.
If no workouts are found, inform the user and perhaps suggest broader criteria."""

agent_executor = create_react_agent(llm, tools, messages_modifier=system_message) # Using create_react_agent

# Example of running the agent (this would typically be part of your application logic)
# config = {"configurable": {"thread_id": "user_123"}}
# for event in agent_executor.stream({"messages": [("user", "I'm looking for a 60-minute endurance ride at medium intensity.")]}, config=config):
# for key, value in event.items():
# print(f"Event: {key}")
# if isinstance(value, dict) and "messages" in value:
# print(value["messages"][-1].pretty_print())
```

## Prompting and Usage

When interacting with the agent, users can make requests like:

-   "Find me a 60-minute cycling workout."
-   "I want a HIIT workout that focuses on climbing, around 45 minutes."
-   "Suggest a low-intensity recovery ride for today."

The agent, guided by its system message and the description of the `airtable_tool`, will invoke the tool with the extracted parameters to fetch relevant workouts from Airtable.

## Error Handling and Refinements

-   **No Results:** The tool and agent should gracefully handle cases where no workouts match the criteria. The agent can suggest broadening the search or trying different parameters.
-   **API Errors:** Implement robust error handling for Airtable API calls (e.g., network issues, authentication failures).
-   **Input Validation:** Consider adding stricter input validation to the tool (e.g., using Pydantic models for `args_schema`) to ensure data consistency.
-   **Data Formatting:** The tool formats the output for clarity. This can be further customized based on how the agent is expected to present the information.
-   **Tool Description:** The clarity of the tool's description is crucial for the LLM to understand its purpose and how to use its arguments effectively.

This setup allows for a dynamic and data-driven approach to providing cycling workout recommendations through a LangGraph agent.
The `airtable-mcp-integration.ts` in the `tools` directory seems to be a more complete, TypeScript version of such a tool.
The python script above is more of a standalone test / example. 