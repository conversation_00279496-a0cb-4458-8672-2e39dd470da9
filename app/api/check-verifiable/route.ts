import { NextRequest, NextResponse } from "next/server";
import { AzureOpenAI } from "openai";

const azureClient = new AzureOpenAI({
  apiKey: process.env.AZURE_OPENAI_KEY as string,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT as string,
  apiVersion: "2024-08-01-preview",
  deployment: process.env.AZURE_DEPLOYMENT_NAME,
});

export async function POST(req: NextRequest) {
  console.log("Starting verifiable content check");

  try {
    const { text } = await req.json();
    console.log("Checking text:", text.substring(0, 100) + "...");

    const analysis = await azureClient.chat.completions.create({
      model: process.env.AZURE_DEPLOYMENT_NAME as string,
      messages: [
        {
          role: "system",
          content:
            "You are an expert at identifying if text contains verifiable scientific or technical claims. Return a JSON object with 'isVerifiable: true' if the text contains specific claims that could be verified through academic sources, and 'isVerifiable: false' if it contains only general advice or common knowledge.",
        },
        {
          role: "user",
          content: `Does this text contain verifiable scientific or technical claims? Text: ${text}`,
        },
      ],
      response_format: { type: "json_object" },
      temperature: 0.1,
    });

    console.log("OpenAI response:", analysis.choices[0].message.content);

    const result = JSON.parse(analysis.choices[0].message.content || "{}");
    console.log("Parsed result:", result);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error checking verifiable content:", error);
    return NextResponse.json({ isVerifiable: false });
  }
}
