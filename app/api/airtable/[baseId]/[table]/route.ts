import { fetchTableData } from "../../../../lib/airtable";
import { NextRequest, NextResponse } from "next/server";
import { DomainMetrics, PhaseDomainData } from "@/types/plan";
import { FieldSet, Collaborator, Attachment } from "airtable";

type SportType = "running" | "cycling" | "strength";

interface RawDomainData {
  skills?: DomainMetrics;
  strength?: DomainMetrics;
  mindset?: DomainMetrics;
  nutrition?: DomainMetrics;
  recovery?: DomainMetrics;
}

type AirtableField =
  | string
  | number
  | boolean
  | readonly string[]
  | Collaborator
  | readonly Collaborator[]
  | Attachment
  | readonly Attachment[]
  | undefined
  | null;

function convertToString(value: AirtableField | undefined | null): string {
  if (!value) return "";
  if (typeof value === "string") return value;
  if (typeof value === "number") return value.toString();
  if (typeof value === "boolean") return value.toString();
  if (Array.isArray(value)) return JSON.stringify(value);
  return JSON.stringify(value);
}

function transformDomainData(
  domainData: AirtableField,
  sportType: SportType,
): string {
  try {
    const stringData = convertToString(domainData);
    if (!stringData) {
      console.log("Empty string data, returning empty string");
      return "";
    }

    const data = JSON.parse(stringData) as RawDomainData;

    if (!data || !data.skills) {
      console.log("No skills data found, returning original string");
      return stringData;
    }

    const { skills, ...restData } = data;

    const transformedData: Partial<PhaseDomainData> = {
      ...restData,
      [sportType]: skills,
    };

    return JSON.stringify(transformedData);
  } catch (error) {
    console.error("Error transforming domain data:", error);
    return convertToString(domainData);
  }
}

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ baseId: string; table: string }> },
) {
  // Access params using context
  const baseId = await (await context.params).baseId;
  const table = await (await context.params).table;
  const limit = Number(req.nextUrl.searchParams.get("limit")) || 20;
  const offset = req.nextUrl.searchParams.get("offset") || undefined;

  try {
    // Fetch cycling plans
    if (table === "cycling-plans") {
      const { records: plans, offset: nextOffset } = await fetchTableData(
        baseId,
        "Cycling Plans",
        { limit, offset },
      );

      // Add logging for cycling plans and their images
      console.log("Fetched cycling plans:", plans.length);
      plans.forEach((plan) => {
        console.log(`Plan ${plan["Plan ID"]} image data:`, {
          planId: plan["Plan ID"],
          planName: plan["Plan Name"],
          rawImageData: plan["Plan Image"],
        });
      });

      return NextResponse.json({
        data: plans.map((plan) => {
          // Add logging for each transformed plan
          const transformedPlan = {
            id: plan["Plan ID"],
            name: plan["Plan Name"],
            description: plan["Plan Description"],
            level: plan["Level"],
            ageGroup: plan["Age Group"],
            audience: plan["Audience"],
            cyclingDiscipline: plan["Discipline"],
            duration:
              typeof plan["Duration"] === "number"
                ? `${plan["Duration"]} weeks`
                : plan["Duration"]?.toString() || "N/A",
            gender: plan["Gender"],
            numberOfPhases: plan["Number of Phases"],
            phaseNames: plan["Phase Names"],
            weeklyVolume: plan["Weekly Volume"],
            targetEvents:
              typeof plan["Target Events"] === "string"
                ? plan["Target Events"].split(",").map((s) => s.trim())
                : [],
            planImage: plan["Plan Image"],
            programComponents:
              typeof plan["Program Components"] === "string"
                ? plan["Program Components"].split(",").map((s) => s.trim())
                : [],
            phaseIds: plan["Cycling Phases"] || [],
            planType: "Cycling",
          };

          // Log the transformed plan's image data
          console.log(`Transformed plan ${transformedPlan.id} image:`, {
            planId: transformedPlan.id,
            planName: transformedPlan.name,
            transformedImage: transformedPlan.planImage,
          });

          return transformedPlan;
        }),
        metadata: { nextOffset, total: plans.length },
      });
    }

    // Fetch S&C plans
    if (table === "sc-plans") {
      const { records: plans, offset: nextOffset } = await fetchTableData(
        baseId,
        "S&C Plans",
        { limit, offset },
      );
      return NextResponse.json({
        data: plans.map((plan) => ({
          id: plan["Plan ID"],
          name: plan["Plan Name"],
          description: plan["Plan Description"],
          sport: plan["Sport"],
          duration:
            typeof plan["Plan Duration (weeks)"] === "number"
              ? `${plan["Plan Duration (weeks)"]} weeks`
              : plan["Plan Duration (weeks)"]?.toString() || "N/A",
          level: plan["Level"],
          ageGroup: plan["Age Group"],
          audience: plan["Audience"],
          gender: plan["Gender"],
          discipline: plan["Discipline"],
          weeklyVolume: plan["Weekly Volume"],
          numberOfPhases: plan["Number of Phases"],
          targetEvents:
            typeof plan["Target Events"] === "string"
              ? plan["Target Events"].split(",").map((s) => s.trim())
              : [],
          planImage: plan["Plan Image"],
          techniques:
            typeof plan["Techniques Used"] === "string"
              ? plan["Techniques Used"].split(",").map((s) => s.trim())
              : [],
          phaseIds: plan["Phase IDs"] || [],
          planType: "Strength",
        })),
        metadata: { nextOffset, total: plans.length },
      });
    }

    // Fetch running plans
    if (table === "running-plans") {
      const { records: plans, offset: nextOffset } = await fetchTableData(
        baseId,
        "Running Plans",
        { limit, offset },
      );
      return NextResponse.json({
        data: plans.map((plan) => ({
          id: plan["Plan ID"],
          name: plan["Plan Name"],
          description: plan["Plan Description"],
          sport: plan["Sport"],
          duration:
            typeof plan["Plan Duration (weeks)"] === "number"
              ? `${plan["Plan Duration (weeks)"]} weeks`
              : plan["Plan Duration (weeks)"]?.toString() || "N/A",
          gender: plan["Gender"],
          level: plan["Level"],
          ageGroup: plan["Age Group"],
          audience: plan["Audience"],
          discipline: plan["Discipline"],
          weeklyVolume: plan["Weekly Volume"],
          numberOfPhases: plan["Number of Phases"],
          phases:
            typeof plan["Phases"] === "string"
              ? plan["Phases"].split(",").map((s) => s.trim())
              : [],
          targetEvents:
            typeof plan["Target Events"] === "string"
              ? plan["Target Events"].split(",").map((s) => s.trim())
              : [],
          planImage: plan["Plan Image"],
          techniques:
            typeof plan["Techniques Used"] === "string"
              ? plan["Techniques Used"].split(",").map((s) => s.trim())
              : [],
          phaseIds: plan["Phase ID Rollup (from Running Phases)"] || [],
          runningSessions: plan["SessionID (from Running Sessions)"] || [],
          sessionNames: plan["SessionName (from Running Sessions)"] || [],
          planType: "Running",
        })),
        metadata: { nextOffset, total: plans.length },
      });
    }

    // Fetch running phases
    if (table === "running-phases") {
      const { records: phases } = await fetchTableData(
        baseId,
        "Running Phases",
        { fetchAll: true },
      );
      const transformedPhases = phases.map((phase) => {
        const rawDomainData = phase["Phase Domain Data"];
        const transformedDomainData = transformDomainData(
          rawDomainData,
          "running",
        );
        return {
          id: phase["Phase ID"],
          name: phase["Phase Name"],
          planId: Array.isArray(phase["Plan ID (from Running Plans)"])
            ? phase["Plan ID (from Running Plans)"][0]
            : phase["Plan ID (from Running Plans)"],
          planIds: Array.isArray(phase["Plan ID (from Running Plans)"])
            ? phase["Plan ID (from Running Plans)"]
            : [phase["Plan ID (from Running Plans)"]],
          description: phase["Phase Description"],
          duration: phase["Phase Duration"],
          order: phase["Phase Order Number"],
          orderText: phase["Phase Order Text"],
          focus: phase["Phase Focus"],
          adaptations: phase["Phase Adaptations"],
          rationale: phase["Phase Rationale"],
          skillsTraining: phase["Phase Skills Training"],
          fitnessTraining: phase["Phase Fitness Training"],
          strengthTraining: phase["Phase Strength Training"],
          mindsetTraining: phase["Phase Mindset Training"],
          nutritionRecommendations: phase["Phase Nutrition Recommendations"],
          recoveryRecommendations: phase["Phase Recovery Recommendations"],
          resources: phase["Resources"],
          weeklyTime: phase["Average Weekly Time"],
          weeklySessions: phase["Average Weekly Sessions"],
          domainData: transformedDomainData,
        };
      });

      return NextResponse.json({
        data: transformedPhases,
        metadata: { total: phases.length },
      });
    }

    // Fetch cycling phases
    if (table === "cycling-phases") {
      const { records: phases } = await fetchTableData(
        baseId,
        "Cycling Phases",
        { fetchAll: true },
      );

      const transformedPhases = phases.map((phase) => {
        const rawDomainData = phase["Phase Domain Data"];
        const transformedDomainData = transformDomainData(
          rawDomainData,
          "cycling",
        );
        return {
          id: phase["Phase ID"],
          name: phase["Phase Name"],
          planId: Array.isArray(phase["Plan ID (from Cycling Plans)"])
            ? phase["Plan ID (from Cycling Plans)"][0]
            : phase["Plan ID (from Cycling Plans)"],
          planIds: Array.isArray(phase["Plan ID (from Cycling Plans)"])
            ? phase["Plan ID (from Cycling Plans)"]
            : [phase["Plan ID (from Cycling Plans)"]],
          description: phase["Phase Description"],
          duration: phase["Phase Duration"],
          order: phase["Phase Order Number"],
          orderText: phase["Phase Order Text"],
          focus: phase["Phase Focus"],
          adaptations: phase["Phase Adaptations"],
          rationale: phase["Phase Rationale"],
          skillsTraining: phase["Phase Skills Training"],
          fitnessTraining: phase["Phase Fitness Training"],
          strengthTraining: phase["Phase Strength Training"],
          mindsetTraining: phase["Phase Mindset Training"],
          nutritionRecommendations: phase["Phase Nutrition Recommendations"],
          recoveryRecommendations: phase["Phase Recovery Recommendations"],
          resources: phase["Resources"],
          weeklyTime: phase["Average Weekly Time"],
          weeklySessions: phase["Average Weekly Sessions"],
          domainData: transformedDomainData,
        };
      });

      return NextResponse.json({
        data: transformedPhases,
        metadata: { total: phases.length },
      });
    }

    // Fetch S&C phases
    if (table === "sc-phases") {
      const { records: phases } = await fetchTableData(baseId, "S&C Phases", {
        fetchAll: true,
      });

      const transformedPhases = phases.map((phase) => {
        const rawDomainData = phase["Phase Domain Data"];
        const transformedDomainData = transformDomainData(
          rawDomainData,
          "strength",
        );
        return {
          id: phase["Phase ID"],
          name: phase["Phase Name"],
          planId: Array.isArray(phase["Plan ID (from Plan ID)"])
            ? phase["Plan ID (from Plan ID)"][0]
            : phase["Plan ID (from Plan ID)"],
          planIds: Array.isArray(phase["Plan ID (from Plan ID)"])
            ? phase["Plan ID (from Plan ID)"]
            : [phase["Plan ID (from Plan ID)"]],
          description: phase["Phase Description"],
          duration: phase["Phase Duration"],
          order: phase["Phase Order Number"],
          orderText: phase["Phase Order Text"],
          focus: phase["Phase Focus"],
          adaptations: phase["Phase Adaptations"],
          rationale: phase["Phase Rationale"],
          skillsTraining: phase["Phase Skills Training"],
          fitnessTraining: phase["Phase Fitness Training"],
          strengthTraining: phase["Phase Strength Training"],
          mindsetTraining: phase["Phase Mindset Training"],
          nutritionRecommendations: phase["Phase Nutrition Recommendations"],
          recoveryRecommendations: phase["Phase Recovery Recommendations"],
          resources: phase["Resources"],
          weeklyTime: phase["Average Weekly Time"],
          weeklySessions: phase["Average Weekly Sessions"],
          domainData: transformedDomainData,
        };
      });

      return NextResponse.json({
        data: transformedPhases,
        metadata: { total: phases.length },
      });
    }

    console.log("Invalid table requested:", table);
    return NextResponse.json(
      { error: "Invalid table requested" },
      { status: 400 },
    );
  } catch (error) {
    console.error(`Failed to fetch data:`, error);
    return NextResponse.json(
      { error: `Failed to fetch data from ${table}` },
      { status: 500 },
    );
  }
}
