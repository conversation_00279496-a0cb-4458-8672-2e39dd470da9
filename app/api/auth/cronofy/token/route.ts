import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";



export async function GET(req: NextRequest) {
  console.log("GET request received!");
  try {
    const userId = req.nextUrl.searchParams.get("user_id");
    if (!userId) {
      return new NextResponse(JSON.stringify({ error: "Missing user_id" }), {
        status: 400,
      });
    }
    const client = await clientPromise;
    const db = client.db("test");

    const collection = db.collection("cronofy_tokens");
    const tokenData = await collection.findOne({ userId: userId });

    return new NextResponse(
      JSON.stringify({ data: tokenData || {} }),
      { status: 200 },
    );
  } catch (error) {
    console.error("Error fetching calendar tasks:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to fetch calendar tasks" }),
      { status: 500 },
    );
  }
}