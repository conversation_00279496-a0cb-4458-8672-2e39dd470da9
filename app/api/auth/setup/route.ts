import { NextRequest, NextResponse } from "next/server";
import { getAuth } from "firebase/auth";
import { firebaseConfig } from "@/config/firebaseConfig";

export async function GET(req: NextRequest) {
  try {
    // Return the Firebase configuration and any other auth setup data needed by the client
    return NextResponse.json(
      {
        firebaseConfig,
        status: "success",
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Error in auth setup:", error);
    return NextResponse.json(
      {
        error: "Failed to initialize auth setup",
        status: "error",
      },
      { status: 500 },
    );
  }
}
