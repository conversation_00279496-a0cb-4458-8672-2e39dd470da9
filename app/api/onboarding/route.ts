import { NextRequest, NextResponse } from "next/server";
import { HumanMessage, AIMessage, BaseMessage } from "@langchain/core/messages";
import { Command, StateSnapshot } from "@langchain/langgraph";
import { createOnboardingGraph } from "./graph"; // Assuming graph.ts exports this
import { StreamEvent } from "@langchain/core/tracers/log_stream";
import { OnboardingState, SidebarStateData } from "./state"; // Import OnboardingState and SidebarStateData
import { isEqual } from "lodash"; // Import isEqual for deep comparison
import { IterableReadableStream } from "@langchain/core/utils/stream"; // Import correct stream type
import { MongoDbCheckpointer } from "../coaching/lib/checkpointer/mongodb-checkpointer";
import { RunnableConfig } from "@langchain/core/runnables"; // Ensure RunnableConfig is imported

// Define the structure for expected input data
interface OnboardingRequestBody {
  userId: string;
  threadId?: string; // Optional, can be derived from userId
  message?: string; // User's message or initial trigger
}

// Define the structure for SSE data sent to the client
interface StreamEventData {
  type:
    | "token"
    | "agent_start" // Can represent the onboarding assistant starting
    | "need_input" // <<< RE-ADD
    | "sidebar_update" // New event type for sidebar data
    | "complete" // Sent when the onboarding graph finishes
    | "error";
  agent?: string; // e.g., "onboarding_assistant"
  content?: string; // Token content or error message
  prompt?: string; // <<< RE-ADD
  field?: string; // <<< RE-ADD (optional, based on interrupt payload)
  sidebarData?: SidebarStateData; // <<< RESTORE THIS FIELD
}

// --- Helper function to deduplicate consecutive identical messages ---
// REMOVED - No longer needed as we don't manually combine history

// Instantiate the graph (assuming createOnboardingGraph compiles)
// Note: Type errors in graph.ts might cause runtime issues here.
const graph = createOnboardingGraph();

// --- Instantiate Checkpointer ---
const checkpointer = new MongoDbCheckpointer();

export async function POST(req: NextRequest) {
  let requestBody: OnboardingRequestBody;
  try {
    requestBody = await req.json();
  } catch (error) {
    console.error("[Onboarding API POST] Invalid request body", error);
    return NextResponse.json(
      { error: "Invalid request body" },
      { status: 400 },
    );
  }

  // Remove isResumeCommand and resumeValue from extraction
  const {
    userId,
    threadId: requestThreadId,
    message, // Keep message
  } = requestBody;

  if (!userId) {
    console.error("[Onboarding API POST] Missing required userId");
    return NextResponse.json(
      { error: "Missing required userId parameter" },
      { status: 400 },
    );
  }

  // Logic needs either a message OR a resume command
  if (!message) {
    console.error("[Onboarding API POST] Missing required message");
    return NextResponse.json(
      { error: "Missing required message parameter" },
      { status: 400 },
    );
  }

  const threadId = requestThreadId || `onboarding-${userId}`;
  console.log(
    `[Onboarding API POST] Processing request for userId: ${userId}, threadId: ${threadId}`,
  );

  // --- Create Runnable Config ---
  const config: RunnableConfig = {
    configurable: {
      thread_id: threadId,
      userId: userId,
      checkpointer: checkpointer,
    },
  };

  const stream = new ReadableStream({
    async start(controller) {
      let lastSentSidebarData: SidebarStateData | null = null;

      // Declare flags *before* the try block
      let needsInputDetected = false;
      let finalInterruptPayload: any = null;

      // Flexible helper to format and send events
      const sendEvent = (data: StreamEventData) => {
        if (controller.desiredSize === null) {
          console.warn(
            "[Onboarding API] Trying to send event after controller closed:",
            data.type,
          );
          return; // Controller closed, cannot send
        }
        // This handles both 'null' and numbers (positive, negative, 0)
        if (controller.desiredSize <= 0) {
          console.warn(
            "[Onboarding API] Controller backpressure reached, not sending:",
            data.type,
          );
          return; // Backpressure indication, skip
        }
        controller.enqueue(`data: ${JSON.stringify(data)}\n\n`);
      };

      // Function to check and send sidebar updates
      const checkAndSendSidebarUpdate = async () => {
        try {
          const currentState = await graph.getState(config);
          const currentSidebarData = currentState?.values?.sidebarData;
          if (
            currentSidebarData &&
            !isEqual(currentSidebarData, lastSentSidebarData)
          ) {
            console.log(
              "[Onboarding API] Detected sidebarData change, sending update.",
            );
            sendEvent({
              type: "sidebar_update",
              sidebarData: currentSidebarData,
            });
            lastSentSidebarData = currentSidebarData; // Update last sent data
          }
        } catch (error) {
          console.error(
            "[Onboarding API] Error fetching state for sidebar update check:",
            error,
          );
        }
      };

      try {
        sendEvent({ type: "agent_start", agent: "Athlea" });

        // --- Execute graph ONLY with new message --- START
        let eventStream: IterableReadableStream<StreamEvent>;

        // ALWAYS process the input as a new message, remove the isResumeCommand logic
        if (message) {
          console.log(
            "[Onboarding API POST] Processing user message:",
            message,
          );
          // Prepare graph input with ONLY the new message
          const graphInput = { messages: [new HumanMessage(message)] };

          console.log(
            `[Onboarding API POST] Calling streamEvents with 1 new message.`,
          );
          eventStream = graph.streamEvents(graphInput, {
            ...config,
            version: "v2" as const,
          });
        } else {
          // This case should now be caught by initial validation
          // if (!message) { ... }
          console.error(
            "[Onboarding API POST] No message found in request body.",
          );
          throw new Error("Internal Error: Message is required for POST.");
        }
        // --- Execute graph ONLY with new message --- END

        console.log("[Onboarding API POST] Processing event stream...");
        let shouldBreakLoop = false;

        for await (const event of eventStream) {
          if (controller.desiredSize === null || shouldBreakLoop) break; // Check for closure or flag

          // Check sidebar on key events
          if (event.event === "on_chain_end" || event.event === "on_llm_end") {
            await checkAndSendSidebarUpdate();
          }

          // Check for the tag before sending token
          if (
            event.event === "on_chat_model_stream" &&
            event.tags?.includes("final_response")
          ) {
            const chunk = event.data?.chunk;
            if (chunk?.content) {
              sendEvent({
                type: "token",
                agent: "Athlea",
                content: chunk.content as string,
              });
            }
          } else if (
            event.event === "on_chain_end" ||
            event.event === "on_chain_start" ||
            event.event === "on_llm_end"
          ) {
            // Check if the 'needInput' node completed
            if (event.event === "on_chain_end" && event.name === "needInput") {
              try {
                const currentState = await graph.getState(config);
                const needsInput = currentState?.values?.needsInput === true;

                if (needsInput) {
                  console.log(
                    "[Onboarding API POST] Detected needsInput=true after needInput node completed. Will check at end of stream.",
                  );
                  needsInputDetected = true; // Mark that we need input
                  // Capture potential interrupt data if available now
                  const interruptData =
                    currentState?.values?._langgraph_interrupt;
                  if (interruptData?.interrupts?.length > 0) {
                    finalInterruptPayload = interruptData.interrupts[0]
                      .value ?? { question: "Input needed", field: null };
                  } else {
                    finalInterruptPayload = {
                      question: "Input needed",
                      field: null,
                    };
                  }
                }
              } catch (e) {
                console.error(
                  "[Onboarding API POST] Error checking for needsInput flag after needInput node:",
                  e,
                );
              }
            } else if (
              event.event === "on_chain_end" &&
              event.name === "__graph__"
            ) {
              console.log(
                "[Onboarding API POST] Graph execution completed normally.",
              );
              // Let finally block handle sending 'complete'
              break; // Exit loop after graph finishes
            }
            // No longer need to check other node names for setting shouldBreakLoop early
          } else if (event.event === "on_interrupt") {
            // This block should ideally not be hit now, but log if it does
            console.warn(
              "[!!! Onboarding API POST: UNEXPECTED on_interrupt !!!] Event received:",
              event.data,
            );
            // Set shouldBreakLoop just in case to exit gracefully
            shouldBreakLoop = true;
            needsInputDetected = true; // Assume we need input if interrupted
            finalInterruptPayload = {
              question: "Input needed (unexpected interrupt)",
              field: null,
            };
          }
          // Removed the explicit break check based on shouldBreakLoop here
          // Let the loop finish processing all events naturally
        }
        console.log("[Onboarding API POST] Finished processing event stream.");

        // After the loop, check if we detected a need for input
        if (needsInputDetected) {
          console.log(
            "[Onboarding API POST] Stream finished. needsInput was detected, sending need_input event.",
          );
          sendEvent({
            type: "need_input",
            prompt: finalInterruptPayload?.question || "Input needed",
            field: finalInterruptPayload?.field,
          });
          shouldBreakLoop = true; // Signal to finally block that we handled the 'end'
        }
      } catch (error: any) {
        console.error(
          "[Onboarding API POST] Error during graph execution:",
          error,
        );
        // Send generic error for any execution errors
        sendEvent({
          type: "error",
          content:
            error.message || "An unknown error occurred during execution",
        });
      } finally {
        console.log(
          `[Onboarding API POST] Entering finally block. Stream ended. needsInputDetected=${needsInputDetected}`,
        );
        await checkAndSendSidebarUpdate();
        if (controller.desiredSize !== null) {
          // Only send COMPLETE if we DID NOT detect a need for input
          if (!needsInputDetected) {
            console.log(
              "[Onboarding API POST] No needInput detected, sending COMPLETE in finally.",
            );
            sendEvent({ type: "complete" });
          } else {
            console.log(
              "[Onboarding API POST] needsInput detected, NOT sending complete.",
            );
          }
          // Always close the controller if it's open
          console.log("[Onboarding API POST] Closing controller in finally.");
          controller.close();
        } else {
          console.log(
            "[Onboarding API POST] Controller already closed in finally.",
          );
        }
      }
    },
  });

  return new NextResponse(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  });
}

export async function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl;
  const userId = searchParams.get("userId");
  // threadId might be passed explicitly or derived from userId if needed
  const threadIdParam = searchParams.get("threadId");
  // An initial message might be passed (like "start"), but we'll primarily trigger with {}
  // const initialMessage = searchParams.get("initialMessage");

  if (!userId) {
    return NextResponse.json({ error: "userId is required" }, { status: 400 });
  }

  // Use provided threadId or create a default one for onboarding
  const threadId = threadIdParam || `onboarding-${userId}`;

  console.log(
    `[Onboarding API GET] Initializing onboarding: userId=${userId}, threadId=${threadId}`,
  );

  // --- Create Runnable Config ---
  const config: RunnableConfig = {
    configurable: {
      thread_id: threadId,
      userId: userId,
      checkpointer: checkpointer,
    },
  };

  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      let lastSentSidebarData: SidebarStateData | null = null;
      let finalStateHasInterrupt = false; // Flag for finally block check

      const sendEvent = (data: StreamEventData) => {
        // Add try-catch for robustness
        try {
          if (controller.desiredSize === null || controller.desiredSize <= 0) {
            console.warn(
              "[Onboarding API GET] Controller backpressure or closed:",
              data.type,
            );
            return;
          }
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(data)}\n\n`),
          );
        } catch (e) {
          console.error(
            "[Onboarding API GET] Error enqueueing SSE event:",
            e,
            "Data:",
            data,
          );
        }
      };

      // Function to check and send sidebar updates (same as in POST)
      const checkAndSendSidebarUpdate = async () => {
        try {
          const currentState = await graph.getState(config);
          const currentSidebarData = currentState?.values?.sidebarData;
          if (
            currentSidebarData &&
            !isEqual(currentSidebarData, lastSentSidebarData)
          ) {
            console.log(
              "[Onboarding API GET] Detected sidebarData change, sending update.",
            );
            sendEvent({
              type: "sidebar_update",
              sidebarData: currentSidebarData,
            });
            lastSentSidebarData = currentSidebarData; // Update last sent data
          }
        } catch (error) {
          console.error(
            "[Onboarding API GET] Error fetching state for sidebar update check:",
            error,
          );
        }
      };

      try {
        sendEvent({
          type: "agent_start",
          agent: "Athlea",
        });
        await checkAndSendSidebarUpdate();

        console.log("[Onboarding API GET] Preparing graph input...");

        // --- Fetch current state and add new message --- START
        // REMOVED fetching state here - graph will load it via checkpointer

        const newMessageContent = searchParams.get("message"); // Get message from GET params
        let graphInput: Partial<OnboardingState>; // Use Partial as input type

        if (newMessageContent) {
          console.log(
            `[Onboarding API GET] Preparing input with new HumanMessage: "${newMessageContent}"`,
          );
          // Prepare graph input with ONLY the new message
          graphInput = { messages: [new HumanMessage(newMessageContent)] };
        } else {
          // If no message param, trigger with empty input
          // LangGraph will load the state from the checkpointer.
          console.log(
            "[Onboarding API GET] No message in GET params, triggering graph with empty input.",
          );
          graphInput = {}; // Trigger graph, it will load history via checkpointer
        }

        // REMOVED deduplication logic here

        console.log(`[Onboarding API GET] Executing graph.streamEvents...`);
        // Pass the prepared graphInput instead of {}\
        const eventStream = await graph.streamEvents(
          graphInput, // Use the prepared input (either {messages: [new]} or {})
          { ...config, version: "v2" as const },
        );

        // Process the stream
        for await (const event of eventStream) {
          if (controller.desiredSize === null) break; // Check for closure

          // Check sidebar on key events
          if (
            event.event === "on_chain_end" ||
            event.event === "on_llm_end" ||
            event.event === "on_tool_end"
          ) {
            await checkAndSendSidebarUpdate();
          }

          // Check for the tag before sending token
          if (
            event.event === "on_chat_model_stream" &&
            event.tags?.includes("final_response")
          ) {
            const chunk = event.data?.chunk;
            if (chunk?.content) {
              sendEvent({
                type: "token",
                agent: "Athlea",
                content: chunk.content as string,
              });
            }
          } else if (
            event.event === "on_chain_end" &&
            event.name === "__graph__"
          ) {
            console.log(
              "[Onboarding API GET] Graph execution completed normally within stream.",
            );
            // Let finally block handle sending 'complete'
            break; // Exit loop after graph finishes
          }
        }
        console.log("[Onboarding API GET] Finished processing event stream.");
      } catch (error: any) {
        console.error(
          "[Onboarding API GET] Error during graph execution:",
          error,
        );
        sendEvent({
          type: "error",
          content: error.message || "An unknown error occurred",
        });
        // Ensure controller is closed on error, otherwise finally might hang
        if (controller.desiredSize !== null) controller.close();
      } finally {
        console.log("[Onboarding API GET] Entering finally block.");

        // Check if controller is still open before doing anything
        if (controller.desiredSize !== null) {
          await checkAndSendSidebarUpdate(); // Final sidebar check

          // --- Check final graph state for interrupt --- START
          let interruptPayload: any = null;
          try {
            const finalStateSnapshot = await graph.getState(config);
            // Check if the needsInput flag is set to true
            const needsInput = finalStateSnapshot?.values?.needsInput;
            console.log(
              `[Onboarding API GET] Checking needsInput flag: ${needsInput} (${typeof needsInput})`,
            );

            // Check the internal _langgraph_interrupt field
            const interruptData =
              finalStateSnapshot?.values?._langgraph_interrupt;

            if (needsInput === true) {
              finalStateHasInterrupt = true;
              // Get any interrupt data if available for additional context
              if (
                interruptData &&
                interruptData.interrupts &&
                interruptData.interrupts.length > 0
              ) {
                interruptPayload = interruptData.interrupts[0].value ?? {};
              } else {
                interruptPayload = {}; // Empty payload if no interrupt data
              }
              console.log(
                "[Onboarding API GET] needsInput is TRUE. Treating as an interrupt for input.",
                interruptPayload,
              );
            } else if (
              interruptData &&
              interruptData.interrupts &&
              interruptData.interrupts.length > 0
            ) {
              finalStateHasInterrupt = true;
              interruptPayload = interruptData.interrupts[0].value ?? {};
              console.log(
                "[Onboarding API GET] Found interrupt in final state:",
                interruptPayload,
              );
            } else {
              console.log(
                "[Onboarding API GET] No interrupt or needsInput found in final state.",
              );
            }
          } catch (e) {
            console.error(
              "[Onboarding API GET] Error fetching final state in finally:",
              e,
            );
            // Proceed assuming no interrupt if state fetch fails
            finalStateHasInterrupt = false;
          }
          // --- Check final graph state for interrupt --- END

          // Send need_input if interrupt was found in state
          if (finalStateHasInterrupt) {
            const question = interruptPayload?.question || "Input needed";
            const field = interruptPayload?.field;
            console.log(
              `[Onboarding API GET] Sending need_input from finally block. Field: ${field}`,
            );
            sendEvent({ type: "need_input", prompt: question, field: field });
          } else {
            // Only send COMPLETE if NO interrupt was found
            console.log(
              "[Onboarding API GET] No interrupt, sending COMPLETE in finally.",
            );
            sendEvent({ type: "complete" });
          }

          // Always close the controller if it's open
          console.log("[Onboarding API GET] Closing controller in finally.");
          controller.close();
        } else {
          console.log(
            "[Onboarding API GET] Controller already closed in finally.",
          );
        }
      }
    },
  });

  return new NextResponse(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  });
}
