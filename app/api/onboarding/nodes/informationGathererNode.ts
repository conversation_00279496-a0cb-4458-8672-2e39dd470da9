import {
  OnboardingState,
  SidebarStateData,
  onboardingChannels,
  SummaryItem,
} from "../state";
import {
  AIMessage,
  BaseMessage,
  HumanMessage,
  SystemMessage,
} from "@langchain/core/messages";
import { RunnableLambda } from "@langchain/core/runnables";
import { createAzureChatOpenAI } from "../../lib/global-azure-openai";

// System prompt handling both initial greeting and ongoing gathering
const systemPrompt = `<PERSON> are <PERSON><PERSON><PERSON>, a comprehensive AI fitness coach specializing in multi-sport athlete development.

Analyze the conversation history. The last message is the most recent user input.

**Initial Greeting (If history is very short, e.g., 1-2 messages):**
- Adopt an enthusiastic, welcoming tone (under 150 words).
- Briefly introduce yourself as their AI fitness coach for multi-sport athletes.
- Explain that the onboarding process will personalize their experience.
- Ask an open-ended question about their athletic goals and the sports/activities they pursue.
- End with an encouraging statement.

**Ongoing Information Gathering (If history is longer):**
- Your goal is to gather information needed to create a personalized fitness plan by asking questions sequentially.
- Required Information Categories (adapt based on sports mentioned):
    1.  **Primary Fitness Goal(s):** Specific, measurable goals for EACH sport/activity mentioned.
    2.  **Experience Level:** Overall and potentially per sport if relevant.
    3.  **Time Commitment:** Days per week, duration per session, time of day preference.
    4.  **Equipment Access:** Specifics relevant to their sports.
    5.  **Priorities/Connections:** How goals/sports relate, seasonal constraints, main focus.

- **Response Rules for Ongoing Gathering:**
    - **If ANY information IS missing:**
        1. Briefly acknowledge the user's last message.
        2. Concisely summarize what you've learned *so far* across all relevant categories/sports.
        3. Ask a natural, conversational question to gather the *next single piece* of missing information. Be specific based on the sport if applicable:
           - Tennis: Ask about specific skills (backhand, serve, etc.).
           - Swimming: Inquire about seasonal availability and specific strokes.
           - Running: Explore distance preferences and performance targets.
           - Strength Training: Ask about specific lifts and strength goals.
           - Other Sports: Ask relevant goal/performance questions.
    - **If ALL relevant information SEEMS present:** Provide a polite closing statement indicating you have the necessary details and the process will continue. Example: "Thanks! It sounds like I have a good understanding of your goals and background across your sports."
    - **If the user asks a question or goes off-topic:** Briefly acknowledge/answer it, then gently guide them back by asking for the next piece of required information or provide the closing statement if all info seems present.
- Maintain a collaborative, detail-oriented tone.
`;

// System Prompt for Goal Extraction - Enhanced for multi-sport
const goalExtractionSystemPrompt = `Analyze the user messages in the provided conversation history. Identify and extract ALL specific fitness goals mentioned by the user, noting the sport/activity if specified.

IMPORTANT:
- Focus ONLY on what the USER has explicitly stated as their goal(s).
- Do not include general statements or questions from the coach.
- Look for phrases like "I want to...", "My goal for [sport] is...", etc.

Examples of fitness goals:
- Build muscle
- Lose weight
- Run a faster 5k (Running)
- Improve backhand consistency (Tennis)
- Increase bench press max (Strength Training)
- Swim 1500m freestyle efficiently (Swimming)

Return ONLY the identified goals, separated by commas. If the sport is clearly mentioned with the goal, include it in parentheses. 
For example: "Build muscle, Lose weight, Run a faster 5k (Running), Improve backhand consistency (Tennis)"

If no specific fitness goals are mentioned by the user, respond ONLY with: "NO_GOALS".`;

// System Prompt for Structured Summary Extraction - UPDATED for category/details and isImportant
const summaryExtractionSystemPrompt = `You are a fitness coach assistant analyzing a conversation history between a coach and a user during onboarding.
Your task is to extract ALL key pieces of information the USER has provided relevant to creating a fitness plan, especially for a multi-sport athlete.

Output ONLY a valid JSON object. This object MUST contain a single key named "summaryList".
The value of "summaryList" MUST be an array of objects. Each object MUST have: { "category": string, "details": string, "isImportant": boolean }
  - "category": A concise category name (e.g., "Sports/Activities", "Overall Goals", "Goals (Running)", "Experience Level", "Availability", "Equipment Access", "Priorities/Seasonality", "Medical Conditions", "Dietary Restrictions", "Upcoming Events"). ONLY include titles for categories where the user actually provided information.
  - "details": A SINGLE string containing the specific detail(s) for that category, potentially synthesized. For lists like goals or equipment, combine them into one string.
  - "isImportant": A boolean flag. Set to true if the category pertains to crucial information that directly impacts plan safety or structure. Examples of important categories include:
    - Selected Sports/Activities
    - Injuries (past or present)
    - Medical Conditions (e.g., asthma, heart conditions)
    - Allergies (especially if food-related and nutrition is discussed)
    - Strict Dietary Restrictions (e.g., celiac, vegan)
    - Confirmed Key Races or Events the user is training for.
    Set to false for general preferences, non-critical goals, or less impactful information.

Example Output Structure:
{
  "summaryList": [
    { "category": "Sports/Activities", "details": "Running, Tennis", "isImportant": true },
    { "category": "Overall Goals", "details": "Improve general fitness", "isImportant": false },
    { "category": "Goals (Running)", "details": "Run a 10k race in 3 months", "isImportant": false },
    { "category": "Medical Conditions", "details": "Asthma (mild, uses inhaler as needed)", "isImportant": true },
    { "category": "Dietary Restrictions", "details": "Vegetarian", "isImportant": true },
    { "category": "Upcoming Events", "details": "City Marathon in October", "isImportant": true },
    { "category": "Availability", "details": "Mon, Wed, Fri evenings", "isImportant": false }
  ]
}

IMPORTANT:
- Synthesize information from the ENTIRE conversation history.
- Focus ONLY on information provided explicitly by the USER.
- Capture details per sport where provided.
- If the user has not provided ANY relevant information yet, respond ONLY with: NO_INFO_SHARED
`;

// Define the JSON schema for the expected WRAPPER object - UPDATED for category/details and isImportant
const summaryWrapperSchema = {
  title: "UserInformationSummaryWrapper",
  description:
    "A wrapper object containing a list of categorized information provided by the user.",
  type: "object", // Top level must be object
  properties: {
    summaryList: {
      type: "array",
      description: "The list of categorized summary items.",
      items: {
        type: "object",
        properties: {
          category: {
            type: "string",
            description:
              "The category of information (e.g., Fitness Goals, Availability).",
          },
          details: {
            type: "string",
            description:
              "Specific detail(s) provided by the user for this category.",
          },
          isImportant: {
            type: "boolean",
            description:
              "Whether this information is critical for plan generation (e.g., medical, injuries, primary sport).",
          },
        },
        required: ["category", "details", "isImportant"], // Make isImportant required from LLM
      },
    },
  },
  required: ["summaryList"],
};

// Initialize LLM instance
const llm = createAzureChatOpenAI({ temperature: 0.3 });

// Runnable for Goal Extraction (uses updated prompt)
const extractGoals = new RunnableLambda({
  func: async (messages: BaseMessage[]) => {
    // Filter for user messages only
    const userMessages = messages.filter((msg) => msg._getType() === "human");
    if (userMessages.length === 0) {
      console.log("[extractGoals] No user messages found in history.");
      return { goals: [], exists: false }; // No goals if no user input
    }

    // Prepare history for goal extraction LLM
    const goalHistory: BaseMessage[] = [
      new SystemMessage(goalExtractionSystemPrompt),
      ...userMessages, // Send only user messages for goal context
    ];

    console.log(
      "[extractGoals] Calling LLM for goal extraction with history:",
      goalHistory.map(
        (m) => `${m._getType()}: ${String(m.content).substring(0, 50)}...`,
      ),
    );

    try {
      const response = await llm.invoke(goalHistory);
      const content =
        typeof response.content === "string" ? response.content.trim() : "";
      console.log("[extractGoals] Raw LLM Response:", content);

      if (content === "NO_GOALS" || content === "") {
        console.log("[extractGoals] LLM indicated no goals found.");
        return { goals: [], exists: false };
      }

      // Split by comma and trim whitespace
      const extractedGoals = content
        .split(",")
        .map((goal) => goal.trim())
        .filter((g) => g !== ""); // Filter empty strings

      if (extractedGoals.length > 0) {
        console.log("[extractGoals] Extracted Goals:", extractedGoals);
        return { goals: extractedGoals, exists: true };
      } else {
        console.log("[extractGoals] Parsed goals list is empty.");
        return { goals: [], exists: false };
      }
    } catch (error) {
      console.error(
        "[extractGoals] Error calling LLM for goal extraction:",
        error,
      );
      // Fallback: return no goals if extraction fails
      return { goals: [], exists: false };
    }
  },
}).withConfig({ runName: "extractGoals" });

// Runnable for Structured Summary Extraction (uses updated prompt and schema)
const extractSummary = new RunnableLambda({
  func: async (messages: BaseMessage[]): Promise<SummaryItem[]> => {
    const historyForLLM = messages.filter((msg) => msg._getType() !== "system");
    if (historyForLLM.length === 0) {
      console.log("[extractSummary] No non-system messages.");
      return [];
    }

    // Use the WRAPPER schema with the LLM
    const modelWithStructuredOutput =
      llm.withStructuredOutput(summaryWrapperSchema);

    console.log(
      "[extractSummary] Calling LLM for structured summary (wrapper object) with history:",
      historyForLLM.length,
    );

    try {
      const response = await modelWithStructuredOutput.invoke([
        new SystemMessage(summaryExtractionSystemPrompt),
        ...historyForLLM,
      ]);

      // Handle NO_INFO_SHARED string response first
      if (typeof response === "string" && response === "NO_INFO_SHARED") {
        console.log("[extractSummary] LLM indicated no info shared.");
        return [];
      }

      // Check if the response is an object and has the 'summaryList' property
      if (
        typeof response === "object" &&
        response !== null &&
        Array.isArray((response as any).summaryList)
      ) {
        const summaryList = (response as any).summaryList as SummaryItem[];
        console.log("[extractSummary] Extracted summaryList:", summaryList);
        return summaryList;
      } else {
        console.error(
          "[extractSummary] LLM response was not the expected wrapper object:",
          response,
        );
        return [];
      }
    } catch (error: any) {
      if (error.message?.includes("NO_INFO_SHARED")) {
        console.log(
          "[extractSummary] Caught NO_INFO_SHARED response in error.",
        );
        return [];
      }
      console.error(
        "[extractSummary] Error calling/parsing LLM for structured summary wrapper:",
        error,
      );
      return [];
    }
  },
}).withConfig({ runName: "extractSummaryItems" });

// Define some common sport suggestions
const commonSportSuggestions = [
  { label: "🏃 Running", value: "Running" },
  { label: "🚲 Cycling", value: "Cycling" },
  { label: "🏋️ Strength", value: "Strength Training" },
  { label: "🧘 General Fitness", value: "General Fitness" },
];
// Create a set of valid sport values for quick lookup
const validSportValues = new Set(commonSportSuggestions.map((s) => s.value));

// Main node logic
export const informationGathererNode = async (
  state: OnboardingState,
): Promise<Partial<OnboardingState>> => {
  console.log("[Node: informationGathererNode] Entering node.");
  const messages = state.messages || [];
  const currentSidebarData =
    state.sidebarData || onboardingChannels.sidebarData.default();
  // Read selectedSport and suggestions from sidebarData
  let currentSelectedSport = currentSidebarData.selectedSport; // Use let as we might update it
  let currentSelectedSports = currentSidebarData.selectedSports || [];
  const userInput = state.userInput; // Get the latest user input text
  const previousSuggestions = currentSidebarData.sportSuggestions; // Check suggestions in sidebarData

  let sportSelectedThisTurn = false;

  // --- Check if user input is a sport selection ---
  if (
    (!currentSelectedSport || currentSelectedSports.length === 0) && // Allow selection if either single or multi is empty
    previousSuggestions &&
    userInput &&
    validSportValues.has(userInput)
  ) {
    console.log(
      `[Node: informationGathererNode] User selected sport: ${userInput}`,
    );
    sportSelectedThisTurn = true;
    currentSelectedSport = userInput; // Update the single sport for this turn's logic
    // Add to the multiple sports array, avoiding duplicates
    if (!currentSelectedSports.includes(userInput)) {
      currentSelectedSports = [...currentSelectedSports, userInput];
    }
    // We will clear suggestions in the state update later
  }

  // --- 1. Extract Goals --- (Run after potential sport selection)
  const goalExtractionResult = await extractGoals.invoke(messages);

  // --- 2. Extract Structured Summary Items --- (Run after potential sport selection)
  const summaryItemsResult = await extractSummary.invoke(messages);

  // --- 3. Generate Conversational Response ---
  const history = messages.filter((msg) => msg._getType() !== "system");
  const isFirstTurn = history.length <= 1; // Re-evaluate? Maybe check history length before adding potential selection message
  console.log(
    `[Node: informationGathererNode] Is first turn check? ${isFirstTurn}`,
  );

  // --- Dynamically create the system prompt ---
  let dynamicSystemPrompt = systemPrompt; // Start with the base prompt
  // Use the potentially updated currentSelectedSport for the prompt
  if (currentSelectedSport && !isFirstTurn) {
    const sportGuidance = `\n\n**Current Focus:** The user has selected **${currentSelectedSport}** as a primary focus. Tailor your next question towards gathering information specifically relevant to ${currentSelectedSport}, based on the required categories and what's still missing.`;
    dynamicSystemPrompt += sportGuidance;
    console.log(
      "[Node: informationGathererNode] Added sport-specific guidance to prompt:",
      sportGuidance,
    );
  }
  // --- End dynamic prompt creation ---

  // Use the combined prompt - the LLM will handle initial greeting vs ongoing logic
  const conversationHistoryWithPrompt: BaseMessage[] = [
    new SystemMessage(dynamicSystemPrompt), // Use the potentially modified prompt
    ...history,
  ];

  console.log(
    "[Node: informationGathererNode] Calling main LLM for conversation turn...",
  );
  let aiResponse: AIMessage | null = null;
  try {
    aiResponse = await llm.invoke(conversationHistoryWithPrompt, {
      tags: ["final_response"],
    });
    console.log(
      "[Node: informationGathererNode] Raw LLM Conversational Response:",
      aiResponse.content,
    );
  } catch (error) {
    console.error(
      "[Node: informationGathererNode] Error calling main conversational LLM:",
      error,
    );
    aiResponse = new AIMessage(
      "Sorry, I encountered an issue. Let's try continuing.",
    );
  }

  // --- 4. Prepare State Update ---
  const updatedSidebarData: SidebarStateData = {
    ...currentSidebarData,
    currentStage: isFirstTurn ? "greeting" : "gathering",
    goals: {
      list: goalExtractionResult.goals,
      exists: goalExtractionResult.exists,
    },
    summaryItems: summaryItemsResult,
    generatedPlan: currentSidebarData.generatedPlan,
    // Add suggestions ONLY on the first turn OR if a sport was just selected
    sportSuggestions:
      isFirstTurn && !sportSelectedThisTurn
        ? commonSportSuggestions
        : undefined,
    // Update selectedSport and selectedSports in sidebar data
    selectedSport: currentSelectedSport,
    selectedSports: currentSelectedSports,
  };

  const updates: Partial<OnboardingState> = {
    messages: aiResponse ? [aiResponse] : [],
    sidebarData: updatedSidebarData, // Update the whole sidebarData object
    hasEnoughInfo: false,
    // Remove direct updates to selectedSport and sportSuggestions here
    // selectedSport: currentSelectedSport,
    // sportSuggestions: isFirstTurn && !sportSelectedThisTurn ? commonSportSuggestions : undefined,
  };

  // No need for the separate clearing logic below, handled in updatedSidebarData
  // if (!(history.length <= 1) && updates.sportSuggestions) {
  //   console.log(
  //     "[Node: informationGathererNode] Clearing stale top-level sport suggestions.",
  //   );
  //   updates.sportSuggestions = undefined;
  // }

  console.log(
    `[Node: informationGathererNode] Goals:`,
    goalExtractionResult,
    `SummaryItems:`,
    summaryItemsResult,
    "Sidebar Updated:",
    updatedSidebarData,
    // Log if suggestions are being added
    isFirstTurn ? "Adding sport suggestions." : "Not adding sport suggestions.",
    `Selected Sport Updated To: ${currentSelectedSport}`,
  );

  return updates;
};
