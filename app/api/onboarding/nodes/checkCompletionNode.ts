import { OnboardingState } from "../state";
import { BaseMessage, SystemMessage } from "@langchain/core/messages";
import { RunnableLambda } from "@langchain/core/runnables";
import { createAzureChatOpenAI } from "../../lib/global-azure-openai";

// Initialize a simple LLM for the boolean check
const llm = createAzureChatOpenAI({ temperature: 0.0 }); // Low temp for deterministic check

// Stricter prompt for completion check
const completionCheckSystemPrompt = `Analyze the provided conversation history, focusing ONLY on information explicitly stated by the USER.

**Your Task:** Verify if the USER has provided information covering ALL of the following categories:
1.  **Specific Fitness Goal(s):** At least one clear goal mentioned for their primary sport(s).
2.  **Experience Level:** Some statement about their experience (overall or per sport).
3.  **Time Commitment:** Details on days/week, duration, or time of day.
4.  **Equipment Access:** Mention of available equipment or workout location relevant to their goals.
5.  **Priorities/Connections/Seasonality:** Statement on how goals relate, primary focus, or relevant time constraints.

**Additional Check:** Examine the VERY LAST user message. Does it clearly indicate readiness to proceed or explicitly ask for the plan (e.g., "Okay", "Yes", "Let's do it", "Generate the plan", "Sounds good", confirming the last piece of info)?

**Response Rules:**
- Respond ONLY with the word "true" IF AND ONLY IF:
    - There is clear evidence from the USER for **ALL 5 required categories** listed above.
    - AND the **last user message** indicates readiness.
- Respond ONLY with the word "false" otherwise (if any category is missing OR the user isn't ready).

Do not provide any explanation or other text.`;

export const checkCompletionNode = async (
  state: OnboardingState,
): Promise<Partial<OnboardingState>> => {
  console.log("[Node: checkCompletionNode] Entering node.");
  const messages = state.messages || [];

  if (messages.length === 0) {
    console.log("[Node: checkCompletionNode] No messages, cannot be complete.");
    return { hasEnoughInfo: false };
  }

  // Prepare history for the check LLM
  const historyForCheck: BaseMessage[] = [
    new SystemMessage(completionCheckSystemPrompt),
    ...messages.filter((msg) => msg._getType() !== "system"), // Exclude other system prompts
  ];

  console.log(
    "[Node: checkCompletionNode] Calling LLM for completion check with history length:",
    historyForCheck.length - 1,
  );

  try {
    const response = await llm.invoke(historyForCheck);
    const content =
      typeof response.content === "string"
        ? response.content.trim().toLowerCase()
        : "";
    console.log("[Node: checkCompletionNode] Raw LLM Response:", content);

    const isComplete = content === "true";
    console.log(
      "[Node: checkCompletionNode] Determined completion:",
      isComplete,
    );

    return { hasEnoughInfo: isComplete }; // Update only the flag
  } catch (error) {
    console.error("[Node: checkCompletionNode] Error calling LLM:", error);
    return { hasEnoughInfo: false }; // Default to false on error
  }
};
