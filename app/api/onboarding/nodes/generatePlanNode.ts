import {
  OnboardingState,
  onboardingChannels,
  SidebarStateData,
} from "../state";
import { AIMessage, SystemMessage } from "@langchain/core/messages";
import { RunnableLambda } from "@langchain/core/runnables";
import { createAzureChatOpenAI } from "../../lib/global-azure-openai";
import { PlanDetails } from "@/types/plan";

// --- JSON Schema Definition for PlanDetails (Simplified) ---
const planPhaseJsonSchema = {
  type: "object",
  properties: {
    phaseName: {
      type: "string",
      description:
        "Name of the training phase (e.g., 'Base Building', 'Peak Week')",
    },
    duration: {
      type: "string",
      description: "Duration of the phase (e.g., '4 weeks')",
    },
    description: {
      type: "string",
      description:
        "Detailed description of the phase's focus, key goals, and typical weekly structure.",
    },
  },
  required: ["phaseName", "duration", "description"],
};

// --- JSON Schema Definition for Example Session (Simplified) ---
const exampleSessionSchema = {
  type: "object",
  properties: {
    SessionName: {
      type: "string",
      description: "A descriptive name for the example session.",
    },
    sessionType: {
      type: "string",
      description:
        "Type of session (e.g., 'Endurance', 'Tempo', 'Intervals', 'Strength', 'Recovery'). Infer from plan/phase.",
    },
    Duration: {
      type: "string",
      description:
        "Estimated duration of the session (e.g., '60 minutes', '90 minutes').",
    },
    SessionDescription: {
      type: "string",
      description:
        "Brief description of the session's main activity or purpose.",
    },
  },
  required: ["SessionName", "sessionType", "Duration", "SessionDescription"],
};

const planDetailsJsonSchema = {
  title: "PlanDetails",
  description: "Schema for a personalized fitness plan structure.",
  type: "object",
  properties: {
    planId: {
      type: "string",
      description: "Unique identifier for the plan (generate a UUID string)",
      format: "uuid", // Hint for the format
    },
    name: {
      type: "string",
      description:
        "Catchy and descriptive name for the generated fitness plan.",
    },
    description: {
      type: "string",
      description:
        "Detailed description of the plan, its goals, and target audience.",
    },
    duration: {
      type: "string",
      description:
        "Total duration of the plan (e.g., '12 weeks'). Estimate based on user info.",
    },
    level: {
      type: "string",
      description:
        "Recommended fitness level (e.g., 'Beginner', 'Intermediate', 'Advanced'). Infer from user info.",
    },
    planType: {
      type: "string",
      description:
        "Primary type of plan (e.g., 'Running', 'Strength', 'Weight Loss'). Infer from user goals.",
    },
    disciplines: {
      type: "array",
      items: { type: "string" },
      description:
        "List of disciplines involved (e.g., ['Running', 'Strength']). Infer from user goals.",
    },
    rationale: {
      type: "string",
      description:
        "Explanation of why this plan structure and content is suitable for the user based on their provided information.",
    },
    phases: {
      type: "array",
      items: planPhaseJsonSchema, // Reference the phase schema
      description:
        "List of training phases. Generate 1-3 simple placeholder phases.",
    },
    exampleSessions: {
      type: "array",
      items: exampleSessionSchema, // Reference the example session schema
      description:
        "List of 2-3 example training sessions relevant to the plan's start.",
    },
  },
  required: [
    "planId",
    "name",
    "description",
    "duration",
    "level",
    "planType",
    "disciplines",
    "rationale",
    "phases",
    "exampleSessions",
  ],
};
// --- End JSON Schema Definition ---

// Initialize LLM instance
const llm = createAzureChatOpenAI({ temperature: 0.3 });

// --- generatePlanNode --- uses JSON Schema, summaryItems, and updates sidebarData
export const generatePlanNode = new RunnableLambda({
  func: async (state: OnboardingState): Promise<Partial<OnboardingState>> => {
    console.log(
      "[Node generatePlanNode] Running with state sidebarData:",
      state.sidebarData,
    );
    const currentSidebarData =
      state.sidebarData || onboardingChannels.sidebarData.default();

    // Bind the JSON schema to the LLM
    const modelWithStructuredOutput = llm.withStructuredOutput(
      planDetailsJsonSchema,
      { name: "PlanDetails" },
    );

    // Format summary items for the prompt
    const summaryString =
      currentSidebarData.summaryItems
        ?.map((item) => `${item.category}: ${item.details}`)
        ?.join("\n - ") || "No summary available.";

    const userGoals =
      currentSidebarData.goals?.list?.join(", ") || "Not specified";

    // Prepare the prompt using the formatted summary
    const systemPrompt = `You are an expert fitness coach. Based on the user's goals and the summarized information, generate a personalized fitness plan structure. 
    User Goals: ${userGoals}
    User Information Summary:
 - ${summaryString}
    
    Respond ONLY with the structured plan details conforming to the provided JSON schema. Create a unique planId (UUID format), infer planType, level, and disciplines from the goals/summary. Generate a suitable name, description, total duration, and rationale. Create 1-3 phases with names, durations, and DETAILED descriptions covering focus and weekly structure. Generate 2-3 diverse EXAMPLE sessions (using the exampleSessionSchema structure) that fit within the first phase, including SessionName, sessionType, Duration, and SessionDescription.`;

    console.log(
      "[Node generatePlanNode] Calling LLM for structured plan generation.",
    );

    try {
      // Invoke the model
      const generatedPlanObject = await modelWithStructuredOutput.invoke([
        new SystemMessage(systemPrompt),
      ]);

      const finalPlan = generatedPlanObject as PlanDetails;

      console.log(
        "[Node generatePlanNode] Successfully generated structured plan:",
        finalPlan,
      );

      const summaryMessage = new AIMessage({ content: `Plan generated!` });

      // Prepare the updated sidebar data including the plan
      const updatedSidebarData: SidebarStateData = {
        ...currentSidebarData,
        generatedPlan: finalPlan,
        currentStage: "complete",
      };

      // Return the state update
      return {
        messages: [summaryMessage],
        generatedPlan: finalPlan, // Keep top level
        sidebarData: updatedSidebarData, // Pass for streaming
        onboardingStage: "complete",
      };
    } catch (error) {
      console.error("[Node generatePlanNode] Error generating plan:", error);
      const errorSidebarData: SidebarStateData = {
        ...currentSidebarData,
        currentStage: "error",
      };
      const errorMessage = new AIMessage({ content: "Error generating plan." });
      return {
        messages: [errorMessage],
        sidebarData: errorSidebarData,
        onboardingStage: "error",
      };
    }
  },
}).withConfig({ runName: "generatePlanNode" });
