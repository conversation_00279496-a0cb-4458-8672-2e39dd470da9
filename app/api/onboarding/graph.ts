import {
  StateGraph,
  START,
  E<PERSON>,
  MemorySaver,
  type StateGraphArgs,
} from "@langchain/langgraph";
import { OnboardingState, onboardingChannels, SidebarStateData } from "./state";
import {
  AIMessage,
  BaseMessage,
  HumanMessage,
  SystemMessage,
} from "@langchain/core/messages";
import { RunnableLambda } from "@langchain/core/runnables";
import { createAzureChatOpenAI } from "../lib/global-azure-openai";
import { PlanDetails } from "@/types/plan"; // Import the TS Interface
import { informationGathererNode } from "./nodes/informationGathererNode";
import { needInputNode } from "./nodes/needInputNode";
import { checkCompletionNode } from "./nodes/checkCompletionNode"; // Import the new node
import { generatePlanNode } from "./nodes/generatePlanNode"; // Import the separated node

// Initialize LLM instance
const llm = createAzureChatOpenAI({ temperature: 0.3 });

// Define the checkpointer
const memorySaver = new MemorySaver();

/**
 * Creates a LangGraph for onboarding using LLM nodes.
 */
export function createOnboardingGraph() {
  const graphArgs: StateGraphArgs<OnboardingState> = {
    channels: onboardingChannels as any,
  };
  const graph = new StateGraph<OnboardingState>(graphArgs);

  // Add nodes
  graph.addNode("gatherInfo", informationGathererNode as any);
  graph.addNode("checkCompletion", checkCompletionNode as any); // Add the new node
  graph.addNode("needInput", needInputNode as any);
  graph.addNode("generatePlan", generatePlanNode as any);

  // Define edges
  graph.addEdge(START, "gatherInfo" as any);
  graph.addEdge("gatherInfo" as any, "checkCompletion" as any); // Go to check after gathering

  // Conditional edge after checkCompletion node runs
  graph.addConditionalEdges(
    "checkCompletion" as any, // Source is the new check node
    (state: OnboardingState): "generatePlan" | "needInput" => {
      // Check the hasEnoughInfo flag set by checkCompletionNode
      if (state.hasEnoughInfo === true) {
        console.log(
          "[Graph Router] hasEnoughInfo is true. Routing to generatePlan.",
        );
        return "generatePlan";
      }
      // Otherwise, signal that we need user input
      console.log(
        "[Graph Router] hasEnoughInfo is false. Routing to needInput.",
      );
      return "needInput";
    },
    {
      generatePlan: "generatePlan" as any,
      needInput: "needInput" as any, // Route to needInput if check fails
    },
  );

  // Edge from needInput to END (Interrupts to wait for user)
  graph.addEdge("needInput" as any, END);

  // Edge from generatePlan to END
  graph.addEdge("generatePlan" as any, END);

  // Compile the graph
  return graph.compile({
    checkpointer: memorySaver,
  });
}
