import { NextRequest, NextResponse } from "next/server";

/**
 * Redirect handler for the deprecated /api/supervision route
 * Forwards all requests to the new /api/coaching endpoint
 */
export async function GET(req: NextRequest) {
  console.log(
    "API REDIRECT: Redirecting from /api/supervision to /api/coaching",
  );

  // Copy all search parameters
  const url = new URL(req.url);
  url.pathname = url.pathname.replace("/api/supervision", "/api/coaching");

  return NextResponse.redirect(url);
}

export async function POST(req: NextRequest) {
  console.log(
    "API REDIRECT: Redirecting from /api/supervision to /api/coaching",
  );

  // Copy all search parameters
  const url = new URL(req.url);
  url.pathname = url.pathname.replace("/api/supervision", "/api/coaching");

  // Need to clone the request body for forwarding
  const body = await req.text();

  // Forward the request to the new endpoint with the same body
  return NextResponse.redirect(url, {
    headers: {
      "Content-Type": req.headers.get("Content-Type") || "application/json",
    },
  });
}
