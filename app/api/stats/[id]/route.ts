import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

interface Params {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(req: NextRequest, props: Params) {
  const params = await props.params;
  console.log("🔍 GET request received for stats");
  console.log("User ID param:", params.id);

  try {
    const userId = params.id;
    console.log("Processing request for user:", userId);

    if (!userId) {
      console.error("❌ No user_id provided");
      return NextResponse.json({ error: "Missing user_id" }, { status: 400 });
    }

    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    console.log("Query parameters:", { limit, offset, startDate, endDate });

    const client = await clientPromise;
    console.log("📡 Connected to MongoDB");

    const db = client.db("AthleaUserData");
    const collection = db.collection("stats");

    // Build query with optional date filtering
    const query: any = { user_id: userId };
    if (startDate && endDate) {
      query.startDate = { $gte: new Date(startDate) };
      query.endDate = { $lte: new Date(endDate) };
    }

    console.log("MongoDB query:", query);

    const total = await collection.countDocuments(query);
    console.log("Total matching documents:", total);

    const stats = await collection
      .find(query)
      .sort({ startDate: -1 })
      .skip(offset)
      .limit(limit)
      .toArray();

    console.log(`✅ Successfully retrieved ${stats.length} stats records`);

    return NextResponse.json({
      success: true,
      stats,
      pagination: {
        total,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error("❌ Error retrieving stats:", error);
    return NextResponse.json(
      {
        error: "Failed to retrieve stats",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
