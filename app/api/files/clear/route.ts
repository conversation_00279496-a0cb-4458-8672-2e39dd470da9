import { NextRequest, NextResponse } from "next/server";
import clientPromise from "@/app/lib/mongodb";

export async function POST(request: NextRequest) {
  try {
    const { user_id, clear_documents, clear_stats, dev } = await request.json();

    if (!user_id) {
      return NextResponse.json(
        { error: "user_id is required" },
        { status: 400 },
      );
    }

    // Only allow clearing in development mode
    if (!dev) {
      return NextResponse.json(
        { error: "File clearing only allowed in development mode" },
        { status: 403 },
      );
    }

    console.log(`[DEV] Clearing files for user ${user_id}`);

    const client = await clientPromise;
    const db = client.db("AthleaUserData");

    let documentsDeleted = 0;
    let statsDeleted = 0;

    // Clear documents collection
    if (clear_documents) {
      const documentsCollection = db.collection("documents");
      const documentsResult = await documentsCollection.deleteMany({
        userId: user_id,
      });
      documentsDeleted = documentsResult.deletedCount;
      console.log(
        `[DEV] Deleted ${documentsDeleted} documents for user ${user_id}`,
      );
    }

    // Clear stats collection
    if (clear_stats) {
      const statsCollection = db.collection("stats");
      const statsResult = await statsCollection.deleteMany({
        user_id: user_id,
      });
      statsDeleted = statsResult.deletedCount;
      console.log(`[DEV] Deleted ${statsDeleted} stats for user ${user_id}`);
    }

    return NextResponse.json({
      success: true,
      message: "Files cleared successfully",
      deleted: {
        documents: documentsDeleted,
        stats: statsDeleted,
      },
    });
  } catch (error) {
    console.error("[DEV] Error clearing files:", error);
    return NextResponse.json(
      { error: "Failed to clear files" },
      { status: 500 },
    );
  }
}
