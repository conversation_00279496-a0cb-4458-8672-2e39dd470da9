import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

const Home: React.FC = () => {
    return (
        <div className='flex flex-row h-full w-full justify-center items-center absolute top-0 left-0 right-0 left-0'>
            <div className='' style={{ width: "60%", height: '100vh' }}>
                <img src='/images/login/LoginImage.png' alt="home brand Logo" style={{ height: '100vh', objectFit: "cover" }} />
            </div>
            <div className='flex flex-col bg-[#fafafa]' style={{ width: "40%", height: '100vh' }}>
                <div className='flex flex-col justify-center items-center' style={{ height: '85%', width: "100%" }}>
                    <div className="mb-5 flex flex-row font-bold text-[#FF81A8] dark:text-white" style={{ fontSize: 40 }}>
                        Get started
                    </div>
                    <div className='flex flex-row justify-center items-center' style={{ width: "100%" }}>
                        <Link href="/auth/signin" style={{ width: '30%', marginRight: 20 }}>
                            <button className={`flex h-12 cursor-pointer items-center justify-center rounded-full bg-[#FF81A8] hover:bg-[#fb7185] text-base font-medium text-white`}
                                style={{ width: '100%' }}
                            >
                                Log in
                            </button>
                        </Link>
                        <Link href="/auth/signup" style={{ width: '30%' }}>
                            <button className={`flex h-12 cursor-pointer items-center justify-center rounded-full bg-[#FF81A8] hover:bg-[#fb7185] text-base font-medium text-white`}
                                style={{ width: '100%' }}
                            >
                                Sign up
                            </button>
                        </Link>
                    </div>
                </div>
                <div className='flex flex-col justify-center items-center' style={{ width: '100%', height: "15%" }}>
                    <Image
                        className=""
                        src={"/images/logo/Athlea__Black.svg"}
                        alt="athlea Logo"
                        width={100}
                        height={40}
                    />
                    <div className='flex justify-center items-center mt-6'>
                        <p className="font-medium underline">
                            Terms of use
                        </p>
                        <p className="font-medium mx-5">
                            |
                        </p>
                        <p className="font-medium underline">
                            Privacy policy
                        </p>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Home