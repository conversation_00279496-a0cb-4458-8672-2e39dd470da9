"use client";

import { MarkdownComponents } from "@/components/Chat/MarkdownComponents";
import {
  AIMessage,
  // PlannerMessage, // These are handled by AIMessage based on domain
  // ReasoningMessage,
  UserMessage,
} from "@/components/Chat/MessageTypes"; // Corrected path
import {
  Message,
  StreamEventData,
  ToolCall,
  isPlannerOrReasoningDomain,
  MessageDomain,
} from "@/app/types/chat";
import { getFormattedTime } from "@/app/utils/formatUtils";
import { fetchKeywords } from "@/app/utils/keywordUtils";
import SystemMessage from "@/components/Chat/Messages/SystemMessage";
import OnboardingSidebar from "@/components/Chat/OnboardingSidebar";
import ToolSidebar from "@/components/Chat/ToolSidebar";
import UserInput from "@/components/Chat/UserInput";
import PlanGenerationSidebar from "@/components/PlanGenerationSidebar";
import { Card, CardContent } from "@/components/ui/cards";
import { useKeyword } from "@/context/KeywordContext";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { updatePlanNameThunk } from "@/store/slices/chatSlice";
import {
  setNeedInitialMessages,
  setSidebarItemName,
  setInitialMessage,
} from "@/store/slices/jobSlice";
import { RootState } from "@/store/store";
import { debounce } from "lodash";
import dynamic from "next/dynamic";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { SidebarStateData } from "@/app/api/onboarding/state";
import { useQuery } from "@tanstack/react-query";

// Add a utility function to help with debugging
const DEBUG = false; // Set to true to enable debug logs
const debugLog = (...args: any[]) => {
  if (DEBUG) {
    console.log("[StreamDebug]", ...args);
  }
};

const LocationMap = dynamic(() => import("@/components/Chat/Map/LocationMap"), {
  ssr: false,
  loading: () => <p>Loading map...</p>,
});

export default function LangGraphChatPage() {
  const params = useParams<{ id: string }>();
  const threadIdFromUrl = params?.id;
  const router = useRouter();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { initialMessage, needInitialMessages, selectedCoach } = useAppSelector(
    (state: RootState) => state.job,
  );

  const { setKeyWordList } = useKeyword();

  const planTitle = useAppSelector((state) => state.job.planTitle);
  const lastPlanNameUpdateTime = useAppSelector(
    (state) => state.chat.lastPlanNameUpdateTime,
  );

  const userData = useSelector((state: RootState) => state.user.userData);
  const userId = userData?.user_id;

  const [messages, setMessages] = useState<Message[]>([]);
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [collapsedMessages, setCollapsedMessages] = useState<
    Record<string, boolean>
  >({});
  const wasDisabledRef = useRef<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const toolCallsEndRef = useRef<HTMLDivElement>(null);
  const currentAgentMessageIdsRef = useRef<Record<string, string>>({});
  const hasReceivedFirstTokenThisTurn = useRef(false);
  const lastStreamingAgentRef = useRef<string | null>(null);
  const submissionInProgressRef = useRef(false);
  const currentTurnPlannerMsgIdRef = useRef<string | null>(null);
  const initialCallAttemptedRef = useRef(false);
  const [messageDone, setMessageDone] = useState(false);

  const eventSourceRef = useRef<EventSource | null>(null);

  const [sidebarData, setSidebarData] = useState<SidebarStateData | null>(null);
  console.log("---sidebarData---", sidebarData);

  // Add waitingForInput state and inputPrompt for onboarding
  const [waitingForInput, setWaitingForInput] = useState(false);
  const [inputPrompt, setInputPrompt] = useState("");

  // Track sidebarData changes
  useEffect(() => {
    console.log("[SIDEBAR STATE DEBUG] sidebarData changed:", {
      has_sidebarData: !!sidebarData,
      sportSuggestions: sidebarData?.sportSuggestions,
      sportSuggestions_count: sidebarData?.sportSuggestions?.length || 0,
      selectedSports: sidebarData?.selectedSports,
      selectedSports_count: sidebarData?.selectedSports?.length || 0,
      currentStage: sidebarData?.currentStage,
    });
  }, [sidebarData]);

  // Development and resume state
  const [onboardingStatus, setOnboardingStatus] = useState<{
    status: "not_started" | "in_progress" | "completed" | "loading";
    stage?: string;
    message?: string;
    canResume?: boolean;
  }>({ status: "loading" });

  // Development controls
  const isDevelopment = process.env.NODE_ENV === "development";
  const forceReset = searchParams?.get("reset") === "true";
  const skipResume = searchParams?.get("skip_resume") === "true";

  // Functions for onboarding state management
  const checkOnboardingStatus = useCallback(async () => {
    if (!userId || !threadIdFromUrl) return;

    try {
      console.log(
        `[RESUME] Checking onboarding status for user ${userId}, thread ${threadIdFromUrl}`,
      );

      // Check our frontend database for existing onboarding data
      console.log(`[RESUME] Checking frontend database for existing data`);
      const frontendResponse = await fetch(
        `/api/users/${userId}?onboarding_data=true`,
      );

      if (frontendResponse.ok) {
        const frontendResult = await frontendResponse.json();
        console.log("[RESUME] Frontend API response:", frontendResult);

        // Check if we have onboarding data - look for sidebar data, thread ID, or last updated
        const hasOnboardingData = !!(
          frontendResult.onboarding_sidebar_data &&
          frontendResult.onboarding_thread_id &&
          frontendResult.onboarding_last_updated
        );

        if (hasOnboardingData) {
          const savedThreadId = frontendResult.onboarding_thread_id;
          console.log(
            `[RESUME] Found saved thread ID: ${savedThreadId}, current: ${threadIdFromUrl}`,
          );

          // If we have a different thread ID saved, redirect immediately
          if (savedThreadId !== threadIdFromUrl) {
            console.log(
              `[RESUME] Different thread ID detected, redirecting to original: ${savedThreadId}`,
            );
            router.replace(`/onboarding/${savedThreadId}?onboarding=true`);
            return true; // Indicates we're redirecting
          } else {
            // Same thread ID, set status to in progress and continue
            console.log(
              `[RESUME] Same thread ID, existing data found - setting status to in progress`,
            );
            setOnboardingStatus({
              status: "in_progress",
              stage: "resumed",
              message: "Existing onboarding data found",
              canResume: true,
            });
            return true; // Indicates we have existing data
          }
        } else {
          // No existing data found
          console.log(`[RESUME] No existing onboarding data found in frontend`);
          setOnboardingStatus({
            status: "not_started",
            stage: "initial",
            message: "No existing onboarding data",
            canResume: false,
          });
          return false; // No resume needed
        }
      } else {
        console.error(
          `[RESUME] Frontend API error: ${frontendResponse.status}`,
        );
        setOnboardingStatus({ status: "not_started" });
        return false;
      }
    } catch (error) {
      console.error("[RESUME] Error checking onboarding status:", error);
      // On error, assume not started
      setOnboardingStatus({ status: "not_started" });
      return false;
    }
  }, [userId, threadIdFromUrl, router]);

  const resetOnboarding = useCallback(async () => {
    if (!userId || !threadIdFromUrl) return;

    try {
      console.log(
        `[RESUME] Resetting onboarding for user ${userId}, thread ${threadIdFromUrl}`,
      );
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_PYTHON_LANGGRAPH_URL || "http://localhost:8000"}/api/onboarding/reset/${userId}?thread_id=${threadIdFromUrl}`,
        {
          method: "POST",
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log("[RESUME] Reset result:", result);

      setOnboardingStatus({ status: "not_started" });

      // Clear any existing messages and sidebar data
      setMessages([]);
      setSidebarData(null);
    } catch (error) {
      console.error("[RESUME] Error resetting onboarding:", error);
    }
  }, [userId, threadIdFromUrl]);

  const clearOnboardingData = useCallback(async () => {
    if (!userId) return;

    try {
      console.log(`[DEV] Clearing onboarding data for user ${userId}`);

      // Step 1: Clear onboarding sidebar data from user profile
      const response = await fetch("/api/users", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: userId,
          action: "clear_onboarding",
          dev: true, // Development flag
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log("[DEV] Clear onboarding data result:", result);

      // Step 2: Clear uploaded files from MongoDB collections
      console.log(`[DEV] Clearing uploaded files for user ${userId}`);
      try {
        const filesResponse = await fetch("/api/files/clear", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: userId,
            clear_documents: true,
            clear_stats: true,
            dev: true,
          }),
        });

        if (filesResponse.ok) {
          console.log("[DEV] Uploaded files cleared successfully");
        } else {
          console.warn(
            "[DEV] Failed to clear uploaded files, but continuing...",
          );
        }
      } catch (error) {
        console.warn("[DEV] Error clearing uploaded files:", error);
      }

      // Step 3: Clear messages for this thread if we have a threadId
      if (threadIdFromUrl) {
        console.log(`[DEV] Clearing messages for thread ${threadIdFromUrl}`);
        try {
          const messagesResponse = await fetch(
            `/api/messages/${threadIdFromUrl}?clear_all=true`,
            {
              method: "DELETE",
            },
          );

          if (messagesResponse.ok) {
            console.log("[DEV] Messages cleared successfully");
          } else {
            console.warn("[DEV] Failed to clear messages, but continuing...");
          }
        } catch (error) {
          console.warn("[DEV] Error clearing messages:", error);
        }
      }

      // Step 4: Clear onboarding files from Python backend filesystem
      console.log(`[DEV] Clearing onboarding files from Python backend`);
      try {
        const pythonBackendUrl =
          process.env.NEXT_PUBLIC_PYTHON_LANGGRAPH_URL ||
          "http://localhost:8000";

        const backendFilesResponse = await fetch(
          `${pythonBackendUrl}/api/onboarding/clear-files/${userId}`,
          {
            method: "DELETE",
          },
        );

        if (backendFilesResponse.ok) {
          console.log(
            "[DEV] Python backend onboarding files cleared successfully",
          );
        } else {
          console.warn(
            "[DEV] Failed to clear Python backend files, but continuing...",
          );
        }
      } catch (error) {
        console.warn("[DEV] Error clearing Python backend files:", error);
      }

      // Step 5: Reset the onboarding flow in Python backend
      await resetOnboarding();

      // Step 6: Clear local state and prepare for fresh start
      setMessages([]);
      setSidebarData(null);
      setToolCalls([]);

      // Step 7: Navigate with fresh_start parameter to trigger initial message
      const freshStartUrl = `/onboarding/${threadIdFromUrl}?fresh_start=true&direct_onboarding=true`;

      console.log(
        "✅ Development: Onboarding data, messages, and files cleared! Navigating to fresh start...",
      );
      console.log(`[DEV] Navigating to: ${freshStartUrl}`);

      // Use router.push for more reliable navigation
      router.push(freshStartUrl);
    } catch (error) {
      console.error("[DEV] Error clearing onboarding data:", error);
      alert("❌ Error clearing onboarding data. Check console for details.");
    }
  }, [userId, threadIdFromUrl, resetOnboarding, router]);

  const loadExistingOnboardingData = useCallback(async () => {
    if (!userId) return null;

    try {
      console.log(
        `[RESUME] Loading existing onboarding data for user ${userId}`,
      );
      const response = await fetch(`/api/users/${userId}?onboarding_data=true`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log("=== LOAD EXISTING ONBOARDING DATA DEBUG ===");
      console.log("[RESUME] Raw API response:", result);
      console.log("[RESUME] Response structure:", {
        has_data: result.has_data,
        has_data_type: typeof result.has_data,
        onboarding_sidebar_data_exists: !!result.onboarding_sidebar_data,
        onboarding_sidebar_data_type: typeof result.onboarding_sidebar_data,
        onboarding_sidebar_data_content: result.onboarding_sidebar_data,
        onboarding_thread_id: result.onboarding_thread_id,
        all_keys: Object.keys(result),
      });

      // Check if we have onboarding data - look for sidebar data, thread ID, or last updated
      const hasOnboardingData = !!(
        result.onboarding_sidebar_data &&
        result.onboarding_thread_id &&
        result.onboarding_last_updated
      );

      if (hasOnboardingData) {
        console.log("[RESUME] ✅ Found existing sidebar data, setting it");
        console.log(
          "[RESUME] Sidebar data to be set:",
          result.onboarding_sidebar_data,
        );

        // Call setSidebarData with detailed logging
        console.log("[RESUME] 🔄 Calling setSidebarData...");
        setSidebarData(result.onboarding_sidebar_data);
        console.log("[RESUME] ✅ setSidebarData called successfully");

        // Check if we need to redirect to the original thread ID
        const savedThreadId = result.onboarding_thread_id;
        if (savedThreadId && savedThreadId !== threadIdFromUrl) {
          console.log(
            `[RESUME] Redirecting to original thread ID: ${savedThreadId} (current: ${threadIdFromUrl})`,
          );
          // Redirect to the original thread ID to resume with the same conversation
          router.replace(`/onboarding/${savedThreadId}?onboarding=true`);
          return result.onboarding_sidebar_data;
        }

        console.log("[RESUME] ✅ Sidebar data loaded and set successfully");
        return result.onboarding_sidebar_data;
      } else {
        console.log("[RESUME] ❌ No existing sidebar data found", {
          onboarding_sidebar_data_exists: !!result.onboarding_sidebar_data,
          onboarding_thread_id_exists: !!result.onboarding_thread_id,
          onboarding_last_updated_exists: !!result.onboarding_last_updated,
          why_not_loaded: !result.onboarding_sidebar_data
            ? "onboarding_sidebar_data is missing"
            : !result.onboarding_thread_id
              ? "onboarding_thread_id is missing"
              : "onboarding_last_updated is missing",
        });
        return null;
      }
    } catch (error) {
      console.error(
        "[RESUME] ❌ Error loading existing onboarding data:",
        error,
      );
      return null;
    } finally {
      console.log("=== LOAD EXISTING ONBOARDING DATA DEBUG END ===");
    }
  }, [userId, threadIdFromUrl, router]);

  const toggleCollapse = (messageId: string) => {
    setCollapsedMessages((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
  };

  const toggleToolCallExpanded = (id: string) => {
    setToolCalls((prev) =>
      prev.map((tool) =>
        tool.id === id ? { ...tool, expanded: !tool.expanded } : tool,
      ),
    );
  };

  const processBatchKeywords = useCallback(
    (keywords: string[]) => {
      if (keywords && keywords.length > 0) {
        console.log("Processing batch keywords:", keywords);
        setKeyWordList(keywords);
      }
    },
    [setKeyWordList],
  );

  const componentMarkdownComponents = {
    ...MarkdownComponents,
  };

  const processEventSource = useCallback(
    async (eventSourceUrl: string) => {
      console.log(
        "CLIENT: [processEventSource ENTRY] Attempting to connect to:",
        eventSourceUrl,
      );

      // Close existing connection if any
      if (eventSourceRef.current) {
        console.log("Closing previous EventSource connection.");
        eventSourceRef.current.close();
      }

      return new Promise<void>((resolve, reject) => {
        console.log(
          "CLIENT: [processEventSource] Creating new EventSource object...",
        );
        const newEventSource = new EventSource(eventSourceUrl);
        eventSourceRef.current = newEventSource; // Store in ref

        let connectionEstablished = false;
        let streamCompleted = false; // Track if stream has completed
        let receivedNeedInput = false; // Track if we received need_input event

        newEventSource.onopen = () => {
          console.log(
            "CLIENT: [processEventSource] SSE Connection Opened (onopen triggered)",
          );
          connectionEstablished = true;
          setIsStreaming(true);
          currentAgentMessageIdsRef.current = {};
        };

        newEventSource.onerror = (error) => {
          console.error(
            "CLIENT: [processEventSource] SSE Connection Error (onerror triggered):",
            error,
            "ReadyState:",
            newEventSource.readyState,
          );

          // If stream already completed successfully, ignore the error
          if (streamCompleted) {
            console.log(
              "CLIENT: [processEventSource] Ignoring error after successful stream completion",
            );
            resolve();
            return;
          }

          if (isLoading || isStreaming) {
            setIsLoading(false);
            setIsStreaming(false);
            try {
              newEventSource.close();
            } catch (e) {
              console.warn(
                "CLIENT: Error closing EventSource in onerror (might be already closed):",
                e,
              );
            }
            setMessages((prev) => {
              const lastMsg = prev[prev.length - 1];
              if (lastMsg?.id?.startsWith("err-")) return prev;
              const messagesWithoutIndicator = prev.filter(
                (msg) => msg.id !== "typing-indicator",
              );
              return [
                ...messagesWithoutIndicator,
                {
                  id: `err-connect-${Date.now()}`,
                  type: "system",
                  domain: "general",
                  content: "Connection error. Please try again.",
                  lastUpdated: Date.now(),
                },
              ];
            });
            currentAgentMessageIdsRef.current = {};
            lastStreamingAgentRef.current = null;
            reject(new Error("SSE connection error"));
          }
        };

        newEventSource.onmessage = async (event) => {
          console.log(
            "CLIENT: [processEventSource] SSE Message received (onmessage triggered)",
          );

          // Skip processing if stream already completed
          if (streamCompleted) {
            console.log(
              "CLIENT: [SSE] Ignoring event after stream completion:",
              event.type,
            );
            return;
          }

          try {
            debugLog(
              "Raw SSE event data:",
              event.data.substring(0, 100) +
                (event.data.length > 100 ? "..." : ""),
            );
            // Use the updated shared StreamEventData type
            const eventData = JSON.parse(event.data) as StreamEventData;
            debugLog("Parsed event type:", eventData.type);

            switch (eventData.type) {
              case "agent_start":
                if (eventData.agent) {
                  const agentDomain = eventData.agent as MessageDomain;
                  console.log(
                    `CLIENT: Agent domain '${agentDomain}' starting.`,
                  );

                  // Register agent start, await first token for message creation
                  currentAgentMessageIdsRef.current[agentDomain] = "";
                  console.log(
                    `CLIENT: Agent domain ${agentDomain} started. Awaiting first token to create message bubble.`,
                  );
                }
                break;

              case "token":
                console.log(
                  "CLIENT: Received token event:",
                  eventData.agent ? `Agent: ${eventData.agent}` : "No agent",
                  eventData.content
                    ? `Content (first 30 chars): ${eventData.content.substring(0, 30)}...`
                    : "No content",
                );
                if (eventData.agent && eventData.content) {
                  const agentDomain = eventData.agent as MessageDomain;
                  const tokenContent = eventData.content;
                  debugLog(
                    `Token from \"${agentDomain}\": \"${tokenContent.substring(0, 30)}${tokenContent.length > 30 ? "..." : ""}\"`,
                  );

                  if (agentDomain === "planner") {
                    console.log(
                      `CLIENT: Received token for 'planner' domain. Content: ${tokenContent.substring(0, 50)}...`,
                    );
                  }

                  setMessages((prevMessages) => {
                    console.log(
                      `[Token Handler] Processing token for domain: ${agentDomain}. Current message ID in ref: ${currentAgentMessageIdsRef.current[agentDomain] || "none"}`,
                    );
                    let updatedMessages = [...prevMessages];
                    const domain = agentDomain;
                    console.log(
                      `[Token Handler] Using domain from event: "${domain}"`,
                    );
                    const messageName = domain === "Athlea" ? "Athlea" : domain;

                    const isPlanner = domain === "planner";
                    const isReasoning = domain === "reasoning";
                    let messageId = currentAgentMessageIdsRef.current[domain];
                    let existingMsgIndex = -1;

                    // Try to find the message using the ID from the ref *before* modifying updatedMessages
                    if (messageId) {
                      existingMsgIndex = updatedMessages.findIndex(
                        (msg) => msg.id === messageId,
                      );
                    }

                    if (hasReceivedFirstTokenThisTurn.current === false) {
                      console.log(
                        "[Token Handler] First token of the turn received. Removing typing indicator(s).",
                      );
                      updatedMessages = updatedMessages.filter(
                        (msg) =>
                          !(
                            typeof msg.id === "string" &&
                            msg.id.startsWith("typing-indicator-")
                          ) && msg.id !== "typing-indicator",
                      );
                      // Re-calculate index after filtering if needed, although it might be safer to filter *after* message creation/update logic
                      if (messageId) {
                        existingMsgIndex = updatedMessages.findIndex(
                          (msg) => msg.id === messageId,
                        );
                      }
                      hasReceivedFirstTokenThisTurn.current = true;
                    }

                    // --- Revised Logic ---
                    if (messageId && existingMsgIndex > -1) {
                      // --- Case 1: Message ID exists in ref AND was found in prevMessages ---
                      console.log(
                        `[Token Handler - Update] Updating existing message ${messageId} for domain ${domain}.`,
                      );
                      const isFirstUpdate =
                        updatedMessages[existingMsgIndex].content === ""; // Check if it was just an empty shell

                      updatedMessages[existingMsgIndex] = {
                        ...updatedMessages[existingMsgIndex],
                        ...(isFirstUpdate && {
                          // Apply name/domain only on first actual content update if needed
                          name: messageName,
                          domain: domain,
                          isLoading: false, // Ensure loading is off
                        }),
                        content:
                          updatedMessages[existingMsgIndex].content +
                          tokenContent,
                        lastUpdated: Date.now(),
                        metadata: {
                          ...(updatedMessages[existingMsgIndex].metadata || {}),
                          hideUntilContent: false,
                        },
                      };

                      // Planner/Reasoning specific logic for EXISTING messages
                      if (
                        isPlanner &&
                        isFirstUpdate &&
                        currentTurnPlannerMsgIdRef.current !== messageId
                      ) {
                        console.log(
                          "[Token Handler - Update] Tracking current turn planner message ID:",
                          messageId,
                        );
                        currentTurnPlannerMsgIdRef.current = messageId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageId];
                          return newCollapsed;
                        });
                      }
                    } else if (messageId && existingMsgIndex === -1) {
                      // --- Case 2: Message ID exists in ref BUT wasn't found in prevMessages (or after filtering) ---
                      // Create the message using the ID from the ref.
                      console.log(
                        `[Token Handler - Create (Delayed)] First effective token for ${domain}. Creating message bubble with stored ID: ${messageId}`,
                      );
                      const newMessage: Message = {
                        id: messageId, // Use the ID from the ref
                        type: "ai",
                        domain: domain,
                        name: messageName,
                        content: tokenContent, // Start with the current token's content
                        isLoading: false,
                        lastUpdated: Date.now(),
                        metadata: {
                          isCollapsed:
                            isPlanner || isReasoning ? false : undefined,
                          hideUntilContent: false,
                        },
                      };
                      updatedMessages.push(newMessage);
                      // No need to update the ref here, it already holds the correct ID.

                      // Planner specific logic for NEW messages created this way
                      if (isPlanner) {
                        console.log(
                          "[Token Handler - Create (Delayed)] Tracking current turn planner message ID:",
                          messageId,
                        );
                        currentTurnPlannerMsgIdRef.current = messageId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageId];
                          return newCollapsed;
                        });
                      }
                    } else if (!messageId) {
                      // --- Case 3: No Message ID in ref ---
                      // This is the *very first* token received for this agent in this turn.
                      messageId = `msg-${domain}-${Date.now()}`;
                      console.log(
                        `[Token Handler - Create (Initial)] First token for ${domain}. Creating new message bubble with ID: ${messageId}`,
                      );
                      currentAgentMessageIdsRef.current[domain] = messageId; // Store the new ID
                      const newMessage: Message = {
                        id: messageId,
                        type: "ai",
                        domain: domain,
                        name: messageName,
                        content: tokenContent,
                        isLoading: false,
                        lastUpdated: Date.now(),
                        metadata: {
                          isCollapsed:
                            isPlanner || isReasoning ? false : undefined,
                          hideUntilContent: false,
                        },
                      };
                      updatedMessages.push(newMessage);

                      // Planner specific logic for NEW messages created initially
                      if (isPlanner) {
                        console.log(
                          "[Token Handler - Create (Initial)] Tracking current turn planner message ID:",
                          messageId,
                        );
                        currentTurnPlannerMsgIdRef.current = messageId;
                        setCollapsedMessages((prevCollapsed) => {
                          const newCollapsed = { ...prevCollapsed };
                          delete newCollapsed[messageId];
                          return newCollapsed;
                        });
                      }
                    } else {
                      // Should not happen with the logic above, but add a safeguard log
                      console.error(
                        `[Token Handler - Error] Unexpected state: messageId=${messageId}, existingMsgIndex=${existingMsgIndex}`,
                      );
                    }

                    // Collapse logic (can run after message creation/update)
                    if (!isPlanner && currentTurnPlannerMsgIdRef.current) {
                      console.log(
                        `[Token Handler] Coach (${agentDomain}) token received. Collapsing planner message: ${currentTurnPlannerMsgIdRef.current}`,
                      );
                      const plannerMsgIdToCollapse =
                        currentTurnPlannerMsgIdRef.current;
                      setCollapsedMessages((prev) => ({
                        ...prev,
                        [plannerMsgIdToCollapse]: true,
                      }));
                      currentTurnPlannerMsgIdRef.current = null; // Clear after collapsing
                    }

                    if (!isReasoning) {
                      const reasoningMsgsToCollapse = updatedMessages.filter(
                        // Check potentially updated list
                        (msg) =>
                          msg.domain === "reasoning" &&
                          !collapsedMessages[msg.id], // Only collapse if not already set
                      );
                      if (reasoningMsgsToCollapse.length > 0) {
                        console.log(
                          `[Token Handler] Non-reasoning agent (${agentDomain}) token received. Collapsing ${reasoningMsgsToCollapse.length} reasoning messages.`,
                        );
                        setCollapsedMessages((prev) => {
                          const updatedCollapsed = { ...prev };
                          reasoningMsgsToCollapse.forEach((msg) => {
                            updatedCollapsed[msg.id] = true;
                          });
                          return updatedCollapsed;
                        });
                      }
                    }

                    lastStreamingAgentRef.current = domain;
                    setIsLoading(false);
                    return updatedMessages;
                  });
                } else {
                  debugLog(
                    "Received token with missing agent or content",
                    eventData,
                  );
                }
                break;

              case "tool_result": {
                const { agent, toolName, content } = eventData;
                const domain = (agent || "general") as MessageDomain;
                const timestamp = Date.now();
                const toolCallId = `${toolName}-${timestamp}`;
                const contentAsAny = content as any;
                const extractedContent =
                  contentAsAny &&
                  typeof contentAsAny === "object" &&
                  contentAsAny.kwargs &&
                  contentAsAny.kwargs.content
                    ? contentAsAny.kwargs.content
                    : contentAsAny;

                setToolCalls((prev) => [
                  ...prev,
                  {
                    id: toolCallId,
                    domain,
                    toolName: toolName || "unknown_tool",
                    timestamp,
                    content: extractedContent,
                  },
                ]);
                break;
              }

              case "need_input":
              case "needs_input":
                debugLog("Received need_input event:", eventData);
                console.log(
                  "CLIENT: Received need_input event:",
                  "Prompt:",
                  eventData.prompt || "None",
                  "Field:",
                  eventData.field || "None",
                );
                // Set flag to indicate we received need_input
                receivedNeedInput = true;
                // Set waitingForInput and inputPrompt for onboarding
                setInputPrompt(eventData.prompt || "Please provide input.");
                setWaitingForInput(true);
                setIsLoading(false);
                setIsStreaming(false);
                newEventSource.close();
                currentAgentMessageIdsRef.current = {};
                setMessages((prev) => {
                  console.log(
                    "CLIENT: Cleaning up typing indicators after need_input",
                  );
                  return prev.filter(
                    (msg) =>
                      msg.id !== "typing-indicator" &&
                      !(
                        typeof msg.id === "string" &&
                        msg.id.startsWith("typing-indicator-")
                      ),
                  );
                });
                break;

              case "complete":
              case "done":
                // Mark stream as completed to prevent further processing
                streamCompleted = true;
                console.log(
                  "CLIENT: [SSE] Stream marked as completed, type:",
                  eventData.type,
                );

                // Set messageDone to trigger save effect
                setMessageDone(true);

                setMessages((prevMessages) => {
                  const finalMessages = prevMessages.filter(
                    (msg) =>
                      msg.id !== "typing-indicator" &&
                      !(
                        typeof msg.id === "string" &&
                        msg.id.startsWith("typing-indicator-")
                      ),
                  );

                  // Restore Keyword fetching logic
                  finalMessages.forEach((msg) => {
                    if (
                      msg.type === "ai" &&
                      msg.content &&
                      msg.content.trim() !== "" &&
                      !msg.keywords &&
                      msg.domain !== "reasoning" &&
                      msg.domain !== "planner"
                    ) {
                      console.log(
                        `[SSE Done Handler] Triggering keyword fetch for message: ${msg.id}`,
                      );
                      // Pass necessary state/functions to fetchKeywords
                      fetchKeywords(
                        msg.id,
                        msg.content,
                        threadIdFromUrl, // Assuming threadIdFromUrl is accessible here
                        finalMessages, // Pass the filtered messages
                        setMessages, // Pass the state setter
                        processBatchKeywords, // Pass the callback
                      );
                    }
                  });

                  return finalMessages;
                });

                hasReceivedFirstTokenThisTurn.current = false;
                currentAgentMessageIdsRef.current = {};
                currentTurnPlannerMsgIdRef.current = null;

                setIsLoading(false);
                setIsStreaming(false);
                // Only reset waitingForInput if we didn't receive a need_input event
                if (!receivedNeedInput) {
                  setWaitingForInput(false);
                  console.log(
                    "CLIENT: Resetting waitingForInput to false on completion",
                  );
                } else {
                  console.log(
                    "CLIENT: Preserving waitingForInput=true due to need_input event",
                  );
                }
                try {
                  newEventSource.close();
                  console.log(
                    "CLIENT: 'done'/'complete' received, closed EventSource.",
                  );
                } catch (e) {
                  console.warn(
                    "CLIENT: Error closing EventSource in 'done'/'complete' handler:",
                    e,
                  );
                }
                lastStreamingAgentRef.current = null;

                // Resolve the Promise to complete the stream
                resolve();
                break;

              case "error":
                // ... (existing error logic) ...
                console.error(
                  "CLIENT: Received error from server:",
                  eventData.message,
                );
                setIsLoading(false);
                setIsStreaming(false);
                newEventSource.close();
                setMessages((prev) => {
                  const indicator = prev.find(
                    (msg) => msg.id === "typing-indicator",
                  );
                  let messagesWithoutIndicator = prev;
                  if (
                    indicator &&
                    indicator.content &&
                    indicator.content.length > 0
                  ) {
                    messagesWithoutIndicator = prev.filter(
                      (msg) => msg.id !== "typing-indicator",
                    );
                  }
                  return [
                    ...messagesWithoutIndicator,
                    {
                      id: `err-${Date.now()}`,
                      type: "system",
                      domain: "general",
                      content: `Error: ${eventData.message || "Unknown server error"}`,
                      lastUpdated: Date.now(),
                    },
                  ];
                });
                currentAgentMessageIdsRef.current = {};
                break;

              case "sidebar_update":
                console.log(
                  "CLIENT: Received sidebar_update event:",
                  eventData.sidebarData,
                );
                console.log("[SIDEBAR DEBUG] Detailed sidebar update:", {
                  sportSuggestions: eventData.sidebarData?.sportSuggestions,
                  sportSuggestions_count:
                    eventData.sidebarData?.sportSuggestions?.length || 0,
                  selectedSports: eventData.sidebarData?.selectedSports,
                  selectedSports_count:
                    eventData.sidebarData?.selectedSports?.length || 0,
                  selectedSport: eventData.sidebarData?.selectedSport,
                  currentStage: eventData.sidebarData?.currentStage,
                });
                if (eventData.sidebarData) {
                  setSidebarData(eventData.sidebarData as SidebarStateData);
                }
                break;

              // case "history_loaded": // Likely not needed if starting fresh
              //   break;

              default:
                console.warn(
                  "CLIENT: Received unknown SSE event type:",
                  (eventData as any).type,
                );
            }
          } catch (e) {
            // ... (existing parsing error logic) ...
            console.error(
              "CLIENT: Error parsing SSE message:",
              e,
              "Raw data:",
              event.data,
            );
            setIsLoading(false);
            setIsStreaming(false);
            setMessages((prev) => {
              const indicator = prev.find(
                (msg) => msg.id === "typing-indicator",
              );
              if (
                indicator &&
                indicator.content &&
                indicator.content.length > 0
              ) {
                return prev.filter((msg) => msg.id !== "typing-indicator");
              } else {
                return prev;
              }
            });
          }
        };
      });
    },
    [
      setMessages,
      setIsStreaming,
      setToolCalls,
      setIsLoading,
      setSidebarData,
      processBatchKeywords,
      setInputPrompt,
      setWaitingForInput,
      threadIdFromUrl,
    ],
  );

  // Cleanup eventSource on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        console.log("Closing EventSource connection on component unmount.");
        eventSourceRef.current.close();
      }
    };
  }, []);

  const handleSubmit = useCallback(
    async (messageToSend: string, isInitialSubmit = false) => {
      console.log(
        `CLIENT: [handleSubmit] Called with message: '${messageToSend}', isInitialSubmit: ${isInitialSubmit}`,
      );
      console.log("[handleSubmit] Current state:", {
        isLoading,
        isStreaming,
        submissionInProgress: submissionInProgressRef.current,
        threadIdFromUrl,
        userId,
        waitingForInput,
        inputPrompt,
        messageLength: messageToSend?.length || 0,
      });

      // --- Prevent duplicate initial call ---
      if (isInitialSubmit) {
        if (initialCallAttemptedRef.current) {
          console.log(
            "CLIENT: [handleSubmit] Initial call already attempted, exiting.",
          );
          return;
        }
        console.log("CLIENT: [handleSubmit] Processing initial call attempt.");
        initialCallAttemptedRef.current = true;
      }

      // Prevent submission if already loading/streaming
      if (isLoading || isStreaming || submissionInProgressRef.current) {
        console.log(
          "CLIENT: Submission already in progress or loading, ignoring call.",
        );
        console.log("CLIENT: State details:", {
          isLoading,
          isStreaming,
          submissionInProgress: submissionInProgressRef.current,
        });
        return;
      }
      submissionInProgressRef.current = true;
      console.log("[handleSubmit] Set submissionInProgress to true");

      if (!messageToSend || !threadIdFromUrl || !userId) {
        // ... (error handling) ...
        console.error("CLIENT Error: Missing input, threadId, or userId.");
        console.error("CLIENT Error details:", {
          messageToSend: !!messageToSend,
          messageLength: messageToSend?.length || 0,
          threadIdFromUrl: !!threadIdFromUrl,
          userId: !!userId,
        });
        setMessages((prev) => [
          ...prev.filter((msg) => msg.type !== "system"),
          {
            id: `error-${Date.now()}`,
            type: "system",
            content:
              "An error occurred while communicating with the server. Please try again.",
            lastUpdated: Date.now(),
            domain: "general", // Add domain for consistency
          },
        ]);
        submissionInProgressRef.current = false;
        return;
      }

      console.log("[handleSubmit] Setting isLoading to true");
      setIsLoading(true);
      hasReceivedFirstTokenThisTurn.current = false;
      lastStreamingAgentRef.current = null;
      // Clear system messages *before* adding user message or indicator
      setMessages((prev) => prev.filter((msg) => msg.type !== "system"));

      // Add user message locally (only if not initial submit, or if it's a non-default initial message)
      if (
        !isInitialSubmit ||
        (isInitialSubmit && messageToSend !== "start_onboarding")
      ) {
        console.log("[handleSubmit] Adding user message to UI");
        const userMessage: Message = {
          id: `user-${Date.now()}`,
          type: "human",
          content: messageToSend,
          lastUpdated: Date.now(),
          domain: "general",
        };
        setMessages((prev) => [...prev, userMessage]);
      } else if (isInitialSubmit) {
        console.log(
          "CLIENT: Initial submit with default trigger - initiating onboarding.",
        );
        // Optionally add a different placeholder message for onboarding start
        setMessages((prev) => [
          ...prev,
          {
            id: `system-onboarding-start-${Date.now()}`,
            type: "system",
            content: "Starting your onboarding process...",
            lastUpdated: Date.now(),
            domain: "general",
          },
        ]);
      }

      // Add typing indicator *after* potentially adding user message
      const uniqueTypingId = `typing-indicator-${Date.now()}`;
      // Onboarding always uses Athlea domain
      const initialIndicatorDomain: MessageDomain = "Athlea";
      const initialIndicatorName = "Athlea";
      console.log(
        `[handleSubmit] Creating typing indicator ${uniqueTypingId} with domain: ${initialIndicatorDomain}`,
      );
      const typingIndicatorMessage: Message = {
        id: uniqueTypingId,
        type: "ai",
        domain: initialIndicatorDomain,
        name: initialIndicatorName,
        content: "",
        isLoading: true,
        lastUpdated: Date.now(),
        metadata: { hideUntilContent: true },
      };
      setMessages((prev) => [...prev, typingIndicatorMessage]);
      currentAgentMessageIdsRef.current = {};

      try {
        // Determine if it's an onboarding flow (for endpoint selection)
        const isDirectOnboarding =
          searchParams?.get("direct_onboarding") === "true";
        const isResumedOnboarding = searchParams?.get("onboarding") === "true";
        const isOnboardingFlow = isDirectOnboarding || isResumedOnboarding;

        console.log("--- handleSubmit Routing Check ---");
        console.log(`isOnboardingFlow: ${isOnboardingFlow}`);
        console.log("Search params:", {
          direct_onboarding: searchParams?.get("direct_onboarding"),
          onboarding: searchParams?.get("onboarding"),
          all_params: Object.fromEntries(searchParams?.entries() || []),
        });

        // Always use EventSource now, choose endpoint based on flow type
        let endpointUrl = "";
        if (isOnboardingFlow) {
          endpointUrl = "/api/onboarding-python"; // Use the Python onboarding proxy endpoint
          console.log(
            `CLIENT: [handleSubmit] ✅ Onboarding flow detected - using GET to ${endpointUrl}`,
          );
        } else {
          endpointUrl = "/api/coaching"; // Use the coaching SSE endpoint
          console.log(
            `CLIENT: [handleSubmit] ❌ Regular chat message - using GET to ${endpointUrl}`,
          );
        }

        const params = new URLSearchParams({
          message: messageToSend, // Send the message that triggered the submit
          threadId: threadIdFromUrl!,
          userId: userId!,
        });

        // Add resume flag if we're resuming onboarding
        if (
          onboardingStatus.status === "in_progress" &&
          onboardingStatus.canResume
        ) {
          console.log("[handleSubmit] Adding resume=true parameter");
          params.set("resume", "true");
        }

        // Add singleCoach param only if NOT an onboarding flow
        if (!isOnboardingFlow && selectedCoach) {
          params.set("singleCoach", selectedCoach);
          console.log(
            `CLIENT: Adding singleCoach=${selectedCoach} to message params.`,
          );
        }

        const fullUrl = endpointUrl + `?${params.toString()}`;

        console.log(
          `🚀 CLIENT: [handleSubmit] About to connect via EventSource (GET)... URL: ${fullUrl}`,
        );
        console.log(
          `🚀 CLIENT: [handleSubmit] Full params:`,
          Object.fromEntries(params.entries()),
        );
        console.log(
          `🚀 CLIENT: [handleSubmit] Message being sent: "${messageToSend}"`,
        );

        console.log("🚀 CLIENT: [handleSubmit] Calling processEventSource...");
        await processEventSource(fullUrl);
        console.log("🚀 CLIENT: [handleSubmit] processEventSource completed");
        console.log("[handleSubmit] processEventSource completed");
      } catch (error) {
        console.error("CLIENT Error during handleSubmit:", error);
        setMessages((prev) => [
          // Ensure indicator is removed on error
          ...prev.filter((msg) => !msg.id?.startsWith("typing-indicator")),
          // Add error message
          {
            id: `error-submit-${Date.now()}`,
            type: "system",
            content:
              "An error occurred while communicating with the server. Please try again.",
            lastUpdated: Date.now(),
            domain: "general", // Add domain
          },
        ]);
        setIsLoading(false);
        setIsStreaming(false);
      } finally {
        console.log("[handleSubmit] Setting submissionInProgress to false");
        submissionInProgressRef.current = false;
      }
    },
    // Updated dependencies
    [
      threadIdFromUrl,
      userId,
      selectedCoach,
      setIsLoading,
      setIsStreaming,
      setMessages,
      processEventSource, // Keep for GET requests
      searchParams,
      dispatch, // Assuming dispatch is used inside handleSubmit
    ],
  );

  // Handle submitting input when waiting for user response
  const handleSubmitWaitingInput = useCallback(
    async (userInput: string) => {
      console.log(
        "🎯 [ONBOARDING] handleSubmitWaitingInput called with:",
        userInput,
      );
      console.log("🎯 [ONBOARDING] Current state before submit:", {
        waitingForInput,
        inputPrompt,
        isLoading,
        isStreaming,
        userInputLength: userInput?.length || 0,
      });
      // Don't reset waitingForInput until after successful submission
      try {
        console.log("🎯 [ONBOARDING] Calling handleSubmit...");
        await handleSubmit(userInput, false);
        console.log("🎯 [ONBOARDING] handleSubmit completed successfully");
        // Only reset state after successful submission
        setWaitingForInput(false);
        setInputPrompt("");
        console.log("🎯 [ONBOARDING] State reset after successful submission");
      } catch (error) {
        console.error("🎯 [ONBOARDING] Error submitting waiting input:", error);
        // Reset state even on error to prevent stuck state
        setWaitingForInput(false);
        setInputPrompt("");
        console.log("🎯 [ONBOARDING] State reset after error");
      }
    },
    [handleSubmit, waitingForInput, inputPrompt, isLoading, isStreaming],
  );

  // Load existing onboarding data when component mounts
  useEffect(() => {
    if (userId && threadIdFromUrl) {
      console.log("[ONBOARDING] Loading existing sidebar data on mount");
      loadExistingOnboardingData();
    }
  }, [userId, threadIdFromUrl, loadExistingOnboardingData]);

  // Check onboarding status when component mounts
  useEffect(() => {
    if (userId && threadIdFromUrl && !initialCallAttemptedRef.current) {
      console.log("[RESUME] Checking if we should check onboarding status...");
      console.log(`Force reset: ${forceReset}, Skip resume: ${skipResume}`);

      if (forceReset) {
        console.log("[RESUME] Force reset requested, resetting onboarding");
        resetOnboarding();
      } else {
        console.log("[RESUME] Checking onboarding status...");
        checkOnboardingStatus();
      }
    }
  }, [
    userId,
    threadIdFromUrl,
    checkOnboardingStatus,
    resetOnboarding,
    forceReset,
  ]);

  // Add back initial message handling, particularly for onboarding
  useEffect(() => {
    let timerId: NodeJS.Timeout | null = null; // Declare timerId here

    // Log dependencies at the start of the effect
    console.log("--- Initial Message useEffect Check ---");
    console.log(`Current needInitialMessages: ${needInitialMessages}`);
    console.log(`Current userId: ${userId}`);
    console.log(`Current threadIdFromUrl: ${threadIdFromUrl}`);
    console.log(`Current initialMessage: ${initialMessage}`);
    console.log(`Current onboardingStatus: ${onboardingStatus.status}`);

    console.log(
      `Current searchParams.get("direct_onboarding"): ${searchParams?.get("direct_onboarding")}`,
    );
    console.log(
      `Current searchParams.get("onboarding"): ${searchParams?.get("onboarding")}`,
    );
    console.log(
      `Current searchParams.get("fresh_start"): ${searchParams?.get("fresh_start")}`,
    );
    console.log(
      `Initial call attempted ref: ${initialCallAttemptedRef.current}`,
    ); // Log initial call attempt status

    // Check for fresh_start parameter and set needInitialMessages if found
    const isFreshStart = searchParams?.get("fresh_start") === "true";
    if (
      isFreshStart &&
      !needInitialMessages &&
      !initialCallAttemptedRef.current
    ) {
      console.log(
        "[Initial Effect] Fresh start detected, setting needInitialMessages to true",
      );
      dispatch(setNeedInitialMessages(true));
      dispatch(setInitialMessage("Hi, how can you help me today?"));
      return; // Exit early, let the next effect run handle the actual submission
    }

    // Don't start onboarding if we're still checking status
    if (onboardingStatus.status === "loading") {
      console.log("[Initial Effect] Waiting for onboarding status check");
      return;
    }

    if (
      needInitialMessages &&
      userId &&
      threadIdFromUrl &&
      !initialCallAttemptedRef.current
    ) {
      // Added check for initialCallAttemptedRef
      console.log(
        "CLIENT: [Initial Effect] Conditions MET. Preparing to call handleSubmit.", // Added identifier
        `Direct Onboarding: ${searchParams?.get("direct_onboarding") === "true"}`,
        `Resumed Onboarding: ${searchParams?.get("onboarding") === "true"}`,
        `Fresh Start: ${isFreshStart}`,
        `Onboarding Status: ${onboardingStatus.status}`,
        initialMessage
          ? `With message: ${initialMessage}`
          : "With default trigger message.",
      );

      // Use initialMessage if available, otherwise send a default trigger
      const messageToSend = initialMessage || "Hi, how can you help me today?"; // Use a more natural default
      console.log(
        `CLIENT: [Initial Effect] Calling handleSubmit with message: '${messageToSend}', isInitialSubmit: true`,
      ); // Added identifier
      // Wrap handleSubmit in setTimeout to defer execution slightly
      timerId = setTimeout(() => {
        // Assign to outer variable
        console.log(
          "CLIENT: [Initial Effect] Executing delayed handleSubmit call",
        );
        handleSubmit(messageToSend, true);
      }, 100); // Added a slight delay (e.g., 100ms)

      // Reset the flag after scheduling the call
      console.log(
        "CLIENT: [Initial Effect] Setting needInitialMessages to false.",
      );
      dispatch(setNeedInitialMessages(false));
    } else {
      console.log(
        "CLIENT: [Initial Effect] Initial message conditions NOT met or already attempted.",
        `needInitialMessages: ${needInitialMessages}`,
        `isFreshStart: ${isFreshStart}`,
        `userId: ${Boolean(userId)}`,
        `threadIdFromUrl: ${Boolean(threadIdFromUrl)}`,
        `initialCallAttemptedRef: ${initialCallAttemptedRef.current}`,
        `onboardingStatus: ${onboardingStatus.status}`,
      ); // Added identifier and detailed reasons
    }

    // Cleanup function: ONLY reset submissionInProgressRef.
    // DO NOT clear the timerId here.
    return () => {
      console.log(
        "CLIENT: [Initial Effect Cleanup] Running cleanup. Resetting submissionInProgressRef.", // Modify log message
      );
      // if (timerId) clearTimeout(timerId); // Keep this commented out or removed
      submissionInProgressRef.current = false; // Reset submission lock on cleanup
    };
  }, [
    needInitialMessages,
    userId,
    threadIdFromUrl,
    initialMessage,
    handleSubmit,
    dispatch,
    onboardingStatus.status,
    // Removed searchParams dependency to prevent infinite loops
  ]);

  // ... (scroll effects remain) ...
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  useEffect(() => {
    toolCallsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [toolCalls]);

  // Remove plan name update logic or adapt if needed
  // useEffect(() => { updatePlanNameThunk... }, ...);

  // Restore profile update check effect
  useEffect(() => {
    if (!userId || messages.length === 0) {
      return;
    }

    const lastMessage = messages[messages.length - 1];

    // Check only for the latest *human* message that isn't the typing indicator
    if (lastMessage.type === "human" && lastMessage.id !== "typing-indicator") {
      console.log(
        "CLIENT: New human message detected. Checking for profile updates...",
        `Message ID: ${lastMessage.id}`,
      );

      const checkForProfileUpdate = () => {
        fetch("/api/update-profile", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userId: userId,
            message: lastMessage.content,
          }),
        })
          .then((response) => {
            if (!response.ok) {
              console.error(
                "CLIENT: Error response from /api/update-profile:",
                response.status,
              );
              return null;
            }
            return response.json();
          })
          .then((data) => {
            if (!data) return;

            console.log("CLIENT: /api/update-profile response:", data);

            if (data.updated && data.systemMessage) {
              // Avoid adding system messages starting with common errors
              if (
                !data.systemMessage?.startsWith(
                  "Your Connection error profile was updated with",
                ) &&
                !data.systemMessage?.startsWith("Error updating profile")
              ) {
                console.log(
                  "CLIENT: Profile updated. Adding system message to chat.",
                );
                const systemUpdateMessage: Message = {
                  id: `system-update-${Date.now()}`,
                  type: "system",
                  content: data.systemMessage,
                  lastUpdated: Date.now(),
                  domain: "general",
                };
                // Use functional update to avoid stale closure issues
                setMessages((prevMessages) => [
                  ...prevMessages,
                  systemUpdateMessage,
                ]);
              } else {
                console.warn(
                  "CLIENT: Suppressed potentially malformed system message from API:",
                  data.systemMessage,
                );
              }
            } else {
              console.log(
                "CLIENT: Profile update check complete, no update needed or no system message.",
              );
            }
          })
          .catch((error) => {
            console.error("CLIENT: Error calling /api/update-profile:", error);
          });
      };

      // Debounce or throttle this if it causes issues, but for now, call directly
      checkForProfileUpdate();
    }
  }, [messages, userId, setMessages]); // Depend only on messages and userId

  // Keyword processing logic (keep if still relevant)
  useEffect(() => {
    const allKeywords = messages
      .filter((msg) => msg.keywords && msg.keywords.length > 0)
      .flatMap((msg) => msg.keywords || []);
    if (allKeywords.length > 0) {
      const uniqueKeywords = [...new Set(allKeywords)];
      processBatchKeywords(uniqueKeywords);
    }
  }, [messages, processBatchKeywords]);

  // Determine if it's an onboarding session
  const isOnboarding =
    searchParams?.get("onboarding") === "true" ||
    searchParams?.get("direct_onboarding") === "true";

  // Add back effect to consolidate reasoning messages
  useEffect(() => {
    if (messages.length === 0) return;

    let processedMessages = [...messages];
    const reasoningMessages = processedMessages.filter(
      (msg) => msg.domain === "reasoning",
    );

    if (reasoningMessages.length > 1) {
      console.log(
        `[Message Group Effect] Found ${reasoningMessages.length} reasoning messages. Keeping only the latest one.`,
      );

      const latestReasoningMsg =
        reasoningMessages[reasoningMessages.length - 1];

      // Filter out all reasoning messages except the latest one
      processedMessages = processedMessages.filter(
        (msg) => msg.domain !== "reasoning" || msg.id === latestReasoningMsg.id,
      );

      // Only update state if messages actually changed
      if (processedMessages.length !== messages.length) {
        console.log(
          `[Message Group Effect] Reduced message count from ${messages.length} to ${processedMessages.length}`,
        );
        setMessages(processedMessages);
      }
    }
  }, [messages]); // Rerun whenever messages state updates

  // Add message fetching logic similar to chat page
  const fetchMessage = async () => {
    if (threadIdFromUrl) {
      try {
        const response = await fetch(`/api/messages/${threadIdFromUrl}`);

        if (response.ok) {
          const data = await response.json();
          // Return the whole data object
          return data;
        } else {
          console.error(`Failed to fetch messages. Status: ${response.status}`);
          return undefined;
        }
      } catch (error) {
        console.error("Error fetching messages:", error);
        return undefined;
      }
    } else {
      console.log("No threadId, skipping fetch");
      return undefined;
    }
  };

  const { data: q } = useQuery({
    queryKey: ["messages", threadIdFromUrl],
    queryFn: fetchMessage,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: false,
  });

  // Initialize messages and toolCalls from fetched data
  useEffect(() => {
    if (q) {
      console.log("Loading persisted onboarding messages:", q);
      // q is now the full data object { messageList, artifacts, sourceDocuments }
      setMessages(q.messageList || []); // Fallback to empty array
      setToolCalls(q.artifacts || []); // Fallback for artifacts

      // Update sidebar item name if we have enough messages
      if (q.messageList && q.messageList.length >= 4 && threadIdFromUrl) {
        console.log("---onboarding chat name update---", q.messageList.length);
        const msg = q.messageList[3]?.content.slice(0, 150);
        dispatch(setSidebarItemName({ id: threadIdFromUrl, name: msg }));
      }
    }
  }, [q, threadIdFromUrl, dispatch]);

  // Save messages when they change
  useEffect(() => {
    if (messageDone || messages.length > 0 || toolCalls.length > 0) {
      console.log("---onboarding message save effect triggered---", {
        messageDone,
        messagesLength: messages.length,
        toolCallsLength: toolCalls.length,
        threadIdFromUrl,
        initialMessage,
      });

      // Add a guard to prevent saving during initial load
      if (!threadIdFromUrl) {
        console.log(
          "---onboarding message save effect skipped: no threadId---",
        );
        return;
      }

      fetch(`/api/messages/${threadIdFromUrl}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sourceDocuments: [], // Onboarding doesn't use source documents
          toolCalls,
          messageList: messages,
          initialMessage:
            initialMessage && initialMessage !== "Hi" && messages.length === 0
              ? initialMessage
              : undefined,
        }),
      }).then((r) => {
        setMessageDone(false);
        console.log("---onboarding message save response---", r.status);
      });
    }
  }, [threadIdFromUrl, messages, messageDone, toolCalls, initialMessage]);

  return (
    <div>
      <Card className="border-0 bg-[#F9FBFC] shadow-none">
        <div className="flex h-[calc(100vh-4rem)]">
          <div className="flex flex-1 flex-col">
            <div className="scrollbar-hide flex-1 overflow-y-auto">
              <CardContent className="px-4 pt-4">
                {messages.map((message) => {
                  if (message.type === "system") {
                    return (
                      <div key={message.id} className="mb-4">
                        <SystemMessage
                          message={message.content}
                          onRemove={async (domain, key) => {
                            // ... (onRemove logic)
                          }}
                        />
                      </div>
                    );
                  }
                  // Use AIMessage for planner messages as well, handled by domain prop
                  if (message.domain === "planner") {
                    return <AIMessage key={message.id} message={message} />;
                  }
                  // Use AIMessage for reasoning messages
                  if (message.domain === "reasoning") {
                    const isCollapsed = collapsedMessages[message.id] ?? false;
                    return (
                      <AIMessage
                        key={message.id}
                        message={message}
                        // isCollapsed={isCollapsed} // AIMessage might handle collapse internally or via prop
                        onToggleCollapse={toggleCollapse}
                      />
                    );
                  }
                  if (
                    (message.type === "human" ||
                      (message.type === "ai" &&
                        !isPlannerOrReasoningDomain(message.domain))) &&
                    !(
                      typeof message.id === "string" &&
                      message.id.startsWith("typing-indicator-")
                    )
                  ) {
                    if (message.type === "human") {
                      return <UserMessage key={message.id} message={message} />;
                    } else {
                      return <AIMessage key={message.id} message={message} />;
                    }
                  }
                  return null;
                })}
                <div ref={messagesEndRef} />
              </CardContent>
            </div>
            <div className="p-4">
              <UserInput
                onSubmitRegularMessage={handleSubmit}
                onSubmitWaitingInput={handleSubmitWaitingInput}
                isLoading={isLoading}
                isStreaming={isStreaming}
                waitingForInput={waitingForInput}
                inputPrompt={inputPrompt}
                suggestions={(() => {
                  const suggestionsToPass = sidebarData?.sportSuggestions || [];
                  console.log(
                    "[ONBOARDING DEBUG] Passing suggestions to UserInput:",
                    {
                      sidebarData_exists: !!sidebarData,
                      sportSuggestions_raw: sidebarData?.sportSuggestions,
                      sportSuggestions_length:
                        sidebarData?.sportSuggestions?.length || 0,
                      final_suggestions: suggestionsToPass,
                      final_suggestions_length: suggestionsToPass.length,
                    },
                  );
                  return suggestionsToPass;
                })()}
              />
            </div>
          </div>

          <div className="w-1/2 flex-shrink-0 border-l bg-white">
            <OnboardingSidebar sidebarData={sidebarData} />
          </div>
        </div>

        {/* Development Controls */}
        {isDevelopment && (
          <div className="fixed bottom-4 left-4 z-50">
            <button
              onClick={clearOnboardingData}
              className="rounded-md bg-red-500 px-3 py-2 text-sm text-white shadow-lg hover:bg-red-600"
              title="Clear all onboarding data for this user (Development only)"
            >
              🧹 Clear Onboarding Data
            </button>
          </div>
        )}
      </Card>

      <PlanGenerationSidebar />
    </div>
  );
}
