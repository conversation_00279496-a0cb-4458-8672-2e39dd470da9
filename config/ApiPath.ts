const ApiPath = "wss://athleawebapp.azurewebsites.net/ws/";

// ws://localhost:8001/ws/
// wss://athleawebapp.azurewebsites.net/ws/

const isProduction = true;
// process.env.NODE_ENV === "production";

export const WS_BASE_URL = "wss://athleawebapp.azurewebsites.net/ws/";

export const HTTP_BASE_URL = isProduction
  ? "https://athleawebapp.azurewebsites.net/api"
  : "http://localhost:8001/api";

// Audio Service URLs
export const AUDIO_WS_BASE_URL = "wss://athlea-audio-apim.azure-api.net";
// "wss://audio-athlea-service-b9hxfzd4cycpbsbx.uksouth-01.azurewebsites.net/ws/audio/";

export const AUDIO_HTTP_BASE_URL = isProduction
  ? "https://athleaaudio.azurewebsites.net/api"
  : "http://localhost:8003/api";

export const getTextWebSocketUrl = (
  sessionId: string,
  params: URLSearchParams,
) => `${WS_BASE_URL}${sessionId}?${params.toString()}`;

export const getAudioWebSocketUrl = (
  sessionId: string,
  params: URLSearchParams,
) => `${AUDIO_WS_BASE_URL}/ws/audio?${params.toString()}`;

export const getHttpUrl = (endpoint: string) => `${HTTP_BASE_URL}/${endpoint}`;
export const getAudioHttpUrl = (endpoint: string) =>
  `${AUDIO_HTTP_BASE_URL}/${endpoint}`;

export default ApiPath;
