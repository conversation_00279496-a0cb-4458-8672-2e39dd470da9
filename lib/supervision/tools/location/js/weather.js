import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { AzureMapsService } from "../../../../../app/api/mastra/utils/azure-maps-service";

/**
 * Tool for retrieving current weather data at a specific location
 */
export const getLocationWeatherTool = tool(
  async (args) => {
    // Extract parameters with validation
    const lat =
      args.lat !== undefined
        ? args.lat
        : args.latitude !== undefined
          ? args.latitude
          : null;
    const lon =
      args.lon !== undefined
        ? args.lon
        : args.longitude !== undefined
          ? args.longitude
          : null;

    console.log(`MapTool: Getting weather for ${lat}, ${lon}`);

    if (lat === null || lon === null) {
      return "I need both latitude and longitude to get weather data. Please provide valid coordinates.";
    }

    // Create Azure Maps service with subscription key
    const azureMapsKey = process.env.AZURE_MAPS_SUBSCRIPTION_KEY || "";
    const azureMapsService = new AzureMapsService(azureMapsKey);

    try {
      const weatherResult = await azureMapsService.getCurrentWeather(lat, lon);

      if (!weatherResult) {
        return "I couldn't get the weather data for that location.";
      }

      return {
        temperature: `${weatherResult.temperature.value}${weatherResult.temperature.unit}`,
        feelsLike: `${weatherResult.feelsLike.value}${weatherResult.feelsLike.unit}`,
        condition: weatherResult.condition,
        humidity: `${weatherResult.humidity.value}${weatherResult.humidity.unit}`,
        windSpeed: `${weatherResult.windSpeed.value} ${weatherResult.windSpeed.unit}`,
        precipitation: `${weatherResult.precipitation.value} ${weatherResult.precipitation.unit}`,
        summary: `Current conditions: ${weatherResult.condition} with a temperature of ${weatherResult.temperature.value}${weatherResult.temperature.unit} (feels like ${weatherResult.feelsLike.value}${weatherResult.feelsLike.unit})`,
      };
    } catch (error) {
      console.error("Error in weather tool:", error);
      return `Unable to get weather data. Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
  {
    name: "get_location_weather",
    description: "Get current weather conditions at a specific location",
    schema: z.object({
      lat: z.number().describe("Latitude coordinate of the location"),
      lon: z.number().describe("Longitude coordinate of the location"),
    }),
  },
);
