#!/usr/bin/env python3
"""
Test script to simulate onboarding flow with plan summary confirmation
"""

import asyncio
import aiohttp
import json
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configuration
API_BASE_URL = os.getenv("NEXT_PUBLIC_PYTHON_LANGGRAPH_URL", "http://localhost:8000")
TEST_USER_ID = "test_user_" + datetime.now().strftime("%Y%m%d_%H%M%S")
TEST_THREAD_ID = f"onboarding_{TEST_USER_ID}"

# Simulate conversation history with a plan summary
MOCK_CONVERSATION_HISTORY = [
    {
        "role": "assistant",
        "content": "Hi! I'm <PERSON><PERSON><PERSON>, your AI fitness coach. I'm here to help you create a personalized training plan. What sports or activities are you interested in?",
    },
    {"role": "human", "content": "I'm interested in running and cycling"},
    {
        "role": "assistant",
        "content": """Based on our conversation, here's a summary of your personalized training plan:

**🎯 Your Training Plan Summary**

**Sports:** Running, Cycling  
**Goals:** Improve endurance and speed in both disciplines  
**Experience Level:** Intermediate  
**Time Commitment:** 4-5 sessions per week, 45-90 minutes each  

**Plan Approach:**
- **Phase 1 (Weeks 1-4):** Base Building
  - Focus on establishing consistent training routine
  - Mix of easy runs and rides to build aerobic base
  - 2 runs + 2 rides per week

- **Phase 2 (Weeks 5-8):** Endurance Development  
  - Gradually increase volume and add tempo work
  - Introduce interval training once per week
  - 2-3 runs + 2 rides per week

- **Phase 3 (Weeks 9-12):** Speed & Power
  - Add more intensity with intervals and hill work
  - Maintain endurance with long weekend sessions
  - Focus on race-specific preparation

**Key Focus:**
- Progressive overload with 10% weekly volume increase
- Balance between running and cycling to prevent overuse
- Include recovery days and easy sessions
- Strength training 1-2x per week for injury prevention

Are you ready for me to generate your detailed training plan?""",
    },
]


async def test_onboarding_with_plan_summary():
    """Test the onboarding flow with plan summary confirmation"""

    print(f"🚀 Testing Onboarding Flow")
    print(f"📊 API URL: {API_BASE_URL}")
    print(f"👤 User ID: {TEST_USER_ID}")
    print(f"🧵 Thread ID: {TEST_THREAD_ID}")
    print("=" * 80)

    async with aiohttp.ClientSession() as session:
        # Test 1: Send confirmation message with conversation history
        print("\n📤 Test 1: Sending confirmation with conversation history")
        print("Message: 'Yes, that looks great! Please generate my plan.'")

        request_data = {
            "message": "Yes, that looks great! Please generate my plan.",
            "user_id": TEST_USER_ID,
            "thread_id": TEST_THREAD_ID,
            "conversation_history": MOCK_CONVERSATION_HISTORY,
        }

        try:
            async with session.post(
                f"{API_BASE_URL}/api/onboarding",
                json=request_data,
                headers={"Content-Type": "application/json"},
            ) as response:
                print(f"\n📨 Response Status: {response.status}")

                if response.status == 200:
                    # Read streaming response
                    print("\n📝 Streaming Response:")
                    print("-" * 40)

                    async for line in response.content:
                        if line:
                            line_str = line.decode("utf-8").strip()
                            if line_str.startswith("data: "):
                                try:
                                    data = json.loads(line_str[6:])
                                    event_type = data.get("type")

                                    if event_type == "token":
                                        print(
                                            data.get("content", ""), end="", flush=True
                                        )
                                    elif event_type == "sidebar_update":
                                        print(
                                            f"\n\n📊 Sidebar Update: {data.get('sidebarData', {}).get('current_stage')}"
                                        )
                                    elif event_type == "agent_start":
                                        print(
                                            f"\n\n🤖 Agent Started: {data.get('agent')}"
                                        )
                                    elif event_type == "plan_generated":
                                        print(f"\n\n✅ Plan Generated Successfully!")
                                        print(
                                            f"Plan Name: {data.get('plan', {}).get('name')}"
                                        )
                                except json.JSONDecodeError:
                                    pass

                    print("\n" + "-" * 40)
                    print("✅ Test 1 Completed Successfully!")

                else:
                    error_text = await response.text()
                    print(f"❌ Error: {error_text}")

        except Exception as e:
            print(f"❌ Exception during test: {e}")
            import traceback

            traceback.print_exc()

        # Test 2: Test without conversation history (should fail to recognize plan summary)
        print("\n\n" + "=" * 80)
        print("📤 Test 2: Sending confirmation WITHOUT conversation history")
        print("Message: 'Yes, please generate my plan.'")

        request_data_no_history = {
            "message": "Yes, please generate my plan.",
            "user_id": TEST_USER_ID + "_no_history",
            "thread_id": TEST_THREAD_ID + "_no_history",
            "conversation_history": [],
        }

        try:
            async with session.post(
                f"{API_BASE_URL}/api/onboarding",
                json=request_data_no_history,
                headers={"Content-Type": "application/json"},
            ) as response:
                print(f"\n📨 Response Status: {response.status}")

                if response.status == 200:
                    print("\n📝 Response Preview:")
                    print("-" * 40)

                    response_content = ""
                    line_count = 0
                    async for line in response.content:
                        if line and line_count < 20:  # Only show first 20 lines
                            line_str = line.decode("utf-8").strip()
                            if line_str.startswith("data: "):
                                try:
                                    data = json.loads(line_str[6:])
                                    if data.get("type") == "token":
                                        response_content += data.get("content", "")
                                except:
                                    pass
                            line_count += 1

                    print(
                        response_content[:500] + "..."
                        if len(response_content) > 500
                        else response_content
                    )
                    print("\n" + "-" * 40)
                    print(
                        "⚠️  Test 2: Without history, system doesn't recognize plan confirmation context"
                    )

        except Exception as e:
            print(f"❌ Exception during test 2: {e}")


async def test_saved_user_resume():
    """Test resuming for a user with saved onboarding data"""
    print("\n\n" + "=" * 80)
    print("📤 Test 3: Testing resume for user with saved MongoDB data")

    # Use the user ID from the logs that has plan_summary_ready state
    SAVED_USER_ID = "oocR78vBmCeHSuMu7ORq1Wo6df23"

    async with aiohttp.ClientSession() as session:
        request_data = {
            "message": "Yes, that sounds perfect! Let's create the plan.",
            "user_id": SAVED_USER_ID,
            "thread_id": f"onboarding_{SAVED_USER_ID}",
            "conversation_history": [],  # No history needed - should load from DB
        }

        try:
            async with session.post(
                f"{API_BASE_URL}/api/onboarding",
                json=request_data,
                headers={"Content-Type": "application/json"},
            ) as response:
                print(f"\n📨 Response Status: {response.status}")

                if response.status == 200:
                    print("\n📝 Response (checking if plan generation starts):")
                    print("-" * 40)

                    plan_generation_started = False
                    async for line in response.content:
                        if line:
                            line_str = line.decode("utf-8").strip()
                            if line_str.startswith("data: "):
                                try:
                                    data = json.loads(line_str[6:])
                                    if (
                                        data.get("type") == "agent_start"
                                        and data.get("agent") == "Athlea"
                                    ):
                                        print(
                                            "🤖 Agent started - plan generation beginning!"
                                        )
                                        plan_generation_started = True
                                    elif data.get("type") == "plan_generated":
                                        print("✅ Plan generated successfully!")
                                        break
                                except:
                                    pass

                    if plan_generation_started:
                        print(
                            "\n✅ Test 3: Successfully resumed and started plan generation!"
                        )
                    else:
                        print("\n⚠️  Test 3: Plan generation may not have started")

        except Exception as e:
            print(f"❌ Exception during test 3: {e}")


if __name__ == "__main__":
    print("🧪 Onboarding Flow Test Suite")
    print("=" * 80)

    # Run all tests
    asyncio.run(test_onboarding_with_plan_summary())
    asyncio.run(test_saved_user_resume())
