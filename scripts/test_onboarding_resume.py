#!/usr/bin/env python3
"""
Test script to verify onboarding data loading from MongoDB
"""

import asyncio
import os
import json
from pymongo import MongoClient
from dotenv import load_dotenv

load_dotenv()

# Test user ID - replace with an actual user ID that has onboarding data
TEST_USER_ID = "oocR78vBmCeHSuMu7ORq1Wo6df23"  # Replace with actual user ID


async def test_onboarding_data_load():
    """Test loading onboarding data from MongoDB"""
    mongodb_uri = os.getenv("MONGODB_URI")
    if not mongodb_uri:
        print("❌ MONGODB_URI not set in environment")
        return

    print(f"🔍 Testing onboarding data load for user: {TEST_USER_ID}")
    print(f"📦 MongoDB URI: {mongodb_uri[:30]}...")

    try:
        # Connect to MongoDB
        mongo_client = MongoClient(mongodb_uri)
        db = mongo_client["AthleaUserData"]
        users_collection = db["users"]

        # Query for onboarding data
        user_doc = users_collection.find_one(
            {"user_id": TEST_USER_ID},
            {
                "onboarding_stage": 1,
                "onboarding_sidebar_data": 1,
                "onboarding_thread_id": 1,
                "_id": 0,
            },
        )

        if user_doc:
            print("\n✅ Found user document!")
            print(f"📊 Document keys: {list(user_doc.keys())}")

            # Check onboarding stage
            onboarding_stage = user_doc.get("onboarding_stage")
            print(f"\n🎯 Onboarding Stage: {onboarding_stage}")

            # Check sidebar data
            sidebar_data = user_doc.get("onboarding_sidebar_data")
            if sidebar_data:
                print(f"\n📋 Sidebar Data Found:")
                print(f"  - Current Stage: {sidebar_data.get('current_stage')}")
                print(
                    f"  - Goals: {len(sidebar_data.get('goals', {}).get('list', []))} goals"
                )
                print(f"  - Selected Sports: {sidebar_data.get('selected_sports', [])}")
                print(
                    f"  - Summary Items: {len(sidebar_data.get('summary_items', []))} items"
                )

                # Show summary items
                if sidebar_data.get("summary_items"):
                    print("\n📝 Summary Items:")
                    for item in sidebar_data["summary_items"][:3]:  # Show first 3
                        print(
                            f"    - {item.get('category')}: {item.get('details')[:50]}..."
                        )

                # Check if plan summary was sent
                if sidebar_data.get("current_stage") == "plan_summary_ready":
                    print("\n🎉 User is at plan_summary_ready stage!")
                    print(
                        "   This means they've seen the plan summary and need to confirm."
                    )

            # Check thread ID
            thread_id = user_doc.get("onboarding_thread_id")
            if thread_id:
                print(f"\n🧵 Thread ID: {thread_id}")

            # Simulate the logic that would happen in the API
            print("\n🔧 Simulating API logic:")
            onboarding_data = {
                "onboarding_stage": onboarding_stage,
                "sidebar_data": sidebar_data,
                "thread_id": thread_id,
            }

            # Check if plan_summary_sent would be set
            plan_summary_sent = onboarding_stage == "plan_summary_ready"
            print(f"  - plan_summary_sent would be: {plan_summary_sent}")
            print(
                f"  - This would trigger plan confirmation routing: {plan_summary_sent}"
            )

        else:
            print(f"\n❌ No onboarding data found for user: {TEST_USER_ID}")

        mongo_client.close()

    except Exception as e:
        print(f"\n❌ Error loading onboarding data: {e}")
        import traceback

        traceback.print_exc()


async def test_multiple_users():
    """Test loading onboarding data for multiple users"""
    # Add more user IDs here if you want to test multiple users
    test_users = [
        TEST_USER_ID,
        # "another_user_id",
    ]

    for user_id in test_users:
        global TEST_USER_ID
        TEST_USER_ID = user_id
        await test_onboarding_data_load()
        print("\n" + "=" * 80 + "\n")


if __name__ == "__main__":
    print("🚀 Starting Onboarding Data Load Test")
    print("=" * 80)
    asyncio.run(test_onboarding_data_load())
