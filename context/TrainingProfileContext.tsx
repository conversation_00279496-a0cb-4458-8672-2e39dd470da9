import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import { usePathname } from "next/navigation";

interface TrainingProfileContextType {
  showTrainingProfile: boolean;
  setShowTrainingProfile: React.Dispatch<React.SetStateAction<boolean>>;
  showCurrentTrainingProfile: boolean;
  setshowCurrentTrainingProfile: React.Dispatch<React.SetStateAction<boolean>>;
}

const TrainingProfileContext = createContext<
  TrainingProfileContextType | undefined
>(undefined);

export const TrainingProfileProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const pathname = usePathname();
  const [showTrainingProfile, setShowTrainingProfile] = useState(false);
  const [showCurrentTrainingProfile, setshowCurrentTrainingProfile] =
    useState(false);

  useEffect(() => {
    if (pathname?.startsWith("/onboarding")) {
      setShowTrainingProfile(true);
    } else {
      setShowTrainingProfile(false);
    }
  }, [pathname]);

  return (
    <TrainingProfileContext.Provider
      value={{
        showTrainingProfile,
        setShowTrainingProfile,
        showCurrentTrainingProfile,
        setshowCurrentTrainingProfile,
      }}
    >
      {children}
    </TrainingProfileContext.Provider>
  );
};

export const useTrainingProfile = () => {
  const context = useContext(TrainingProfileContext);
  if (context === undefined) {
    throw new Error(
      "useTrainingProfile must be used within a TrainingProfileProvider",
    );
  }
  return context;
};
