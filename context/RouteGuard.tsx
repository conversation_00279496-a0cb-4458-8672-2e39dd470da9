"use client";
import { ensureUserProfileExists } from "@/app/lib/userUtils";
import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { addSingleNotification } from "@/store/slices/notificationSlice";
import {
  addUserData,
  refetchTrainingProfile,
  setIsNewUser,
} from "@/store/slices/userSlice";
import {
  initializeOnboarding,
  setPlanTitle,
  setSelectedJobId,
} from "@/store/slices/jobSlice";
import { useKindeBrowserClient } from "@kinde-oss/kinde-auth-nextjs";
import axios from "axios";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { initializeApp } from "firebase/app";
import {
  getAuth,
  isSignInWithEmailLink,
  onAuthStateChanged,
  signInWithEmailLink,
} from "firebase/auth";
import { useAuthState } from "react-firebase-hooks/auth";
import { useTrainingPlan } from "./TrainingPlanContext";

const RouteGuard = ({ children }: any) => {
  const dispatch = useAppDispatch();
  const {
    user: kindelUser,
    isAuthenticated,
    isLoading,
  } = useKindeBrowserClient();
  const [authorized, setAuthorized] = useState(true);
  const isLoadingTempVal = useRef<any>(true);
  const isAuthTemp = useRef<any>(true);
  const router: any = useRouter();
  const pathname: any = usePathname();
  const publicPathName: any = ["/auth/signin", "/auth/home"];
  const protectedOnboardingPaths = ["/onboarding/"]; // Add onboarding paths as protected
  const isNewUser = useAppSelector((state) => state.user.userData.isNewUser);
  const [onboardingInitialized, setOnboardingInitialized] = useState(false);
  const [homeUser, setHomeUser] = useState<any>(null);
  const auth = getAuth();
  const [user, loading, error] = useAuthState(auth);
  console.log("firebase auth", user, homeUser);
  const { setShowTrainingPlanModal } = useTrainingPlan();
  const [authCheckAttempted, setAuthCheckAttempted] = useState(false);
  const [domainWhitelistError, setDomainWhitelistError] = useState(false);

  // Helper function to check if current path is an onboarding path
  const isOnboardingPath = (path: string) => {
    return protectedOnboardingPaths.some((onboardingPath) =>
      path.startsWith(onboardingPath),
    );
  };

  console.log("[RouteGuard] Initial Render - Pathname:", pathname);
  console.log("[RouteGuard] Is Onboarding Path:", isOnboardingPath(pathname));
  console.log(
    "[RouteGuard] NEXT_PUBLIC_VERCEL_URL:",
    process.env.NEXT_PUBLIC_VERCEL_URL,
  );

  useEffect(() => {
    console.log("[RouteGuard] checkIsLoading effect runs");
    checkIsLoading();
    const homeUser = localStorage.getItem("homeUSer");
    if (homeUser) {
      setHomeUser(JSON.parse(homeUser));
    }

    // Auto-register current domain with Firebase if needed
    const currentHostname = window.location.hostname;
    // Only register domains that are not localhost/127.0.0.1 and look like preview deployments
    if (
      currentHostname !== "localhost" &&
      !currentHostname.includes("127.0.0.1") &&
      (currentHostname.includes("vercel") ||
        currentHostname.includes("-athlea"))
    ) {
      registerDomainWithFirebase(currentHostname);
    }
  }, [pathname]);

  // Function to register the current domain with Firebase auth
  const registerDomainWithFirebase = async (domain: string) => {
    try {
      console.log(`[RouteGuard] Attempting to register domain: ${domain}`);

      // Call our API endpoint to register the domain (ensure path is correct for app router)
      const response = await axios.post(
        "/api/firebase-auth-domain", // Path should not need changing if using /api prefix
        { domain },
        {
          headers: {
            Authorization: `Bearer ${process.env.NEXT_PUBLIC_FIREBASE_API_UPDATE_KEY || ""}`,
          },
        },
      );

      if (response.data.success) {
        console.log(
          `[RouteGuard] Domain registration response:`,
          response.data,
        );
      } else {
        console.warn(
          `[RouteGuard] Domain registration failed:`,
          response.data.message,
        );
      }
    } catch (error) {
      console.error(
        "[RouteGuard] Error registering domain with Firebase:",
        error,
      );
    }
  };

  const checkIsLoading = () => {
    if (isLoadingTempVal.current) {
      const interval = setInterval(() => {
        if (!isLoadingTempVal.current) {
          clearInterval(interval);
          // authCheck();
        }
      }, 1000);
    }
  };

  console.log(
    "[RouteGuard] Setting up onAuthStateChanged listener. Auth object:",
    auth,
  );

  useEffect(() => {
    console.log("[RouteGuard] onAuthStateChanged effect setup.");
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log("[RouteGuard] onAuthStateChanged triggered. User:", user);
      // alert('firebase user' + JSON.stringify(user))
      if (user) {
        console.log("[RouteGuard] User is signed in:", user.uid);

        // CRITICAL: Check if we're on an onboarding path FIRST
        if (isOnboardingPath(pathname)) {
          console.log(
            "[RouteGuard] Authenticated user on onboarding path",
            pathname,
            ". Preserving onboarding route - NO REDIRECT.",
          );
          setAuthCheckAttempted(true);
          setAuthorized(true);
          // Return early to prevent any other redirect logic
          return;
        }

        // Only redirect if on public paths (signin/home)
        if (publicPathName.includes(pathname)) {
          console.log(
            "[RouteGuard] Authenticated user on public path",
            pathname,
            "Redirecting to /",
          );
          router.push("/");
        } else {
          console.log(
            "[RouteGuard] Authenticated user already on protected path",
            pathname,
            ". No redirect needed.",
          );
          setAuthCheckAttempted(true);
          setAuthorized(true);
        }
      } else {
        // No active user session
        console.log("[RouteGuard] User is not signed in (auth state is null).");

        // CRITICAL: If we're on an onboarding path and not signed in, redirect to signin
        if (isOnboardingPath(pathname)) {
          console.log(
            "[RouteGuard] User not signed in but on onboarding path",
            pathname,
            ". Redirecting to /auth/home",
          );
          router.push("/auth/home");
          setAuthCheckAttempted(true);
          return;
        }

        const email = window.localStorage.getItem("emailForSignIn");
        console.log("[RouteGuard] Retrieved emailForSignIn:", email);
        const currentHref = window.location.href;
        console.log("[RouteGuard] Current window.location.href:", currentHref);
        console.log(
          "[RouteGuard] URL Details - Host:",
          window.location.host,
          "Path:",
          window.location.pathname,
          "Search:",
          window.location.search,
        );

        // If no email, it's definitely not the email link flow for this device
        if (!email) {
          console.log("[RouteGuard] No email found in local storage.");
          // Only redirect if we are not already on a public page like signin/home
          if (!publicPathName.includes(pathname)) {
            console.log(
              "[RouteGuard] Not on public path, redirecting to /auth/home",
            );
            router.push("/auth/home");
          } else {
            console.log(
              "[RouteGuard] Already on public path, no redirect needed.",
            );
          }
          setAuthCheckAttempted(true);
          console.log(
            "[RouteGuard] Setting authCheckAttempted=true (no email case)",
          );
          return;
        }

        // Email found, check if the current URL is a sign-in link
        console.log(
          "[RouteGuard] Checking isSignInWithEmailLink with href:",
          currentHref,
        );
        console.log("[RouteGuard] Auth object before check:", auth);
        const isLink = isSignInWithEmailLink(auth, currentHref);
        console.log("[RouteGuard] isSignInWithEmailLink result:", isLink);

        if (isLink) {
          console.log(
            "[RouteGuard] isLink is true. Attempting signInWithEmailLink...",
          );
          // alert('isSignInWithEmailLink');
          setAuthCheckAttempted(true);
          console.log(
            "[RouteGuard] Setting authCheckAttempted=true (isLink true case)",
          );
          (async () => {
            try {
              console.log(
                "[RouteGuard] Calling signInWithEmailLink with email:",
                email,
                "and href:",
                currentHref,
              );
              const result = await signInWithEmailLink(
                auth,
                email,
                currentHref,
              );
              console.log(
                "[RouteGuard] signInWithEmailLink SUCCESS. User:",
                result.user?.uid,
              );
              // Redirect logic after successful sign-in (removed redundant parts)
              setAuthorized(true);
              window.localStorage.removeItem("emailForSignIn");
              console.log(
                "[RouteGuard] Removed emailForSignIn from local storage.",
              );
              console.log("[RouteGuard] Redirecting signed-in user to /");
              router.push("/");
            } catch (error) {
              console.error(
                "[RouteGuard] Error during signInWithEmailLink:",
                error,
              );
              // Check if the error is due to domain whitelisting issues
              if (
                (error as any).code === "auth/unauthorized-continue-uri" ||
                (error as any).message?.includes("Domain not whitelisted")
              ) {
                console.log("[RouteGuard] Domain whitelist error detected");
                setDomainWhitelistError(true);
                // Display a notification or guidance about whitelist issue
                dispatch(
                  addSingleNotification({
                    title: "Authentication Error",
                    description: `Current domain (${window.location.hostname}) needs to be added to Firebase auth domains. Please contact administrator.`,
                  }),
                );
              }
              // Handle error (e.g., show message, redirect to signin with error)
              window.localStorage.removeItem("emailForSignIn");
              console.log(
                "[RouteGuard] Removed emailForSignIn from local storage after error.",
              );
              console.log(
                "[RouteGuard] Redirecting to /auth/signin?error=invalidLink after error.",
              );
              router.push("/auth/signin?error=invalidLink");
            }
          })();
        } else {
          // Email exists, but isSignInWithEmailLink is false.
          // This could be a timing issue, or the user just landed on the signin page normally
          // while having an old email stored. Don't redirect immediately.
          console.log(
            "[RouteGuard] isLink is false. Email found, but not a valid sign-in link URL currently. Allowing component to render potentially.",
          );
          setAuthCheckAttempted(true);
          console.log(
            "[RouteGuard] Setting authCheckAttempted=true (isLink false case)",
          );
          // If we are *not* on the signin page, but have an email stored, it's likely an old link attempt or stale state. Redirect home.
          if (pathname !== "/auth/signin") {
            console.log(
              "[RouteGuard] Not on signin page, but email found and isLink is false. Redirecting to /auth/home as likely stale state.",
            );
            router.push("/auth/home");
          } else {
            console.log(
              "[RouteGuard] On signin page and isLink is false. No redirect, allowing signin page to handle.",
            );
          }
        }
      }
    });

    console.log("[RouteGuard] onAuthStateChanged listener attached.");

    return () => {
      console.log("[RouteGuard] Cleaning up onAuthStateChanged listener.");
      unsubscribe();
    };
    // Rerun this effect if the pathname changes, as the URL might update
  }, [auth, router, pathname, isNewUser, dispatch]);

  useEffect(() => {
    console.log(
      "[RouteGuard] Authorization Effect Triggered. Loading:",
      loading,
      "AuthCheckAttempted:",
      authCheckAttempted,
      "User:",
      user?.uid,
      "Pathname:",
      pathname,
      "Is Onboarding Path:",
      isOnboardingPath(pathname),
    );
    // This effect handles the case where the initial auth state is loading or hasn't been determined yet.
    // We only want to render children if the auth check has been attempted.
    if (!loading && authCheckAttempted) {
      // If user is authenticated OR if we are on a public path OR on an onboarding path, authorize rendering
      if (
        user ||
        publicPathName.includes(pathname) ||
        isOnboardingPath(pathname)
      ) {
        console.log("[RouteGuard] Authorizing user for path:", pathname);
        setAuthorized(true);
      } else {
        // If not authenticated and not on a public/onboarding path after check, deauthorize
        console.log("[RouteGuard] Denying access to path:", pathname);
        setAuthorized(false);
        // As a fallback, if somehow ended up here unauthed and not on public/onboarding path, redirect.
        if (!publicPathName.includes(pathname) && !isOnboardingPath(pathname)) {
          console.log(
            "[RouteGuard] Redirecting unauthenticated user to /auth/home",
          );
          router.push("/auth/home");
        }
      }
    } else if (!loading && !authCheckAttempted) {
      // Still loading or auth check hasn't run/completed yet
      console.log(
        "[RouteGuard] Auth check not completed, denying access temporarily",
      );
      setAuthorized(false); // Don't authorize rendering until check completes
    } else {
      // Auth is still loading
      console.log("[RouteGuard] Firebase auth still loading, waiting...");
      setAuthorized(false);
    }
  }, [loading, user, pathname, router, authCheckAttempted]);

  useEffect(() => {
    isLoadingTempVal.current = isLoading;
  }, [isLoading]);

  useEffect(() => {
    isAuthTemp.current = user ? true : false;
    console.log("User====>Router", user);
    if (user) {
      handleSaveUserData();
      ensureUserProfileExists(user.uid);
    }
  }, [user, homeUser]);

  // Display a warning if there's a domain whitelist error
  useEffect(() => {
    if (domainWhitelistError) {
      console.warn(`[RouteGuard] Firebase domain whitelist error. 
      Current domain (${window.location.hostname}) needs to be added to Firebase authorized domains.
      Go to Firebase Console -> Authentication -> Settings -> Authorized domains to add this domain.`);
    }
  }, [domainWhitelistError]);

  // LC9dQKiGXHYUQ03IF6f2S45xabF3
  // 673d8dee53dd7475901b03ad
  const handleSaveUserData = async () => {
    try {
      // alert('save new user')
      if (!user || !user.uid) {
        console.error("User data is not available");
        return;
      }

      const response = await axios.post("/api/users", {
        email: user.email,
        family_name: homeUser?.family_name || user?.displayName || undefined,
        given_name: homeUser?.given_name || undefined,
        user_id: user.uid,
        picture: user.photoURL,
      });

      console.log("response.data:", response.data);

      const { isNewUser: newUserStatus, ...userData } = response.data;

      dispatch(addUserData(userData));
      dispatch(setIsNewUser(newUserStatus));

      if (response.data.updatedProfile) {
        dispatch(
          addUserData({ training_profile: response.data.updatedProfile }),
        );
      } else {
        const result = await dispatch(refetchTrainingProfile(user.uid));
        if (refetchTrainingProfile.fulfilled.match(result)) {
          dispatch(
            addSingleNotification({
              title: "Profile Updated",
              description:
                "Your training profile has been successfully updated.",
            }),
          );

          // try {
          //   await axios.post(`/api/notifications`, {
          //     user_id: user.id,
          //     notification: {
          //       title: "Profile Updated",
          //       description:
          //         "Your training profile has been successfully updated.",
          //     },
          //   });
          // } catch (error) {
          //   console.error("Failed to save notification to backend:", error);
          // }
        }
      }

      console.log("isNewUser:", newUserStatus);
      console.log("userData:", userData);
      console.log("User ID for onboarding:", user.uid);
    } catch (error) {
      console.error("Error saving user data:", error);
    }
  };

  return authorized && children;
};

export default RouteGuard;
