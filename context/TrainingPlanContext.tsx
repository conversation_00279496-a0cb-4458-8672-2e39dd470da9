"use client";
import React, { createContext, useState, useContext, ReactNode } from "react";

interface TrainingPlanContextType {
  showTrainingPlanModal: boolean;
  setShowTrainingPlanModal: React.Dispatch<React.SetStateAction<boolean>>;
  selectedPanel: "custom" | "existing" | null;
  setSelectedPanel: React.Dispatch<
    React.SetStateAction<"custom" | "existing" | null>
  >;
  showExportPlanModal: boolean;
  setShowExportPlanModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const TrainingPlanContext = createContext<TrainingPlanContextType | undefined>(
  undefined,
);

export const TrainingPlanProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [showTrainingPlanModal, setShowTrainingPlanModal] = useState(false);
  const [showExportPlanModal, setShowExportPlanModal] = useState(false);
  const [selectedPanel, setSelectedPanel] = useState<
    "custom" | "existing" | null
  >(null);

  return (
    <TrainingPlanContext.Provider
      value={{
        showTrainingPlanModal,
        setShowTrainingPlanModal,
        selectedPanel,
        setSelectedPanel,
        showExportPlanModal,
        setShowExportPlanModal
      }}
    >
      {children}
    </TrainingPlanContext.Provider>
  );
};

export const useTrainingPlan = () => {
  const context = useContext(TrainingPlanContext);
  if (context === undefined) {
    throw new Error(
      "useTrainingPlan must be used within a TrainingPlanProvider",
    );
  }
  return context;
};
