import { useAppDispatch, useAppSelector } from "@/store/jobHook";
import { setRecentKeyWords } from "@/store/slices/userSlice";
import React, { createContext, useState, useContext, ReactNode, useEffect } from "react";

interface KeywordContextType {
  selectedKeyword: string | null;
  setSelectedKeyword: (keyword: string | null) => void;
  setKeyWordList: React.Dispatch<React.SetStateAction<string[]>>
}

const KeywordContext = createContext<KeywordContextType | undefined>(undefined);

export const KeywordProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);
  const [keywordList, setKeyWordList] = useState<string[]>([])
  const dispatch = useAppDispatch();
  const { userData } = useAppSelector((state) => state.user);
  const userId = userData.user_id;
  console.log('selectedKeyword context', selectedKeyword, keywordList);

  useEffect(() => {
    if ((selectedKeyword || keywordList.length > 0) && userId) {
      (async () => {
        try {
          const response = await fetch(`/api/users/${userId}/recent-keyword`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ keywords: keywordList.length > 0 ? keywordList : [selectedKeyword] }),
          });
          if (response.ok) {
            const data = await response.json();
            console.log("Recent keyword data received:", data);
            if (data.recent_keywords) {
              dispatch(setRecentKeyWords(data.recent_keywords))
            }
          }
          setKeyWordList([])
        } catch (error) {
          console.error('Recent keyword save error')
          setKeyWordList([])
        }
      })()
    }
  }, [selectedKeyword, keywordList, userId])

  return (
    <KeywordContext.Provider value={{ selectedKeyword, setSelectedKeyword, setKeyWordList }}>
      {children}
    </KeywordContext.Provider>
  );
};

export const useKeyword = () => {
  const context = useContext(KeywordContext);
  if (context === undefined) {
    throw new Error("useKeyword must be used within a KeywordProvider");
  }
  return context;
};
