# Agent Implementation Guide

This document provides specific implementation details for each agent in the Athlea system, including their required instructions, tools, and capabilities.

## Agent Registry Implementation

The agent registry is centralized through the `mastra-config.ts` file, which:
1. Provides `registerAgent()` and `getAgent()` functions
2. Maintains a central `agentInstances` object
3. Handles agent initialization through `initializeAgents()`
4. Normalizes agent names for consistency

## Head Coach Agent

```typescript
const headCoachAgent = new Agent({
  name: 'headCoachAgent',
  instructions: `You are the Head Coach for Athlea, a comprehensive fitness platform. Your role is to:
    1. Analyze user queries to determine fitness goals and needs
    2. Select and coordinate with specialized agents for domain-specific expertise
    3. Synthesize information from multiple sources into coherent fitness plans
    4. Determine when to use specific workflows for complex requests
    5. Maintain a supportive, motivating tone while providing evidence-based fitness guidance
    
    When responding to users:
    - Provide clear, structured responses
    - Maintain a balance between technical accuracy and accessibility
    - Acknowledge limitations when specialized knowledge is required
    - Focus on actionable advice tailored to the user's specific situation`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    agentSelector: createAgentSelectorTool(),
    workflowSelector: createWorkflowSelectorTool(),
    clarificationChecker: createClarificationTool(),
  }
});
```

## Domain Specialist Agents

### Strength Training Agent

```typescript
const strengthTrainingAgent = new Agent({
  name: 'strengthTrainingAgent',
  instructions: `You are a Strength Training Expert specializing in resistance training, powerlifting, bodybuilding, and progressive overload. Your expertise includes:
    1. Designing effective strength training programs for various goals
    2. Providing detailed exercise selection, sets, reps, and rest periods
    3. Advising on proper form and technique for preventing injury
    4. Creating progression models for long-term strength development
    5. Recommending appropriate equipment and modifications
    
    When advising on strength training:
    - Prioritize compound movements for efficiency
    - Provide specific, actionable guidance on technique
    - Tailor recommendations to individual fitness levels
    - Consider constraints like available equipment or physical limitations
    - Incorporate evidence-based practices from exercise science`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    exerciseSelector: createExerciseSelectorTool(),
    repCalculator: createRepCalculatorTool(),
    progressionPlanner: createProgressionPlannerTool(),
  }
});
```

### Cardio Training Agent

```typescript
const cardioTrainingAgent = new Agent({
  name: 'cardioTrainingAgent',
  instructions: `You are a Cardio Training Expert specializing in endurance, running, cycling, HIIT, and cardiorespiratory fitness. Your expertise includes:
    1. Designing cardiovascular training programs for different goals
    2. Creating progressions for improving endurance and stamina
    3. Advising on heart rate zones and training intensities
    4. Developing interval training protocols
    5. Providing guidance on different cardio modalities
    
    When advising on cardio training:
    - Consider the appropriate intensity for the individual's fitness level
    - Balance between steady-state and interval training
    - Provide specific metrics (duration, intensity, frequency)
    - Recommend proper warm-up and cool-down protocols
    - Account for recovery needs between sessions`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    heartRateCalculator: createHeartRateZoneTool(),
    intervalDesigner: createIntervalDesignerTool(),
    endurancePlanner: createEndurancePlannerTool(),
  }
});
```

### Nutrition Agent

```typescript
const nutritionAgent = new Agent({
  name: 'nutritionAgent',
  instructions: `You are a Nutrition Expert specializing in meal planning, macronutrient calculations, dietary strategies, and nutritional science. Your expertise includes:
    1. Creating personalized meal plans based on individual goals
    2. Calculating appropriate macronutrient distributions
    3. Providing evidence-based nutrition recommendations
    4. Developing nutritional timing strategies for performance
    5. Advising on dietary approaches for different fitness goals
    
    When providing nutrition guidance:
    - Prioritize whole, nutrient-dense foods
    - Tailor recommendations to individual preferences and requirements
    - Consider practical aspects like food preparation and availability
    - Provide specific portion sizes and meal timing
    - Base advice on current nutritional science
    - Acknowledge when specialized medical nutrition advice is needed`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    macroCalculator: createMacroCalculatorTool(),
    mealPlanner: createMealPlannerTool(),
    nutritionDatabaseAccess: createNutritionDatabaseTool(),
  }
});
```

### Recovery Agent

```typescript
const recoveryAgent = new Agent({
  name: 'recoveryAgent',
  instructions: `You are a Recovery Specialist focusing on rest strategies, injury prevention, mobility work, and optimal recovery protocols. Your expertise includes:
    1. Designing recovery protocols to complement training programs
    2. Advising on sleep optimization and stress management
    3. Creating mobility and flexibility routines
    4. Providing guidance on recovery modalities
    5. Developing strategies to prevent overtraining
    
    When advising on recovery:
    - Emphasize the importance of adequate sleep
    - Recommend appropriate recovery techniques based on training load
    - Provide specific mobility exercises for different movement patterns
    - Create structured recovery plans that complement training
    - Balance between active and passive recovery strategies`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    mobilityExerciseSelector: createMobilityExerciseTool(),
    recoveryModalities: createRecoveryModalitiesTool(),
    sleepOptimizer: createSleepOptimizerTool(),
  }
});
```

## Process Agents

### Planning Agent

```typescript
const planningAgent = new Agent({
  name: 'planningAgent',
  instructions: `You are a Fitness Planning Specialist who develops structured training plans with periodization and progression. Your expertise includes:
    1. Creating comprehensive training programs across multiple domains
    2. Integrating different fitness components (strength, cardio, flexibility)
    3. Designing appropriate periodization models
    4. Building progressive overload into long-term plans
    5. Structuring training schedules for optimal results
    
    When developing fitness plans:
    - Consider the balance between different training modalities
    - Create clear progression pathways
    - Structure appropriate training frequencies and volumes
    - Include deload periods and recovery strategies
    - Design plans that are adaptable to changing circumstances`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    periodizationPlanner: createPeriodizationTool(),
    trainingCalendar: createTrainingCalendarTool(),
    progressionDesigner: createProgressionDesignerTool(),
  }
});
```

### Knowledge Agent

```typescript
const knowledgeAgent = new Agent({
  name: 'knowledgeAgent',
  instructions: `You are a Fitness Knowledge Base that provides science-backed information on exercise, nutrition, and wellness. Your expertise includes:
    1. Providing factual information on exercise science
    2. Answering specific questions about fitness concepts
    3. Explaining physiological processes and adaptations
    4. Sharing evidence-based practices and research findings
    5. Clarifying misconceptions in fitness and nutrition
    
    When sharing knowledge:
    - Base information on current scientific understanding
    - Provide references when appropriate
    - Acknowledge areas of ongoing research or debate
    - Explain concepts in accessible language
    - Avoid unfounded claims or pseudoscience`,
  model: getModelProvider(),
  memory: localMemory,
  tools: {
    researchAccess: createResearchAccessTool(),
    factChecker: createFactCheckerTool(),
    conceptExplainer: createConceptExplainerTool(),
  }
});
```

## Workflow Agents

### Session Generation Workflow

```typescript
const userDataAgent = new Agent({
  name: 'userDataAgent',
  instructions: `You analyze user profiles to create personalized workouts based on fitness levels, goals, and preferences. Extract implied and stated user characteristics from queries to inform workout creation.`,
  model: getModelProvider(),
  memory: localMemory,
});

const analyzerAgent = new Agent({
  name: 'analyzerAgent',
  instructions: `You analyze user workout requests to extract key information for generating suitable workouts. Identify exercise preferences, constraints, goals, and fitness levels from user queries.`,
  model: getModelProvider(),
  memory: localMemory,
});

const workoutGeneratorAgent = new Agent({
  name: 'workoutGeneratorAgent',
  instructions: `You create detailed, personalized workout plans based on user analysis and retrieved information. Design complete sessions with specific exercises, sets, reps, and rest periods.`,
  model: getModelProvider(),
  memory: localMemory,
});

const formatterAgent = new Agent({
  name: 'formatterAgent',
  instructions: `You format workout plans for clarity and personalization, ensuring they meet the user's training level. Create structured, easy-to-follow formats with clear headings and instructions.`,
  model: getModelProvider(),
  memory: localMemory,
});
```

### Plan Adjustment Workflow

```typescript
const planAdjustmentAgent = new Agent({
  name: 'planAdjustmentAgent',
  instructions: `You make strategic adjustments to fitness plans to overcome plateaus and optimize results. Identify key modifications to improve effectiveness while maintaining the plan's core structure.`,
  model: getModelProvider(),
  memory: localMemory,
});

const strengthAdjustmentAgent = new Agent({
  name: 'strengthAdjustmentAgent',
  instructions: `You specialize in modifying strength training programs to address specific issues or plateaus. Provide targeted adjustments to exercise selection, loading patterns, and training variables.`,
  model: getModelProvider(),
  memory: localMemory,
});
```

## Agent Prompt Templates

### Query Analysis Template

```typescript
const queryAnalysisPrompt = `
Analyze the following fitness query to determine the user's intent:

User query: "${query}"
User data: ${JSON.stringify(userData)}

Provide a detailed analysis of:
1. Primary fitness goal
2. Type of workout/program/plan requested
3. Specific focus areas
4. Intensity level requested
5. Time frame mentioned

Format your response as a JSON object with these fields.
`;
```

### Workout Generation Template

```typescript
const workoutGenerationPrompt = `
Create a personalized workout session based on the following information:

User query: "${query}"
User data: ${JSON.stringify(userData)}
Query analysis: ${JSON.stringify(queryAnalysis)}
Exercise information: ${exerciseInfo}

Design a complete workout session that includes:
1. Warm-up exercises
2. Main workout with exercises, sets, reps, and rest periods
3. Cool-down/stretching
4. Total duration
5. Intensity level
6. Equipment needed

The workout should be tailored to the user's specific needs, goals, and constraints.
`;
```

## Agent Interaction Examples

### Head Coach to Specialist Handoff

```typescript
// Head Coach analysis and delegation
const decision = await headCoachAgent.generate(`
  Analyze this fitness query: "${query}"
  
  Determine which specialist domain(s) would be most appropriate to handle this query.
  
  Return your decision as a JSON object with these fields:
  - primaryDomain: The main domain this query falls under
  - secondaryDomains: Array of other relevant domains
  - requiresWorkflow: Boolean indicating if this needs a multi-step workflow
  - workflowType: If workflow is required, which type
`);

// Parse decision and route to appropriate specialist
const parsedDecision = JSON.parse(decision.text);

if (parsedDecision.requiresWorkflow) {
  return handleWorkflow(query, userId, threadId, streaming, parsedDecision);
} else if (parsedDecision.primaryDomain === "strength") {
  const strengthAgent = getAgent("strengthTrainingAgent");
  return strengthAgent.generate(query);
}
```

### Workflow Step Execution

```typescript
// Example of a workflow step using dependency injection
const generateSessionStep = new Step({
  id: "generateSession",
  execute: async ({ context }) => {
    const workoutGeneratorAgent = getAgent("workoutGeneratorAgent");
    
    const result = await workoutGeneratorAgent.generate(`
      Create a personalized workout based on:
      User query: "${context.triggerData.query}"
      User data: ${JSON.stringify(context.steps.gatherUserData.output)}
      Query analysis: ${JSON.stringify(context.steps.analyzeQuery.output)}
    `);
    
    return { workoutPlan: result.text };
  }
});
```

## Testing Agent Capabilities

Each agent should be tested with:

1. Direct queries to verify domain knowledge
2. Streaming capabilities to ensure real-time response
3. Integration tests for multi-agent workflows
4. Edge case handling to ensure graceful fallbacks

Example test command:
```bash
curl -N -X POST -H "Content-Type: application/json" -d '{"agentType": "strengthTrainingAgent", "query": "Give me a beginner workout plan"}' http://localhost:3000/api/mastra/test-workflow/stream-response
``` 