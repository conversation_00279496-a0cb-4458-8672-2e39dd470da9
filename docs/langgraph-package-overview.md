# LangGraph Package Overview

## Introduction
LangGraph is a framework for building stateful, multi-agent applications with LangChain. It provides a toolkit for creating complex agent workflows through a graph-based architecture, enabling the orchestration of multiple specialized agents in a structured way.

This document provides a detailed deep dive into LangGraph.js—from simple implementations to advanced multi-agent orchestration patterns. Code examples and conceptual explanations are included to help you understand how to build robust workflows, including support for streaming responses and integrating code execution as a tool.

---

## Core Concepts

### State Graph
At the heart of LangGraph is the `StateGraph` – a directed graph model that represents workflow between agent nodes:
- **Nodes**: Represent agent functions or processing steps.
- **Edges**: Define possible transitions between nodes.
- **State**: Shared data that flows through the graph, accessible to all nodes.

### Annotations
Annotations define the schema for state data that flows through the graph:
- Provide typing information for various state properties.
- Help manage state transitions between nodes.
- *Example*: `MessagesAnnotation` for conversation history.

### Node Types
Several types of nodes can be used within a LangGraph:
- **Agent Nodes**: LLM-powered decision makers (e.g., `strengthAgentNode`).
- **Tool Nodes**: Execute specific operations or tools (e.g., `codeExecutorNode`).
- **Custom Nodes**: Any function that processes and returns state.

### Graph Execution
The execution flow in LangGraph follows these steps:
1. Start at a defined entry point (typically `__start__` or the `START` constant).
2. Execute the current node's function with the current state.
3. Determine the next node based on edges or conditional logic.
4. Move to the next node and update state.
5. Continue until reaching an end node (`END` or `__end__`).

---

## Implementation Details for LangGraph v0.2.56

### Proper Method Chaining
In LangGraph v0.2.56, the API supports method chaining for building graphs:

```javascript
const builder = new StateGraph({ channels: {} })
  .addNode("nodeName", nodeFunction)
  .addEdge(START, "nodeName")  
  .addEdge("nodeName", END);
```

### State Channels
State channels are crucial for proper state management:

```javascript
const channels = {
  userQuery: {},
  userProfile: {},
  streamCallback: {},
  finalAnswer: {}
};

const builder = new StateGraph({ channels });
```

### State Preservation
When returning state from a node, always preserve existing state values:

```javascript
return {
  ...state,  // Important: spread existing state to preserve all properties
  newProperty: someValue
};
```

### START and END Constants
Use the imported constants for entry and exit points:

```javascript
const { StateGraph, START, END } = require("@langchain/langgraph");
// Then use them as:
builder.addEdge(START, "firstNode");
builder.addEdge("lastNode", END);
```

### Conditional Routing
For v0.2.56, use the following pattern for conditional routing:

```javascript
// Add conditional routing
builder.addConditionalEdges("sourceNode", routerFunction);

// Where routerFunction returns the target node name:
const routerFunction = (state) => {
  // Decide which node to route to
  return targetNodeName;
};
```

---

## Memory in LangGraph

### What is Memory?
Memory in AI applications refers to the ability to process, store, and effectively recall information from past interactions. With memory, your agents can learn from feedback and adapt to users' preferences. This guide is divided into two sections based on the scope of memory recall: short-term memory and long-term memory.

### Short-term memory
Short-term memory, or thread-scoped memory, can be recalled at any time from within a single conversational thread with a user. LangGraph manages short-term memory as a part of your agent's state. State is persisted to a database using a checkpointer so the thread can be resumed at any time. Short-term memory updates when the graph is invoked or a step is completed, and the State is read at the start of each step.

Long-term memory is shared across conversational threads. It can be recalled at any time and in any thread. Memories are scoped to any custom namespace, not just within a single thread ID. LangGraph provides stores (reference doc) to let you save and recall long-term memories.

Both are important to understand and implement for your application.

#### Managing long conversation history
Long conversations pose a challenge to today's LLMs. The full history may not even fit inside an LLM's context window, resulting in an irrecoverable error. Even if your LLM technically supports the full context length, most LLMs still perform poorly over long contexts. They get "distracted" by stale or off-topic content, all while suffering from slower response times and higher costs.

Managing short-term memory is an exercise of balancing precision & recall with your application's other performance requirements (latency & cost). As always, it's important to think critically about how you represent information for your LLM and to look at your data. We cover a few common techniques for managing message lists below and hope to provide sufficient context for you to pick the best tradeoffs for your application:

- **Editing message lists:** How to think about trimming and filtering a list of messages before passing to language model.
- **Summarizing past conversations:** A common technique to use when you don't just want to filter the list of messages.

#### Editing message lists
Chat models accept context using messages, which include developer provided instructions (a system message) and user inputs (human messages). In chat applications, messages alternate between human inputs and model responses, resulting in a list of messages that grows longer over time. Because context windows are limited and token-rich message lists can be costly, many applications can benefit from using techniques to manually remove or forget stale information.

The most direct approach is to remove old messages from a list (similar to a least-recently used cache).

The typical technique for deleting content from a list in LangGraph is to return an update from a node telling the system to delete some portion of the list. You get to define what this update looks like, but a common approach would be to let you return an object or dictionary specifying which values to retain.

```typescript
import { Annotation } from "@langchain/langgraph";

const StateAnnotation = Annotation.Root({
  myList: Annotation<any[]>({
    reducer: (
      existing: string[],
      updates: string[] | { type: string; from: number; to?: number }
    ) => {
      if (Array.isArray(updates)) {
        // Normal case, add to the history
        return [...existing, ...updates];
      } else if (typeof updates === "object" && updates.type === "keep") {
        // You get to decide what this looks like.
        // For example, you could simplify and just accept a string "DELETE"
        // and clear the entire list.
        return existing.slice(updates.from, updates.to);
      }
      // etc. We define how to interpret updates
      return existing;
    },
    default: () => [],
  }),
});

type State = typeof StateAnnotation.State;

function myNode(state: State) {
  return {
    // We return an update for the field "myList" saying to
    // keep only values from index -5 to the end (deleting the rest)
    myList: { type: "keep", from: -5, to: undefined },
  };
}
```
LangGraph will call the "reducer" function any time an update is returned under the key "myList". Within that function, we define what types of updates to accept. Typically, messages will be added to the existing list (the conversation will grow); however, we've also added support to accept a dictionary that lets you "keep" certain parts of the state. This lets you programmatically drop old message context.

Another common approach is to let you return a list of "remove" objects that specify the IDs of all messages to delete. If you're using the LangChain messages and the messagesStateReducer reducer (or MessagesAnnotation, which uses the same underlying functionality) in LangGraph, you can do this using a RemoveMessage.

```typescript
import { RemoveMessage, AIMessage } from "@langchain/core/messages";
import { MessagesAnnotation } from "@langchain/langgraph";

type State = typeof MessagesAnnotation.State;

function myNode1(state: State) {
  // Add an AI message to the `messages` list in the state
  return { messages: [new AIMessage({ content: "Hi" })] };
}

function myNode2(state: State) {
  // Delete all but the last 2 messages from the `messages` list in the state
  const deleteMessages = state.messages
    .slice(0, -2)
    .map((m) => new RemoveMessage({ id: m.id }));
  return { messages: deleteMessages };
}
```
In the example above, the MessagesAnnotation allows us to append new messages to the messages state key as shown in myNode1. When it sees a RemoveMessage, it will delete the message with that ID from the list (and the RemoveMessage will then be discarded). For more information on LangChain-specific message handling, check out this how-to on using RemoveMessage.

See this [how-to guide](https://js.langchain.com/v0.2/docs/how_to/manage_message_history_llm/) for example usage.

#### Summarizing past conversations
The problem with trimming or removing messages, as shown above, is that we may lose information from culling of the message queue. Because of this, some applications benefit from a more sophisticated approach of summarizing the message history using a chat model.

Simple prompting and orchestration logic can be used to achieve this. As an example, in LangGraph we can extend the MessagesAnnotation to include a summary key.

```typescript
import { MessagesAnnotation, Annotation } from "@langchain/langgraph";

const MyGraphAnnotation = Annotation.Root({
  ...MessagesAnnotation.spec,
  summary: Annotation<string>,
});
```
Then, we can generate a summary of the chat history, using any existing summary as context for the next summary. This summarizeConversation node can be called after some number of messages have accumulated in the messages state key.

```typescript
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, RemoveMessage } from "@langchain/core/messages";

type State = typeof MyGraphAnnotation.State;

async function summarizeConversation(state: State) {
  // First, we get any existing summary
  const summary = state.summary || "";

  // Create our summarization prompt
  let summaryMessage: string;
  if (summary) {
    // A summary already exists
    summaryMessage =
      `This is a summary of the conversation to date: ${summary}

` +
      "Extend the summary by taking into account the new messages above:";
  } else {
    summaryMessage = "Create a summary of the conversation above:";
  }

  // Add prompt to our history
  const messages = [
    ...state.messages,
    new HumanMessage({ content: summaryMessage }),
  ];

  // Assuming you have a ChatOpenAI model instance
  const model = new ChatOpenAI();
  const response = await model.invoke(messages);

  // Delete all but the 2 most recent messages
  const deleteMessages = state.messages
    .slice(0, -2)
    .map((m) => new RemoveMessage({ id: m.id }));

  return {
    summary: response.content,
    messages: deleteMessages,
  };
}
```
See this [how-to here](https://js.langchain.com/v0.2/docs/how_to/manage_message_history_llm/) for example usage.

#### Knowing when to remove messages
Most LLMs have a maximum supported context window (denominated in tokens). A simple way to decide when to truncate messages is to count the tokens in the message history and truncate whenever it approaches that limit. Naive truncation is straightforward to implement on your own, though there are a few "gotchas". Some model APIs further restrict the sequence of message types (must start with human message, cannot have consecutive messages of the same type, etc.). If you're using LangChain, you can use the trimMessages utility and specify the number of tokens to keep from the list, as well as the strategy (e.g., keep the last maxTokens) to use for handling the boundary.

Below is an example.

```typescript
import { trimMessages } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";

trimMessages(messages, {
  // Keep the last <= n_count tokens of the messages.
  strategy: "last",
  // Remember to adjust based on your model
  // or else pass a custom token_encoder
  tokenCounter: new ChatOpenAI({ modelName: "gpt-4" }),
  // Remember to adjust based on the desired conversation
  // length
  maxTokens: 45,
  // Most chat models expect that chat history starts with either:
  // (1) a HumanMessage or
  // (2) a SystemMessage followed by a HumanMessage
  startOn: "human",
  // Most chat models expect that chat history ends with either:
  // (1) a HumanMessage or
  // (2) a ToolMessage
  endOn: ["human", "tool"],
  // Usually, we want to keep the SystemMessage
  // if it's present in the original history.
  // The SystemMessage has special instructions for the model.
  includeSystem: true,
});
```

### Long-term memory
Long-term memory in LangGraph allows systems to retain information across different conversations or sessions. Unlike short-term memory, which is thread-scoped, long-term memory is saved within custom "namespaces."

LangGraph stores long-term memories as JSON documents in a store (reference doc). Each memory is organized under a custom namespace (similar to a folder) and a distinct key (like a filename). Namespaces often include user or org IDs or other labels that makes it easier to organize information. This structure enables hierarchical organization of memories. Cross-namespace searching is then supported through content filters. See the example below for an example.

```typescript
import { InMemoryStore } from "@langchain/langgraph";

// InMemoryStore saves data to an in-memory dictionary. Use a DB-backed store in production use.
const store = new InMemoryStore();
const userId = "my-user";
const applicationContext = "chitchat";
const namespace = [userId, applicationContext];
await store.put(namespace, "a-memory", {
  rules: [
    "User likes short, direct language",
    "User only speaks English & TypeScript",
  ],
  "my-key": "my-value",
});
// get the "memory" by ID
const item = await store.get(namespace, "a-memory");
// list "memories" within this namespace, filtering on content equivalence
const items = await store.search(namespace, {
  filter: { "my-key": "my-value" },
});
```
When adding long-term memory to your agent, it's important to think about how to write memories, how to store and manage memory updates, and how to recall & represent memories for the LLM in your application. These questions are all interdependent: how you want to recall & format memories for the LLM dictates what you should store and how to manage it. Furthermore, each technique has tradeoffs. The right approach for you largely depends on your application's needs. LangGraph aims to give you the low-level primitives to directly control the long-term memory of your application, based on memory Store's.

Long-term memory is far from a solved problem. While it is hard to provide generic advice, we have provided a few reliable patterns below for your consideration as you implement long-term memory.

- **Do you want to write memories "on the hot path" or "in the background"**

  Memory can be updated either as part of your primary application logic (e.g. "on the hot path" of the application) or as a background task (as a separate function that generates memories based on the primary application's state). We document some tradeoffs for each approach in the writing memories section below.

- **Do you want to manage memories as a single profile or as a collection of documents?**

  We provide two main approaches to managing long-term memory: a single, continuously updated document (referred to as a "profile" or "schema") or a collection of documents. Each method offers its own benefits, depending on the type of information you need to store and how you intend to access it.

  Managing memories as a single, continuously updated "profile" or "schema" is useful when there is well-scoped, specific information you want to remember about a user, organization, or other entity (including the agent itself). You can define the schema of the profile ahead of time, and then use an LLM to update this based on interactions. Querying the "memory" is easy since it's a simple GET operation on a JSON document. We explain this in more detail in remember a profile. This technique can provide higher precision (on known information use cases) at the expense of lower recall (since you have to anticipate and model your domain, and updates to the doc tend to delete or rewrite away old information at a greater frequency).

  Managing long-term memory as a collection of documents, on the other hand, lets you store an unbounded amount of information. This technique is useful when you want to repeatedly extract & remember items over a long time horizon but can be more complicated to query and manage over time. Similar to the "profile" memory, you still define schema(s) for each memory. Rather than overwriting a single document, you instead will insert new ones (and potentially update or re-contextualize existing ones in the process). We explain this approach in more detail in "managing a collection of memories".

- **Do you want to present memories to your agent as updated instructions or as few-shot examples?**

  Memories are typically provided to the LLM as a part of the system prompt. Some common ways to "frame" memories for the LLM include providing raw information as "memories from previous interactions with user A", as system instructions or rules, or as few-shot examples.

  Framing memories as "learning rules or instructions" typically means dedicating a portion of the system prompt to instructions the LLM can manage itself. After each conversation, you can prompt the LLM to evaluate its performance and update the instructions to better handle this type of task in the future. We explain this approach in more detail in this section.

  Storing memories as few-shot examples lets you store and manage instructions as cause and effect. Each memory stores an input or context and expected response. Including a reasoning trajectory (a chain-of-thought) can also help provide sufficient context so that the memory is less likely to be mis-used in the future. We elaborate on this concept more in this section.

We will expand on techniques for writing, managing, and recalling & formatting memories in the following section.

#### Writing memories
Humans form long-term memories when we sleep, but when and how should our agents create new memories? The two most common ways we see agents write memories are "on the hot path" and "in the background".

##### Writing memories in the hot path
This involves creating memories while the application is running. To provide a popular production example, ChatGPT manages memories using a "save_memories" tool to upsert memories as content strings. It decides whether (and how) to use this tool every time it receives a user message and multi-tasks memory management with the rest of the user instructions.

This has a few benefits. First of all, it happens "in real time". If the user starts a new thread right away that memory will be present. The user also transparently sees when memories are stored, since the bot has to explicitly decide to store information and can relate that to the user.

This also has several downsides. It complicates the decisions the agent must make (what to commit to memory). This complication can degrade its tool-calling performance and reduce task completion rates. It will slow down the final response since it needs to decide what to commit to memory. It also typically leads to fewer things being saved to memory (since the assistant is multi-tasking), which will cause lower recall in later conversations.

##### Writing memories in the background
This involves updating memory as a conceptually separate task, typically as a completely separate graph or function. Since it happens in the background, it incurs no latency. It also splits up the application logic from the memory logic, making it more modular and easy to manage. It also lets you separate the timing of memory creation, letting you avoid redundant work. Your agent can focus on accomplishing its immediate task without having to consciously think about what it needs to remember.

This approach is not without its downsides, however. You have to think about how often to write memories. If it doesn't run in realtime, the user's interactions on other threads won't benefit from the new context. You also have to think about when to trigger this job. We typically recommend scheduling memories after some point of time, cancelling and re-scheduling for the future if new events occur on a given thread. Other popular choices are to form memories on some cron schedule or to let the user or application logic manually trigger memory formation.

#### Managing memories
Once you've sorted out memory scheduling, it's important to think about how to update memory with new information.

There are two main approaches: you can either continuously update a single document (memory profile) or insert new documents each time you receive new information.

We will outline some tradeoffs between these two approaches below, understanding that most people will find it most appropriate to combine approaches and to settle somewhere in the middle.

##### Manage individual profiles
A profile is generally just a JSON document with various key-value pairs you've selected to represent your domain. When remembering a profile, you will want to make sure that you are updating the profile each time. As a result, you will want to pass in the previous profile and ask the LLM to generate a new profile (or some JSON patch to apply to the old profile).

The larger the document, the more error-prone this can become. If your document becomes too large, you may want to consider splitting up the profiles into separate sections. You will likely need to use generation with retries and/or strict decoding when generating documents to ensure the memory schemas remains valid.

##### Manage a collection of memories
Saving memories as a collection of documents simplifies some things. Each individual memory can be more narrowly scoped and easier to generate. It also means you're less likely to lose information over time, since it's easier for an LLM to generate new objects for new information than it is for it to reconcile that new information with information in a dense profile. This tends to lead to higher recall downstream.

This approach shifts some complexity to how you prompt the LLM to apply memory updates. You now have to enable the LLM to delete or update existing items in the list. This can be tricky to prompt the LLM to do. Some LLMs may default to over-inserting; others may default to over-updating. Tuning the behavior here is best done through evals, something you can do with a tool like LangSmith.

This also shifts complexity to memory search (recall). You have to think about what relevant items to use. Right now we support filtering by metadata. We will be adding semantic search shortly.

Finally, this shifts some complexity to how you represent the memories for the LLM (and by extension, the schemas you use to save each memories). It's very easy to write memories that can easily be mistaken out-of-context. It's important to prompt the LLM to include all necessary contextual information in the given memory so that when you use it in later conversations it doesn't mistakenly mis-apply that information.

#### Representing memories
Once you have saved memories, the way you then retrieve and present the memory content for the LLM can play a large role in how well your LLM incorporates that information in its responses. The following sections present a couple of common approaches. Note that these sections also will largely inform how you write and manage memories. Everything in memory is connected!

##### Update own instructions
While instructions are often static text written by the developer, many AI applications benefit from letting the users personalize the rules and instructions the agent should follow whenever it interacts with that user. This ideally can be inferred by its interactions with the user (so the user doesn't have to explicitly change settings in your app). In this sense, instructions are a form of long-form memory!

One way to apply this is using "reflection" or "Meta-prompting" steps. Prompt the LLM with the current instruction set (from the system prompt) and a conversation with the user, and instruct the LLM to refine its instructions. This approach allows the system to dynamically update and improve its own behavior, potentially leading to better performance on various tasks. This is particularly useful for tasks where the instructions are challenging to specify a priori.

Meta-prompting uses past information to refine prompts. For instance, a Tweet generator employs meta-prompting to enhance its paper summarization prompt for Twitter. You could implement this using LangGraph's memory store to save updated instructions in a shared namespace. In this case, we will namespace the memories as "agent_instructions" and key the memory based on the agent.

```typescript
import { BaseStore } from "@langchain/langgraph/store";
import { State } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";

// Node that *uses* the instructions
const callModel = async (state: State, store: BaseStore) => {
  const namespace = ["agent_instructions"];
  const instructions = await store.get(namespace, "agent_a");
  // Application logic
  const prompt = promptTemplate.format({
    instructions: instructions[0].value.instructions,
  });
  // ... rest of the logic
};

// Node that updates instructions
const updateInstructions = async (state: State, store: BaseStore) => {
  const namespace = ["instructions"];
  const currentInstructions = await store.search(namespace);
  // Memory logic
  const prompt = promptTemplate.format({
    instructions: currentInstructions[0].value.instructions,
    conversation: state.messages,
  });
  const llm = new ChatOpenAI();
  const output = await llm.invoke(prompt);
  const newInstructions = output.content; // Assuming the LLM returns the new instructions
  await store.put(["agent_instructions"], "agent_a", {
    instructions: newInstructions,
  });
  // ... rest of the logic
};
```
##### Few-shot examples
Sometimes it's easier to "show" than "tell." LLMs learn well from examples. Few-shot learning lets you "program" your LLM by updating the prompt with input-output examples to illustrate the intended behavior. While various best-practices can be used to generate few-shot examples, often the challenge lies in selecting the most relevant examples based on user input.

Note that the memory store is just one way to store data as few-shot examples. If you want to have more developer involvement, or tie few-shots more closely to your evaluation harness, you can also use a LangSmith Dataset to store your data. Then dynamic few-shot example selectors can be used out-of-the box to achieve this same goal. LangSmith will index the dataset for you and enable retrieval of few shot examples that are most relevant to the user input based upon keyword similarity (using a BM25-like algorithm for keyword based similarity).

See this [how-to video](https://www.youtube.com/watch?v=QNBgUu8lM18) for example usage of dynamic few-shot example selection in LangSmith. Also, see this [blog post](https://blog.langchain.dev/improving-tool-use-via-prompt-level-few-shot-learning/) showcasing few-shot prompting to improve tool calling performance and this [blog post](https://blog.langchain.dev/reducing-repetitive-output-with-few-shot-prompting/) using few-shot example to align an LLMs to human preferences.

### Checkpointers vs. Memory Stores for Persistence

LangGraph offers two distinct mechanisms for managing persistence and memory, serving different purposes:

1.  **Checkpointers (`BaseCheckpointSaver` implementations like `MemorySaver`, custom database checkpointers):**
    *   **Purpose:** Automatically saves and loads the **entire state snapshot** of a graph execution for a specific conversation thread (`thread_id`). This includes the full message history *within that thread*, intermediate node results, and the current execution step.
    *   **Mechanism:** Passed during graph compilation (`graph.compile({ checkpointer: ... })`). LangGraph handles invoking the checkpointer's `put`, `getTuple`, etc., methods automatically during graph execution (`invoke`, `stream`) based on the provided `thread_id` config.
    *   **Use Case:** Enables **pausing and resuming** conversations, maintaining the exact state across potentially long periods or even server restarts (if using a persistent backend like a database checkpointer). Handles the persistence of the immediate conversational flow.
    *   **"Intelligence":** Managed by the LangGraph framework, which determines when and what state to save/load for thread continuity.

2.  **Memory Stores (`Store` interface implementations like `InMemoryStore`, database-backed Stores):**
    *   **Purpose:** Manages **long-term, cross-thread knowledge**. This is for storing arbitrary information like user preferences, facts learned, or summaries of past interactions that should be accessible across *different* conversation threads, often associated with a user or other entity.
    *   **Mechanism:** Requires **explicit logic** within your graph nodes or agent implementation.
        *   **Saving:** Often done via a dedicated tool (e.g., an `upsert_memory` tool) that the agent calls when it identifies important information, or within specific nodes that analyze the conversation and decide to `store.put(namespace, key, value)`. You define the structure (`namespace`, `key`, `value`).
        *   **Recalling:** Typically involves a pre-processing step or node that uses `store.search(namespace, { query: "..." })` (often leveraging semantic search) based on the current input/context. The retrieved memories are then formatted and injected into the agent's prompt for the current turn.
    *   **Use Case:** Provides the agent with relevant background context, personalization details, or learned information from previous, separate interactions, enhancing response quality and continuity of understanding *about* the user/topic across conversations.
    *   **"Intelligence":** Resides in your custom agent/graph logic, which must decide *what* information is important enough to save long-term and *which* stored memories are relevant to recall and inject for the current task.

**In essence:** Use a **Checkpointer** for saving the execution state *of* a single conversation thread. Use a **Memory Store** for building a persistent, searchable knowledge base *about* users or topics accessible *across* multiple conversations, requiring explicit agent logic for saving and recalling memories. They often work together: the checkpointer saves the volatile state (including messages), while the Memory Store provides relevant long-term context fetched via explicit search logic within graph nodes.

---

## Fitness Coach Multi-Agent System
The implementation in this codebase creates a hierarchical multi-agent system for fitness coaching:

### System Architecture
- **Head Coach**: Central supervisor agent that orchestrates domain experts.
- **Domain Experts**: Specialized agents for different fitness aspects:
  - Strength Coach
  - Cardio Coach
  - Nutrition Coach
  - Recovery Coach
  - Mental Training Coach
- **Code Executor**: Utility node for running JavaScript calculations (optional).

### Graph Structure
```
START → headCoach → [domain experts] → headCoach → END
```

With conditional routing to determine which domain experts to call.

### State Management
The state includes:
- `userQuery`: The user's question or request.
- `userProfile`: User fitness profile information.
- Domain-specific responses (e.g. `strengthResponse`, `nutritionResponse`, etc.).
- `finalAnswer`: The synthesized response to return.
- `streamCallback`: Function for token streaming support.

### Streaming Implementation
LangGraph supports streaming responses through callback functions:

```javascript
// Define a streaming callback
const streamCallback = (token) => {
  process.stdout.write(token);
};

// Add streaming to LLM config
const llm = new ChatOpenAI({
  modelName: "gpt-4o-mini",
  temperature: 0.7,
  streaming: !!streamCallback,
  callbacks: streamCallback
    ? [
        {
          handleLLMNewToken(token) {
            streamCallback(token);
          },
        },
      ]
    : undefined,
});

// Pass callback via state
const initialState = {
  userQuery: "How can I build muscle?",
  userProfile: { /* ... */ },
  streamCallback: streamCallback,
};
```

This allows LangGraph to push LLM tokens to the client in real time (for example, using SSE in a Next.js API route).

## Human-in-the-Loop

LangGraph supports human-in-the-loop (HITL) workflows that integrate human judgment at critical decision points in the agent execution flow. This enables dynamic interaction where agents can request clarification, validation, or approval from human users.

### Core HITL Concepts

#### The `interrupt` Function
The `interrupt` function is the primary mechanism for implementing HITL workflows:

```javascript
import { interrupt } from "@langchain/langgraph";

function humanApprovalNode(state) {
  // Pause execution and wait for human input
  const humanInput = interrupt({
    question: "Do you approve this recommendation?",
    context: state.recommendationText,
    timestamp: Date.now()
  });
  
  // When execution resumes, humanInput will contain the value provided by the human
  return {
    ...state,
    humanApproved: humanInput,
    humanInTheLoopActive: false
  };
}
```

When `interrupt()` is called, LangGraph throws a special `GraphInterrupt` error that pauses graph execution. The execution can be resumed later by providing input from the human.

#### Command Object
The `Command` object is used to resume execution after an interrupt:

```javascript
import { Command } from "@langchain/langgraph";

// Resume execution with human input
await graph.invoke(
  new Command({ resume: "Approved" }),
  { configurable: { thread_id: "thread-123" }}
);
```

#### Checkpointer
To support HITL, the graph must use a checkpointer that saves the state between executions:

```javascript
// Simple in-memory checkpointer implementation
const memoryCheckpointer = {
  getState: async (ns, threadId) => stateStorage[`${threadId}:${ns}`] || null,
  setState: async (ns, threadId, state) => {
    stateStorage[`${threadId}:${ns}`] = state;
    return state;
  },
  listCheckpoints: async (threadId) => 
    Object.keys(stateStorage)
      .filter(key => key.startsWith(`${threadId}:`))
      .map(key => key.split(':')[1])
};

// Storage for the in-memory checkpointer
const stateStorage = {};

// Using the checkpointer when compiling the graph
const graph = stateGraph.compile({
  checkpointer: memoryCheckpointer
});
```

### Common HITL Patterns

#### 1. Approval/Review Pattern
Pausing for human approval of agent actions:

```javascript
function approvalNode(state) {
  const isApproved = interrupt({
    action: "review",
    content: state.generatedContent,
    timestamp: Date.now()
  });
  
  if (isApproved === true) {
    return { ...state, approved: true };
  } else {
    return { ...state, approved: false, feedback: isApproved };
  }
}
```

#### 2. Information Request Pattern
Requesting additional information from users:

```javascript
function informationRequestNode(state) {
  const userInfo = interrupt({
    action: "input",
    query: "What is your fitness level?",
    options: ["Beginner", "Intermediate", "Advanced"],
    timestamp: Date.now()
  });
  
  return {
    ...state,
    userFitnessLevel: userInfo
  };
}
```

#### 3. Interactive Tool Use
Allowing humans to approve or modify tool parameters before use:

```javascript
function toolApprovalNode(state) {
  const toolParams = state.toolParams;
  
  const approvedParams = interrupt({
    action: "tool_approval",
    tool: state.toolName,
    params: toolParams,
    timestamp: Date.now()
  });
  
  return {
    ...state,
    approvedToolParams: approvedParams
  };
}
```

### API Implementation for HITL

In a Next.js API route, HITL requires:

1. Thread management for maintaining execution state
2. Detecting and handling interrupts
3. Providing a way for clients to resume execution

```javascript
// API route handler example
export async function POST(req: NextRequest) {
  const { message, threadId, isResumeCommand, resumeValue } = await req.json();
  
  // Thread configuration
  const threadConfig = { 
    configurable: { thread_id: threadId || generateUniqueId() }
  };
  
  // Create stream response
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Initialize graph with appropriate callbacks
        const graph = createGraph(streamCallback);
        
        try {
          if (isResumeCommand && resumeValue) {
            // Resume execution with human input
            await graph.stream(
              new Command({ resume: resumeValue }),
              threadConfig
            );
          } else {
            // Regular execution
            await graph.stream({
              messages: [{ role: "user", content: message }],
              callbacks: [
                {
                  handleLLMNewToken(token) {
                    streamCallback(token);
                  },
                },
              ],
            }, threadConfig);
          }
        } catch (error) {
          // Check if this is an interrupt error
          if (error.name === "GraphInterrupt" || error.message?.includes("interrupt")) {
            // Get the interrupt information
            const state = await graph.getState(threadConfig);
            const interruptTask = state.tasks.find(
              task => task.interrupts && task.interrupts.length > 0
            );
            
            if (interruptTask) {
              // Send interrupt info to client
              controller.enqueue(
                new TextEncoder().encode(
                  `data: __INTERRUPT:${JSON.stringify(interruptTask.interrupts[0].value)}__\n\n`
                )
              );
            }
          } else {
            // Handle regular errors
            controller.enqueue(
              new TextEncoder().encode(
                `data: Error: ${error.message}\n\n`
              ),
            );
          }
        }
        
        // Signal end of stream
        controller.enqueue(new TextEncoder().encode(`data: [DONE]\n\n`));
        controller.close();
      } catch (error) {
        controller.error(error);
      }
    },
  });
  
  // Return SSE response
  return new NextResponse(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  });
}
```

### Example: Specialty Agent with HITL

The following pattern demonstrates a specialized agent that can request human input when needed:

```javascript
// Creating an agent with HITL capability
const expertAgent = createReactAgent({
  llm,
  tools: [
    specializedTool,
    askHumanTool, // Tool for requesting human input
  ],
  name: "expert_agent",
  prompt: `You are an expert agent with specialized knowledge. When you need clarification or additional information from the user, use the ask_human tool.`,
});

// Ask human tool definition
const askHumanTool = tool(
  async (args) => {
    console.log(`Requesting human input: ${args.question}`);
    
    // Signal that human input is needed
    return {
      humanInTheLoopRequested: true,
      question: args.question,
      domain: args.domain || "unknown",
      action: args.action || "input",
    };
  },
  {
    name: "ask_human",
    description: "Ask a human for input on a specific question",
    schema: z.object({
      question: z.string().describe("The question to ask the human"),
      domain: z.string().optional().describe("The domain context (optional)"),
      action: z.string().optional().describe("The action type (optional)"),
    }),
  },
);
```

This approach enables building complex, interactive agent systems where humans contribute their judgment and expertise at strategic points in the workflow.

## LangGraph.js Multi-Agent Orchestration: From Simple to Advanced

### Basic Implementation

#### 1. Simple StateGraph Example
A basic example demonstrating how nodes, edges, and state work together:

```typescript
import { StateGraph, START, END, Annotation } from "@langchain/langgraph";

// Define state schema with an annotation
const State = Annotation.Root({
  count: Annotation<number>({
    reducer: (current, update) => current + update,
    default: 0,
  }),
});

// Implement simple nodes that increment the count
async function nodeOne(state: typeof State.State) {
  return { ...state, count: 1 };
}
async function nodeTwo(state: typeof State.State) {
  return { ...state, count: 1 };
}
async function nodeThree(state: typeof State.State) {
  return { ...state, count: 1 };
}

// Build the graph with method chaining
const workflow = new StateGraph(State)
  .addNode("one", nodeOne)
  .addNode("two", nodeTwo)
  .addNode("three", nodeThree)
  .addEdge(START, "one")
  .addEdge("one", "two")
  .addEdge("two", "three")
  .addEdge("three", END)
  .compile();

// Run the graph
const result = await workflow.invoke({});
console.log(result); // Expected output: { count: 3 }
```

This simple graph shows how each node updates the state sequentially.

### Intermediate Implementation: Multi-Agent Collaboration

#### 2. Domain Specialist Nodes and Supervisor Node

##### Domain Specialist Node Example (Strength)
```typescript
// lib/graph/nodes/strengthAgentNode.ts
import { Node, StateType } from 'langgraph';
import { ChatOpenAI } from 'langchain/chat_models/openai';

export const strengthAgentNode = new Node({
  id: 'strengthAgentNode',
  run: async (state: StateType) => {
    const { userQuery, userProfile } = state;
    const llm = new ChatOpenAI({
      openAIApiKey: process.env.OPENAI_API_KEY,
      temperature: 0.7,
      modelName: 'gpt-3.5-turbo',
    });
    const systemPrompt = `
      You are a Strength Training Coach.
      User Profile: ${JSON.stringify(userProfile)}
    `;
    const userPrompt = `User Query: ${userQuery}`;
    const response = await llm.callAsSystem(systemPrompt, userPrompt);
    return { ...state, strengthResponse: response.text };
  },
});
```

(Create similar nodes for cardioAgentNode.ts, nutritionAgentNode.ts, recoveryAgentNode.ts, and mentalAgentNode.ts with their respective domain-specific prompts.)

##### Head Coach Node (Supervisor)
```typescript
// lib/graph/nodes/headCoachNode.ts
import { Node, StateType, dispatch, Command } from 'langgraph';
import { strengthAgentNode } from './strengthAgentNode';
import { cardioAgentNode } from './cardioAgentNode';
import { nutritionAgentNode } from './nutritionAgentNode';
import { recoveryAgentNode } from './recoveryAgentNode';
import { mentalAgentNode } from './mentalAgentNode';

// Simple domain detection via keyword matching
function detectDomains(query: string): string[] {
  const domains: string[] = [];
  if (/strength|lift|weights/i.test(query)) domains.push('strength');
  if (/cardio|run|cycle|hiit/i.test(query)) domains.push('cardio');
  if (/nutrition|diet|calories|macros/i.test(query)) domains.push('nutrition');
  if (/sore|recover|pain|injury/i.test(query)) domains.push('recovery');
  if (/motivation|stress|mental|anxiety/i.test(query)) domains.push('mental');
  return domains;
}

export const headCoachNode = new Node({
  id: 'headCoachNode',
  run: async (state: StateType, context) => {
    const { userQuery = '' } = state;
    const domains = detectDomains(userQuery);

    // If no domain matches, note it in state
    if (domains.length === 0) {
      return { 
        ...state, 
        headCoachNote: `No domain detected; handling as general query.`
      };
    }

    // Dispatch domain nodes in parallel based on detected domains
    const tasks = [];
    if (domains.includes('strength')) tasks.push(dispatch(strengthAgentNode));
    if (domains.includes('cardio')) tasks.push(dispatch(cardioAgentNode));
    if (domains.includes('nutrition')) tasks.push(dispatch(nutritionAgentNode));
    if (domains.includes('recovery')) tasks.push(dispatch(recoveryAgentNode));
    if (domains.includes('mental')) tasks.push(dispatch(mentalAgentNode));

    const newState = await context.runParallel(tasks, state);

    // Merge responses to form a final answer
    const combinedAnswer = `
      Strength: ${newState.strengthResponse || 'N/A'}
      Cardio: ${newState.cardioResponse || 'N/A'}
      Nutrition: ${newState.nutritionResponse || 'N/A'}
      Recovery: ${newState.recoveryResponse || 'N/A'}
      Mental: ${newState.mentalResponse || 'N/A'}
    `.trim();

    return { ...newState, finalAnswer: combinedAnswer };
  },
});
```

##### Supervised Multi-Agent Workflow Example
```typescript
// lib/graph/fitnessGraph.ts
import { Graph } from 'langgraph';
import { headCoachNode } from './nodes/headCoachNode';

export const fitnessGraph = new Graph({
  id: 'fitnessGraph',
  entrypoint: 'headCoachNode',
  nodes: [
    headCoachNode,
    // Domain nodes can be dynamically dispatched from headCoachNode.
  ],
});
```

Run the graph with initial state:

```typescript
const initialState = {
  userQuery: "I need a 30-minute HIIT session for cardio and some advice on recovery for sore muscles.",
  userProfile: { name: "Alice", fitnessLevel: "intermediate", goals: ["build muscle"] },
};
const resultState = await fitnessGraph.run(initialState);
console.log(resultState.finalAnswer);
```

### Advanced Implementation: Hierarchical Teams & Human-in-the-Loop

#### Hierarchical Agent Teams (Subgraphs)
You can encapsulate a set of nodes as a subgraph. For example, suppose you have a Research Team subgraph:

```typescript
// lib/graph/researchTeamGraph.ts
import { StateGraph, START, END } from 'langgraph';
import { searcherAgent } from './nodes/searcherAgent';
import { webScraperAgent } from './nodes/webScraperAgent';

const researchTeamGraph = new StateGraph(/* appropriate state annotation */)
  .addNode("searcher", searcherAgent)
  .addNode("scraper", webScraperAgent)
  .addEdge(START, "searcher")
  .addEdge("searcher", "scraper")
  .addEdge("scraper", END)
  .compile();

export default researchTeamGraph;
```

Then, in your main graph, you can include this subgraph as a node:

```typescript
// lib/graph/largeProjectGraph.ts
import { StateGraph } from 'langgraph';
import researchTeamGraph from './researchTeamGraph';
import { headCoachNode } from './nodes/headCoachNode';

const largeWorkflow = new StateGraph(/* state annotation for full project */)
  .addNode("HeadSupervisor", headCoachNode)
  .addNode("ResearchTeam", researchTeamGraph) // subgraph node
  .addEdge(START, "HeadSupervisor")
  .addEdge("HeadSupervisor", "ResearchTeam")
  .addEdge("ResearchTeam", END)
  .compile();

await largeWorkflow.invoke({ /* initial state */ });
```

#### Adding Human-in-the-Loop
Insert an "Approval" node that pauses execution:

```typescript
// lib/graph/nodes/approvalNode.ts
import { Node, StateType } from 'langgraph';

export const approvalNode = new Node({
  id: 'approvalNode',
  run: async (state: StateType) => {
    // Do nothing – wait for human to set state.approved = true
    return state;
  },
});
```

Then, use a conditional edge:

```typescript
builder.addConditionalEdges("approvalNode", (state) => state.approved, {
  true: END,
  false: "approvalNode"  // loop until approved is true
});
```

#### Code Execution Integration (Optional)
Create a node to safely execute JavaScript code:

```typescript
// lib/graph/nodes/codeExecutorNode.ts
import { Node, StateType } from 'langgraph';
import vm from 'node:vm';

export const codeExecutorNode = new Node({
  id: 'codeExecutorNode',
  run: async (state: StateType) => {
    const { codeToRun } = state;
    if (!codeToRun) return state;
    try {
      const result = vm.runInNewContext(codeToRun, {}, { timeout: 1000 });
      return { ...state, codeResult: String(result) };
    } catch (err: any) {
      return { ...state, codeResult: `Error: ${err.message}` };
    }
  },
});
```

You can dispatch this node from your supervisor if a code execution is needed.

### Next.js API Route with Streaming
Below is an example Next.js API route that leverages LangGraph's streaming capabilities. This route receives a user query, invokes the graph in streaming mode, and streams LLM tokens via Server-Sent Events (SSE).

```typescript
// app/api/domain-coach/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { fitnessGraph } from '@/lib/graph/fitnessGraph';

export async function POST(req: NextRequest) {
  try {
    const { query, userId } = await req.json();
    if (!query) {
      return NextResponse.json({ error: 'No query provided' }, { status: 400 });
    }

    // (Optional) Load user profile from DB if needed.
    const userProfile = { name: "Alice", fitnessLevel: "intermediate" };

    // Build initial state for the graph.
    const initState = { userQuery: query, userProfile };

    // Invoke the graph in streaming mode:
    // Assuming fitnessGraph.stream returns an object with textStream as an async iterable.
    const streamResult = await fitnessGraph.stream(initState);
    const { textStream } = streamResult;
    const encoder = new TextEncoder();

    const sseStream = new ReadableStream({
      async start(controller) {
        try {
          for await (const token of textStream) {
            const chunk = `data: ${token}\n\n`;
            controller.enqueue(encoder.encode(chunk));
          }
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
          controller.close();
        } catch (err) {
          console.error('Error streaming tokens:', err);
          controller.enqueue(encoder.encode(`data: [ERROR]\n\n`));
          controller.close();
        }
      },
    });

    return new Response(sseStream, {
      headers: {
        'Content-Type': 'text/event-stream; charset=utf-8',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
      },
    });
  } catch (error: any) {
    console.error('Error in domain-coach route:', error);
    return NextResponse.json({ error: error.message || 'Unknown error' }, { status: 500 });
  }
}
```

This route creates an SSE stream that delivers tokens as they are produced by the graph. On the client side, you can use the EventSource API to listen for data: events and build the final response in real time.

### Advanced Streaming and Event Handling Considerations

While LangGraph provides streaming capabilities, handling events correctly, especially in complex multi-agent scenarios, requires careful attention.

#### Token Attribution with `streamEvents`

A common challenge when using the `streamEvents` method (especially with `version: "v2"`) is reliably associating streamed LLM tokens back to the specific agent node that generated them. The event data (e.g., in `on_llm_stream` or `on_chat_model_stream`) might not inherently contain the originating node's name, particularly when multiple nodes might execute concurrently or asynchronously.

**Workaround: Tracking Active Nodes**

A robust workaround involves manually tracking which nodes are currently executing using the `on_chain_start` and `on_chain_end` events:

1.  **Initialize a Set:** Maintain a `Set` (e.g., `activeNodes`) outside the event loop to store the names of currently running agent nodes.
2.  **`on_chain_start`:** When this event occurs for an agent node, add its name (`event.name`) to the `activeNodes` set.
3.  **`on_chain_end`:** When this event occurs for an agent node, remove its name from the `activeNodes` set.
4.  **Token Handling (`on_llm_stream`/`on_chat_model_stream`):** Before processing a token, check the size of the `activeNodes` set.
    *   If `activeNodes.size === 1`, you can confidently attribute the token to the single agent name in the set.
    *   If `activeNodes.size !== 1` (0 or >1), the origin is ambiguous. You might choose to suppress the token or assign it a default/general origin.

```typescript
// Example within an SSE stream handler

const activeNodes = new Set<string>();
const agentNodeNames = ["agent_a", "agent_b", "supervisor"]; // Define your node names

// ... inside the event loop ...
for await (const event of eventStream) {
  const eventType = event.event;
  const eventName = event.name;
  const eventData = event.data;

  if (eventType === "on_chain_start" && agentNodeNames.includes(eventName)) {
    activeNodes.add(eventName);
    console.log(`Active nodes: ${Array.from(activeNodes)}`);
    // Send agent_start signal to client if needed
  } else if (eventType === "on_chain_end" && agentNodeNames.includes(eventName)) {
    activeNodes.delete(eventName);
    console.log(`Active nodes: ${Array.from(activeNodes)}`);
  } else if (eventType === "on_chat_model_stream") {
    const chunk = eventData?.chunk;
    let agentToSend: string | null = null;
    if (activeNodes.size === 1) {
      agentToSend = Array.from(activeNodes)[0];
    } else {
      console.warn(`Ambiguous token origin. Active: ${Array.from(activeNodes)}`);
    }

    const content = chunk?.content as string | undefined;
    if (agentToSend && content) {
      // Send token attributed to agentToSend
      controller.enqueue(
        encoder.encode(
          `data: ${JSON.stringify({ type: "token", agent: agentToSend, content: content })}\n\n`,
        ),
      );
    } else {
      // Handle ambiguous or empty token
    }
  }
  // ... handle other events like on_interrupt, on_tool_end etc. ...
}
```

---

### Human-in-the-Loop (HITL) - Interrupt Handling Notes

When implementing HITL using `interrupt()` and `streamEvents`, pay close attention to how the `on_interrupt` event is handled within your stream processing loop:

*   **Reliability:** Ensure the `on_interrupt` event reliably triggers your handler logic. Log extensively to confirm it fires when expected.
*   **State Capture:** Correctly capture the necessary information from the interrupt event (e.g., the question/prompt for the user). The exact structure of `event.data` might vary, so inspect it carefully.
*   **Loop Control:** Crucially, ensure your `on_interrupt` handler **breaks** the event processing loop. Failing to break the loop after capturing the interrupt can lead to the graph finishing execution *after* the interrupt was signaled, potentially causing the server to send a premature `DONE` signal instead of the expected `NEED_INPUT` signal to the client.

```typescript
// Example within an SSE stream handler's event loop

let lastInterruptValue: any = null;

// ... inside the event loop ...
for await (const event of eventStream) {
    // ... handle other events ...

    if (event.event === "on_interrupt") {
        console.log("Interrupt event received:", event);
        // Extract interrupt value (adjust based on actual event structure)
        lastInterruptValue = event.data?.interrupts?.[0]?.value ?? "Default prompt";
        console.log("Interrupt value captured:", lastInterruptValue);
        // *** CRITICAL: Break the loop ***
        break;
    }

    // ... handle on_chain_end for graph completion ...
    if (event.event === "on_chain_end" && event.name === "Graph") {
        console.log("Graph execution finished.");
        if (lastInterruptValue !== null) {
             // Send NEED_INPUT because an interrupt occurred before graph end
             controller.enqueue(/* NEED_INPUT signal */);
        } else {
             // Send DONE because graph finished normally
             controller.enqueue(/* DONE signal */);
        }
        controller.close();
        break; // Also break here
    }
}

// After the loop, double-check interrupt state if loop ended unexpectedly
if (lastInterruptValue !== null) {
    // Ensure NEED_INPUT is sent if loop exited due to interrupt
    controller.enqueue(/* NEED_INPUT signal */);
    controller.close(); // Ensure closed if not already
} else if (!controller.closed) { // Check if already closed by graph end
    // Ensure DONE is sent if loop finished without interrupt and wasn't closed
    controller.enqueue(/* DONE signal */);
    controller.close();
}

```

---

### Streaming Implementation Notes

*   **Structured SSE:** When streaming events (like tokens, agent starts, tool results, interrupts) to a client using Server-Sent Events (SSE), it's highly recommended to define a clear, structured JSON format for the `data:` payload. This makes parsing on the client-side much more robust than relying on simple string prefixes or unstructured text.

```json
// Example SSE Data Structure
{
  "type": "token" | "agent_start" | "tool_result" | "need_input" | "done" | "error",
  "agent"?: string,    // For token, agent_start, tool_result
  "content"?: string,  // For token, tool_result
  "prompt"?: string,   // For need_input
  "message"?: string   // For error
}

// Example SSE message
`data: ${JSON.stringify({ type: "token", agent: "nutrition_coach", content: "eat more veggies" })}\n\n`
`data: ${JSON.stringify({ type: "need_input", prompt: "What is your current weight?" })}\n\n`
```

---

